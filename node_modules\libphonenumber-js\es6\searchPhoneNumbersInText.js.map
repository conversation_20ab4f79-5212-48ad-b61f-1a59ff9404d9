{"version": 3, "file": "searchPhoneNumbersInText.js", "names": ["PhoneNumberMatcher", "normalizeArguments", "searchPhoneNumbersInText", "_normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "_objectSpread", "v2", "_defineProperty", "Symbol", "iterator", "next", "hasNext", "done", "value"], "sources": ["../source/searchPhoneNumbersInText.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function searchPhoneNumbersInText() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, { ...options, v2: true }, metadata)\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (matcher.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: matcher.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,kBAAkB,MAAM,yBAAyB;AAExD,eAAe,SAASC,wBAAwBA,CAAA,EAAG;EAClD,IAAAC,mBAAA,GAAoCF,kBAAkB,CAACG,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC/B,IAAMC,OAAO,GAAG,IAAIR,kBAAkB,CAACK,IAAI,EAAAI,aAAA,CAAAA,aAAA,KAAOH,OAAO;IAAEI,EAAE,EAAE;EAAI,IAAIH,QAAQ,CAAC;EAChF,OAAAI,eAAA,KACEC,MAAM,CAACC,QAAQ,cAAI;IACnB,OAAO;MACHC,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAQ;QACX,IAAIN,OAAO,CAACO,OAAO,CAAC,CAAC,EAAE;UACzB,OAAO;YACNC,IAAI,EAAE,KAAK;YACXC,KAAK,EAAET,OAAO,CAACM,IAAI,CAAC;UACrB,CAAC;QACF;QACA,OAAO;UACNE,IAAI,EAAE;QACP,CAAC;MACC;IACJ,CAAC;EACF,CAAC;AAEH", "ignoreList": []}