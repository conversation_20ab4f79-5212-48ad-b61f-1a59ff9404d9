{"version": 3, "sources": ["../../../../src/build/webpack/plugins/telemetry-plugin.ts"], "names": ["NormalModule", "FEATURE_MODULE_MAP", "Map", "FEATURE_MODULE_REGEXP_MAP", "BUILD_FEATURES", "eliminatedPackages", "Set", "findFeatureInModule", "module", "type", "normalizedIdentifier", "identifier", "replace", "feature", "path", "endsWith", "regexp", "test", "findUniqueOriginModulesInConnections", "connections", "originModule", "originModules", "connection", "has", "add", "TelemetryPlugin", "constructor", "buildFeaturesMap", "usageTracker", "featureName", "set", "invocationCount", "get", "keys", "apply", "compiler", "hooks", "make", "tapAsync", "name", "compilation", "callback", "finishModules", "modules", "modulesFinish", "moduleGraph", "getIncomingConnections", "size", "options", "mode", "watchMode", "tap", "moduleHooks", "getCompilationHooks", "loader", "loaderContext", "usages", "values", "packagesUsedInServerSideProps", "Array", "from"], "mappings": "AACA,SAASA,YAAY,QAAQ,qCAAoC;AAkEjE,sEAAsE;AACtE,MAAMC,qBAAmD,IAAIC,IAAI;IAC/D;QAAC;QAAc;KAAiB;IAChC;QAAC;QAAqB;KAAwB;IAC9C;QAAC;QAAqB;KAAwB;IAC9C;QAAC;QAAe;KAAkB;IAClC;QAAC;QAAgB;KAAmB;CACrC;AACD,MAAMC,4BAA0D,IAAID,IAAI;IACtE;QAAC;QAAqB;KAAwC;IAC9D;QAAC;QAAoB;KAAuC;IAC5D;QAAC;QAAoB;KAAuC;IAC5D;QAAC;QAAmB;KAAsC;CAC3D;AAED,uDAAuD;AACvD,MAAME,iBAAiC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,qBAAqB,IAAIC;AAE/B;;CAEC,GACD,SAASC,oBAAoBC,MAAc;IACzC,IAAIA,OAAOC,IAAI,KAAK,mBAAmB;QACrC;IACF;IACA,MAAMC,uBAAuBF,OAAOG,UAAU,GAAGC,OAAO,CAAC,OAAO;IAChE,KAAK,MAAM,CAACC,SAASC,KAAK,IAAIb,mBAAoB;QAChD,IAAIS,qBAAqBK,QAAQ,CAACD,OAAO;YACvC,OAAOD;QACT;IACF;IACA,KAAK,MAAM,CAACA,SAASG,OAAO,IAAIb,0BAA2B;QACzD,IAAIa,OAAOC,IAAI,CAACP,uBAAuB;YACrC,OAAOG;QACT;IACF;AACF;AAEA;;;;CAIC,GACD,SAASK,qCACPC,WAAyB,EACzBC,YAAoB;IAEpB,MAAMC,gBAAgB,IAAIf;IAC1B,KAAK,MAAMgB,cAAcH,YAAa;QACpC,IACE,CAACE,cAAcE,GAAG,CAACD,WAAWF,YAAY,KAC1CE,WAAWF,YAAY,KAAKA,cAC5B;YACAC,cAAcG,GAAG,CAACF,WAAWF,YAAY;QAC3C;IACF;IACA,OAAOC;AACT;AAEA;;;;CAIC,GACD,OAAO,MAAMI;IAMX,qEAAqE;IACrEC,YAAYC,gBAAuC,CAAE;aAN7CC,eAA2C,IAAI1B;QAOrD,KAAK,MAAM2B,eAAezB,eAAgB;YACxC,IAAI,CAACwB,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiBJ,iBAAiBK,GAAG,CAACH,eAAe,IAAI;YAC3D;QACF;QAEA,KAAK,MAAMA,eAAe5B,mBAAmBgC,IAAI,GAAI;YACnD,IAAI,CAACL,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiB;YACnB;QACF;QAEA,KAAK,MAAMF,eAAe1B,0BAA0B8B,IAAI,GAAI;YAC1D,IAAI,CAACL,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiB;YACnB;QACF;IACF;IAEAG,MAAMC,QAA0B,EAAQ;QACtCA,SAASC,KAAK,CAACC,IAAI,CAACC,QAAQ,CAC1Bb,gBAAgBc,IAAI,EACpB,OAAOC,aAAkCC;YACvCD,YAAYJ,KAAK,CAACM,aAAa,CAACJ,QAAQ,CACtCb,gBAAgBc,IAAI,EACpB,OAAOI,SAA2BC;gBAChC,KAAK,MAAMpC,UAAUmC,QAAS;oBAC5B,MAAM9B,UAAUN,oBAAoBC;oBACpC,IAAI,CAACK,SAAS;wBACZ;oBACF;oBACA,MAAMM,cAAc,AAClBqB,YACAK,WAAW,CAACC,sBAAsB,CAACtC;oBACrC,MAAMa,gBAAgBH,qCACpBC,aACAX;oBAEF,IAAI,CAACoB,YAAY,CAACI,GAAG,CAACnB,SAAUkB,eAAe,GAC7CV,cAAc0B,IAAI;gBACtB;gBACAH;YACF;YAEFH;QACF;QAEF,IAAIN,SAASa,OAAO,CAACC,IAAI,KAAK,gBAAgB,CAACd,SAASe,SAAS,EAAE;YACjEf,SAASC,KAAK,CAACI,WAAW,CAACW,GAAG,CAAC1B,gBAAgBc,IAAI,EAAE,CAACC;gBACpD,MAAMY,cAAcpD,aAAaqD,mBAAmB,CAACb;gBACrDY,YAAYE,MAAM,CAACH,GAAG,CAAC1B,gBAAgBc,IAAI,EAAE,CAACgB;oBAC5CA,cAAclD,kBAAkB,GAAGA;gBACrC;YACF;QACF;IACF;IAEAmD,SAAyB;QACvB,OAAO;eAAI,IAAI,CAAC5B,YAAY,CAAC6B,MAAM;SAAG;IACxC;IAEAC,gCAA0C;QACxC,OAAOC,MAAMC,IAAI,CAACvD;IACpB;AACF"}