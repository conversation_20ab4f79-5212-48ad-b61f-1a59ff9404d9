{"version": 3, "file": "parsePhoneNumber.js", "names": ["_normalizeArguments2", "_interopRequireDefault", "require", "_parsePhoneNumber_", "e", "__esModule", "parsePhoneNumber", "_normalizeArguments", "normalizeArguments", "arguments", "text", "options", "metadata", "parsePhoneNumber_"], "sources": ["../source/parsePhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumber_ from './parsePhoneNumber_.js'\r\n\r\nexport default function parsePhoneNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn parsePhoneNumber_(text, options, metadata)\r\n}\r\n"], "mappings": ";;;;;;AAAA,IAAAA,oBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAsD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEvC,SAASE,gBAAgBA,CAAA,EAAG;EAC1C,IAAAC,mBAAA,GAAoC,IAAAC,+BAAkB,EAACC,SAAS,CAAC;IAAzDC,IAAI,GAAAH,mBAAA,CAAJG,IAAI;IAAEC,OAAO,GAAAJ,mBAAA,CAAPI,OAAO;IAAEC,QAAQ,GAAAL,mBAAA,CAARK,QAAQ;EAC/B,OAAO,IAAAC,6BAAiB,EAACH,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAClD", "ignoreList": []}