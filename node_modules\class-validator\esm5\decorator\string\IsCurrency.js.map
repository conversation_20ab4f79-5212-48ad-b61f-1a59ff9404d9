{"version": 3, "file": "IsCurrency.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsCurrency.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,mBAAmB,MAAM,0BAA0B,CAAC;AAG3D,MAAM,CAAC,IAAM,WAAW,GAAG,YAAY,CAAC;AAExC;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,KAAc,EAAE,OAAuC;IAChF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1E,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,UAAU,CACxB,OAAuC,EACvC,iBAAqC;IAErC,OAAO,UAAU,CACf;QACE,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,UAAU,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAvC,CAAuC;YAC3E,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,8BAA8B,EAA3C,CAA2C,EAAE,iBAAiB,CAAC;SAC3G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isCurrencyValidator from 'validator/lib/isCurrency';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_CURRENCY = 'isCurrency';\n\n/**\n * Checks if the string is a valid currency amount.\n * If given value is not a string, then it returns false.\n */\nexport function isCurrency(value: unknown, options?: ValidatorJS.IsCurrencyOptions): boolean {\n  return typeof value === 'string' && isCurrencyValidator(value, options);\n}\n\n/**\n * Checks if the string is a valid currency amount.\n * If given value is not a string, then it returns false.\n */\nexport function IsCurrency(\n  options?: ValidatorJS.IsCurrencyOptions,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_CURRENCY,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isCurrency(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a currency', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}