{"version": 3, "file": "getExampleNumber.test.js", "names": ["examples", "type", "metadata", "getExampleNumber", "describe", "it", "phoneNumber", "expect", "nationalNumber", "to", "equal", "number", "countryCallingCode", "country", "be", "undefined"], "sources": ["../source/getExampleNumber.test.js"], "sourcesContent": ["import examples from '../examples.mobile.json' with { type: 'json' }\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\nimport getExampleNumber from './getExampleNumber.js'\r\n\r\ndescribe('getExampleNumber', () => {\r\n\tit('should get an example number', () => {\r\n\t\tconst phoneNumber = getExampleNumber('RU', examples, metadata)\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('9123456789')\r\n\t\texpect(phoneNumber.number).to.equal('+79123456789')\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('7')\r\n\t\texpect(phoneNumber.country).to.equal('RU')\r\n\t})\r\n\r\n\tit('should handle a non-existing country', () => {\r\n\t\texpect(getExampleNumber('XX', examples, metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,yBAAyB,QAAQC,IAAI,EAAE,MAAM;AAClE,OAAOC,QAAQ,MAAM,sBAAsB,QAAQD,IAAI,EAAE,MAAM;AAC/D,OAAOE,gBAAgB,MAAM,uBAAuB;AAEpDC,QAAQ,CAAC,kBAAkB,EAAE,YAAM;EAClCC,EAAE,CAAC,8BAA8B,EAAE,YAAM;IACxC,IAAMC,WAAW,GAAGH,gBAAgB,CAAC,IAAI,EAAEH,QAAQ,EAAEE,QAAQ,CAAC;IAC9DK,MAAM,CAACD,WAAW,CAACE,cAAc,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACzDH,MAAM,CAACD,WAAW,CAACK,MAAM,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACnDH,MAAM,CAACD,WAAW,CAACM,kBAAkB,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpDH,MAAM,CAACD,WAAW,CAACO,OAAO,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAC3C,CAAC,CAAC;EAEFL,EAAE,CAAC,sCAAsC,EAAE,YAAM;IAChDE,MAAM,CAACJ,gBAAgB,CAAC,IAAI,EAAEH,QAAQ,EAAEE,QAAQ,CAAC,CAAC,CAACO,EAAE,CAACK,EAAE,CAACC,SAAS;EACnE,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}