{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNextFontError.ts"], "names": ["SimpleWebpackError", "getNextFontError", "err", "module", "resourceResolveData", "loaders", "find", "loader", "test", "file", "JSON", "parse", "query", "slice", "path", "name", "message", "stack"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,uBAAsB;AAEzD,OAAO,SAASC,iBACdC,GAAU,EACVC,MAAW;IAEX,IAAI;QACF,MAAMC,sBAAsBD,OAAOC,mBAAmB;QACtD,IACE,CAACD,OAAOE,OAAO,CAACC,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;YACA,OAAO;QACT;QAEA,mFAAmF;QACnF,2CAA2C;QAC3C,MAAME,OAAOC,KAAKC,KAAK,CAACP,oBAAoBQ,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;QAEhE,IAAIZ,IAAIa,IAAI,KAAK,iBAAiB;YAChC,8DAA8D;YAC9D,OAAO,IAAIf,mBACTS,MACA,CAAC,sBAAsB,EAAEP,IAAIc,OAAO,CAAC,CAAC;QAE1C,OAAO;YACL,qCAAqC;YACrC,OAAO,IAAIhB,mBACTS,MACA,CAAC,uCAAuC,EAAEP,IAAIe,KAAK,CAAC,CAAC;QAEzD;IACF,EAAE,OAAM;QACN,OAAO;IACT;AACF"}