{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/file-protocol.ts"], "names": ["prepend", "remove", "candidate", "Array", "isArray", "sources", "Object", "assign", "map", "Error", "replace"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,GAEA;;CAEC;;;;;;;;;;;;;;;IACeA,OAAO;eAAPA;;IAmBAC,MAAM;eAANA;;;AAnBT,SAASD,QAAQE,SAAc;IACpC,IAAI,OAAOA,cAAc,UAAU;QACjC,OAAO,YAAYA;IACrB,OAAO,IACLA,aACA,OAAOA,cAAc,YACrBC,MAAMC,OAAO,CAACF,UAAUG,OAAO,GAC/B;QACA,OAAOC,OAAOC,MAAM,CAAC,CAAC,GAAGL,WAAW;YAClCG,SAASH,UAAUG,OAAO,CAACG,GAAG,CAACR;QACjC;IACF,OAAO;QACL,MAAM,IAAIS,MAAM;IAClB;AACF;AAKO,SAASR,OAAOC,SAAc;IACnC,IAAI,OAAOA,cAAc,UAAU;QACjC,OAAOA,UAAUQ,OAAO,CAAC,eAAe;IAC1C,OAAO,IACLR,aACA,OAAOA,cAAc,YACrBC,MAAMC,OAAO,CAACF,UAAUG,OAAO,GAC/B;QACA,OAAOC,OAAOC,MAAM,CAAC,CAAC,GAAGL,WAAW;YAClCG,SAASH,UAAUG,OAAO,CAACG,GAAG,CAACP;QACjC;IACF,OAAO;QACL,MAAM,IAAIQ,MAAM;IAClB;AACF"}