{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "names": ["getImageError", "getNotFoundError", "getModuleTrace", "input", "compilation", "visitedModules", "Set", "moduleTrace", "current", "module", "has", "add", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>", "push", "getSourceFrame", "fileName", "result", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "createOriginalStackFrame", "source", "rootDirectory", "options", "context", "modulePath", "frame", "arguments", "file", "methodName", "lineNumber", "start", "line", "column", "originalCodeFrame", "originalStackFrame", "toString", "getFormattedFileName", "loaders", "find", "loader", "test", "JSON", "parse", "resourceResolveData", "query", "slice", "path", "formattedFileName", "cyan", "yellow", "name", "message", "errorMessage", "error", "replace", "green", "importTrace", "readableIdentifier", "requestShortener", "length", "join", "red", "bold", "SimpleWebpackError", "err", "page", "rawRequest", "importedFile", "buffer", "split", "some", "includes", "concat"], "mappings": ";;;;;;;;;;;;;;;IA2KsBA,aAAa;eAAbA;;IArEAC,gBAAgB;eAAhBA;;;4BAtGyB;oCACZ;4BACM;AAGzC,6IAA6I;AAC7I;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,SAASC,eAAeC,KAAU,EAAEC,WAAgB;IAClD,MAAMC,iBAAiB,IAAIC;IAC3B,MAAMC,cAAc,EAAE;IACtB,IAAIC,UAAUL,MAAMM,MAAM;IAC1B,MAAOD,QAAS;QACd,IAAIH,eAAeK,GAAG,CAACF,UAAU,OAAM,mDAAmD;QAC1FH,eAAeM,GAAG,CAACH;QACnB,MAAMI,SAASR,YAAYS,WAAW,CAACC,SAAS,CAACN;QACjD,IAAI,CAACI,QAAQ;QACbL,YAAYQ,IAAI,CAAC;YAAEH;YAAQH,QAAQD;QAAQ;QAC3CA,UAAUI;IACZ;IAEA,OAAOL;AACT;AAEA,eAAeS,eACbb,KAAU,EACVc,QAAa,EACbb,WAAgB;IAEhB,IAAI;YAoBYc,uCAAAA,4BACJA,mCAAAA;QApBV,MAAMC,MACJhB,MAAMgB,GAAG,IAAIhB,MAAMiB,YAAY,CAACC,GAAG,CAAC,CAACC,IAAWA,EAAEH,GAAG,EAAEI,MAAM,CAACC,QAAQ,CAAC,EAAE;QAC3E,MAAMC,iBAAiBtB,MAAMM,MAAM,CAACgB,cAAc;QAElD,MAAMP,SAAS,MAAMQ,IAAAA,oCAAwB,EAAC;YAC5CC,QAAQF;YACRG,eAAexB,YAAYyB,OAAO,CAACC,OAAO;YAC1CC,YAAYd;YACZe,OAAO;gBACLC,WAAW,EAAE;gBACbC,MAAMjB;gBACNkB,YAAY;gBACZC,YAAYjB,IAAIkB,KAAK,CAACC,IAAI;gBAC1BC,QAAQpB,IAAIkB,KAAK,CAACE,MAAM;YAC1B;QACF;QAEA,OAAO;YACLP,OAAOd,CAAAA,0BAAAA,OAAQsB,iBAAiB,KAAI;YACpCJ,YAAYlB,CAAAA,2BAAAA,6BAAAA,OAAQuB,kBAAkB,sBAA1BvB,wCAAAA,2BAA4BkB,UAAU,qBAAtClB,sCAAwCwB,QAAQ,OAAM;YAClEH,QAAQrB,CAAAA,2BAAAA,8BAAAA,OAAQuB,kBAAkB,sBAA1BvB,oCAAAA,4BAA4BqB,MAAM,qBAAlCrB,kCAAoCwB,QAAQ,OAAM;QAC5D;IACF,EAAE,OAAM;QACN,OAAO;YAAEV,OAAO;YAAII,YAAY;YAAIG,QAAQ;QAAG;IACjD;AACF;AAEA,SAASI,qBACP1B,QAAgB,EAChBR,OAAW,EACX2B,UAAmB,EACnBG,MAAe;QAGb9B;IADF,KACEA,kBAAAA,QAAOmC,OAAO,qBAAdnC,gBAAgBoC,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;QACA,mFAAmF;QACnF,2CAA2C;QAC3C,OAAOE,KAAKC,KAAK,CAACxC,QAAOyC,mBAAmB,CAACC,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;IACnE,OAAO;QACL,IAAIC,oBAA4BC,IAAAA,gBAAI,EAACtC;QACrC,IAAImB,cAAcG,QAAQ;YACxBe,qBAAqB,CAAC,CAAC,EAAEE,IAAAA,kBAAM,EAACpB,YAAY,CAAC,EAAEoB,IAAAA,kBAAM,EAACjB,QAAQ,CAAC;QACjE;QAEA,OAAOe;IACT;AACF;AAEO,eAAerD,iBACpBG,WAAgC,EAChCD,KAAU,EACVc,QAAgB,EAChBR,OAAW;IAEX,IACEN,MAAMsD,IAAI,KAAK,yBACf,CACEtD,CAAAA,MAAMsD,IAAI,KAAK,sBACf,gCAAgCV,IAAI,CAAC5C,MAAMuD,OAAO,CAAA,GAEpD;QACA,OAAO;IACT;IAEA,IAAI;QACF,MAAM,EAAE1B,KAAK,EAAEI,UAAU,EAAEG,MAAM,EAAE,GAAG,MAAMvB,eAC1Cb,OACAc,UACAb;QAGF,MAAMuD,eAAexD,MAAMyD,KAAK,CAACF,OAAO,CACrCG,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,wBAAwB,CAAC,eAAe,EAAEC,IAAAA,iBAAK,EAAC,MAAM,CAAC,CAAC;QAEnE,MAAMC,cAAc;YAClB,MAAMxD,cAAcL,eAAeC,OAAOC,aACvCiB,GAAG,CAAC,CAAC,EAAET,MAAM,EAAE,GACdA,OAAOoD,kBAAkB,CAAC5D,YAAY6D,gBAAgB,GAEvD1C,MAAM,CACL,CAACkC,OACCA,QACA,CAAC,0FAA0FV,IAAI,CAC7FU,SAEF,CAAC,+BAA+BV,IAAI,CAACU,SACrC,CAAC,mBAAmBV,IAAI,CAACU;YAE/B,IAAIlD,YAAY2D,MAAM,KAAK,GAAG,OAAO;YAErC,OAAO,CAAC,sCAAsC,EAAE3D,YAAY4D,IAAI,CAAC,MAAM,CAAC;QAC1E;QAEA,IAAIT,UACFU,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,uBACT,CAAC,EAAE,EAAEV,aAAa,CAAC,GACnB,OACA3B,QACCA,CAAAA,UAAU,KAAK,OAAO,EAAC,IACxB,0DACA+B;QAEF,MAAMT,oBAAoBX,qBACxB1B,UACAR,SACA2B,YACAG;QAGF,OAAO,IAAI+B,sCAAkB,CAAChB,mBAAmBI;IACnD,EAAE,OAAOa,KAAK;QACZ,8CAA8C;QAC9C,OAAOpE;IACT;AACF;AAEO,eAAeH,cACpBI,WAAgB,EAChBD,KAAU,EACVoE,GAAU;IAEV,IAAIA,IAAId,IAAI,KAAK,2BAA2B;QAC1C,OAAO;IACT;IAEA,MAAMlD,cAAcL,eAAeC,OAAOC;IAC1C,MAAM,EAAEQ,MAAM,EAAEH,QAAAA,OAAM,EAAE,GAAGF,WAAW,CAAC,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,CAACH,SAAQ;QACtB,OAAO;IACT;IACA,MAAM+D,OAAO5D,OAAO6D,UAAU,CAACZ,OAAO,CAAC,uBAAuB;IAC9D,MAAMa,eAAejE,QAAOgE,UAAU;IACtC,MAAM9C,SAASf,OAAOa,cAAc,GAAGkD,MAAM,GAAGjC,QAAQ,CAAC;IACzD,IAAIN,aAAa,CAAC;IAClBT,OAAOiD,KAAK,CAAC,MAAMC,IAAI,CAAC,CAACvC;QACvBF;QACA,OAAOE,KAAKwC,QAAQ,CAACJ;IACvB;IACA,OAAO,IAAIJ,sCAAkB,CAC3B,CAAC,EAAEf,IAAAA,gBAAI,EAACiB,MAAM,CAAC,EAAEhB,IAAAA,kBAAM,EAACpB,WAAWM,QAAQ,IAAI,CAAC,EAChD0B,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,UAAUU,MAAM,CACvB,CAAC,gBAAgB,EAAEL,aAAa,iFAAiF,CAAC;AAGxH"}