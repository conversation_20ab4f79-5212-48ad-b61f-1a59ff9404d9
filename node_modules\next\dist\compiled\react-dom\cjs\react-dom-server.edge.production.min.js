/*
 React
 react-dom-server.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var ba=require("next/dist/compiled/react"),da=require("react-dom"),ea=Symbol.for("react.element"),ha=Symbol.for("react.portal"),ka=Symbol.for("react.fragment"),la=Symbol.for("react.strict_mode"),ma=Symbol.for("react.profiler"),sa=Symbol.for("react.provider"),ta=Symbol.for("react.consumer"),ua=Symbol.for("react.context"),Da=Symbol.for("react.forward_ref"),Ea=Symbol.for("react.suspense"),Fa=Symbol.for("react.suspense_list"),Ga=Symbol.for("react.memo"),Ha=Symbol.for("react.lazy"),Ia=Symbol.for("react.scope"),
Ja=Symbol.for("react.debug_trace_mode"),Sa=Symbol.for("react.offscreen"),Ta=Symbol.for("react.legacy_hidden"),Ua=Symbol.for("react.cache"),Va=Symbol.iterator,Wa=Array.isArray;
function Xa(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=***********(f&65535)+((***********(f>>>16)&65535)<<16)&**********;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&**********;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&**********;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=***********(f&65535)+((***********(f>>>16)&65535)<<16)&**********,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&**********}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&**********;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&**********;return(e^e>>>16)>>>0}var k=null,n=0;
function r(a,b){if(0!==b.byteLength)if(2048<b.byteLength)0<n&&(a.enqueue(new Uint8Array(k.buffer,0,n)),k=new Uint8Array(2048),n=0),a.enqueue(b);else{var c=k.length-n;c<b.byteLength&&(0===c?a.enqueue(k):(k.set(b.subarray(0,c),n),a.enqueue(k),b=b.subarray(c)),k=new Uint8Array(2048),n=0);k.set(b,n);n+=b.byteLength}}function w(a,b){r(a,b);return!0}function Ya(a){k&&0<n&&(a.enqueue(new Uint8Array(k.buffer,0,n)),k=null,n=0)}var Za=new TextEncoder;function A(a){return Za.encode(a)}
function C(a){return Za.encode(a)}function ib(a,b){"function"===typeof a.error?a.error(b):a.close()}
var D=Object.assign,E=Object.prototype.hasOwnProperty,pb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),qb={},rb={};
function sb(a){if(E.call(rb,a))return!0;if(E.call(qb,a))return!1;if(pb.test(a))return rb[a]=!0;qb[a]=!0;return!1}
var tb=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),ub=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),vb=/["'&<>]/;
function J(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=vb.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var wb=/([A-Z])/g,xb=/^ms-/,yb=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,zb={pending:!1,data:null,method:null,action:null},Ab=da.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,Tb={prefetchDNS:Bb,preconnect:Cb,preload:Db,preloadModule:Pb,preinitStyle:Qb,preinitScript:Rb,preinitModuleScript:Sb},K=[],Ub=C('"></template>'),Vb=C("<script>"),Wb=C("\x3c/script>"),Xb=C('<script src="'),Yb=C('<script type="module" src="'),Zb=C('" nonce="'),$b=C('" integrity="'),ac=C('" crossorigin="'),
bc=C('" async="">\x3c/script>'),cc=/(<\/|<)(s)(cript)/gi;function dc(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}var ec=C('<script type="importmap">'),fc=C("\x3c/script>");
function gc(a,b,c,d,e,f){var g=void 0===b?Vb:C('<script nonce="'+J(b)+'">'),h=a.idPrefix,l=[],p=null,q=a.bootstrapScriptContent,m=a.bootstrapScripts,v=a.bootstrapModules;void 0!==q&&l.push(g,A((""+q).replace(cc,dc)),Wb);void 0!==c&&("string"===typeof c?(p={src:c,chunks:[]},hc(p.chunks,{src:c,async:!0,integrity:void 0,nonce:b})):(p={src:c.src,chunks:[]},hc(p.chunks,{src:c.src,async:!0,integrity:c.integrity,nonce:b})));c=[];void 0!==d&&(c.push(ec),c.push(A((""+JSON.stringify(d)).replace(cc,dc))),c.push(fc));
d=e?{preconnects:"",fontPreloads:"",highImagePreloads:"",remainingCapacity:"number"===typeof f?f:2E3}:null;e={placeholderPrefix:C(h+"P:"),segmentPrefix:C(h+"S:"),boundaryPrefix:C(h+"B:"),startInlineScript:g,htmlChunks:null,headChunks:null,externalRuntimeScript:p,bootstrapChunks:l,importMapChunks:c,onHeaders:e,headers:d,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],viewportChunks:[],hoistableChunks:[],preconnects:new Set,fontPreloads:new Set,
highImagePreloads:new Set,styles:new Map,bootstrapScripts:new Set,scripts:new Set,bulkPreloads:new Set,preloads:{images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map},nonce:b,hoistableState:null,stylesToHoist:!1};if(void 0!==m)for(g=0;g<m.length;g++)c=m[g],d=p=void 0,f={rel:"preload",as:"script",fetchPriority:"low",nonce:b},"string"===typeof c?f.href=h=c:(f.href=h=c.src,f.integrity=d="string"===typeof c.integrity?c.integrity:void 0,f.crossOrigin=p="string"===typeof c||null==c.crossOrigin?
void 0:"use-credentials"===c.crossOrigin?"use-credentials":""),c=a,q=h,c.scriptResources[q]=null,c.moduleScriptResources[q]=null,c=[],M(c,f),e.bootstrapScripts.add(c),l.push(Xb,A(J(h))),b&&l.push(Zb,A(J(b))),"string"===typeof d&&l.push($b,A(J(d))),"string"===typeof p&&l.push(ac,A(J(p))),l.push(bc);if(void 0!==v)for(m=0;m<v.length;m++)f=v[m],p=h=void 0,d={rel:"modulepreload",fetchPriority:"low",nonce:b},"string"===typeof f?d.href=g=f:(d.href=g=f.src,d.integrity=p="string"===typeof f.integrity?f.integrity:
void 0,d.crossOrigin=h="string"===typeof f||null==f.crossOrigin?void 0:"use-credentials"===f.crossOrigin?"use-credentials":""),f=a,c=g,f.scriptResources[c]=null,f.moduleScriptResources[c]=null,f=[],M(f,d),e.bootstrapScripts.add(f),l.push(Yb,A(J(g))),b&&l.push(Zb,A(J(b))),"string"===typeof p&&l.push($b,A(J(p))),"string"===typeof h&&l.push(ac,A(J(h))),l.push(bc);return e}
function ic(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function N(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function jc(a){return N("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function kc(a,b,c){switch(b){case "noscript":return N(2,null,a.tagScope|1);case "select":return N(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return N(3,null,a.tagScope);case "picture":return N(2,null,a.tagScope|2);case "math":return N(4,null,a.tagScope);case "foreignObject":return N(2,null,a.tagScope);case "table":return N(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return N(6,null,a.tagScope);case "colgroup":return N(8,null,a.tagScope);case "tr":return N(7,null,a.tagScope)}return 5<=
a.insertionMode?N(2,null,a.tagScope):0===a.insertionMode?"html"===b?N(1,null,a.tagScope):N(2,null,a.tagScope):1===a.insertionMode?N(2,null,a.tagScope):a}var lc=C("\x3c!-- --\x3e");function mc(a,b,c,d){if(""===b)return d;d&&a.push(lc);a.push(A(J(b)));return!0}var nc=new Map,oc=C(' style="'),pc=C(":"),qc=C(";");
function rc(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(E.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=A(J(d));e=A(J((""+e).trim()))}else f=nc.get(d),void 0===f&&(f=C(J(d.replace(wb,"-$1").toLowerCase().replace(xb,"-ms-"))),nc.set(d,f)),e="number"===typeof e?0===e||tb.has(d)?A(""+
e):A(e+"px"):A(J((""+e).trim()));c?(c=!1,a.push(oc,f,pc,e)):a.push(qc,f,pc,e)}}c||a.push(O)}var Q=C(" "),Fc=C('="'),O=C('"'),Gc=C('=""');function Hc(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,A(b),Gc)}function R(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(Q,A(b),Fc,A(J(c)),O)}function Ic(a){var b=a.nextFormID++;return a.idPrefix+b}var Jc=C(J("javascript:throw new Error('React form unexpectedly submitted.')")),Kc=C('<input type="hidden"');
function Lc(a,b){this.push(Kc);if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");R(this,"name",b);R(this,"value",a);this.push(Mc)}
function Nc(a,b,c,d,e,f,g,h){var l=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=Ic(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,l=b.data):(a.push(Q,A("formAction"),Fc,Jc,O),g=f=e=d=h=null,Oc(b,c)));null!=h&&T(a,"name",h);null!=d&&T(a,"formAction",d);null!=e&&T(a,"formEncType",e);null!=f&&T(a,"formMethod",f);null!=g&&T(a,"formTarget",g);return l}
function T(a,b,c){switch(b){case "className":R(a,"class",c);break;case "tabIndex":R(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":R(a,b,c);break;case "style":rc(a,c);break;case "src":case "href":case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(Q,A(b),Fc,A(J(c)),O);break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "ref":break;
case "autoFocus":case "multiple":case "muted":Hc(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;c=""+c;a.push(Q,A("xlink:href"),Fc,A(J(c)),O);break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,A(b),Fc,A(J(c)),O);break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,A(b),Gc);break;case "capture":case "download":!0===c?a.push(Q,A(b),Gc):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(Q,A(b),Fc,A(J(c)),O);break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(Q,A(b),Fc,A(J(c)),O);break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(Q,A(b),Fc,A(J(c)),O);break;case "xlinkActuate":R(a,"xlink:actuate",
c);break;case "xlinkArcrole":R(a,"xlink:arcrole",c);break;case "xlinkRole":R(a,"xlink:role",c);break;case "xlinkShow":R(a,"xlink:show",c);break;case "xlinkTitle":R(a,"xlink:title",c);break;case "xlinkType":R(a,"xlink:type",c);break;case "xmlBase":R(a,"xml:base",c);break;case "xmlLang":R(a,"xml:lang",c);break;case "xmlSpace":R(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=ub.get(b)||b,sb(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(Q,A(b),Fc,A(J(c)),O)}}}var U=C(">"),Mc=C("/>");
function Pc(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(A(""+b))}}function Qc(a){var b="";ba.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}var Rc=C(' selected=""'),Sc=C('addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'React form unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.ownerDocument||c,(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,d,b))}});');
function Oc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,Sc,Wb))}var Tc=C("\x3c!--F!--\x3e"),Uc=C("\x3c!--F--\x3e");function M(a,b){a.push(V("link"));for(var c in b)if(E.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:T(a,c,d)}}a.push(Mc);return null}
function Vc(a,b,c){a.push(V(c));for(var d in b)if(E.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:T(a,d,e)}}a.push(Mc);return null}
function Wc(a,b){a.push(V("title"));var c=null,d=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:T(a,e,f)}}a.push(U);b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(A(J(""+b)));Pc(a,d,c);a.push(Xc("title"));return null}
function hc(a,b){a.push(V("script"));var c=null,d=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:T(a,e,f)}}a.push(U);Pc(a,d,c);"string"===typeof c&&a.push(A(J(c)));a.push(Xc("script"));return null}
function Yc(a,b,c){a.push(V(c));var d=c=null,e;for(e in b)if(E.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:T(a,e,f)}}a.push(U);Pc(a,d,c);return"string"===typeof c?(a.push(A(J(c))),null):c}var Zc=C("\n"),$c=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,ad=new Map;function V(a){var b=ad.get(a);if(void 0===b){if(!$c.test(a))throw Error("Invalid tag: "+a);b=C("<"+a);ad.set(a,b)}return b}var bd=C("<!DOCTYPE html>");
function cd(a,b,c,d,e,f,g,h,l){switch(b){case "div":case "span":case "svg":case "path":break;case "a":break;case "g":case "p":case "li":break;case "select":a.push(V("select"));var p=null,q=null,m;for(m in c)if(E.call(c,m)){var v=c[m];if(null!=v)switch(m){case "children":p=v;break;case "dangerouslySetInnerHTML":q=v;break;case "defaultValue":case "value":break;default:T(a,m,v)}}a.push(U);Pc(a,q,p);return p;case "option":var t=g.selectedValue;a.push(V("option"));var y=null,x=null,u=null,S=null,W;for(W in c)if(E.call(c,
W)){var H=c[W];if(null!=H)switch(W){case "children":y=H;break;case "selected":u=H;break;case "dangerouslySetInnerHTML":S=H;break;case "value":x=H;default:T(a,W,H)}}if(null!=t){var P=null!==x?""+x:Qc(y);if(Wa(t))for(var z=0;z<t.length;z++){if(""+t[z]===P){a.push(Rc);break}}else""+t===P&&a.push(Rc)}else u&&a.push(Rc);a.push(U);Pc(a,S,y);return y;case "textarea":a.push(V("textarea"));var B=null,na=null,aa=null,G;for(G in c)if(E.call(c,G)){var F=c[G];if(null!=F)switch(G){case "children":aa=F;break;case "value":B=
F;break;case "defaultValue":na=F;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");default:T(a,G,F)}}null===B&&null!==na&&(B=na);a.push(U);if(null!=aa){if(null!=B)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(Wa(aa)){if(1<aa.length)throw Error("<textarea> can only have at most one child.");B=""+aa[0]}B=""+aa}"string"===typeof B&&"\n"===B[0]&&a.push(Zc);null!==B&&a.push(A(J(""+B)));return null;case "input":a.push(V("input"));
var ia=null,va=null,wa=null,Ka=null,xa=null,La=null,oa=null,sc=null,tc=null,$a;for($a in c)if(E.call(c,$a)){var ca=c[$a];if(null!=ca)switch($a){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":ia=ca;break;case "formAction":va=ca;break;case "formEncType":wa=ca;break;case "formMethod":Ka=ca;break;case "formTarget":xa=ca;break;case "defaultChecked":tc=ca;break;case "defaultValue":oa=
ca;break;case "checked":sc=ca;break;case "value":La=ca;break;default:T(a,$a,ca)}}var Od=Nc(a,d,e,va,wa,Ka,xa,ia);null!==sc?Hc(a,"checked",sc):null!==tc&&Hc(a,"checked",tc);null!==La?T(a,"value",La):null!==oa&&T(a,"value",oa);a.push(Mc);null!==Od&&Od.forEach(Lc,a);return null;case "button":a.push(V("button"));var ab=null,Pd=null,Qd=null,Rd=null,Sd=null,Td=null,Ud=null,bb;for(bb in c)if(E.call(c,bb)){var ja=c[bb];if(null!=ja)switch(bb){case "children":ab=ja;break;case "dangerouslySetInnerHTML":Pd=ja;
break;case "name":Qd=ja;break;case "formAction":Rd=ja;break;case "formEncType":Sd=ja;break;case "formMethod":Td=ja;break;case "formTarget":Ud=ja;break;default:T(a,bb,ja)}}var Vd=Nc(a,d,e,Rd,Sd,Td,Ud,Qd);a.push(U);null!==Vd&&Vd.forEach(Lc,a);Pc(a,Pd,ab);if("string"===typeof ab){a.push(A(J(ab)));var Wd=null}else Wd=ab;return Wd;case "form":a.push(V("form"));var cb=null,Xd=null,pa=null,db=null,eb=null,fb=null,gb;for(gb in c)if(E.call(c,gb)){var qa=c[gb];if(null!=qa)switch(gb){case "children":cb=qa;break;
case "dangerouslySetInnerHTML":Xd=qa;break;case "action":pa=qa;break;case "encType":db=qa;break;case "method":eb=qa;break;case "target":fb=qa;break;default:T(a,gb,qa)}}var uc=null,vc=null;if("function"===typeof pa)if("function"===typeof pa.$$FORM_ACTION){var Nf=Ic(d),Ma=pa.$$FORM_ACTION(Nf);pa=Ma.action||"";db=Ma.encType;eb=Ma.method;fb=Ma.target;uc=Ma.data;vc=Ma.name}else a.push(Q,A("action"),Fc,Jc,O),fb=eb=db=pa=null,Oc(d,e);null!=pa&&T(a,"action",pa);null!=db&&T(a,"encType",db);null!=eb&&T(a,"method",
eb);null!=fb&&T(a,"target",fb);a.push(U);null!==vc&&(a.push(Kc),R(a,"name",vc),a.push(Mc),null!==uc&&uc.forEach(Lc,a));Pc(a,Xd,cb);if("string"===typeof cb){a.push(A(J(cb)));var Yd=null}else Yd=cb;return Yd;case "menuitem":a.push(V("menuitem"));for(var Eb in c)if(E.call(c,Eb)){var Zd=c[Eb];if(null!=Zd)switch(Eb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:T(a,Eb,Zd)}}a.push(U);return null;case "title":if(3===
g.insertionMode||g.tagScope&1||null!=c.itemProp)var wc=Wc(a,c);else l?wc=null:(Wc(e.hoistableChunks,c),wc=void 0);return wc;case "link":var Of=c.rel,ra=c.href,Fb=c.precedence;if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp||"string"!==typeof Of||"string"!==typeof ra||""===ra){M(a,c);var hb=null}else if("stylesheet"===c.rel)if("string"!==typeof Fb||null!=c.disabled||c.onLoad||c.onError)hb=M(a,c);else{var Na=e.styles.get(Fb),Gb=d.styleResources.hasOwnProperty(ra)?d.styleResources[ra]:void 0;
if(null!==Gb){d.styleResources[ra]=null;Na||(Na={precedence:A(J(Fb)),rules:[],hrefs:[],sheets:new Map},e.styles.set(Fb,Na));var Hb={state:0,props:D({},c,{"data-precedence":c.precedence,precedence:null})};if(Gb){2===Gb.length&&dd(Hb.props,Gb);var xc=e.preloads.stylesheets.get(ra);xc&&0<xc.length?xc.length=0:Hb.state=1}Na.sheets.set(ra,Hb);f&&f.stylesheets.add(Hb)}else if(Na){var $d=Na.sheets.get(ra);$d&&f&&f.stylesheets.add($d)}h&&a.push(lc);hb=null}else c.onLoad||c.onError?hb=M(a,c):(h&&a.push(lc),
hb=l?null:M(e.hoistableChunks,c));return hb;case "script":var yc=c.async;if("string"!==typeof c.src||!c.src||!yc||"function"===typeof yc||"symbol"===typeof yc||c.onLoad||c.onError||3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var ae=hc(a,c);else{var Ib=c.src;if("module"===c.type){var Jb=d.moduleScriptResources;var be=e.preloads.moduleScripts}else Jb=d.scriptResources,be=e.preloads.scripts;var Kb=Jb.hasOwnProperty(Ib)?Jb[Ib]:void 0;if(null!==Kb){Jb[Ib]=null;var zc=c;if(Kb){2===Kb.length&&(zc=
D({},c),dd(zc,Kb));var ce=be.get(Ib);ce&&(ce.length=0)}var de=[];e.scripts.add(de);hc(de,zc)}h&&a.push(lc);ae=null}return ae;case "style":var Lb=c.precedence,ya=c.href;if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp||"string"!==typeof Lb||"string"!==typeof ya||""===ya){a.push(V("style"));var Oa=null,ee=null,jb;for(jb in c)if(E.call(c,jb)){var Mb=c[jb];if(null!=Mb)switch(jb){case "children":Oa=Mb;break;case "dangerouslySetInnerHTML":ee=Mb;break;default:T(a,jb,Mb)}}a.push(U);var kb=Array.isArray(Oa)?
2>Oa.length?Oa[0]:null:Oa;"function"!==typeof kb&&"symbol"!==typeof kb&&null!==kb&&void 0!==kb&&a.push(A(J(""+kb)));Pc(a,ee,Oa);a.push(Xc("style"));var fe=null}else{var za=e.styles.get(Lb);if(null!==(d.styleResources.hasOwnProperty(ya)?d.styleResources[ya]:void 0)){d.styleResources[ya]=null;za?za.hrefs.push(A(J(ya))):(za={precedence:A(J(Lb)),rules:[],hrefs:[A(J(ya))],sheets:new Map},e.styles.set(Lb,za));var ge=za.rules,Pa=null,he=null,Nb;for(Nb in c)if(E.call(c,Nb)){var Ac=c[Nb];if(null!=Ac)switch(Nb){case "children":Pa=
Ac;break;case "dangerouslySetInnerHTML":he=Ac}}var lb=Array.isArray(Pa)?2>Pa.length?Pa[0]:null:Pa;"function"!==typeof lb&&"symbol"!==typeof lb&&null!==lb&&void 0!==lb&&ge.push(A(J(""+lb)));Pc(ge,he,Pa)}za&&f&&f.styles.add(za);h&&a.push(lc);fe=void 0}return fe;case "meta":if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var ie=Vc(a,c,"meta");else h&&a.push(lc),ie=l?null:"string"===typeof c.charSet?Vc(e.charsetChunks,c,"meta"):"viewport"===c.name?Vc(e.viewportChunks,c,"meta"):Vc(e.hoistableChunks,
c,"meta");return ie;case "listing":case "pre":a.push(V(b));var mb=null,nb=null,ob;for(ob in c)if(E.call(c,ob)){var Ob=c[ob];if(null!=Ob)switch(ob){case "children":mb=Ob;break;case "dangerouslySetInnerHTML":nb=Ob;break;default:T(a,ob,Ob)}}a.push(U);if(null!=nb){if(null!=mb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof nb||!("__html"in nb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");
var Aa=nb.__html;null!==Aa&&void 0!==Aa&&("string"===typeof Aa&&0<Aa.length&&"\n"===Aa[0]?a.push(Zc,A(Aa)):a.push(A(""+Aa)))}"string"===typeof mb&&"\n"===mb[0]&&a.push(Zc);return mb;case "img":var L=c.src,I=c.srcSet;if(!("lazy"===c.loading||!L&&!I||"string"!==typeof L&&null!=L||"string"!==typeof I&&null!=I)&&"low"!==c.fetchPriority&&!1===!!(g.tagScope&2)&&("string"!==typeof L||":"!==L[4]||"d"!==L[0]&&"D"!==L[0]||"a"!==L[1]&&"A"!==L[1]||"t"!==L[2]&&"T"!==L[2]||"a"!==L[3]&&"A"!==L[3])&&("string"!==
typeof I||":"!==I[4]||"d"!==I[0]&&"D"!==I[0]||"a"!==I[1]&&"A"!==I[1]||"t"!==I[2]&&"T"!==I[2]||"a"!==I[3]&&"A"!==I[3])){var je="string"===typeof c.sizes?c.sizes:void 0,Qa=I?I+"\n"+(je||""):L,Bc=e.preloads.images,Ba=Bc.get(Qa);if(Ba){if("high"===c.fetchPriority||10>e.highImagePreloads.size)Bc.delete(Qa),e.highImagePreloads.add(Ba)}else if(!d.imageResources.hasOwnProperty(Qa)){d.imageResources[Qa]=K;var Cc=c.crossOrigin;var ke="string"===typeof Cc?"use-credentials"===Cc?Cc:"":void 0;var fa=e.headers,
Dc;fa&&0<fa.remainingCapacity&&("high"===c.fetchPriority||500>fa.highImagePreloads.length)&&(Dc=ed(L,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:ke,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(fa.remainingCapacity-=Dc.length))?(e.resets.image[Qa]=K,fa.highImagePreloads&&(fa.highImagePreloads+=", "),fa.highImagePreloads+=Dc):(Ba=[],M(Ba,{rel:"preload",as:"image",href:I?void 0:L,imageSrcSet:I,imageSizes:je,crossOrigin:ke,
integrity:c.integrity,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(Ba):(e.bulkPreloads.add(Ba),Bc.set(Qa,Ba)))}}return Vc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return Vc(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;
case "head":if(2>g.insertionMode&&null===e.headChunks){e.headChunks=[];var le=Yc(e.headChunks,c,"head")}else le=Yc(a,c,"head");return le;case "html":if(0===g.insertionMode&&null===e.htmlChunks){e.htmlChunks=[bd];var me=Yc(e.htmlChunks,c,"html")}else me=Yc(a,c,"html");return me;default:if(-1!==b.indexOf("-")){a.push(V(b));var Ec=null,ne=null,Ra;for(Ra in c)if(E.call(c,Ra)){var Ca=c[Ra];if(null!=Ca){var Pf=Ra;switch(Ra){case "children":Ec=Ca;break;case "dangerouslySetInnerHTML":ne=Ca;break;case "style":rc(a,
Ca);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":case "ref":break;default:sb(Ra)&&"function"!==typeof Ca&&"symbol"!==typeof Ca&&a.push(Q,A(Pf),Fc,A(J(Ca)),O)}}}a.push(U);Pc(a,ne,Ec);return Ec}}return Yc(a,c,b)}var fd=new Map;function Xc(a){var b=fd.get(a);void 0===b&&(b=C("</"+a+">"),fd.set(a,b));return b}function gd(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)r(a,b[c]);return c<b.length?(c=b[c],b.length=0,w(a,c)):!0}
var hd=C('<template id="'),id=C('"></template>'),jd=C("\x3c!--$--\x3e"),kd=C('\x3c!--$?--\x3e<template id="'),ld=C('"></template>'),md=C("\x3c!--$!--\x3e"),nd=C("\x3c!--/$--\x3e"),od=C("<template"),pd=C('"'),qd=C(' data-dgst="');C(' data-msg="');C(' data-stck="');var rd=C("></template>");function sd(a,b,c){r(a,kd);if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");r(a,b.boundaryPrefix);r(a,A(c.toString(16)));return w(a,ld)}
var td=C('<div hidden id="'),ud=C('">'),vd=C("</div>"),wd=C('<svg aria-hidden="true" style="display:none" id="'),xd=C('">'),yd=C("</svg>"),zd=C('<math aria-hidden="true" style="display:none" id="'),Ad=C('">'),Bd=C("</math>"),Cd=C('<table hidden id="'),Dd=C('">'),Ed=C("</table>"),Fd=C('<table hidden><tbody id="'),Gd=C('">'),Hd=C("</tbody></table>"),Id=C('<table hidden><tr id="'),Jd=C('">'),Kd=C("</tr></table>"),Ld=C('<table hidden><colgroup id="'),Md=C('">'),Nd=C("</colgroup></table>");
function oe(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return r(a,td),r(a,b.segmentPrefix),r(a,A(d.toString(16))),w(a,ud);case 3:return r(a,wd),r(a,b.segmentPrefix),r(a,A(d.toString(16))),w(a,xd);case 4:return r(a,zd),r(a,b.segmentPrefix),r(a,A(d.toString(16))),w(a,Ad);case 5:return r(a,Cd),r(a,b.segmentPrefix),r(a,A(d.toString(16))),w(a,Dd);case 6:return r(a,Fd),r(a,b.segmentPrefix),r(a,A(d.toString(16))),w(a,Gd);case 7:return r(a,Id),r(a,b.segmentPrefix),r(a,A(d.toString(16))),w(a,Jd);
case 8:return r(a,Ld),r(a,b.segmentPrefix),r(a,A(d.toString(16))),w(a,Md);default:throw Error("Unknown insertion mode. This is a bug in React.");}}function pe(a,b){switch(b.insertionMode){case 0:case 1:case 2:return w(a,vd);case 3:return w(a,yd);case 4:return w(a,Bd);case 5:return w(a,Ed);case 6:return w(a,Hd);case 7:return w(a,Kd);case 8:return w(a,Nd);default:throw Error("Unknown insertion mode. This is a bug in React.");}}
var qe=C('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),re=C('$RS("'),se=C('","'),te=C('")\x3c/script>'),ue=C('<template data-rsi="" data-sid="'),ve=C('" data-pid="'),we=C('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("'),
xe=C('$RC("'),ye=C('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
ze=C('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("'),
Ae=C('$RR("'),Be=C('","'),Ce=C('",'),De=C('"'),Ee=C(")\x3c/script>"),Fe=C('<template data-rci="" data-bid="'),Ge=C('<template data-rri="" data-bid="'),He=C('" data-sid="'),Ie=C('" data-sty="'),Je=C('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("'),Ke=C('$RX("'),Le=C('"'),Me=C(","),Ne=C(")\x3c/script>"),Oe=C('<template data-rxi="" data-bid="'),Pe=C('" data-dgst="'),
Qe=C('" data-msg="'),Re=C('" data-stck="'),Se=/[<\u2028\u2029]/g;function Te(a){return JSON.stringify(a).replace(Se,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Ue=/[&><\u2028\u2029]/g;
function Ve(a){return JSON.stringify(a).replace(Ue,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}
var We=C('<style media="not all" data-precedence="'),Xe=C('" data-href="'),Ye=C('">'),Ze=C("</style>"),$e=!1,af=!0;function bf(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){r(this,We);r(this,a.precedence);for(r(this,Xe);d<c.length-1;d++)r(this,c[d]),r(this,cf);r(this,c[d]);r(this,Ye);for(d=0;d<b.length;d++)r(this,b[d]);af=w(this,Ze);$e=!0;b.length=0;c.length=0}}function df(a){return 2!==a.state?$e=!0:!1}
function ef(a,b,c){$e=!1;af=!0;b.styles.forEach(bf,a);b.stylesheets.forEach(df);$e&&(c.stylesToHoist=!0);return af}function ff(a){for(var b=0;b<a.length;b++)r(this,a[b]);a.length=0}var gf=[];function hf(a){M(gf,a.props);for(var b=0;b<gf.length;b++)r(this,gf[b]);gf.length=0;a.state=2}var jf=C('<style data-precedence="'),kf=C('" data-href="'),cf=C(" "),lf=C('">'),mf=C("</style>");
function nf(a){var b=0<a.sheets.size;a.sheets.forEach(hf,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){r(this,jf);r(this,a.precedence);a=0;if(d.length){for(r(this,kf);a<d.length-1;a++)r(this,d[a]),r(this,cf);r(this,d[a])}r(this,lf);for(a=0;a<c.length;a++)r(this,c[a]);r(this,mf);c.length=0;d.length=0}}
function of(a){if(0===a.state){a.state=1;var b=a.props;M(gf,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<gf.length;a++)r(this,gf[a]);gf.length=0}}function pf(a){a.sheets.forEach(of,this);a.sheets.clear()}var qf=C("["),rf=C(",["),sf=C(","),tf=C("]");
function uf(a,b){r(a,qf);var c=qf;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)r(a,c),r(a,A(Ve(""+d.props.href))),r(a,tf),c=rf;else{r(a,c);var e=d.props["data-precedence"],f=d.props;r(a,A(Ve(""+d.props.href)));e=""+e;r(a,sf);r(a,A(Ve(e)));for(var g in f)if(E.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":case "ref":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!sb(g))break a;h=""+h}r(e,sf);r(e,A(Ve(l)));r(e,sf);r(e,
A(Ve(h)))}}}r(a,tf);c=rf;d.state=3}});r(a,tf)}
function vf(a,b){r(a,qf);var c=qf;b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)r(a,c),r(a,A(J(JSON.stringify(""+d.props.href)))),r(a,tf),c=rf;else{r(a,c);var e=d.props["data-precedence"],f=d.props;r(a,A(J(JSON.stringify(""+d.props.href))));e=""+e;r(a,sf);r(a,A(J(JSON.stringify(e))));for(var g in f)if(E.call(f,g)){var h=f[g];if(null!=h)switch(g){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:a:{e=
a;var l=g.toLowerCase();switch(typeof h){case "function":case "symbol":break a}switch(g){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":case "ref":break a;case "className":l="class";h=""+h;break;case "hidden":if(!1===h)break a;h="";break;case "src":case "href":h=""+h;break;default:if(2<g.length&&("o"===g[0]||"O"===g[0])&&("n"===g[1]||"N"===g[1])||!sb(g))break a;h=""+h}r(e,sf);r(e,A(J(JSON.stringify(l))));r(e,sf);r(e,
A(J(JSON.stringify(h))))}}}r(a,tf);c=rf;d.state=3}});r(a,tf)}function wf(){return{styles:new Set,stylesheets:new Set}}
function Bb(a){var b=xf();if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(yf,zf)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],M(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}Af(b)}}}
function Cb(a,b){var c=xf();if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(yf,zf)+">; rel=preconnect";if("string"===typeof b){var l=(""+b).replace(Bf,Cf);h+='; crossorigin="'+l+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],M(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}Af(c)}}}
function Db(a,b,c){var d=xf();if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var l=c.fetchPriority}var p=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(p))return;e.imageResources[p]=K;e=f.headers;var q;e&&0<e.remainingCapacity&&"high"===l&&(q=ed(a,b,c),2<=(e.remainingCapacity-=q.length))?(f.resets.image[p]=K,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=q):(e=[],M(e,D({rel:"preload",href:g?void 0:
a,as:b},c)),"high"===l?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(p,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];M(g,D({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?K:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
M(g,D({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?K:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=K;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(p=ed(a,b,c),2<=(e.remainingCapacity-=p.length)))f.resets.font[a]=K,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=p;else switch(e=[],
a=D({rel:"preload",href:a,as:b},c),M(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}Af(d)}}}
function Pb(a,b){var c=xf();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?K:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=K}M(f,D({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);Af(c)}}}
function Qb(a,b,c){var d=xf();if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:A(J(b)),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:D({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&dd(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),Af(d))}}}
function Rb(a,b){var c=xf();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=D({src:a,async:!0},b),f&&(2===f.length&&dd(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),hc(a,b),Af(c))}}}
function Sb(a,b){var c=xf();if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=D({src:a,type:"module",async:!0},b),f&&(2===f.length&&dd(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),hc(a,b),Af(c))}}}function dd(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function ed(a,b,c){a=(""+a).replace(yf,zf);b=(""+b).replace(Bf,Cf);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)E.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(Bf,Cf)+'"'));return b}var yf=/[<>\r\n]/g;
function zf(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var Bf=/["';,\r\n]/g;
function Cf(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function Df(a){this.styles.add(a)}function Ef(a){this.stylesheets.add(a)}
var Ff="function"===typeof AsyncLocalStorage,Gf=Ff?new AsyncLocalStorage:null,Hf=Symbol.for("react.client.reference");
function If(a){if(null==a)return null;if("function"===typeof a)return a.$$typeof===Hf?null:a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case ka:return"Fragment";case ha:return"Portal";case ma:return"Profiler";case la:return"StrictMode";case Ea:return"Suspense";case Fa:return"SuspenseList";case Ua:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case sa:return(a._context.displayName||"Context")+".Provider";case ua:return(a.displayName||"Context")+".Consumer";case Da:var b=
a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case Ga:return b=a.displayName||null,null!==b?b:If(a.type)||"Memo";case Ha:b=a._payload;a=a._init;try{return If(a(b))}catch(c){}}return null}var Jf={};function Kf(a,b){a=a.contextTypes;if(!a)return Jf;var c={},d;for(d in a)c[d]=b[d];return c}var Lf=null;
function Mf(a,b){if(a!==b){a.context._currentValue=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Mf(a,c)}b.context._currentValue=b.value}}function Qf(a){a.context._currentValue=a.parentValue;a=a.parent;null!==a&&Qf(a)}
function Rf(a){var b=a.parent;null!==b&&Rf(b);a.context._currentValue=a.value}function Sf(a,b){a.context._currentValue=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Mf(a,b):Sf(a,b)}
function Tf(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Mf(a,c):Tf(a,c);b.context._currentValue=b.value}function Uf(a){var b=Lf;b!==a&&(null===b?Rf(a):null===a?Qf(b):b.depth===a.depth?Mf(b,a):b.depth>a.depth?Sf(b,a):Tf(b,a),Lf=a)}
var Vf={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Wf(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Vf;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:D({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Vf.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=D({},f,h)):D(f,h))}a.state=f}else f.queue=null}
var Xf={id:1,overflow:""};function Yf(a,b,c){var d=a.id;a=a.overflow;var e=32-Zf(d)-1;d&=~(1<<e);c+=1;var f=32-Zf(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Zf(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Zf=Math.clz32?Math.clz32:$f,ag=Math.log,bg=Math.LN2;function $f(a){a>>>=0;return 0===a?32:31-(ag(a)/bg|0)|0}var cg=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function dg(){}function eg(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(dg,dg),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}fg=b;throw cg;}}var fg=null;
function gg(){if(null===fg)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=fg;fg=null;return a}function hg(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var ig="function"===typeof Object.is?Object.is:hg,jg=null,kg=null,lg=null,mg=null,ng=null,X=null,og=!1,pg=!1,qg=0,rg=0,sg=-1,tg=0,ug=null,vg=null,wg=0;
function xg(){if(null===jg)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return jg}
function yg(){if(0<wg)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function zg(){null===X?null===ng?(og=!1,ng=X=yg()):(og=!0,X=ng):null===X.next?(og=!1,X=X.next=yg()):(og=!0,X=X.next);return X}function Ag(){var a=ug;ug=null;return a}function Bg(){mg=lg=kg=jg=null;pg=!1;ng=null;wg=0;X=vg=null}function Cg(a,b){return"function"===typeof b?b(a):b}
function Dg(a,b,c){jg=xg();X=zg();if(og){var d=X.queue;b=d.dispatch;if(null!==vg&&(c=vg.get(d),void 0!==c)){vg.delete(d);d=X.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);X.memoizedState=d;return[d,b]}return[X.memoizedState,b]}a=a===Cg?"function"===typeof b?b():b:void 0!==c?c(b):b;X.memoizedState=a;a=X.queue={last:null,dispatch:null};a=a.dispatch=Eg.bind(null,jg,a);return[X.memoizedState,a]}
function Fg(a,b){jg=xg();X=zg();b=void 0===b?null:b;if(null!==X){var c=X.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!ig(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();X.memoizedState=[a,b];return a}
function Eg(a,b,c){if(25<=wg)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===jg)if(pg=!0,a={action:c,next:null},null===vg&&(vg=new Map),c=vg.get(b),void 0===c)vg.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function Gg(){throw Error("startTransition cannot be called during server rendering.");}function Hg(){throw Error("Cannot update optimistic state while rendering.");}
function Ig(a){var b=tg;tg+=1;null===ug&&(ug=[]);return eg(ug,a,b)}function Jg(){throw Error("Cache cannot be refreshed during server rendering.");}function Kg(){}
var Mg={readContext:function(a){return a._currentValue},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return Ig(a);if(a.$$typeof===ua)return a._currentValue}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){xg();return a._currentValue},useMemo:Fg,useReducer:Dg,useRef:function(a){jg=xg();X=zg();var b=X.memoizedState;return null===b?(a={current:a},X.memoizedState=a):b},useState:function(a){return Dg(Cg,a)},useInsertionEffect:Kg,
useLayoutEffect:Kg,useCallback:function(a,b){return Fg(function(){return a},b)},useImperativeHandle:Kg,useEffect:Kg,useDebugValue:Kg,useDeferredValue:function(a){xg();return a},useTransition:function(){xg();return[!1,Gg]},useId:function(){var a=kg.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Zf(a)-1)).toString(32)+b;var c=Lg;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=qg++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+b.toString(32));
return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return Jg},useHostTransitionStatus:function(){xg();return zb},useOptimistic:function(a){xg();return[a,Hg]},useFormState:function(a,b,c){xg();var d=rg++,e=lg;if("function"===typeof a.$$FORM_ACTION){var f=null,g=mg;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===
typeof h){var l=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+Xa(JSON.stringify([g,null,d]),0),l===f&&(sg=d,b=e[0]))}var p=a.bind(null,b);a=function(m){p(m)};"function"===typeof p.$$FORM_ACTION&&(a.$$FORM_ACTION=function(m){m=p.$$FORM_ACTION(m);void 0!==c&&(c+="",m.action=c);var v=m.data;v&&(null===f&&(f=void 0!==c?"p"+c:"k"+Xa(JSON.stringify([g,null,d]),0)),v.append("$ACTION_KEY",f));return m});return[b,a]}var q=a.bind(null,b);return[b,function(m){q(m)}]}},Lg=null,Ng={getCacheSignal:function(){throw Error("Not implemented.");
},getCacheForType:function(){throw Error("Not implemented.");}},Og;function Pg(a){if(void 0===Og)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);Og=b&&b[1]||""}return"\n"+Og+a}var Qg=!1;
function Rg(a,b){if(!a||Qg)return"";Qg=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var d={DetermineComponentFrameRoot:function(){try{if(b){var m=function(){throw Error();};Object.defineProperty(m.prototype,"props",{set:function(){throw Error();}});if("object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(m,[])}catch(t){var v=t}Reflect.construct(a,[],m)}else{try{m.call()}catch(t){v=t}a.call(m.prototype)}}else{try{throw Error();}catch(t){v=t}(m=a())&&"function"===typeof m.catch&&
m.catch(function(){})}}catch(t){if(t&&v&&"string"===typeof t.stack)return[t.stack,v.stack]}return[null,null]}};d.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var e=Object.getOwnPropertyDescriptor(d.DetermineComponentFrameRoot,"name");e&&e.configurable&&Object.defineProperty(d.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});try{var f=d.DetermineComponentFrameRoot(),g=f[0],h=f[1];if(g&&h){var l=g.split("\n"),p=h.split("\n");for(e=d=0;d<l.length&&!l[d].includes("DetermineComponentFrameRoot");)d++;
for(;e<p.length&&!p[e].includes("DetermineComponentFrameRoot");)e++;if(d===l.length||e===p.length)for(d=l.length-1,e=p.length-1;1<=d&&0<=e&&l[d]!==p[e];)e--;for(;1<=d&&0<=e;d--,e--)if(l[d]!==p[e]){if(1!==d||1!==e){do if(d--,e--,0>e||l[d]!==p[e]){var q="\n"+l[d].replace(" at new "," at ");a.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",a.displayName));return q}while(1<=d&&0<=e)}break}}}finally{Qg=!1,Error.prepareStackTrace=c}return(c=a?a.displayName||a.name:"")?Pg(c):""}
var Sg=yb.ReactCurrentDispatcher,Tg=yb.ReactCurrentCache;function Ug(a){console.error(a);return null}function Vg(){}
function Wg(a,b,c,d,e,f,g,h,l,p,q,m){Ab.current=Tb;var v=[],t=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:t,pingedTasks:v,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?Ug:f,onPostpone:void 0===q?Vg:q,onAllReady:void 0===g?
Vg:g,onShellReady:void 0===h?Vg:h,onShellError:void 0===l?Vg:l,onFatalError:void 0===p?Vg:p,formState:void 0===m?null:m};c=Xg(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Yg(b,null,a,-1,null,c,null,t,null,d,Jf,null,Xf,null,!1);v.push(a);return b}var Zg=null;function xf(){if(Zg)return Zg;if(Ff){var a=Gf.getStore();if(a)return a}return null}function $g(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return ah(a)},0))}
function bh(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,contentState:wf(),fallbackState:wf(),trackedContentKeyPath:null,trackedFallbackNode:null}}
function Yg(a,b,c,d,e,f,g,h,l,p,q,m,v,t,y){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var x={replay:null,node:c,childIndex:d,ping:function(){return $g(a,x)},blockedBoundary:e,blockedSegment:f,hoistableState:g,abortSet:h,keyPath:l,formatContext:p,legacyContext:q,context:m,treeContext:v,componentStack:t,thenableState:b,isFallback:y};h.add(x);return x}
function ch(a,b,c,d,e,f,g,h,l,p,q,m,v,t,y){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var x={replay:c,node:d,childIndex:e,ping:function(){return $g(a,x)},blockedBoundary:f,blockedSegment:null,hoistableState:g,abortSet:h,keyPath:l,formatContext:p,legacyContext:q,context:m,treeContext:v,componentStack:t,thenableState:b,isFallback:y};h.add(x);return x}
function Xg(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}function dh(a,b){return{tag:0,parent:a.componentStack,type:b}}
function eh(a,b){if(b&&null!==a.trackedPostpones){try{a="";do{switch(b.tag){case 0:a+=Pg(b.type,null);break;case 1:a+=Rg(b.type,!1);break;case 2:a+=Rg(b.type,!0)}b=b.parent}while(b);var c=a}catch(d){c="\nError generating stack: "+d.message+"\n"+d.stack}c={componentStack:c}}else c={};return c}function Y(a,b,c){a=a.onError(b,c);if(null==a||"string"===typeof a)return a}
function fh(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,ib(a.destination,b)):(a.status=1,a.fatalError=b)}function gh(a,b,c,d,e,f){var g=b.thenableState;b.thenableState=null;jg={};kg=b;lg=a;mg=c;rg=qg=0;sg=-1;tg=0;ug=g;for(a=d(e,f);pg;)pg=!1,rg=qg=0,sg=-1,tg=0,wg+=1,X=null,a=d(e,f);Bg();return a}
function hh(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((If(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=D({},c,d)}b.legacyContext=e;Z(a,b,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,Z(a,b,f,-1),b.keyPath=e}
function ih(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var l=b.blockedSegment;if(null!==l){h=!0;l=l.chunks;for(var p=0;p<f;p++)p===g?l.push(Tc):l.push(Uc)}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Yf(c,1,0),jh(a,b,d,-1),b.treeContext=c):h?jh(a,b,d,-1):Z(a,b,d,-1);b.keyPath=f}function kh(a,b){if(a&&a.defaultProps){b=D({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function lh(a,b,c,d,e,f){if("function"===typeof d)if(d.prototype&&d.prototype.isReactComponent){f=b.componentStack;b.componentStack={tag:2,parent:b.componentStack,type:d};var g=Kf(d,b.legacyContext);var h=d.contextType;h=new d(e,"object"===typeof h&&null!==h?h._currentValue:g);Wf(h,d,e,g);hh(a,b,c,h,d);b.componentStack=f}else{f=Kf(d,b.legacyContext);g=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:d};h=gh(a,b,c,d,e,f);var l=0!==qg,p=rg,q=sg;"object"===typeof h&&null!==h&&"function"===
typeof h.render&&void 0===h.$$typeof?(Wf(h,d,e,f),hh(a,b,c,h,d)):ih(a,b,c,h,l,p,q);b.componentStack=g}else if("string"===typeof d){f=b.componentStack;b.componentStack=dh(b,d);g=b.blockedSegment;if(null===g)g=e.children,h=b.formatContext,l=b.keyPath,b.formatContext=kc(h,d,e),b.keyPath=c,jh(a,b,g,-1),b.formatContext=h,b.keyPath=l;else{l=cd(g.chunks,d,e,a.resumableState,a.renderState,b.hoistableState,b.formatContext,g.lastPushedText,b.isFallback);g.lastPushedText=!1;h=b.formatContext;p=b.keyPath;b.formatContext=
kc(h,d,e);b.keyPath=c;jh(a,b,l,-1);b.formatContext=h;b.keyPath=p;a:{c=g.chunks;a=a.resumableState;switch(d){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}c.push(Xc(d))}g.lastPushedText=!1}b.componentStack=
f}else{switch(d){case Ta:case Ja:case la:case ma:case ka:d=b.keyPath;b.keyPath=c;Z(a,b,e.children,-1);b.keyPath=d;return;case Sa:"hidden"!==e.mode&&(d=b.keyPath,b.keyPath=c,Z(a,b,e.children,-1),b.keyPath=d);return;case Fa:d=b.componentStack;b.componentStack=dh(b,"SuspenseList");f=b.keyPath;b.keyPath=c;Z(a,b,e.children,-1);b.keyPath=f;b.componentStack=d;return;case Ia:throw Error("ReactDOMServer does not yet support scope components.");case Ea:a:if(null!==b.replay){d=b.keyPath;b.keyPath=c;c=e.children;
try{jh(a,b,c,-1)}finally{b.keyPath=d}}else{var m=b.componentStack;d=b.componentStack=dh(b,"Suspense");var v=b.keyPath;f=b.blockedBoundary;var t=b.hoistableState,y=b.blockedSegment;g=e.fallback;var x=e.children;e=new Set;h=bh(a,e);null!==a.trackedPostpones&&(h.trackedContentKeyPath=c);l=Xg(a,y.chunks.length,h,b.formatContext,!1,!1);y.children.push(l);y.lastPushedText=!1;var u=Xg(a,0,null,b.formatContext,!1,!1);u.parentFlushed=!0;b.blockedBoundary=h;b.hoistableState=h.contentState;b.blockedSegment=
u;b.keyPath=c;try{if(jh(a,b,x,-1),u.lastPushedText&&u.textEmbedded&&u.chunks.push(lc),u.status=1,mh(h,u),0===h.pendingTasks&&0===h.status){h.status=1;b.componentStack=m;break a}}catch(S){u.status=4,h.status=4,p=eh(a,b.componentStack),q=Y(a,S,p),h.errorDigest=q,nh(a,h)}finally{b.blockedBoundary=f,b.hoistableState=t,b.blockedSegment=y,b.keyPath=v,b.componentStack=m}p=[c[0],"Suspense Fallback",c[2]];q=a.trackedPostpones;null!==q&&(m=[p[1],p[2],[],null],q.workingMap.set(p,m),5===h.status?q.workingMap.get(c)[4]=
m:h.trackedFallbackNode=m);b=Yg(a,null,g,-1,f,l,h.fallbackState,e,p,b.formatContext,b.legacyContext,b.context,b.treeContext,d,!0);a.pingedTasks.push(b)}return}if("object"===typeof d&&null!==d)switch(d.$$typeof){case Da:g=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:d.render};e=gh(a,b,c,d.render,e,f);ih(a,b,c,e,0!==qg,rg,sg);b.componentStack=g;return;case Ga:d=d.type;e=kh(d,e);lh(a,b,c,d,e,f);return;case sa:g=e.children;f=b.keyPath;d=d._context;e=e.value;h=d._currentValue;
d._currentValue=e;l=Lf;Lf=e={parent:l,depth:null===l?0:l.depth+1,context:d,parentValue:h,value:e};b.context=e;b.keyPath=c;Z(a,b,g,-1);a=Lf;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");a.context._currentValue=a.parentValue;a=Lf=a.parent;b.context=a;b.keyPath=f;return;case ua:e=e.children;e=e(d._currentValue);d=b.keyPath;b.keyPath=c;Z(a,b,e,-1);b.keyPath=d;return;case ta:case Ha:f=b.componentStack;b.componentStack=dh(b,"Lazy");g=d._init;d=g(d._payload);
e=kh(d,e);lh(a,b,c,d,e,void 0);b.componentStack=f;return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==d?d:typeof d)+"."));}}
function oh(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Xg(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,jh(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(mh(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function Z(a,b,c,d){if(null!==b.replay&&"number"===typeof b.replay.slots)oh(a,b,b.replay.slots,c,d);else if(b.node=c,b.childIndex=d,null!==c){if("object"===typeof c){switch(c.$$typeof){case ea:var e=c.type,f=c.key,g=c.props;var h=c.ref;var l=If(e),p=null==f?-1===d?0:d:f;f=[b.keyPath,l,p];if(null!==b.replay)a:{var q=b.replay;d=q.nodes;for(c=0;c<d.length;c++){var m=d[c];if(p===m[1]){if(4===m.length){if(null!==l&&l!==m[0])throw Error("Expected the resume to render <"+m[0]+"> in this slot but instead it rendered <"+
l+">. The tree doesn't match so React will fallback to client rendering.");var v=m[2];l=m[3];p=b.node;b.replay={nodes:v,slots:l,pendingTasks:1};try{lh(a,b,f,e,g,h);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(B){if("object"===typeof B&&null!==B&&(B===cg||"function"===typeof B.then))throw b.node===p&&(b.replay=q),B;
b.replay.pendingTasks--;g=eh(a,b.componentStack);f=a;a=b.blockedBoundary;e=B;g=Y(f,e,g);ph(f,a,v,l,e,g)}b.replay=q}else{if(e!==Ea)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(If(e)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{q=void 0;e=m[5];h=m[2];l=m[3];p=null===m[4]?[]:m[4][2];m=null===m[4]?null:m[4][3];var t=b.componentStack,y=b.componentStack=dh(b,"Suspense"),x=b.keyPath,u=b.replay,S=b.blockedBoundary,
W=b.hoistableState,H=g.children;g=g.fallback;var P=new Set,z=bh(a,P);z.parentFlushed=!0;z.rootSegmentID=e;b.blockedBoundary=z;b.hoistableState=z.contentState;b.replay={nodes:h,slots:l,pendingTasks:1};try{jh(a,b,H,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===z.pendingTasks&&0===z.status){z.status=1;a.completedBoundaries.push(z);
break b}}catch(B){z.status=4,v=eh(a,b.componentStack),q=Y(a,B,v),z.errorDigest=q,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(z)}finally{b.blockedBoundary=S,b.hoistableState=W,b.replay=u,b.keyPath=x,b.componentStack=t}b=ch(a,null,{nodes:p,slots:m,pendingTasks:0},g,-1,S,z.fallbackState,P,[f[0],"Suspense Fallback",f[2]],b.formatContext,b.legacyContext,b.context,b.treeContext,y,!0);a.pingedTasks.push(b)}}d.splice(c,1);break a}}}else lh(a,b,f,e,g,h);return;case ha:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");
case Ha:g=b.componentStack;b.componentStack=dh(b,"Lazy");f=c._init;c=f(c._payload);b.componentStack=g;Z(a,b,c,d);return}if(Wa(c)){qh(a,b,c,d);return}null===c||"object"!==typeof c?g=null:(g=Va&&c[Va]||c["@@iterator"],g="function"===typeof g?g:null);if(g&&(g=g.call(c))){c=g.next();if(!c.done){f=[];do f.push(c.value),c=g.next();while(!c.done);qh(a,b,f,d)}return}if("function"===typeof c.then)return b.thenableState=null,Z(a,b,Ig(c),d);if(c.$$typeof===ua)return Z(a,b,c._currentValue,d);d=Object.prototype.toString.call(c);
throw Error("Objects are not valid as a React child (found: "+("[object Object]"===d?"object with keys {"+Object.keys(c).join(", ")+"}":d)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof c?(d=b.blockedSegment,null!==d&&(d.lastPushedText=mc(d.chunks,c,a.renderState,d.lastPushedText))):"number"===typeof c&&(d=b.blockedSegment,null!==d&&(d.lastPushedText=mc(d.chunks,""+c,a.renderState,d.lastPushedText)))}}
function qh(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var l=g[h];if(l[1]===d){d=l[2];l=l[3];b.replay={nodes:d,slots:l,pendingTasks:1};try{qh(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(m){if("object"===typeof m&&
null!==m&&(m===cg||"function"===typeof m.then))throw m;b.replay.pendingTasks--;c=eh(a,b.componentStack);var p=b.blockedBoundary,q=m;c=Y(a,q,c);ph(a,p,d,l,q,c)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++)l=c[d],b.treeContext=Yf(f,g,d),p=h[d],"number"===typeof p?(oh(a,b,p,l,d),delete h[d]):jh(a,b,l,d);b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Yf(f,g,h),
jh(a,b,d,h);b.treeContext=f;b.keyPath=e}function nh(a,b){a=a.trackedPostpones;null!==a&&(b=b.trackedContentKeyPath,null!==b&&(b=a.workingMap.get(b),void 0!==b&&(b.length=4,b[2]=[],b[3]=null)))}
function jh(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,l=b.treeContext,p=b.componentStack,q=b.blockedSegment;if(null===q)try{return Z(a,b,c,d)}catch(t){if(Bg(),c=t===cg?gg():t,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Ag();a=ch(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.hoistableState,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null,b.isFallback).ping;c.then(a,
a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;b.componentStack=p;Uf(g);return}}else{var m=q.children.length,v=q.chunks.length;try{return Z(a,b,c,d)}catch(t){if(Bg(),q.children.length=m,q.chunks.length=v,c=t===cg?gg():t,"object"===typeof c&&null!==c&&"function"===typeof c.then){d=Ag();q=b.blockedSegment;m=Xg(a,q.chunks.length,null,b.formatContext,q.lastPushedText,!0);q.children.push(m);q.lastPushedText=!1;a=Yg(a,d,b.node,b.childIndex,b.blockedBoundary,m,b.hoistableState,
b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null,b.isFallback).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;b.componentStack=p;Uf(g);return}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=l;Uf(g);throw c;}function rh(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,sh(this,b,a))}
function ph(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)ph(a,b,h[2],h[3],e,f);else{h=h[5];var l=a,p=f,q=bh(l,new Set);q.parentFlushed=!0;q.rootSegmentID=h;q.status=4;q.errorDigest=p;q.parentFlushed&&l.clientRenderedBoundaries.push(q)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var m in d)delete d[m]}}
function th(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(d={},1!==b.status&&2!==b.status){a=a.replay;if(null===a){Y(b,c,d);fh(b,c);return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&(d=Y(b,c,d),ph(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&uh(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,a=eh(b,a.componentStack),a=Y(b,c,a),d.errorDigest=a,nh(b,d),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return th(f,
b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&vh(b)}
function wh(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var l=h.value.sheets.values(),p=l.next();0<e.remainingCapacity&&!p.done;p=l.next()){var q=p.value,m=q.props,v=m.href,t=q.props,y=ed(t.href,"style",{crossOrigin:t.crossOrigin,integrity:t.integrity,
nonce:t.nonce,type:t.type,fetchPriority:t.fetchPriority,referrerPolicy:t.referrerPolicy,media:t.media});if(2<=(e.remainingCapacity-=y.length))c.resets.style[v]=K,f&&(f+=", "),f+=y,c.resets.style[v]="string"===typeof m.crossOrigin||"string"===typeof m.integrity?[m.crossOrigin,m.integrity]:K;else break b}}f?d({Link:f}):d({})}}}catch(x){Y(a,x,{})}}function uh(a){null===a.trackedPostpones&&wh(a,!0);a.onShellError=Vg;a=a.onShellReady;a()}
function vh(a){wh(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function mh(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&mh(a,c)}else a.completedSegments.push(b)}
function sh(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&uh(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&mh(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(rh,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(mh(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&vh(a)}
function ah(a){if(2!==a.status){var b=Lf,c=Sg.current;Sg.current=Mg;var d=Tg.current;Tg.current=Ng;var e=Zg;Zg=a;var f=Lg;Lg=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var l=g[h],p=a,q=l.blockedSegment;if(null===q){var m=p;if(0!==l.replay.pendingTasks){Uf(l.context);try{Z(m,l,l.node,l.childIndex);if(1===l.replay.pendingTasks&&0<l.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
l.replay.pendingTasks--;l.abortSet.delete(l);sh(m,l.blockedBoundary,null)}catch(F){Bg();var v=F===cg?gg():F;if("object"===typeof v&&null!==v&&"function"===typeof v.then){var t=l.ping;v.then(t,t);l.thenableState=Ag()}else{l.replay.pendingTasks--;l.abortSet.delete(l);var y=eh(m,l.componentStack);p=void 0;var x=m,u=l.blockedBoundary,S=v,W=l.replay.nodes,H=l.replay.slots;p=Y(x,S,y);ph(x,u,W,H,S,p);m.pendingRootTasks--;0===m.pendingRootTasks&&uh(m);m.allPendingTasks--;0===m.allPendingTasks&&vh(m)}}finally{}}}else if(m=
void 0,x=q,0===x.status){Uf(l.context);var P=x.children.length,z=x.chunks.length;try{Z(p,l,l.node,l.childIndex),x.lastPushedText&&x.textEmbedded&&x.chunks.push(lc),l.abortSet.delete(l),x.status=1,sh(p,l.blockedBoundary,x)}catch(F){Bg();x.children.length=P;x.chunks.length=z;var B=F===cg?gg():F;if("object"===typeof B&&null!==B&&"function"===typeof B.then){var na=l.ping;B.then(na,na);l.thenableState=Ag()}else{var aa=eh(p,l.componentStack);l.abortSet.delete(l);x.status=4;var G=l.blockedBoundary;m=Y(p,
B,aa);null===G?fh(p,B):(G.pendingTasks--,4!==G.status&&(G.status=4,G.errorDigest=m,nh(p,G),G.parentFlushed&&p.clientRenderedBoundaries.push(G)));p.allPendingTasks--;0===p.allPendingTasks&&vh(p)}}finally{}}}g.splice(0,h);null!==a.destination&&xh(a,a.destination)}catch(F){Y(a,F,{}),fh(a,F)}finally{Lg=f,Sg.current=c,Tg.current=d,c===Mg&&Uf(b),Zg=e}}}
function yh(a,b,c,d){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:return d=c.id,c.lastPushedText=!1,c.textEmbedded=!1,a=a.renderState,r(b,hd),r(b,a.placeholderPrefix),a=A(d.toString(16)),r(b,a),w(b,id);case 1:c.status=2;var e=!0,f=c.chunks,g=0;c=c.children;for(var h=0;h<c.length;h++){for(e=c[h];g<e.index;g++)r(b,f[g]);e=zh(a,b,e,d)}for(;g<f.length-1;g++)r(b,f[g]);g<f.length&&(e=w(b,f[g]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function zh(a,b,c,d){var e=c.boundary;if(null===e)return yh(a,b,c,d);e.parentFlushed=!0;if(4===e.status)e=e.errorDigest,w(b,md),r(b,od),e&&(r(b,qd),r(b,A(J(e))),r(b,pd)),w(b,rd),yh(a,b,c,d);else if(1!==e.status)0===e.status&&(e.rootSegmentID=a.nextSegmentId++),0<e.completedSegments.length&&a.partialBoundaries.push(e),sd(b,a.renderState,e.rootSegmentID),d&&(e=e.fallbackState,e.styles.forEach(Df,d),e.stylesheets.forEach(Ef,d)),yh(a,b,c,d);else if(e.byteSize>a.progressiveChunkSize)e.rootSegmentID=a.nextSegmentId++,
a.completedBoundaries.push(e),sd(b,a.renderState,e.rootSegmentID),yh(a,b,c,d);else{d&&(c=e.contentState,c.styles.forEach(Df,d),c.stylesheets.forEach(Ef,d));w(b,jd);c=e.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");zh(a,b,c[0],d)}return w(b,nd)}function Ah(a,b,c,d){oe(b,a.renderState,c.parentFormatContext,c.id);zh(a,b,c,d);return pe(b,c.parentFormatContext)}
function Bh(a,b,c){for(var d=c.completedSegments,e=0;e<d.length;e++)Ch(a,b,c,d[e]);d.length=0;ef(b,c.contentState,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.contentState;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(r(b,a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,r(b,ye)):0===(d.instructions&8)?(d.instructions|=8,r(b,ze)):r(b,Ae):0===(d.instructions&2)?(d.instructions|=2,r(b,we)):r(b,xe)):f?r(b,Ge):r(b,Fe);d=A(e.toString(16));
r(b,a.boundaryPrefix);r(b,d);g?r(b,Be):r(b,He);r(b,a.segmentPrefix);r(b,d);f?g?(r(b,Ce),uf(b,c)):(r(b,Ie),vf(b,c)):g&&r(b,De);d=g?w(b,Ee):w(b,Ub);return gd(b,a)&&d}
function Ch(a,b,c,d){if(2===d.status)return!0;var e=c.contentState,f=d.id;if(-1===f){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return Ah(a,b,d,e)}if(f===c.rootSegmentID)return Ah(a,b,d,e);Ah(a,b,d,e);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(r(b,a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,r(b,qe)):r(b,re)):r(b,ue);r(b,a.segmentPrefix);f=A(f.toString(16));r(b,f);d?r(b,se):r(b,ve);r(b,
a.placeholderPrefix);r(b,f);b=d?w(b,te):w(b,Ub);return b}
function xh(a,b){k=new Uint8Array(2048);n=0;try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,l=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(l))}var p=e.htmlChunks,q=e.headChunks,m;if(p){for(m=0;m<p.length;m++)r(b,p[m]);if(q)for(m=0;m<q.length;m++)r(b,q[m]);else r(b,
V("head")),r(b,U)}else if(q)for(m=0;m<q.length;m++)r(b,q[m]);var v=e.charsetChunks;for(m=0;m<v.length;m++)r(b,v[m]);v.length=0;e.preconnects.forEach(ff,b);e.preconnects.clear();var t=e.viewportChunks;for(m=0;m<t.length;m++)r(b,t[m]);t.length=0;e.fontPreloads.forEach(ff,b);e.fontPreloads.clear();e.highImagePreloads.forEach(ff,b);e.highImagePreloads.clear();e.styles.forEach(nf,b);var y=e.importMapChunks;for(m=0;m<y.length;m++)r(b,y[m]);y.length=0;e.bootstrapScripts.forEach(ff,b);e.scripts.forEach(ff,
b);e.scripts.clear();e.bulkPreloads.forEach(ff,b);e.bulkPreloads.clear();var x=e.hoistableChunks;for(m=0;m<x.length;m++)r(b,x[m]);x.length=0;p&&null===q&&r(b,Xc("head"));zh(a,b,d,null);a.completedRootSegment=null;gd(b,a.renderState)}else return;var u=a.renderState;d=0;var S=u.viewportChunks;for(d=0;d<S.length;d++)r(b,S[d]);S.length=0;u.preconnects.forEach(ff,b);u.preconnects.clear();u.fontPreloads.forEach(ff,b);u.fontPreloads.clear();u.highImagePreloads.forEach(ff,b);u.highImagePreloads.clear();u.styles.forEach(pf,
b);u.scripts.forEach(ff,b);u.scripts.clear();u.bulkPreloads.forEach(ff,b);u.bulkPreloads.clear();var W=u.hoistableChunks;for(d=0;d<W.length;d++)r(b,W[d]);W.length=0;var H=a.clientRenderedBoundaries;for(c=0;c<H.length;c++){var P=H[c];u=b;var z=a.resumableState,B=a.renderState,na=P.rootSegmentID,aa=P.errorDigest,G=P.errorMessage,F=P.errorComponentStack,ia=0===z.streamingFormat;ia?(r(u,B.startInlineScript),0===(z.instructions&4)?(z.instructions|=4,r(u,Je)):r(u,Ke)):r(u,Oe);r(u,B.boundaryPrefix);r(u,
A(na.toString(16)));ia&&r(u,Le);if(aa||G||F)ia?(r(u,Me),r(u,A(Te(aa||"")))):(r(u,Pe),r(u,A(J(aa||""))));if(G||F)ia?(r(u,Me),r(u,A(Te(G||"")))):(r(u,Qe),r(u,A(J(G||""))));F&&(ia?(r(u,Me),r(u,A(Te(F)))):(r(u,Re),r(u,A(J(F)))));if(ia?!w(u,Ne):!w(u,Ub)){a.destination=null;c++;H.splice(0,c);return}}H.splice(0,c);var va=a.completedBoundaries;for(c=0;c<va.length;c++)if(!Bh(a,b,va[c])){a.destination=null;c++;va.splice(0,c);return}va.splice(0,c);Ya(b);k=new Uint8Array(2048);n=0;var wa=a.partialBoundaries;
for(c=0;c<wa.length;c++){var Ka=wa[c];a:{H=a;P=b;var xa=Ka.completedSegments;for(z=0;z<xa.length;z++)if(!Ch(H,P,Ka,xa[z])){z++;xa.splice(0,z);var La=!1;break a}xa.splice(0,z);La=ef(P,Ka.contentState,H.renderState)}if(!La){a.destination=null;c++;wa.splice(0,c);return}}wa.splice(0,c);var oa=a.completedBoundaries;for(c=0;c<oa.length;c++)if(!Bh(a,b,oa[c])){a.destination=null;c++;oa.splice(0,c);return}oa.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&
0===a.completedBoundaries.length?(a.flushScheduled=!1,c=a.resumableState,c.hasBody&&r(b,Xc("body")),c.hasHtml&&r(b,Xc("html")),Ya(b),b.close(),a.destination=null):Ya(b)}}function Dh(a){a.flushScheduled=null!==a.destination;Ff?setTimeout(function(){return Gf.run(a,ah,a)},0):setTimeout(function(){return ah(a)},0);null===a.trackedPostpones&&(Ff?setTimeout(function(){return Gf.run(a,Eh,a)},0):setTimeout(function(){return Eh(a)},0))}function Eh(a){wh(a,0===a.pendingRootTasks)}
function Af(a){!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(a.flushScheduled=!0,setTimeout(function(){var b=a.destination;b?xh(a,b):a.flushScheduled=!1},0))}function Fh(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return th(e,a,d)});c.clear()}null!==a.destination&&xh(a,a.destination)}catch(e){Y(a,e,{}),fh(a,e)}}
exports.renderToReadableStream=function(a,b){return new Promise(function(c,d){var e,f,g=new Promise(function(t,y){f=t;e=y}),h=b?b.onHeaders:void 0,l;h&&(l=function(t){h(new Headers(t))});var p=ic(b?b.identifierPrefix:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.bootstrapScriptContent:void 0,b?b.bootstrapScripts:void 0,b?b.bootstrapModules:void 0),q=Wg(a,p,gc(p,b?b.nonce:void 0,b?b.unstable_externalRuntimeSrc:void 0,b?b.importMap:void 0,l,b?b.maxHeadersLength:void 0),jc(b?b.namespaceURI:void 0),
b?b.progressiveChunkSize:void 0,b?b.onError:void 0,f,function(){var t=new ReadableStream({type:"bytes",pull:function(y){if(1===q.status)q.status=2,ib(y,q.fatalError);else if(2!==q.status&&null===q.destination){q.destination=y;try{xh(q,y)}catch(x){Y(q,x,{}),fh(q,x)}}},cancel:function(y){q.destination=null;Fh(q,y)}},{highWaterMark:0});t.allReady=g;c(t)},function(t){g.catch(function(){});d(t)},e,b?b.onPostpone:void 0,b?b.formState:void 0);if(b&&b.signal){var m=b.signal;if(m.aborted)Fh(q,m.reason);else{var v=
function(){Fh(q,m.reason);m.removeEventListener("abort",v)};m.addEventListener("abort",v)}}Dh(q)})};exports.version="18.3.0-canary-178c267a4e-20241218";

//# sourceMappingURL=react-dom-server.edge.production.min.js.map
