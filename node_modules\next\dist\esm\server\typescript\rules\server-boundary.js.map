{"version": 3, "sources": ["../../../../src/server/typescript/rules/server-boundary.ts"], "names": ["NEXT_TS_ERRORS", "getTs", "getType<PERSON><PERSON>cker", "isPromiseType", "type", "typeC<PERSON>cker", "typeReferenceType", "target", "test", "typeToString", "isFunctionReturningPromise", "node", "ts", "getTypeAtLocation", "signatures", "getSignaturesOfType", "SignatureKind", "Call", "isPromise", "length", "signature", "returnType", "getReturnType", "isUnion", "t", "types", "serverBoundary", "getSemanticDiagnosticsForExportDeclaration", "source", "diagnostics", "exportClause", "isNamedExports", "e", "elements", "push", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_SERVER_ENTRY_RETURN", "messageText", "start", "getStart", "getWidth", "getSemanticDiagnosticsForExportVariableStatement", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "initializer", "isArrowFunction", "isFunctionDeclaration", "isFunctionExpression", "isCallExpression", "isIdentifier", "getSemanticDiagnosticsForFunctionExport"], "mappings": "AAAA,mFAAmF;AAEnF,SAASA,cAAc,QAAQ,cAAa;AAC5C,SAASC,KAAK,EAAEC,cAAc,QAAQ,WAAU;AAGhD,qCAAqC;AACrC,SAASC,cAAcC,IAAmB,EAAEC,WAAiC;IAC3E,MAAMC,oBAAoBF;IAC1B,IAAI,CAACE,kBAAkBC,MAAM,EAAE,OAAO;IAEtC,2CAA2C;IAC3C,IACE,CAAC,mBAAmBC,IAAI,CAACH,YAAYI,YAAY,CAACH,kBAAkBC,MAAM,IAC1E;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASG,2BACPC,IAAmB,EACnBN,WAAiC,EACjCO,EAAmB;IAEnB,MAAMR,OAAOC,YAAYQ,iBAAiB,CAACF;IAC3C,MAAMG,aAAaT,YAAYU,mBAAmB,CAChDX,MACAQ,GAAGI,aAAa,CAACC,IAAI;IAGvB,IAAIC,YAAY;IAChB,IAAIJ,WAAWK,MAAM,EAAE;QACrB,KAAK,MAAMC,aAAaN,WAAY;YAClC,MAAMO,aAAaD,UAAUE,aAAa;YAC1C,IAAID,WAAWE,OAAO,IAAI;gBACxB,KAAK,MAAMC,KAAKH,WAAWI,KAAK,CAAE;oBAChC,IAAI,CAACtB,cAAcqB,GAAGnB,cAAc;wBAClCa,YAAY;wBACZ;oBACF;gBACF;YACF,OAAO;gBACLA,YAAYf,cAAckB,YAAYhB;YACxC;QACF;IACF,OAAO;QACLa,YAAY;IACd;IAEA,OAAOA;AACT;AAEA,MAAMQ,iBAAiB;IACrBC,4CACEC,MAA2B,EAC3BjB,IAAgC;QAEhC,MAAMC,KAAKX;QACX,MAAMI,cAAcH;QACpB,IAAI,CAACG,aAAa,OAAO,EAAE;QAE3B,MAAMwB,cAAqC,EAAE;QAE7C,MAAMC,eAAenB,KAAKmB,YAAY;QACtC,IAAIA,gBAAgBlB,GAAGmB,cAAc,CAACD,eAAe;YACnD,KAAK,MAAME,KAAKF,aAAaG,QAAQ,CAAE;gBACrC,IAAI,CAACvB,2BAA2BsB,GAAG3B,aAAaO,KAAK;oBACnDiB,YAAYK,IAAI,CAAC;wBACfC,MAAMP;wBACNQ,UAAUxB,GAAGyB,kBAAkB,CAACC,KAAK;wBACrCC,MAAMvC,eAAewC,2BAA2B;wBAChDC,aAAa,CAAC,sDAAsD,CAAC;wBACrEC,OAAOV,EAAEW,QAAQ;wBACjBxB,QAAQa,EAAEY,QAAQ;oBACpB;gBACF;YACF;QACF;QAEA,OAAOf;IACT;IAEAgB,kDACEjB,MAA2B,EAC3BjB,IAAgC;QAEhC,MAAMC,KAAKX;QAEX,MAAM4B,cAAqC,EAAE;QAE7C,IAAIjB,GAAGkC,yBAAyB,CAACnC,KAAKoC,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAerC,KAAKoC,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAMC,cAAcF,YAAYE,WAAW;gBAC3C,IACEA,eACCtC,CAAAA,GAAGuC,eAAe,CAACD,gBAClBtC,GAAGwC,qBAAqB,CAACF,gBACzBtC,GAAGyC,oBAAoB,CAACH,gBACxBtC,GAAG0C,gBAAgB,CAACJ,gBACpBtC,GAAG2C,YAAY,CAACL,YAAW,GAC7B;oBACArB,YAAYK,IAAI,IACXR,eAAe8B,uCAAuC,CACvD5B,QACAsB;gBAGN,OAAO;oBACLrB,YAAYK,IAAI,CAAC;wBACfC,MAAMP;wBACNQ,UAAUxB,GAAGyB,kBAAkB,CAACC,KAAK;wBACrCC,MAAMvC,eAAewC,2BAA2B;wBAChDC,aAAa,CAAC,sDAAsD,CAAC;wBACrEC,OAAOM,YAAYL,QAAQ;wBAC3BxB,QAAQ6B,YAAYJ,QAAQ;oBAC9B;gBACF;YACF;QACF;QAEA,OAAOf;IACT;IAEA2B,yCACE5B,MAA2B,EAC3BjB,IAKuB;QAEvB,MAAMC,KAAKX;QACX,MAAMI,cAAcH;QACpB,IAAI,CAACG,aAAa,OAAO,EAAE;QAE3B,MAAMwB,cAAqC,EAAE;QAE7C,IAAI,CAACnB,2BAA2BC,MAAMN,aAAaO,KAAK;YACtDiB,YAAYK,IAAI,CAAC;gBACfC,MAAMP;gBACNQ,UAAUxB,GAAGyB,kBAAkB,CAACC,KAAK;gBACrCC,MAAMvC,eAAewC,2BAA2B;gBAChDC,aAAa,CAAC,mHAAmH,CAAC;gBAClIC,OAAO/B,KAAKgC,QAAQ;gBACpBxB,QAAQR,KAAKiC,QAAQ;YACvB;QACF;QAEA,OAAOf;IACT;AACF;AAEA,eAAeH,eAAc"}