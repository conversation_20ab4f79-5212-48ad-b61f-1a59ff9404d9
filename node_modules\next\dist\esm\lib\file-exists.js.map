{"version": 3, "sources": ["../../src/lib/file-exists.ts"], "names": ["existsSync", "promises", "isError", "FileType", "fileExists", "fileName", "type", "stats", "stat", "isFile", "isDirectory", "err", "code"], "mappings": "AAAA,SAASA,UAAU,EAAEC,QAAQ,QAAQ,KAAI;AACzC,OAAOC,aAAa,aAAY;;UAEpBC;;;GAAAA,aAAAA;AAKZ,OAAO,eAAeC,WACpBC,QAAgB,EAChBC,IAAe;IAEf,IAAI;QACF,IAAIA,iBAAwB;YAC1B,MAAMC,QAAQ,MAAMN,SAASO,IAAI,CAACH;YAClC,OAAOE,MAAME,MAAM;QACrB,OAAO,IAAIH,sBAA6B;YACtC,MAAMC,QAAQ,MAAMN,SAASO,IAAI,CAACH;YAClC,OAAOE,MAAMG,WAAW;QAC1B;QAEA,OAAOV,WAAWK;IACpB,EAAE,OAAOM,KAAK;QACZ,IACET,QAAQS,QACPA,CAAAA,IAAIC,IAAI,KAAK,YAAYD,IAAIC,IAAI,KAAK,cAAa,GACpD;YACA,OAAO;QACT;QACA,MAAMD;IACR;AACF"}