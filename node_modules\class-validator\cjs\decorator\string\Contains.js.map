{"version": 3, "file": "Contains.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/Contains.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAE1C,QAAA,QAAQ,GAAG,UAAU,CAAC;AAEnC;;;GAGG;AACH,SAAgB,QAAQ,CAAC,KAAc,EAAE,IAAY;IACnD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,kBAAiB,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrE,CAAC;AAFD,4BAEC;AAED;;;GAGG;AACH,SAAgB,QAAQ,CAAC,IAAY,EAAE,iBAAqC;IAC1E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gBAAQ;QACd,WAAW,EAAE,CAAC,IAAI,CAAC;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACzE,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,8CAA8C,EACzE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,4BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport containsValidator from 'validator/lib/contains';\n\nexport const CONTAINS = 'contains';\n\n/**\n * Checks if the string contains the seed.\n * If given value is not a string, then it returns false.\n */\nexport function contains(value: unknown, seed: string): boolean {\n  return typeof value === 'string' && containsValidator(value, seed);\n}\n\n/**\n * Checks if the string contains the seed.\n * If given value is not a string, then it returns false.\n */\nexport function Contains(seed: string, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: CONTAINS,\n      constraints: [seed],\n      validator: {\n        validate: (value, args): boolean => contains(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain a $constraint1 string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}