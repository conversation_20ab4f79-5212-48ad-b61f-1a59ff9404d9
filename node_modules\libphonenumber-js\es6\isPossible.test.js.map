{"version": 3, "file": "isPossible.test.js", "names": ["metadata", "type", "_isPossibleNumber", "parsePhoneNumber", "isPossibleNumber", "v2", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "undefined", "_objectSpread", "extract", "apply", "describe", "it", "expect", "to", "equal", "phone", "country", "countryCallingCode", "nationalNumber"], "sources": ["../source/isPossible.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\nimport _isPossibleNumber from './isPossible.js'\r\nimport parsePhoneNumber from './parsePhoneNumber.js'\r\n\r\nfunction isPossibleNumber(...parameters) {\r\n\tlet v2\r\n\tif (parameters.length < 1) {\r\n\t\t// `input` parameter.\r\n\t\tparameters.push(undefined)\r\n\t} else {\r\n\t\t// Convert string `input` to a `PhoneNumber` instance.\r\n\t\tif (typeof parameters[0] === 'string') {\r\n\t\t\tv2 = true\r\n\t\t\tparameters[0] = parsePhoneNumber(parameters[0], {\r\n\t\t\t\t...parameters[1],\r\n\t\t\t\textract: false\r\n\t\t\t}, metadata)\r\n\t\t}\r\n\t}\r\n\tif (parameters.length < 2) {\r\n\t\t// `options` parameter.\r\n\t\tparameters.push(undefined)\r\n\t}\r\n\t// Set `v2` flag.\r\n\tparameters[1] = {\r\n\t\tv2,\r\n\t\t...parameters[1]\r\n\t}\r\n\t// Add `metadata` parameter.\r\n\tparameters.push(metadata)\r\n\t// Call the function.\r\n\treturn _isPossibleNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isPossible', () => {\r\n\tit('should work', function()\r\n\t{\r\n\t\texpect(isPossibleNumber('+79992223344')).to.equal(true)\r\n\r\n\t\texpect(isPossibleNumber({ phone: '1112223344', country: 'RU' })).to.equal(true)\r\n\t\texpect(isPossibleNumber({ phone: '111222334', country: 'RU' })).to.equal(false)\r\n\t\texpect(isPossibleNumber({ phone: '11122233445', country: 'RU' })).to.equal(false)\r\n\r\n\t\texpect(isPossibleNumber({ phone: '1112223344', countryCallingCode: 7 })).to.equal(true)\r\n\t})\r\n\r\n\tit('should work v2', () => {\r\n\t\texpect(\r\n            isPossibleNumber({ nationalNumber: '111222334', countryCallingCode: 7 }, { v2: true })\r\n        ).to.equal(false)\r\n\t\texpect(\r\n            isPossibleNumber({ nationalNumber: '1112223344', countryCallingCode: 7 }, { v2: true })\r\n        ).to.equal(true)\r\n\t\texpect(\r\n            isPossibleNumber({ nationalNumber: '11122233445', countryCallingCode: 7 }, { v2: true })\r\n        ).to.equal(false)\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// Invalid `PhoneNumber` argument.\r\n\t\texpect(() => isPossibleNumber({}, { v2: true })).to.throw('Invalid phone number object passed')\r\n\r\n\t\t// Empty input is passed.\r\n\t\t// This is just to support `isValidNumber({})`\r\n\t\t// for cases when `parseNumber()` returns `{}`.\r\n\t\texpect(isPossibleNumber({})).to.equal(false)\r\n\t\texpect(() => isPossibleNumber({ phone: '1112223344' })).to.throw('Invalid phone number object passed')\r\n\r\n\t\t// Incorrect country.\r\n\t\texpect(() => isPossibleNumber({ phone: '1112223344', country: 'XX' })).to.throw('Unknown country')\r\n\t})\r\n})"], "mappings": ";;;;;;AAAA,OAAOA,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAC/D,OAAOC,iBAAiB,MAAM,iBAAiB;AAC/C,OAAOC,gBAAgB,MAAM,uBAAuB;AAEpD,SAASC,gBAAgBA,CAAA,EAAgB;EACxC,IAAIC,EAAE;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADsBC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEtC,IAAIF,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE;IAC1B;IACAC,UAAU,CAACG,IAAI,CAACC,SAAS,CAAC;EAC3B,CAAC,MAAM;IACN;IACA,IAAI,OAAOJ,UAAU,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACtCJ,EAAE,GAAG,IAAI;MACTI,UAAU,CAAC,CAAC,CAAC,GAAGN,gBAAgB,CAACM,UAAU,CAAC,CAAC,CAAC,EAAAK,aAAA,CAAAA,aAAA,KAC1CL,UAAU,CAAC,CAAC,CAAC;QAChBM,OAAO,EAAE;MAAK,IACZf,QAAQ,CAAC;IACb;EACD;EACA,IAAIS,UAAU,CAACD,MAAM,GAAG,CAAC,EAAE;IAC1B;IACAC,UAAU,CAACG,IAAI,CAACC,SAAS,CAAC;EAC3B;EACA;EACAJ,UAAU,CAAC,CAAC,CAAC,GAAAK,aAAA;IACZT,EAAE,EAAFA;EAAE,GACCI,UAAU,CAAC,CAAC,CAAC,CAChB;EACD;EACAA,UAAU,CAACG,IAAI,CAACZ,QAAQ,CAAC;EACzB;EACA,OAAOE,iBAAiB,CAACc,KAAK,CAAC,IAAI,EAAEP,UAAU,CAAC;AACjD;AAEAQ,QAAQ,CAAC,YAAY,EAAE,YAAM;EAC5BC,EAAE,CAAC,aAAa,EAAE,YAClB;IACCC,MAAM,CAACf,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAACgB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAEvDF,MAAM,CAACf,gBAAgB,CAAC;MAAEkB,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC/EF,MAAM,CAACf,gBAAgB,CAAC;MAAEkB,KAAK,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC/EF,MAAM,CAACf,gBAAgB,CAAC;MAAEkB,KAAK,EAAE,aAAa;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAEjFF,MAAM,CAACf,gBAAgB,CAAC;MAAEkB,KAAK,EAAE,YAAY;MAAEE,kBAAkB,EAAE;IAAE,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACxF,CAAC,CAAC;EAEFH,EAAE,CAAC,gBAAgB,EAAE,YAAM;IAC1BC,MAAM,CACIf,gBAAgB,CAAC;MAAEqB,cAAc,EAAE,WAAW;MAAED,kBAAkB,EAAE;IAAE,CAAC,EAAE;MAAEnB,EAAE,EAAE;IAAK,CAAC,CACzF,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACvBF,MAAM,CACIf,gBAAgB,CAAC;MAAEqB,cAAc,EAAE,YAAY;MAAED,kBAAkB,EAAE;IAAE,CAAC,EAAE;MAAEnB,EAAE,EAAE;IAAK,CAAC,CAC1F,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACtBF,MAAM,CACIf,gBAAgB,CAAC;MAAEqB,cAAc,EAAE,aAAa;MAAED,kBAAkB,EAAE;IAAE,CAAC,EAAE;MAAEnB,EAAE,EAAE;IAAK,CAAC,CAC3F,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EACxB,CAAC,CAAC;EAEFH,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC;IACAC,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,CAAC,CAAC,EAAE;QAAEC,EAAE,EAAE;MAAK,CAAC,CAAC;IAAA,EAAC,CAACe,EAAE,SAAM,CAAC,oCAAoC,CAAC;;IAE/F;IACA;IACA;IACAD,MAAM,CAACf,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAACgB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC;QAAEkB,KAAK,EAAE;MAAa,CAAC,CAAC;IAAA,EAAC,CAACF,EAAE,SAAM,CAAC,oCAAoC,CAAC;;IAEtG;IACAD,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC;QAAEkB,KAAK,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IAAA,EAAC,CAACH,EAAE,SAAM,CAAC,iBAAiB,CAAC;EACnG,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}