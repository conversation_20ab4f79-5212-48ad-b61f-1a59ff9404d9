{"version": 3, "sources": ["../../../src/build/swc/options.ts"], "names": ["getJestSWCOptions", "getLoaderSWCOptions", "getParserOptions", "nextDistPath", "nodeModulesPath", "regeneratorRuntimePath", "require", "resolve", "isTypeScriptFile", "filename", "endsWith", "isCommonJSFile", "shouldOutputCommonJs", "test", "jsConfig", "rest", "isTSFile", "hasTsSyntax", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "esm", "modularizeImports", "swcPlugins", "resolvedBaseUrl", "swcCacheDir", "serverComponents", "bundleLayer", "isReactServerLayer", "WEBPACK_LAYERS", "reactServerComponents", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "experimental", "keepImportAttributes", "emitAssertForImportAttributes", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "emotion", "runtime", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "removeConsole", "reactRemoveProperties", "Object", "fromEntries", "entries", "mod", "config", "key", "value", "relay", "styledJsx", "getEmotionOptions", "styledComponents", "getStyledComponentsOptions", "serverActions", "enabled", "hashSalt", "preferEsm", "styledComponentsConfig", "displayName", "emotionConfig", "autoLabel", "sourcemap", "importMap", "labelFormat", "sourceMap", "isServer", "pagesDir", "baseOptions", "useCjsModules", "env", "targets", "node", "module", "type", "disableNextSsg", "disablePageConfig", "appDir", "isPageFile", "optimizeServerReact", "optimizePackageImports", "supportedBrowsers", "relativeFilePathFromRoot", "fontLoaders", "cjsRequireOptimizer", "packages", "transforms", "NextRequest", "NextResponse", "ImageResponse", "userAgentFromString", "userAgent", "optimize_use_state", "autoModularizeImports", "isNodeModules", "isAppBrowserLayer", "appPagesBrowser", "moduleResolutionConfig", "isDevelopment", "isServerCompiler", "length", "target"], "mappings": ";;;;;;;;;;;;;;;;IA0QgBA,iBAAiB;eAAjBA;;IA0DAC,mBAAmB;eAAnBA;;IApSAC,gBAAgB;eAAhBA;;;2BAhCsC;AAStD,MAAMC,eACJ;AAEF,MAAMC,kBAAkB;AAExB,MAAMC,yBAAyBC,QAAQC,OAAO,CAC5C;AAGF,SAASC,iBAAiBC,QAAgB;IACxC,OAAOA,SAASC,QAAQ,CAAC,UAAUD,SAASC,QAAQ,CAAC;AACvD;AAEA,SAASC,eAAeF,QAAgB;IACtC,OAAOA,SAASC,QAAQ,CAAC;AAC3B;AAEA,qEAAqE;AACrE,mHAAmH;AACnH,SAASE,qBAAqBH,QAAgB;IAC5C,OAAOE,eAAeF,aAAaN,aAAaU,IAAI,CAACJ;AACvD;AAEO,SAASP,iBAAiB,EAAEO,QAAQ,EAAEK,QAAQ,EAAE,GAAGC,MAAW;QAIjED;IAHF,MAAME,WAAWP,SAASC,QAAQ,CAAC;IACnC,MAAMO,cAAcT,iBAAiBC;IACrC,MAAMS,mBAAmBC,QACvBL,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2BO,sBAAsB;IAEnD,OAAO;QACL,GAAGN,IAAI;QACPO,QAAQL,cAAc,eAAe;QACrCM,eAAe;QACfC,YAAYN;QACZ,qKAAqK;QACrK,CAACD,cAAc,QAAQ,MAAM,EAAE,CAACD;QAChCS,kBAAkB;IACpB;AACF;AAEA,SAASC,kBAAkB,EACzBjB,QAAQ,EACRkB,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,YAAY,EACZC,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfc,eAAe,EACfpB,QAAQ,EACRqB,WAAW,EACXC,gBAAgB,EAChBC,WAAW,EAgBZ;QAIevB,2BAEZA,4BAGAA,4BAGAA,4BAoCQA;IA/CV,MAAMwB,qBACJD,gBAAgBE,yBAAc,CAACC,qBAAqB;IACtD,MAAMC,eAAevC,iBAAiB;QAAEO;QAAUK;IAAS;IAC3D,MAAM4B,QAAQ5B,6BAAAA,4BAAAA,SAAUM,eAAe,qBAAzBN,0BAA2B4B,KAAK;IAC9C,MAAMxB,mBAAmBC,QACvBL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BO,sBAAsB;IAEnD,MAAMsB,wBAAwBxB,QAC5BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2B6B,qBAAqB;IAElD,MAAMC,0BAA0BzB,QAC9BL,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2B8B,uBAAuB;IAEpD,MAAMC,UAAU,AAACZ,CAAAA,cAAc,EAAE,AAAD,EAC7Ba,MAAM,CAACC,MAAMC,OAAO,EACpBC,GAAG,CAAC,CAAC,CAACC,MAAMC,QAAa,GAAK;YAAC7C,QAAQC,OAAO,CAAC2C;YAAOC;SAAQ;IAEjE,OAAO;QACLC,KAAK;YACH,GAAIlB,mBAAmBQ,QACnB;gBACEW,SAASnB,gBAAgBmB,OAAO;gBAChCX;YACF,IACA,CAAC,CAAC;YACNY,iBAAiB,CAACC,QAAQC,QAAQ,CAACC,GAAG,IAAI,CAAC9B;YAC3C+B,QAAQjB;YACRkB,cAAc;gBACZC,sBAAsB;gBACtBC,+BAA+B;gBAC/BhB;gBACAiB,WAAW3B;YACb;YACA4B,WAAW;gBACT,sIAAsI;gBACtI,GAAIpC,OACA;oBACEqC,QAAQ;wBACNrC,MAAM;oBACR;gBACF,IACA,CAAC,CAAC;gBACNsC,iBAAiB/C;gBACjBgD,mBAAmBvB;gBACnBC,yBAAyBA;gBACzBuB,OAAO;oBACLC,cACEtD,CAAAA,6BAAAA,6BAAAA,SAAUM,eAAe,qBAAzBN,2BAA2BuD,eAAe,KACzCjD,CAAAA,CAAAA,mCAAAA,gBAAiBkD,OAAO,KAAI,CAAChC,qBAC1B,mBACA,OAAM;oBACZiC,SAAS;oBACTC,YAAY;oBACZC,kBAAkB;oBAClB7C,aAAa,CAAC,CAACA;oBACf8C,aAAa;oBACbC,SAAS,CAAC,CAAC9C;gBACb;gBACA+C,WAAW;oBACTC,UAAU;oBACVC,SAASnD,OACL,OACA;wBACEoD,SAAS;4BACPC,QAAQlD,eAAe,WAAW;wBACpC;wBACAmD,MAAM;4BACJC,UAAUtD,cAAc,kBAAkB;wBAC5C;oBAEF;gBACN;gBACAuD,aAAa;oBACXC,YAAY/E;gBACd;YACF;QACF;QACAgF,YAAY1D,OAAO,WAAW2D;QAC9BC,aAAa,EAAEnE,mCAAAA,gBAAiBmE,aAAa;QAC7C,sDAAsD;QACtD,yDAAyD;QACzDC,uBAAuB7D,OACnB,QACAP,mCAAAA,gBAAiBoE,qBAAqB;QAC1C,wCAAwC;QACxCxD,mBAAmBA,oBACfyD,OAAOC,WAAW,CAChBD,OAAOE,OAAO,CAAC3D,mBAAmBiB,GAAG,CAAC,CAAC,CAAC2C,KAAKC,OAAO,GAAK;gBACvDD;gBACA;oBACE,GAAGC,MAAM;oBACT9B,WACE,OAAO8B,OAAO9B,SAAS,KAAK,WACxB8B,OAAO9B,SAAS,GAChB0B,OAAOE,OAAO,CAACE,OAAO9B,SAAS,EAAEd,GAAG,CAAC,CAAC,CAAC6C,KAAKC,MAAM,GAAK;4BACrDD;4BACAC;yBACD;gBACT;aACD,KAEHT;QACJU,KAAK,EAAE5E,mCAAAA,gBAAiB4E,KAAK;QAC7B,kFAAkF;QAClFC,WAAW,CAAC;QACZ,2GAA2G;QAC3G,GAAI,CAAC3D,sBAAsB;YACzB,mEAAmE;YACnEgC,SAAS4B,kBAAkB9E,mCAAAA,gBAAiBkD,OAAO,EAAE1C;YACrD,mEAAmE;YACnEuE,kBAAkBC,2BAChBhF,mCAAAA,gBAAiB+E,gBAAgB,EACjCvE;QAEJ,CAAC;QACDQ,kBACEA,oBAAoB,CAACT,OACjB;YACEW;QACF,IACAgD;QACNe,eACEjE,oBAAoB,CAACT,OACjB;YACE,+BAA+B;YAC/B,2BAA2B;YAC3B2E,SAAS;YACThE;YACAiE,UAAU;QACZ,IACAjB;QACN,0CAA0C;QAC1C,gDAAgD;QAChDkB,WAAWzE;IACb;AACF;AAEA,SAASqE,2BACPK,sBAAoE,EACpE7E,WAAgB;IAEhB,IAAI,CAAC6E,wBAAwB;QAC3B,OAAO;IACT,OAAO,IAAI,OAAOA,2BAA2B,UAAU;QACrD,OAAO;YACL,GAAGA,sBAAsB;YACzBC,aAAaD,uBAAuBC,WAAW,IAAIvF,QAAQS;QAC7D;IACF,OAAO;QACL,OAAO;YACL8E,aAAavF,QAAQS;QACvB;IACF;AACF;AAEA,SAASsE,kBACPS,aAAkD,EAClD/E,WAAoB;IAEpB,IAAI,CAAC+E,eAAe;QAClB,OAAO;IACT;IACA,IAAIC,YAAY,CAAC,CAAChF;IAClB,OAAQ,OAAO+E,kBAAkB,YAAYA,cAAcC,SAAS;QAClE,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;QACL;YACE;IACJ;IACA,OAAO;QACLN,SAAS;QACTM;QACAC,WAAWjF;QACX,GAAI,OAAO+E,kBAAkB,YAAY;YACvCG,WAAWH,cAAcG,SAAS;YAClCC,aAAaJ,cAAcI,WAAW;YACtCF,WAAWjF,eAAe+E,cAAcK,SAAS;QACnD,CAAC;IACH;AACF;AAEO,SAAShH,kBAAkB,EAChCiH,QAAQ,EACRxG,QAAQ,EACRsB,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRoB,eAAe,EACfgF,QAAQ,EAYT;IACC,IAAIC,cAAczF,kBAAkB;QAClCjB;QACAkB,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,cAAc,CAACmF;QACfjF;QACAC;QACAb;QACAN;QACAoB;QACAH;QACA,oDAAoD;QACpD,oDAAoD;QACpDM,aAAaiD;QACblD,kBAAkB;IACpB;IAEA,MAAMgF,gBAAgBxG,qBAAqBH;IAC3C,OAAO;QACL,GAAG0G,WAAW;QACdE,KAAK;YACHC,SAAS;gBACP,yCAAyC;gBACzCC,MAAMhE,QAAQC,QAAQ,CAAC+D,IAAI;YAC7B;QACF;QACAC,QAAQ;YACNC,MAAM1F,OAAO,CAACqF,gBAAgB,QAAQ;QACxC;QACAM,gBAAgB;QAChBC,mBAAmB;QACnBT;IACF;AACF;AAEO,SAASjH,oBAAoB,EAClC,+EAA+E;AAC/E,mBAAmB;AACnBQ,QAAQ,EACRmB,WAAW,EACXqF,QAAQ,EACRC,QAAQ,EACRU,MAAM,EACNC,UAAU,EACVhG,eAAe,EACfG,iBAAiB,EACjB8F,mBAAmB,EACnBC,sBAAsB,EACtB9F,UAAU,EACVb,eAAe,EACfN,QAAQ,EACRkH,iBAAiB,EACjB7F,WAAW,EACX8F,wBAAwB,EACxB7F,gBAAgB,EAChBC,WAAW,EACXN,GAAG,EAuBJ;IACC,IAAIoF,cAAmBzF,kBAAkB;QACvCjB;QACAmB;QACAE,cAAc,CAACmF;QACfpF;QACAG;QACAC;QACAb;QACAN;QACA,mBAAmB;QACnBqB;QACAE;QACAD;QACAL,KAAK,CAAC,CAACA;IACT;IACAoF,YAAYe,WAAW,GAAG;QACxBA,aAAa;YACX;YACA;YAEA,8CAA8C;YAC9C;YACA;SACD;QACDD;IACF;IACAd,YAAYgB,mBAAmB,GAAG;QAChCC,UAAU;YACR,eAAe;gBACbC,YAAY;oBACVC,aAAa;oBACbC,cAAc;oBACdC,eAAe;oBACfC,qBAAqB;oBACrBC,WAAW;gBACb;YACF;QACF;IACF;IAEA,IAAIZ,uBAAuBb,YAAY,CAACrF,aAAa;QACnDuF,YAAYW,mBAAmB,GAAG;YAChCa,oBAAoB;QACtB;IACF;IAEA,kDAAkD;IAClD,IAAIZ,wBAAwB;QAC1BZ,YAAYyB,qBAAqB,GAAG;YAClCR,UAAUL;QACZ;IACF;IAEA,MAAMc,gBAAgBzI,gBAAgBS,IAAI,CAACJ;IAC3C,MAAMqI,oBAAoBzG,gBAAgBE,yBAAc,CAACwG,eAAe;IACxE,MAAMC,yBAAyBpI,qBAAqBH,YAChD;QACE+G,QAAQ;YACNC,MAAM;QACR;IACF,IACA,CAAC;IAEL,IAAItE;IACJ,IAAI8D,UAAU;QACZ9D,UAAU;YACR,GAAGgE,WAAW;YACd,GAAG6B,sBAAsB;YACzB,8FAA8F;YAC9FtB,gBAAgB;YAChBC,mBAAmB;YACnBsB,eAAerH;YACfsH,kBAAkBjC;YAClBC;YACAU;YACApB,WAAW,CAAC,CAACzE;YACb8F;YACAR,KAAK;gBACHC,SAAS;oBACP,yCAAyC;oBACzCC,MAAMhE,QAAQC,QAAQ,CAAC+D,IAAI;gBAC7B;YACF;QACF;IACF,OAAO;QACLpE,UAAU;YACR,GAAGgE,WAAW;YACd,GAAG6B,sBAAsB;YACzBtB,gBAAgB,CAACG;YACjBoB,eAAerH;YACfsH,kBAAkBjC;YAClBC;YACAU;YACAC;YACA,GAAIG,qBAAqBA,kBAAkBmB,MAAM,GAAG,IAChD;gBACE9B,KAAK;oBACHC,SAASU;gBACX;YACF,IACA,CAAC,CAAC;QACR;QACA,IAAI,CAAC7E,QAAQkE,GAAG,EAAE;YAChB,6CAA6C;YAC7ClE,QAAQC,GAAG,CAACgG,MAAM,GAAG;QACvB;IACF;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,IAAIN,qBAAqBD,eAAe;YAQlC1F;QAPJA,QAAQuE,cAAc,GAAG;QACzBvE,QAAQwE,iBAAiB,GAAG;QAC5BxE,QAAQ0E,UAAU,GAAG;QACrB1E,QAAQ2E,mBAAmB,GAAGxC;QAC9BnC,QAAQgF,mBAAmB,GAAG7C;QAC9B,6FAA6F;QAC7F,uEAAuE;QACvE,KAAInC,2CAAAA,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,qBAAvC3B,yCAAyC4B,OAAO,EAAE;YACpD,OAAO5B,QAAQC,GAAG,CAACW,SAAS,CAACa,SAAS,CAACE,OAAO,CAACC,OAAO,CAACC,MAAM;QAC/D;IACF;IAEA,OAAO7B;AACT"}