{"version": 3, "file": "AsYouTypeFormatter.util.test.js", "names": ["closeNonPairedParens", "stripNonPairedParens", "repeat", "describe", "it", "expect", "to", "equal"], "sources": ["../source/AsYouTypeFormatter.util.test.js"], "sourcesContent": ["import { closeNonPairedParens, stripNonPairedParens, repeat } from './AsYouTypeFormatter.util.js'\r\n\r\ndescribe('closeNonPairedParens', () => {\r\n\tit('should close non-paired braces', () => {\r\n\t\texpect(closeNonPairedParens('(000) 123-45 (9  )', 15)).to.equal('(000) 123-45 (9  )')\r\n\t})\r\n})\r\n\r\ndescribe('stripNonPairedParens', () => {\r\n\tit('should strip non-paired braces', () => {\r\n\t\texpect(stripNonPairedParens('(000) 123-45 (9')).to.equal('(000) 123-45 9')\r\n\t\texpect(stripNonPairedParens('(000) 123-45 (9)')).to.equal('(000) 123-45 (9)')\r\n\t})\r\n})\r\n\r\ndescribe('repeat', () => {\r\n\tit('should repeat string N times', () => {\r\n\t\texpect(repeat('a', 0)).to.equal('')\r\n\t\texpect(repeat('a', 3)).to.equal('aaa')\r\n\t\texpect(repeat('a', 4)).to.equal('aaaa')\r\n\t})\r\n})"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,oBAAoB,EAAEC,MAAM,QAAQ,8BAA8B;AAEjGC,QAAQ,CAAC,sBAAsB,EAAE,YAAM;EACtCC,EAAE,CAAC,gCAAgC,EAAE,YAAM;IAC1CC,MAAM,CAACL,oBAAoB,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,oBAAoB,CAAC;EACtF,CAAC,CAAC;AACH,CAAC,CAAC;AAEFJ,QAAQ,CAAC,sBAAsB,EAAE,YAAM;EACtCC,EAAE,CAAC,gCAAgC,EAAE,YAAM;IAC1CC,MAAM,CAACJ,oBAAoB,CAAC,iBAAiB,CAAC,CAAC,CAACK,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC1EF,MAAM,CAACJ,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAACK,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EAC9E,CAAC,CAAC;AACH,CAAC,CAAC;AAEFJ,QAAQ,CAAC,QAAQ,EAAE,YAAM;EACxBC,EAAE,CAAC,8BAA8B,EAAE,YAAM;IACxCC,MAAM,CAACH,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACI,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACnCF,MAAM,CAACH,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACI,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACtCF,MAAM,CAACH,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACI,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;EACxC,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}