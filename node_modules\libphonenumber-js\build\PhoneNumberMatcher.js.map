{"version": 3, "file": "PhoneNumberMatcher.js", "names": ["_constants", "require", "_createExtensionPattern", "_interopRequireDefault", "_RegExpCache", "_util", "_utf", "_Leniency", "_parsePreCandidate", "_isValidPreCandidate", "_isValidCandidate", "_interopRequireWildcard", "_metadata", "_parsePhoneNumber", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "_typeof", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "a", "TypeError", "_defineProperties", "length", "enumerable", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "_toPrimitive", "toPrimitive", "String", "Number", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "EXTN_PATTERNS_FOR_MATCHING", "createExtensionPattern", "INNER_MATCHES", "concat", "pZ", "PZ", "leadLimit", "limit", "punctuationLimit", "digitBlockLimit", "MAX_LENGTH_FOR_NSN", "MAX_LENGTH_COUNTRY_CODE", "blockLimit", "punctuation", "VALID_PUNCTUATION", "digitSequence", "pNd", "PATTERN", "LEAD_CLASS", "UNWANTED_END_CHAR_PATTERN", "RegExp", "_pN", "_pL", "MAX_SAFE_INTEGER", "Math", "pow", "PhoneNumberMatcher", "exports", "text", "arguments", "undefined", "options", "metadata", "v2", "defaultCallingCode", "defaultCountry", "isSupportedCountry", "leniency", "extended", "max<PERSON>ries", "<PERSON><PERSON><PERSON>", "state", "searchIndex", "regExpCache", "RegExpCache", "value", "find", "matches", "exec", "candidate", "offset", "index", "parsePreCandidate", "isValidPreCandidate", "match", "parseAndVerify", "extractInnerMatch", "startsAt", "endsAt", "number", "phoneNumber", "result", "phone", "nationalNumber", "country", "countryCallingCode", "ext", "substring", "_i", "_INNER_MATCHES", "innerMatchPattern", "isFirstMatch", "<PERSON><PERSON><PERSON>", "innerMatchRegExp", "trimAfterFirstMatch", "slice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "isValidCandidate", "parsePhoneNumber", "isPossible", "hasNext", "lastMatch", "next", "Error"], "sources": ["../source/PhoneNumberMatcher.js"], "sourcesContent": ["/**\r\n * A port of Google's `PhoneNumberMatcher.java`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberMatcher.java\r\n * Date: 08.03.2018.\r\n */\r\n\r\nimport {\r\n  MAX_LENGTH_FOR_NSN,\r\n  MAX_LENGTH_COUNTRY_CODE,\r\n  VALID_PUNCTUATION\r\n} from './constants.js'\r\n\r\nimport createExtensionPattern from './helpers/extension/createExtensionPattern.js'\r\n\r\nimport RegExpCache from './findNumbers/RegExpCache.js'\r\n\r\nimport {\r\n\tlimit,\r\n\ttrimAfterFirstMatch\r\n} from './findNumbers/util.js'\r\n\r\nimport {\r\n\t_pL,\r\n\t_pN,\r\n\tpZ,\r\n\tPZ,\r\n\tpNd\r\n} from './findNumbers/utf-8.js'\r\n\r\nimport Leniency from './findNumbers/Leniency.js'\r\nimport parsePreCandidate from './findNumbers/parsePreCandidate.js'\r\nimport isValidPreCandidate from './findNumbers/isValidPreCandidate.js'\r\nimport isValidCandidate, { LEAD_CLASS } from './findNumbers/isValidCandidate.js'\r\n\r\nimport { isSupportedCountry } from './metadata.js'\r\n\r\nimport parsePhoneNumber from './parsePhoneNumber.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nconst EXTN_PATTERNS_FOR_MATCHING = createExtensionPattern('matching')\r\n\r\n/**\r\n * Patterns used to extract phone numbers from a larger phone-number-like pattern. These are\r\n * ordered according to specificity. For example, white-space is last since that is frequently\r\n * used in numbers, not just to separate two numbers. We have separate patterns since we don't\r\n * want to break up the phone-number-like text on more than one different kind of symbol at one\r\n * time, although symbols of the same type (e.g. space) can be safely grouped together.\r\n *\r\n * Note that if there is a match, we will always check any text found up to the first match as\r\n * well.\r\n */\r\nconst INNER_MATCHES =\r\n[\r\n\t// Breaks on the slash - e.g. \"************/************\"\r\n\t'\\\\/+(.*)/',\r\n\r\n\t// Note that the bracket here is inside the capturing group, since we consider it part of the\r\n\t// phone number. Will match a pattern like \"(************* (*************\".\r\n\t'(\\\\([^(]*)',\r\n\r\n\t// Breaks on a hyphen - e.g. \"12345 - ************ is my number.\"\r\n\t// We require a space on either side of the hyphen for it to be considered a separator.\r\n\t`(?:${pZ}-|-${pZ})${pZ}*(.+)`,\r\n\r\n\t// Various types of wide hyphens. Note we have decided not to enforce a space here, since it's\r\n\t// possible that it's supposed to be used to break two numbers without spaces, and we haven't\r\n\t// seen many instances of it used within a number.\r\n\t`[\\u2012-\\u2015\\uFF0D]${pZ}*(.+)`,\r\n\r\n\t// Breaks on a full stop - e.g. \"12345. ************ is my number.\"\r\n\t`\\\\.+${pZ}*([^.]+)`,\r\n\r\n\t// Breaks on space - e.g. \"3324451234 8002341234\"\r\n\t`${pZ}+(${PZ}+)`\r\n]\r\n\r\n// Limit on the number of leading (plus) characters.\r\nconst leadLimit = limit(0, 2)\r\n\r\n// Limit on the number of consecutive punctuation characters.\r\nconst punctuationLimit = limit(0, 4)\r\n\r\n/* The maximum number of digits allowed in a digit-separated block. As we allow all digits in a\r\n * single block, set high enough to accommodate the entire national number and the international\r\n * country code. */\r\nconst digitBlockLimit = MAX_LENGTH_FOR_NSN + MAX_LENGTH_COUNTRY_CODE\r\n\r\n// Limit on the number of blocks separated by punctuation.\r\n// Uses digitBlockLimit since some formats use spaces to separate each digit.\r\nconst blockLimit = limit(0, digitBlockLimit)\r\n\r\n/* A punctuation sequence allowing white space. */\r\nconst punctuation = `[${VALID_PUNCTUATION}]` + punctuationLimit\r\n\r\n// A digits block without punctuation.\r\nconst digitSequence = pNd + limit(1, digitBlockLimit)\r\n\r\n/**\r\n * Phone number pattern allowing optional punctuation.\r\n * The phone number pattern used by `find()`, similar to\r\n * VALID_PHONE_NUMBER, but with the following differences:\r\n * <ul>\r\n *   <li>All captures are limited in order to place an upper bound to the text matched by the\r\n *       pattern.\r\n * <ul>\r\n *   <li>Leading punctuation / plus signs are limited.\r\n *   <li>Consecutive occurrences of punctuation are limited.\r\n *   <li>Number of digits is limited.\r\n * </ul>\r\n *   <li>No whitespace is allowed at the start or end.\r\n *   <li>No alpha digits (vanity numbers such as 1-800-SIX-FLAGS) are currently supported.\r\n * </ul>\r\n */\r\nconst PATTERN = '(?:' + LEAD_CLASS + punctuation + ')' + leadLimit\r\n\t+ digitSequence + '(?:' + punctuation + digitSequence + ')' + blockLimit\r\n\t+ '(?:' + EXTN_PATTERNS_FOR_MATCHING + ')?'\r\n\r\n// Regular expression of trailing characters that we want to remove.\r\n// We remove all characters that are not alpha or numerical characters.\r\n// The hash character is retained here, as it may signify\r\n// the previous block was an extension.\r\n//\r\n// // Don't know what does '&&' mean here.\r\n// const UNWANTED_END_CHAR_PATTERN = new RegExp(`[[\\\\P{N}&&\\\\P{L}]&&[^#]]+$`)\r\n//\r\nconst UNWANTED_END_CHAR_PATTERN = new RegExp(`[^${_pN}${_pL}#]+$`)\r\n\r\n// const NON_DIGITS_PATTERN = /(\\D+)/\r\n\r\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER || Math.pow(2, 53) - 1\r\n\r\n/**\r\n * A stateful class that finds and extracts telephone numbers from {@linkplain CharSequence text}.\r\n * Instances can be created using the {@linkplain PhoneNumberUtil#findNumbers factory methods} in\r\n * {@link PhoneNumberUtil}.\r\n *\r\n * <p>Vanity numbers (phone numbers using alphabetic digits such as <tt>1-800-SIX-FLAGS</tt> are\r\n * not found.\r\n *\r\n * <p>This class is not thread-safe.\r\n */\r\nexport default class PhoneNumberMatcher\r\n{\r\n  /**\r\n   * @param {string} text — the character sequence that we will search, null for no text.\r\n   * @param {'POSSIBLE'|'VALID'|'STRICT_GROUPING'|'EXACT_GROUPING'} [options.leniency] — The leniency to use when evaluating candidate phone numbers. See `source/findNumbers/Leniency.js` for more details.\r\n   * @param {number} [options.maxTries] — The maximum number of invalid numbers to try before giving up on the text. This is to cover degenerate cases where the text has a lot of false positives in it. Must be >= 0.\r\n   */\r\n  constructor(text = '', options = {}, metadata)\r\n  {\r\n    options = {\r\n      v2: options.v2,\r\n      defaultCallingCode: options.defaultCallingCode,\r\n      defaultCountry: options.defaultCountry && isSupportedCountry(options.defaultCountry, metadata) ? options.defaultCountry : undefined,\r\n      // Here it should've assigned a default value only if `options.leniency === undefined`.\r\n      leniency: options.leniency || (options.extended ? 'POSSIBLE' : 'VALID'),\r\n      // Here it should've assigned a default value only if `options.maxTries === undefined`.\r\n      maxTries: options.maxTries || MAX_SAFE_INTEGER\r\n    }\r\n\r\n    // Validate `leniency`.\r\n\t\t// if (!options.leniency) {\r\n\t\t// \tthrow new TypeError('`leniency` is required')\r\n\t\t// }\r\n\t\tif (!Leniency[options.leniency]) {\r\n\t\t\tthrow new TypeError(`Unknown leniency: \"${options.leniency}\"`)\r\n\t\t}\r\n    if (options.leniency !== 'POSSIBLE' && options.leniency !== 'VALID') {\r\n      throw new TypeError(`Invalid \\`leniency\\`: \"${options.leniency}\". Supported values: \"POSSIBLE\", \"VALID\".`)\r\n    }\r\n\r\n    // Validate `maxTries`.\r\n\t\tif (options.maxTries < 0) {\r\n\t\t\tthrow new TypeError('`maxTries` must be `>= 0`')\r\n\t\t}\r\n\r\n\t\tthis.text = text\r\n\t\tthis.options = options\r\n    this.metadata = metadata\r\n\r\n\t\t// The degree of phone number validation.\r\n\t\tthis.leniency = Leniency[options.leniency]\r\n\r\n\t\t/** The maximum number of retries after matching an invalid number. */\r\n\t\tthis.maxTries = options.maxTries\r\n\r\n\t\tthis.PATTERN = new RegExp(PATTERN, 'ig')\r\n\r\n    /** The iteration tristate. */\r\n    this.state = 'NOT_READY'\r\n\r\n    /** The next index to start searching at. Undefined in {@link State#DONE}. */\r\n    this.searchIndex = 0\r\n\r\n    // A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\r\n    // countries being used for the same doc with ~10 patterns for each country. Some pages will have\r\n    // a lot more countries in use, but typically fewer numbers for each so expanding the cache for\r\n    // that use-case won't have a lot of benefit.\r\n    this.regExpCache = new RegExpCache(32)\r\n  }\r\n\r\n  /**\r\n   * Attempts to find the next subsequence in the searched sequence on or after {@code searchIndex}\r\n   * that represents a phone number. Returns the next match, null if none was found.\r\n   *\r\n   * @param index  the search index to start searching at\r\n   * @return  the phone number match found, null if none can be found\r\n   */\r\n\tfind() {\r\n\t\t// // Reset the regular expression.\r\n\t\t// this.PATTERN.lastIndex = index\r\n\r\n\t\tlet matches\r\n\t\twhile ((this.maxTries > 0) && (matches = this.PATTERN.exec(this.text)) !== null) {\r\n\t\t\tlet candidate = matches[0]\r\n\t\t\tconst offset = matches.index\r\n\r\n\t\t\tcandidate = parsePreCandidate(candidate)\r\n\r\n\t\t\tif (isValidPreCandidate(candidate, offset, this.text)) {\r\n\t\t\t\tconst match =\r\n\t\t\t\t\t// Try to come up with a valid match given the entire candidate.\r\n\t\t\t\t\tthis.parseAndVerify(candidate, offset, this.text)\r\n\t\t\t\t\t// If that failed, try to find an \"inner match\" -\r\n\t\t\t\t\t// there might be a phone number within this candidate.\r\n\t\t\t\t\t|| this.extractInnerMatch(candidate, offset, this.text)\r\n\r\n\t\t\t\tif (match) {\r\n\t\t\t\t\tif (this.options.v2) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tstartsAt: match.startsAt,\r\n\t\t\t\t\t\t\tendsAt: match.endsAt,\r\n\t\t\t\t\t\t\tnumber: match.phoneNumber\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n            const { phoneNumber } = match\r\n\r\n            const result = {\r\n              startsAt: match.startsAt,\r\n              endsAt: match.endsAt,\r\n              phone: phoneNumber.nationalNumber\r\n            }\r\n\r\n            if (phoneNumber.country) {\r\n              /* istanbul ignore if */\r\n              if (USE_NON_GEOGRAPHIC_COUNTRY_CODE && country === '001') {\r\n                result.countryCallingCode = phoneNumber.countryCallingCode\r\n              } else {\r\n                result.country = phoneNumber.country\r\n              }\r\n            } else {\r\n              result.countryCallingCode = phoneNumber.countryCallingCode\r\n            }\r\n\r\n            if (phoneNumber.ext) {\r\n              result.ext = phoneNumber.ext\r\n            }\r\n\r\n            return result\r\n          }\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tthis.maxTries--\r\n\t\t}\r\n\t}\r\n\r\n  /**\r\n   * Attempts to extract a match from `substring`\r\n   * if the substring itself does not qualify as a match.\r\n   */\r\n  extractInnerMatch(substring, offset, text) {\r\n    for (const innerMatchPattern of INNER_MATCHES) {\r\n      let isFirstMatch = true\r\n      let candidateMatch\r\n      const innerMatchRegExp = new RegExp(innerMatchPattern, 'g')\r\n      while (this.maxTries > 0 && (candidateMatch = innerMatchRegExp.exec(substring)) !== null) {\r\n        if (isFirstMatch) {\r\n          // We should handle any group before this one too.\r\n          const candidate = trimAfterFirstMatch(\r\n            UNWANTED_END_CHAR_PATTERN,\r\n            substring.slice(0, candidateMatch.index)\r\n          )\r\n\r\n          const match = this.parseAndVerify(candidate, offset, text)\r\n\r\n          if (match) {\r\n            return match\r\n          }\r\n\r\n          this.maxTries--\r\n          isFirstMatch = false\r\n        }\r\n\r\n        const candidate = trimAfterFirstMatch(UNWANTED_END_CHAR_PATTERN, candidateMatch[1])\r\n\r\n        // Java code does `groupMatcher.start(1)` here,\r\n        // but there's no way in javascript to get a `candidate` start index,\r\n        // therefore resort to using this kind of an approximation.\r\n        // (`groupMatcher` is called `candidateInSubstringMatch` in this javascript port)\r\n        // https://stackoverflow.com/questions/15934353/get-index-of-each-capture-in-a-javascript-regex\r\n        const candidateIndexGuess = substring.indexOf(candidate, candidateMatch.index)\r\n\r\n        const match = this.parseAndVerify(candidate, offset + candidateIndexGuess, text)\r\n        if (match) {\r\n          return match\r\n        }\r\n\r\n        this.maxTries--\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parses a phone number from the `candidate` using `parse` and\r\n   * verifies it matches the requested `leniency`. If parsing and verification succeed,\r\n   * a corresponding `PhoneNumberMatch` is returned, otherwise this method returns `null`.\r\n   *\r\n   * @param candidate  the candidate match\r\n   * @param offset  the offset of {@code candidate} within {@link #text}\r\n   * @return  the parsed and validated phone number match, or null\r\n   */\r\n  parseAndVerify(candidate, offset, text) {\r\n    if (!isValidCandidate(candidate, offset, text, this.options.leniency)) {\r\n      return\r\n  \t}\r\n\r\n    const phoneNumber = parsePhoneNumber(\r\n      candidate,\r\n      {\r\n        extended: true,\r\n        defaultCountry: this.options.defaultCountry,\r\n        defaultCallingCode: this.options.defaultCallingCode\r\n      },\r\n      this.metadata\r\n    )\r\n\r\n    if (!phoneNumber) {\r\n      return\r\n    }\r\n\r\n    if (!phoneNumber.isPossible()) {\r\n      return\r\n    }\r\n\r\n    if (this.leniency(phoneNumber, {\r\n      candidate,\r\n      defaultCountry: this.options.defaultCountry,\r\n      metadata: this.metadata,\r\n      regExpCache: this.regExpCache\r\n    })) {\r\n      return {\r\n        startsAt: offset,\r\n        endsAt: offset + candidate.length,\r\n        phoneNumber\r\n      }\r\n    }\r\n  }\r\n\r\n  hasNext()\r\n  {\r\n    if (this.state === 'NOT_READY')\r\n    {\r\n      this.lastMatch = this.find() // (this.searchIndex)\r\n\r\n      if (this.lastMatch)\r\n      {\r\n        // this.searchIndex = this.lastMatch.endsAt\r\n        this.state = 'READY'\r\n      }\r\n      else\r\n      {\r\n        this.state = 'DONE'\r\n      }\r\n    }\r\n\r\n    return this.state === 'READY'\r\n  }\r\n\r\n  next()\r\n  {\r\n    // Check the state and find the next match as a side-effect if necessary.\r\n    if (!this.hasNext())\r\n    {\r\n      throw new Error('No next element')\r\n    }\r\n\r\n    // Don't retain that memory any longer than necessary.\r\n    const result = this.lastMatch\r\n    this.lastMatch = null\r\n    this.state = 'NOT_READY'\r\n    return result\r\n  }\r\n}"], "mappings": ";;;;;;AAMA,IAAAA,UAAA,GAAAC,OAAA;AAMA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAAI,KAAA,GAAAJ,OAAA;AAKA,IAAAK,IAAA,GAAAL,OAAA;AAQA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,kBAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,oBAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,iBAAA,GAAAC,uBAAA,CAAAV,OAAA;AAEA,IAAAW,SAAA,GAAAX,OAAA;AAEA,IAAAY,iBAAA,GAAAV,sBAAA,CAAAF,OAAA;AAAoD,SAAAU,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,wBAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,mBAAAT,CAAA,iBAAAA,CAAA,gBAAAU,OAAA,CAAAV,CAAA,0BAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAZ,uBAAAW,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAK,UAAA,GAAAL,CAAA,gBAAAA,CAAA;AAAA,SAAAU,QAAAJ,CAAA,sCAAAI,OAAA,wBAAAU,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAf,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAc,MAAA,IAAAd,CAAA,CAAAgB,WAAA,KAAAF,MAAA,IAAAd,CAAA,KAAAc,MAAA,CAAAG,SAAA,qBAAAjB,CAAA,KAAAI,OAAA,CAAAJ,CAAA;AAAA,SAAAkB,gBAAAC,CAAA,EAAArB,CAAA,UAAAqB,CAAA,YAAArB,CAAA,aAAAsB,SAAA;AAAA,SAAAC,kBAAA3B,CAAA,EAAAG,CAAA,aAAAF,CAAA,MAAAA,CAAA,GAAAE,CAAA,CAAAyB,MAAA,EAAA3B,CAAA,UAAAK,CAAA,GAAAH,CAAA,CAAAF,CAAA,GAAAK,CAAA,CAAAuB,UAAA,GAAAvB,CAAA,CAAAuB,UAAA,QAAAvB,CAAA,CAAAwB,YAAA,kBAAAxB,CAAA,KAAAA,CAAA,CAAAyB,QAAA,QAAAd,MAAA,CAAAC,cAAA,CAAAlB,CAAA,EAAAgC,cAAA,CAAA1B,CAAA,CAAA2B,GAAA,GAAA3B,CAAA;AAAA,SAAA4B,aAAAlC,CAAA,EAAAG,CAAA,EAAAF,CAAA,WAAAE,CAAA,IAAAwB,iBAAA,CAAA3B,CAAA,CAAAuB,SAAA,EAAApB,CAAA,GAAAF,CAAA,IAAA0B,iBAAA,CAAA3B,CAAA,EAAAC,CAAA,GAAAgB,MAAA,CAAAC,cAAA,CAAAlB,CAAA,iBAAA+B,QAAA,SAAA/B,CAAA;AAAA,SAAAgC,eAAA/B,CAAA,QAAAM,CAAA,GAAA4B,YAAA,CAAAlC,CAAA,gCAAAS,OAAA,CAAAH,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAA4B,aAAAlC,CAAA,EAAAE,CAAA,oBAAAO,OAAA,CAAAT,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAmB,MAAA,CAAAgB,WAAA,kBAAApC,CAAA,QAAAO,CAAA,GAAAP,CAAA,CAAAgB,IAAA,CAAAf,CAAA,EAAAE,CAAA,gCAAAO,OAAA,CAAAH,CAAA,UAAAA,CAAA,YAAAmB,SAAA,yEAAAvB,CAAA,GAAAkC,MAAA,GAAAC,MAAA,EAAArC,CAAA,KApCpD;AACA;AACA;AACA;AACA;AAkCA,IAAMsC,+BAA+B,GAAG,KAAK;AAE7C,IAAMC,0BAA0B,GAAG,IAAAC,kCAAsB,EAAC,UAAU,CAAC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,aAAa,GACnB;AACC;AACA,WAAW;AAEX;AACA;AACA,YAAY,EAEZ;AACA;AAAA,MAAAC,MAAA,CACMC,OAAE,SAAAD,MAAA,CAAMC,OAAE,OAAAD,MAAA,CAAIC,OAAE,YAEtB;AACA;AACA;AAAA,wBAAAD,MAAA,CACwBC,OAAE,YAE1B;AAAA,OAAAD,MAAA,CACOC,OAAE,eAET;AAAA,GAAAD,MAAA,CACGC,OAAE,QAAAD,MAAA,CAAKE,OAAE,QACZ;;AAED;AACA,IAAMC,SAAS,GAAG,IAAAC,WAAK,EAAC,CAAC,EAAE,CAAC,CAAC;;AAE7B;AACA,IAAMC,gBAAgB,GAAG,IAAAD,WAAK,EAAC,CAAC,EAAE,CAAC,CAAC;;AAEpC;AACA;AACA;AACA,IAAME,eAAe,GAAGC,6BAAkB,GAAGC,kCAAuB;;AAEpE;AACA;AACA,IAAMC,UAAU,GAAG,IAAAL,WAAK,EAAC,CAAC,EAAEE,eAAe,CAAC;;AAE5C;AACA,IAAMI,WAAW,GAAG,IAAAV,MAAA,CAAIW,4BAAiB,SAAMN,gBAAgB;;AAE/D;AACA,IAAMO,aAAa,GAAGC,QAAG,GAAG,IAAAT,WAAK,EAAC,CAAC,EAAEE,eAAe,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMQ,OAAO,GAAG,KAAK,GAAGC,4BAAU,GAAGL,WAAW,GAAG,GAAG,GAAGP,SAAS,GAC/DS,aAAa,GAAG,KAAK,GAAGF,WAAW,GAAGE,aAAa,GAAG,GAAG,GAAGH,UAAU,GACtE,KAAK,GAAGZ,0BAA0B,GAAG,IAAI;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMmB,yBAAyB,GAAG,IAAIC,MAAM,MAAAjB,MAAA,CAAMkB,QAAG,EAAAlB,MAAA,CAAGmB,QAAG,SAAM,CAAC;;AAElE;;AAEA,IAAMC,gBAAgB,GAAGzB,MAAM,CAACyB,gBAAgB,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,IAUqBC,kBAAkB,GAAAC,OAAA;EAErC;AACF;AACA;AACA;AACA;EACE,SAAAD,mBAAA,EACA;IAAA,IADYE,IAAI,GAAAC,SAAA,CAAAzC,MAAA,QAAAyC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IAAA,IAAEE,OAAO,GAAAF,SAAA,CAAAzC,MAAA,QAAAyC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAEG,QAAQ,GAAAH,SAAA,CAAAzC,MAAA,OAAAyC,SAAA,MAAAC,SAAA;IAAA9C,eAAA,OAAA0C,kBAAA;IAE3CK,OAAO,GAAG;MACRE,EAAE,EAAEF,OAAO,CAACE,EAAE;MACdC,kBAAkB,EAAEH,OAAO,CAACG,kBAAkB;MAC9CC,cAAc,EAAEJ,OAAO,CAACI,cAAc,IAAI,IAAAC,4BAAkB,EAACL,OAAO,CAACI,cAAc,EAAEH,QAAQ,CAAC,GAAGD,OAAO,CAACI,cAAc,GAAGL,SAAS;MACnI;MACAO,QAAQ,EAAEN,OAAO,CAACM,QAAQ,KAAKN,OAAO,CAACO,QAAQ,GAAG,UAAU,GAAG,OAAO,CAAC;MACvE;MACAC,QAAQ,EAAER,OAAO,CAACQ,QAAQ,IAAIhB;IAChC,CAAC;;IAED;IACF;IACA;IACA;IACA,IAAI,CAACiB,oBAAQ,CAACT,OAAO,CAACM,QAAQ,CAAC,EAAE;MAChC,MAAM,IAAInD,SAAS,wBAAAiB,MAAA,CAAuB4B,OAAO,CAACM,QAAQ,OAAG,CAAC;IAC/D;IACE,IAAIN,OAAO,CAACM,QAAQ,KAAK,UAAU,IAAIN,OAAO,CAACM,QAAQ,KAAK,OAAO,EAAE;MACnE,MAAM,IAAInD,SAAS,0BAAAiB,MAAA,CAA2B4B,OAAO,CAACM,QAAQ,mDAA2C,CAAC;IAC5G;;IAEA;IACF,IAAIN,OAAO,CAACQ,QAAQ,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIrD,SAAS,CAAC,2BAA2B,CAAC;IACjD;IAEA,IAAI,CAAC0C,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,OAAO,GAAGA,OAAO;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;;IAE1B;IACA,IAAI,CAACK,QAAQ,GAAGG,oBAAQ,CAACT,OAAO,CAACM,QAAQ,CAAC;;IAE1C;IACA,IAAI,CAACE,QAAQ,GAAGR,OAAO,CAACQ,QAAQ;IAEhC,IAAI,CAACtB,OAAO,GAAG,IAAIG,MAAM,CAACH,OAAO,EAAE,IAAI,CAAC;;IAEtC;IACA,IAAI,CAACwB,KAAK,GAAG,WAAW;;IAExB;IACA,IAAI,CAACC,WAAW,GAAG,CAAC;;IAEpB;IACA;IACA;IACA;IACA,IAAI,CAACC,WAAW,GAAG,IAAIC,uBAAW,CAAC,EAAE,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE,OAAAlD,YAAA,CAAAgC,kBAAA;IAAAjC,GAAA;IAAAoD,KAAA,EAOD,SAAAC,IAAIA,CAAA,EAAG;MACN;MACA;;MAEA,IAAIC,OAAO;MACX,OAAQ,IAAI,CAACR,QAAQ,GAAG,CAAC,IAAK,CAACQ,OAAO,GAAG,IAAI,CAAC9B,OAAO,CAAC+B,IAAI,CAAC,IAAI,CAACpB,IAAI,CAAC,MAAM,IAAI,EAAE;QAChF,IAAIqB,SAAS,GAAGF,OAAO,CAAC,CAAC,CAAC;QAC1B,IAAMG,MAAM,GAAGH,OAAO,CAACI,KAAK;QAE5BF,SAAS,GAAG,IAAAG,6BAAiB,EAACH,SAAS,CAAC;QAExC,IAAI,IAAAI,+BAAmB,EAACJ,SAAS,EAAEC,MAAM,EAAE,IAAI,CAACtB,IAAI,CAAC,EAAE;UACtD,IAAM0B,KAAK;UACV;UACA,IAAI,CAACC,cAAc,CAACN,SAAS,EAAEC,MAAM,EAAE,IAAI,CAACtB,IAAI;UAChD;UACA;UAAA,GACG,IAAI,CAAC4B,iBAAiB,CAACP,SAAS,EAAEC,MAAM,EAAE,IAAI,CAACtB,IAAI,CAAC;UAExD,IAAI0B,KAAK,EAAE;YACV,IAAI,IAAI,CAACvB,OAAO,CAACE,EAAE,EAAE;cACpB,OAAO;gBACNwB,QAAQ,EAAEH,KAAK,CAACG,QAAQ;gBACxBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;gBACpBC,MAAM,EAAEL,KAAK,CAACM;cACf,CAAC;YACF,CAAC,MAAM;cACA,IAAQA,WAAW,GAAKN,KAAK,CAArBM,WAAW;cAEnB,IAAMC,MAAM,GAAG;gBACbJ,QAAQ,EAAEH,KAAK,CAACG,QAAQ;gBACxBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;gBACpBI,KAAK,EAAEF,WAAW,CAACG;cACrB,CAAC;cAED,IAAIH,WAAW,CAACI,OAAO,EAAE;gBACvB;gBACA,IAAIjE,+BAA+B,IAAIiE,OAAO,KAAK,KAAK,EAAE;kBACxDH,MAAM,CAACI,kBAAkB,GAAGL,WAAW,CAACK,kBAAkB;gBAC5D,CAAC,MAAM;kBACLJ,MAAM,CAACG,OAAO,GAAGJ,WAAW,CAACI,OAAO;gBACtC;cACF,CAAC,MAAM;gBACLH,MAAM,CAACI,kBAAkB,GAAGL,WAAW,CAACK,kBAAkB;cAC5D;cAEA,IAAIL,WAAW,CAACM,GAAG,EAAE;gBACnBL,MAAM,CAACK,GAAG,GAAGN,WAAW,CAACM,GAAG;cAC9B;cAEA,OAAOL,MAAM;YACf;UACN;QACD;QAEA,IAAI,CAACtB,QAAQ,EAAE;MAChB;IACD;;IAEC;AACF;AACA;AACA;EAHE;IAAA9C,GAAA;IAAAoD,KAAA,EAIA,SAAAW,iBAAiBA,CAACW,SAAS,EAAEjB,MAAM,EAAEtB,IAAI,EAAE;MACzC,SAAAwC,EAAA,MAAAC,cAAA,GAAgCnE,aAAa,EAAAkE,EAAA,GAAAC,cAAA,CAAAjF,MAAA,EAAAgF,EAAA,IAAE;QAA1C,IAAME,iBAAiB,GAAAD,cAAA,CAAAD,EAAA;QAC1B,IAAIG,YAAY,GAAG,IAAI;QACvB,IAAIC,cAAc;QAClB,IAAMC,gBAAgB,GAAG,IAAIrD,MAAM,CAACkD,iBAAiB,EAAE,GAAG,CAAC;QAC3D,OAAO,IAAI,CAAC/B,QAAQ,GAAG,CAAC,IAAI,CAACiC,cAAc,GAAGC,gBAAgB,CAACzB,IAAI,CAACmB,SAAS,CAAC,MAAM,IAAI,EAAE;UACxF,IAAII,YAAY,EAAE;YAChB;YACA,IAAMtB,UAAS,GAAG,IAAAyB,yBAAmB,EACnCvD,yBAAyB,EACzBgD,SAAS,CAACQ,KAAK,CAAC,CAAC,EAAEH,cAAc,CAACrB,KAAK,CACzC,CAAC;YAED,IAAMG,MAAK,GAAG,IAAI,CAACC,cAAc,CAACN,UAAS,EAAEC,MAAM,EAAEtB,IAAI,CAAC;YAE1D,IAAI0B,MAAK,EAAE;cACT,OAAOA,MAAK;YACd;YAEA,IAAI,CAACf,QAAQ,EAAE;YACfgC,YAAY,GAAG,KAAK;UACtB;UAEA,IAAMtB,SAAS,GAAG,IAAAyB,yBAAmB,EAACvD,yBAAyB,EAAEqD,cAAc,CAAC,CAAC,CAAC,CAAC;;UAEnF;UACA;UACA;UACA;UACA;UACA,IAAMI,mBAAmB,GAAGT,SAAS,CAACU,OAAO,CAAC5B,SAAS,EAAEuB,cAAc,CAACrB,KAAK,CAAC;UAE9E,IAAMG,KAAK,GAAG,IAAI,CAACC,cAAc,CAACN,SAAS,EAAEC,MAAM,GAAG0B,mBAAmB,EAAEhD,IAAI,CAAC;UAChF,IAAI0B,KAAK,EAAE;YACT,OAAOA,KAAK;UACd;UAEA,IAAI,CAACf,QAAQ,EAAE;QACjB;MACF;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EARE;IAAA9C,GAAA;IAAAoD,KAAA,EASA,SAAAU,cAAcA,CAACN,SAAS,EAAEC,MAAM,EAAEtB,IAAI,EAAE;MACtC,IAAI,CAAC,IAAAkD,4BAAgB,EAAC7B,SAAS,EAAEC,MAAM,EAAEtB,IAAI,EAAE,IAAI,CAACG,OAAO,CAACM,QAAQ,CAAC,EAAE;QACrE;MACH;MAEC,IAAMuB,WAAW,GAAG,IAAAmB,4BAAgB,EAClC9B,SAAS,EACT;QACEX,QAAQ,EAAE,IAAI;QACdH,cAAc,EAAE,IAAI,CAACJ,OAAO,CAACI,cAAc;QAC3CD,kBAAkB,EAAE,IAAI,CAACH,OAAO,CAACG;MACnC,CAAC,EACD,IAAI,CAACF,QACP,CAAC;MAED,IAAI,CAAC4B,WAAW,EAAE;QAChB;MACF;MAEA,IAAI,CAACA,WAAW,CAACoB,UAAU,CAAC,CAAC,EAAE;QAC7B;MACF;MAEA,IAAI,IAAI,CAAC3C,QAAQ,CAACuB,WAAW,EAAE;QAC7BX,SAAS,EAATA,SAAS;QACTd,cAAc,EAAE,IAAI,CAACJ,OAAO,CAACI,cAAc;QAC3CH,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBW,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC,EAAE;QACF,OAAO;UACLc,QAAQ,EAAEP,MAAM;UAChBQ,MAAM,EAAER,MAAM,GAAGD,SAAS,CAAC7D,MAAM;UACjCwE,WAAW,EAAXA;QACF,CAAC;MACH;IACF;EAAC;IAAAnE,GAAA;IAAAoD,KAAA,EAED,SAAAoC,OAAOA,CAAA,EACP;MACE,IAAI,IAAI,CAACxC,KAAK,KAAK,WAAW,EAC9B;QACE,IAAI,CAACyC,SAAS,GAAG,IAAI,CAACpC,IAAI,CAAC,CAAC,EAAC;;QAE7B,IAAI,IAAI,CAACoC,SAAS,EAClB;UACE;UACA,IAAI,CAACzC,KAAK,GAAG,OAAO;QACtB,CAAC,MAED;UACE,IAAI,CAACA,KAAK,GAAG,MAAM;QACrB;MACF;MAEA,OAAO,IAAI,CAACA,KAAK,KAAK,OAAO;IAC/B;EAAC;IAAAhD,GAAA;IAAAoD,KAAA,EAED,SAAAsC,IAAIA,CAAA,EACJ;MACE;MACA,IAAI,CAAC,IAAI,CAACF,OAAO,CAAC,CAAC,EACnB;QACE,MAAM,IAAIG,KAAK,CAAC,iBAAiB,CAAC;MACpC;;MAEA;MACA,IAAMvB,MAAM,GAAG,IAAI,CAACqB,SAAS;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACzC,KAAK,GAAG,WAAW;MACxB,OAAOoB,MAAM;IACf;EAAC;AAAA", "ignoreList": []}