{"version": 3, "file": "parseDigits.test.js", "names": ["parseDigits", "describe", "it", "expect", "to", "equal"], "sources": ["../../source/helpers/parseDigits.test.js"], "sourcesContent": ["import parseDigits from './parseDigits.js'\r\n\r\ndescribe('parseDigits', () => {\r\n\tit('should parse digits', () => {\r\n\t\texpect(parseDigits('+٤٤٢٣٢٣٢٣٤')).to.equal('442323234')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAE1CC,QAAQ,CAAC,aAAa,EAAE,YAAM;EAC7BC,EAAE,CAAC,qBAAqB,EAAE,YAAM;IAC/BC,MAAM,CAACH,WAAW,CAAC,YAAY,CAAC,CAAC,CAACI,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EACxD,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}