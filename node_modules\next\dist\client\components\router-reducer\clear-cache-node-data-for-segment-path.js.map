{"version": 3, "sources": ["../../../../src/client/components/router-reducer/clear-cache-node-data-for-segment-path.ts"], "names": ["clearCacheNodeDataForSegmentPath", "newCache", "existingCache", "flightSegmentPath", "isLastEntry", "length", "parallelRouteKey", "segment", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "existingChildSegmentMap", "parallelRoutes", "get", "childSegmentMap", "Map", "set", "existingChildCacheNode", "childCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "lazyDataResolved", "loading", "slice"], "mappings": ";;;;+BAOgBA;;;eAAAA;;;sCALqB;AAK9B,SAASA,iCACdC,QAAmB,EACnBC,aAAwB,EACxBC,iBAAoC;IAEpC,MAAMC,cAAcD,kBAAkBE,MAAM,IAAI;IAEhD,MAAM,CAACC,kBAAkBC,QAAQ,GAAGJ;IACpC,MAAMK,WAAWC,IAAAA,0CAAoB,EAACF;IAEtC,MAAMG,0BACJR,cAAcS,cAAc,CAACC,GAAG,CAACN;IAEnC,IAAIO,kBAAkBZ,SAASU,cAAc,CAACC,GAAG,CAACN;IAElD,IAAI,CAACO,mBAAmBA,oBAAoBH,yBAAyB;QACnEG,kBAAkB,IAAIC,IAAIJ;QAC1BT,SAASU,cAAc,CAACI,GAAG,CAACT,kBAAkBO;IAChD;IAEA,MAAMG,yBAAyBN,2CAAAA,wBAAyBE,GAAG,CAACJ;IAC5D,IAAIS,iBAAiBJ,gBAAgBD,GAAG,CAACJ;IAEzC,yFAAyF;IACzF,IAAIJ,aAAa;QACf,IACE,CAACa,kBACD,CAACA,eAAeC,QAAQ,IACxBD,mBAAmBD,wBACnB;YACAH,gBAAgBE,GAAG,CAACP,UAAU;gBAC5BU,UAAU;gBACVC,KAAK;gBACLC,aAAa;gBACbC,MAAM;gBACNC,cAAc;gBACdX,gBAAgB,IAAIG;gBACpBS,kBAAkB;gBAClBC,SAAS;YACX;QACF;QACA;IACF;IAEA,IAAI,CAACP,kBAAkB,CAACD,wBAAwB;QAC9C,+EAA+E;QAC/E,IAAI,CAACC,gBAAgB;YACnBJ,gBAAgBE,GAAG,CAACP,UAAU;gBAC5BU,UAAU;gBACVC,KAAK;gBACLC,aAAa;gBACbC,MAAM;gBACNC,cAAc;gBACdX,gBAAgB,IAAIG;gBACpBS,kBAAkB;gBAClBC,SAAS;YACX;QACF;QACA;IACF;IAEA,IAAIP,mBAAmBD,wBAAwB;QAC7CC,iBAAiB;YACfC,UAAUD,eAAeC,QAAQ;YACjCC,KAAKF,eAAeE,GAAG;YACvBC,aAAaH,eAAeG,WAAW;YACvCC,MAAMJ,eAAeI,IAAI;YACzBC,cAAcL,eAAeK,YAAY;YACzCX,gBAAgB,IAAIG,IAAIG,eAAeN,cAAc;YACrDY,kBAAkBN,eAAeM,gBAAgB;YACjDC,SAASP,eAAeO,OAAO;QACjC;QACAX,gBAAgBE,GAAG,CAACP,UAAUS;IAChC;IAEA,OAAOjB,iCACLiB,gBACAD,wBACAb,kBAAkBsB,KAAK,CAAC;AAE5B"}