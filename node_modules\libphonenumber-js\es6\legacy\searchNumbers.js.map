{"version": 3, "file": "searchNumbers.js", "names": ["normalizeArguments", "PhoneNumberMatcher", "searchNumbers", "_normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "_defineProperty", "Symbol", "iterator", "next", "hasNext", "done", "value"], "sources": ["../../source/legacy/searchNumbers.js"], "sourcesContent": ["import normalizeArguments from '../normalizeArguments.js'\r\nimport PhoneNumberMatcher from '../PhoneNumberMatcher.js'\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport default function searchNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\r\n\tconst matcher = new PhoneNumberMatcher(text, options, metadata)\r\n\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (matcher.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: matcher.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n"], "mappings": ";;;;AAAA,OAAOA,kBAAkB,MAAM,0BAA0B;AACzD,OAAOC,kBAAkB,MAAM,0BAA0B;;AAEzD;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAAA,EACrC;EACC,IAAAC,mBAAA,GAAoCH,kBAAkB,CAACI,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAE/B,IAAMC,OAAO,GAAG,IAAIP,kBAAkB,CAACI,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAE/D,OAAAE,eAAA,KACEC,MAAM,CAACC,QAAQ,cAAI;IACnB,OAAO;MACHC,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAQ;QACX,IAAIJ,OAAO,CAACK,OAAO,CAAC,CAAC,EAAE;UACzB,OAAO;YACNC,IAAI,EAAE,KAAK;YACXC,KAAK,EAAEP,OAAO,CAACI,IAAI,CAAC;UACrB,CAAC;QACF;QACA,OAAO;UACNE,IAAI,EAAE;QACP,CAAC;MACC;IACJ,CAAC;EACF,CAAC;AAEH", "ignoreList": []}