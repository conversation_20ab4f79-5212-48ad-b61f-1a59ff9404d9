{"version": 3, "file": "AsYouType.js", "names": ["<PERSON><PERSON><PERSON>", "PhoneNumber", "AsYouTypeState", "AsYouTypeFormatter", "DIGIT_PLACEHOLDER", "AsYouType<PERSON><PERSON><PERSON>", "extractFormattedDigitsAndPlus", "getCountryByCallingCode", "getCountryByNationalNumber", "isObject", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "AsYouType", "optionsOrDefaultCountry", "metadata", "_classCallCheck", "_this$getCountryAndCa", "getCountryAndCallingCode", "_this$getCountryAndCa2", "_slicedToArray", "defaultCountry", "defaultCallingCode", "reset", "_createClass", "key", "value", "hasCountry", "undefined", "isNonGeographicCallingCode", "input", "text", "_this$parser$input", "parser", "state", "digits", "justLeadingPlus", "formattedOutput", "determineTheCountryIfNeeded", "nationalSignificantNumber", "formatter", "narrowDownMatchingFormats", "formattedNationalNumber", "hasSelectedNumberingPlan", "format", "reExtractNationalSignificantNumber", "nationalDigits", "getNationalDigits", "getFullNumber", "getNonFormattedNumber", "_this", "onCountryChange", "country", "onCallingCodeChange", "callingCode", "selectNumberingPlan", "numberingPlan", "onNationalSignificantNumberChange", "isInternational", "international", "getCallingCode", "getCountryCallingCode", "getCountry", "_getCountry", "isCountryCallingCodeAmbiguous", "determineTheCountry", "_this2", "prefix", "getInternationalPrefixBeforeCountryCallingCode", "spacing", "concat", "getDigitsWithoutInternationalPrefix", "getNonFormattedNationalNumberWithPrefix", "_this$state", "complexPrefixBeforeNationalSignificantNumber", "nationalPrefix", "number", "nationalSignificantNumberMatchesInput", "getNonFormattedTemplate", "replace", "countryCodes", "getCountryCodesForCallingCode", "length", "setCountry", "nationalNumber", "getNumberValue", "_this$state2", "callingCode_", "countryCallingCode", "getNumber", "_this$state3", "carrierCode", "ambiguousCountries", "exactCountry", "countries", "phoneNumber", "isPossible", "<PERSON><PERSON><PERSON><PERSON>", "getNationalNumber", "getChars", "getTemplate", "default"], "sources": ["../source/AsYouType.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\nimport PhoneNumber from './PhoneNumber.js'\r\nimport AsYouTypeState from './AsYouTypeState.js'\r\nimport AsYouTypeFormatter, { DIGIT_PLACEHOLDER } from './AsYouTypeFormatter.js'\r\nimport AsYouTypeParser, { extractFormattedDigitsAndPlus } from './AsYouTypeParser.js'\r\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js'\r\nimport getCountryByNationalNumber from './helpers/getCountryByNationalNumber.js'\r\nimport isObject from './helpers/isObject.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nexport default class AsYouType {\r\n\t/**\r\n\t * @param {(string|object)?} [optionsOrDefaultCountry] - The default country used for parsing non-international phone numbers. Can also be an `options` object.\r\n\t * @param {Object} metadata\r\n\t */\r\n\tconstructor(optionsOrDefaultCountry, metadata) {\r\n\t\tthis.metadata = new Metadata(metadata)\r\n\t\tconst [defaultCountry, defaultCallingCode] = this.getCountryAndCallingCode(optionsOrDefaultCountry)\r\n\t\t// `this.defaultCountry` and `this.defaultCallingCode` aren't required to be in sync.\r\n\t\t// For example, `this.defaultCountry` could be `\"AR\"` and `this.defaultCallingCode` could be `undefined`.\r\n\t\t// So `this.defaultCountry` and `this.defaultCallingCode` are totally independent.\r\n\t\tthis.defaultCountry = defaultCountry\r\n\t\tthis.defaultCallingCode = defaultCallingCode\r\n\t\tthis.reset()\r\n\t}\r\n\r\n\tgetCountryAndCallingCode(optionsOrDefaultCountry) {\r\n\t\t// Set `defaultCountry` and `defaultCallingCode` options.\r\n\t\tlet defaultCountry\r\n\t\tlet defaultCallingCode\r\n\t\t// Turns out `null` also has type \"object\". Weird.\r\n\t\tif (optionsOrDefaultCountry) {\r\n\t\t\tif (isObject(optionsOrDefaultCountry)) {\r\n\t\t\t\tdefaultCountry = optionsOrDefaultCountry.defaultCountry\r\n\t\t\t\tdefaultCallingCode = optionsOrDefaultCountry.defaultCallingCode\r\n\t\t\t} else {\r\n\t\t\t\tdefaultCountry = optionsOrDefaultCountry\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (defaultCountry && !this.metadata.hasCountry(defaultCountry)) {\r\n\t\t\tdefaultCountry = undefined\r\n\t\t}\r\n\t\tif (defaultCallingCode) {\r\n\t\t\t/* istanbul ignore if */\r\n\t\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\t\tif (this.metadata.isNonGeographicCallingCode(defaultCallingCode)) {\r\n\t\t\t\t\tdefaultCountry = '001'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn [defaultCountry, defaultCallingCode]\r\n\t}\r\n\r\n\t/**\r\n\t * Inputs \"next\" phone number characters.\r\n\t * @param  {string} text\r\n\t * @return {string} Formatted phone number characters that have been input so far.\r\n\t */\r\n\tinput(text) {\r\n\t\tconst {\r\n\t\t\tdigits,\r\n\t\t\tjustLeadingPlus\r\n\t\t} = this.parser.input(text, this.state)\r\n\t\tif (justLeadingPlus) {\r\n\t\t\tthis.formattedOutput = '+'\r\n\t\t} else if (digits) {\r\n\t\t\tthis.determineTheCountryIfNeeded()\r\n\t\t\t// Match the available formats by the currently available leading digits.\r\n\t\t\tif (this.state.nationalSignificantNumber) {\r\n\t\t\t\tthis.formatter.narrowDownMatchingFormats(this.state)\r\n\t\t\t}\r\n\t\t\tlet formattedNationalNumber\r\n\t\t\tif (this.metadata.hasSelectedNumberingPlan()) {\r\n\t\t\t\tformattedNationalNumber = this.formatter.format(digits, this.state)\r\n\t\t\t}\r\n\t\t\tif (formattedNationalNumber === undefined) {\r\n\t\t\t\t// See if another national (significant) number could be re-extracted.\r\n\t\t\t\tif (this.parser.reExtractNationalSignificantNumber(this.state)) {\r\n\t\t\t\t\tthis.determineTheCountryIfNeeded()\r\n\t\t\t\t\t// If it could, then re-try formatting the new national (significant) number.\r\n\t\t\t\t\tconst nationalDigits = this.state.getNationalDigits()\r\n\t\t\t\t\tif (nationalDigits) {\r\n\t\t\t\t\t\tformattedNationalNumber = this.formatter.format(nationalDigits, this.state)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.formattedOutput = formattedNationalNumber\r\n\t\t\t\t? this.getFullNumber(formattedNationalNumber)\r\n\t\t\t\t: this.getNonFormattedNumber()\r\n\t\t}\r\n\t\treturn this.formattedOutput\r\n\t}\r\n\r\n\treset() {\r\n\t\tthis.state = new AsYouTypeState({\r\n\t\t\tonCountryChange: (country) => {\r\n\t\t\t\t// Before version `1.6.0`, the official `AsYouType` formatter API\r\n\t\t\t\t// included the `.country` property of an `AsYouType` instance.\r\n\t\t\t\t// Since that property (along with the others) have been moved to\r\n\t\t\t\t// `this.state`, `this.country` property is emulated for compatibility\r\n\t\t\t\t// with the old versions.\r\n\t\t\t\tthis.country = country\r\n\t\t\t},\r\n\t\t\tonCallingCodeChange: (callingCode, country) => {\r\n\t\t\t\tthis.metadata.selectNumberingPlan(country, callingCode)\r\n\t\t\t\tthis.formatter.reset(this.metadata.numberingPlan, this.state)\r\n\t\t\t\tthis.parser.reset(this.metadata.numberingPlan)\r\n\t\t\t}\r\n\t\t})\r\n\t\tthis.formatter = new AsYouTypeFormatter({\r\n\t\t\tstate: this.state,\r\n\t\t\tmetadata: this.metadata\r\n\t\t})\r\n\t\tthis.parser = new AsYouTypeParser({\r\n\t\t\tdefaultCountry: this.defaultCountry,\r\n\t\t\tdefaultCallingCode: this.defaultCallingCode,\r\n\t\t\tmetadata: this.metadata,\r\n\t\t\tstate: this.state,\r\n\t\t\tonNationalSignificantNumberChange: () => {\r\n\t\t\t\tthis.determineTheCountryIfNeeded()\r\n\t\t\t\tthis.formatter.reset(this.metadata.numberingPlan, this.state)\r\n\t\t\t}\r\n\t\t})\r\n\t\tthis.state.reset({\r\n\t\t\tcountry: this.defaultCountry,\r\n\t\t\tcallingCode: this.defaultCallingCode\r\n\t\t})\r\n\t\tthis.formattedOutput = ''\r\n\t\treturn this\r\n\t}\r\n\r\n\t/**\r\n\t * Returns `true` if the phone number is being input in international format.\r\n\t * In other words, returns `true` if and only if the parsed phone number starts with a `\"+\"`.\r\n\t * @return {boolean}\r\n\t */\r\n\tisInternational() {\r\n\t\treturn this.state.international\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the \"calling code\" part of the phone number when it's being input\r\n\t * in an international format.\r\n\t * If no valid calling code has been entered so far, returns `undefined`.\r\n\t * @return {string} [callingCode]\r\n\t */\r\n\tgetCallingCode() {\r\n\t\t // If the number is being input in national format and some \"default calling code\"\r\n\t\t // has been passed to `AsYouType` constructor, then `this.state.callingCode`\r\n\t\t // is equal to that \"default calling code\".\r\n\t\t //\r\n\t\t // If the number is being input in national format and no \"default calling code\"\r\n\t\t // has been passed to `AsYouType` constructor, then returns `undefined`,\r\n\t\t // even if a \"default country\" has been passed to `AsYouType` constructor.\r\n\t\t //\r\n\t\tif (this.isInternational()) {\r\n\t\t\treturn this.state.callingCode\r\n\t\t}\r\n\t}\r\n\r\n\t// A legacy alias.\r\n\tgetCountryCallingCode() {\r\n\t\treturn this.getCallingCode()\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a two-letter country code of the phone number.\r\n\t * Returns `undefined` for \"non-geographic\" phone numbering plans.\r\n\t * Returns `undefined` if no phone number has been input yet.\r\n\t * @return {string} [country]\r\n\t */\r\n\tgetCountry() {\r\n\t\tconst { digits } = this.state\r\n\t\t// Return `undefined` if no digits have been input yet.\r\n\t\tif (digits) {\r\n\t\t\treturn this._getCountry()\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a two-letter country code of the phone number.\r\n\t * Returns `undefined` for \"non-geographic\" phone numbering plans.\r\n\t * @return {string} [country]\r\n\t */\r\n\t_getCountry() {\r\n\t\tconst { country } = this.state\r\n\t\t/* istanbul ignore if */\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\t// `AsYouType.getCountry()` returns `undefined`\r\n\t\t\t// for \"non-geographic\" phone numbering plans.\r\n\t\t\tif (country === '001') {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn country\r\n\t}\r\n\r\n\tdetermineTheCountryIfNeeded() {\r\n\t\t// Suppose a user enters a phone number in international format,\r\n\t\t// and there're several countries corresponding to that country calling code,\r\n\t\t// and a country has been derived from the number, and then\r\n\t\t// a user enters one more digit and the number is no longer\r\n\t\t// valid for the derived country, so the country should be re-derived\r\n\t\t// on every new digit in those cases.\r\n\t\t//\r\n\t\t// If the phone number is being input in national format,\r\n\t\t// then it could be a case when `defaultCountry` wasn't specified\r\n\t\t// when creating `AsYouType` instance, and just `defaultCallingCode` was specified,\r\n\t\t// and that \"calling code\" could correspond to a \"non-geographic entity\",\r\n\t\t// or there could be several countries corresponding to that country calling code.\r\n\t\t// In those cases, `this.country` is `undefined` and should be derived\r\n\t\t// from the number. Again, if country calling code is ambiguous, then\r\n\t\t// `this.country` should be re-derived with each new digit.\r\n\t\t//\r\n\t\tif (!this.state.country || this.isCountryCallingCodeAmbiguous()) {\r\n\t\t\tthis.determineTheCountry()\r\n\t\t}\r\n\t}\r\n\r\n\t// Prepends `+CountryCode ` in case of an international phone number\r\n\tgetFullNumber(formattedNationalNumber) {\r\n\t\tif (this.isInternational()) {\r\n\t\t\tconst prefix = (text) => this.formatter.getInternationalPrefixBeforeCountryCallingCode(this.state, {\r\n\t\t\t\tspacing: text ? true : false\r\n\t\t\t}) + text\r\n\t\t\tconst { callingCode } = this.state\r\n\t\t\tif (!callingCode) {\r\n\t\t\t\treturn prefix(`${this.state.getDigitsWithoutInternationalPrefix()}`)\r\n\t\t\t}\r\n\t\t\tif (!formattedNationalNumber) {\r\n\t\t\t\treturn prefix(callingCode)\r\n\t\t\t}\r\n\t\t\treturn prefix(`${callingCode} ${formattedNationalNumber}`)\r\n\t\t}\r\n\t\treturn formattedNationalNumber\r\n\t}\r\n\r\n\tgetNonFormattedNationalNumberWithPrefix() {\r\n\t\tconst {\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tcomplexPrefixBeforeNationalSignificantNumber,\r\n\t\t\tnationalPrefix\r\n\t\t} = this.state\r\n\t\tlet number = nationalSignificantNumber\r\n\t\tconst prefix = complexPrefixBeforeNationalSignificantNumber || nationalPrefix\r\n\t\tif (prefix) {\r\n\t\t\tnumber = prefix + number\r\n\t\t}\r\n\t\treturn number\r\n\t}\r\n\r\n\tgetNonFormattedNumber() {\r\n\t\tconst { nationalSignificantNumberMatchesInput } = this.state\r\n\t\treturn this.getFullNumber(\r\n\t\t\tnationalSignificantNumberMatchesInput\r\n\t\t\t\t? this.getNonFormattedNationalNumberWithPrefix()\r\n\t\t\t\t: this.state.getNationalDigits()\r\n\t\t)\r\n\t}\r\n\r\n\tgetNonFormattedTemplate() {\r\n\t\tconst number = this.getNonFormattedNumber()\r\n\t\tif (number) {\r\n\t\t\treturn number.replace(/[\\+\\d]/g, DIGIT_PLACEHOLDER)\r\n\t\t}\r\n\t}\r\n\r\n\tisCountryCallingCodeAmbiguous() {\r\n\t\tconst { callingCode } = this.state\r\n\t\tconst countryCodes = this.metadata.getCountryCodesForCallingCode(callingCode)\r\n\t\treturn countryCodes && countryCodes.length > 1\r\n\t}\r\n\r\n\t// Determines the country of the phone number\r\n\t// entered so far based on the country phone code\r\n\t// and the national phone number.\r\n\tdetermineTheCountry() {\r\n\t\tthis.state.setCountry(getCountryByCallingCode(\r\n\t\t\tthis.isInternational() ? this.state.callingCode : this.defaultCallingCode,\r\n\t\t\t{\r\n\t\t\t\tnationalNumber: this.state.nationalSignificantNumber,\r\n\t\t\t\tdefaultCountry: this.defaultCountry,\r\n\t\t\t\tmetadata: this.metadata\r\n\t\t\t}\r\n\t\t))\r\n\t}\r\n\r\n\t/**\r\n\t * Returns a E.164 phone number value for the user's input.\r\n\t *\r\n\t * For example, for country `\"US\"` and input `\"(*************\"`\r\n\t * it will return `\"+12223334444\"`.\r\n\t *\r\n\t * For international phone number input, it will also auto-correct\r\n\t * some minor errors such as using a national prefix when writing\r\n\t * an international phone number. For example, if the user inputs\r\n\t * `\"+44 0 7400 000000\"` then it will return an auto-corrected\r\n\t * `\"+447400000000\"` phone number value.\r\n\t *\r\n\t * Will return `undefined` if no digits have been input,\r\n\t * or when inputting a phone number in national format and no\r\n\t * default country or default \"country calling code\" have been set.\r\n\t *\r\n\t * @return {string} [value]\r\n\t */\r\n\tgetNumberValue() {\r\n\t\tconst {\r\n\t\t\tdigits,\r\n\t\t\tcallingCode,\r\n\t\t\tcountry,\r\n\t\t\tnationalSignificantNumber\r\n\t\t} = this.state\r\n\r\n\t \t// Will return `undefined` if no digits have been input.\r\n\t\tif (!digits) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tif (this.isInternational()) {\r\n\t\t\tif (callingCode) {\r\n\t\t\t\treturn '+' + callingCode + nationalSignificantNumber\r\n\t\t\t} else {\r\n\t\t\t\treturn '+' + digits\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (country || callingCode) {\r\n\t\t\t\tconst callingCode_ = country ? this.metadata.countryCallingCode() : callingCode\r\n\t\t\t\treturn '+' + callingCode_ + nationalSignificantNumber\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Returns an instance of `PhoneNumber` class.\r\n\t * Will return `undefined` if no national (significant) number\r\n\t * digits have been entered so far, or if no `defaultCountry` has been\r\n\t * set and the user enters a phone number not in international format.\r\n\t */\r\n\tgetNumber() {\r\n\t\tconst {\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tcarrierCode,\r\n\t\t\tcallingCode\r\n\t\t} = this.state\r\n\r\n\t\t// `this._getCountry()` is basically same as `this.state.country`\r\n\t\t// with the only change that it return `undefined` in case of a\r\n\t\t// \"non-geographic\" numbering plan instead of `\"001\"` \"internal use\" value.\r\n\t\tlet country = this._getCountry()\r\n\r\n\t\tif (!nationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// `state.country` and `state.callingCode` aren't required to be in sync.\r\n\t\t// For example, `country` could be `\"AR\"` and `callingCode` could be `undefined`.\r\n\t\t// So `country` and `callingCode` are totally independent.\r\n\r\n\t\tif (!country && !callingCode) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// By default, if `defaultCountry` parameter was passed when\r\n\t\t// creating `AsYouType` instance, `state.country` is gonna be\r\n\t\t// that `defaultCountry`, which doesn't entirely conform with\r\n\t\t// `parsePhoneNumber()`'s behavior where it attempts to determine\r\n\t\t// the country more precisely in cases when multiple countries\r\n\t\t// could correspond to the same `countryCallingCode`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/103#note_1417192969\r\n\t\t//\r\n\t\t// Because `AsYouType.getNumber()` method is supposed to be a 1:1\r\n\t\t// equivalent for `parsePhoneNumber(AsYouType.getNumberValue())`,\r\n\t\t// then it should also behave accordingly in cases of `country` ambiguity.\r\n\t\t// That's how users of this library would expect it to behave anyway.\r\n\t\t//\r\n\t\tif (country) {\r\n\t\t\tif (country === this.defaultCountry) {\r\n\t\t\t\t// `state.country` and `state.callingCode` aren't required to be in sync.\r\n\t\t\t\t// For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\r\n\t\t\t\t// So `state.country` and `state.callingCode` are totally independent.\r\n\t\t\t\tconst metadata = new Metadata(this.metadata.metadata)\r\n\t\t\t\tmetadata.selectNumberingPlan(country)\r\n\t\t\t\tconst callingCode = metadata.numberingPlan.callingCode()\r\n\t\t\t\tconst ambiguousCountries = this.metadata.getCountryCodesForCallingCode(callingCode)\r\n\t\t\t\tif (ambiguousCountries.length > 1) {\r\n\t\t\t\t\tconst exactCountry = getCountryByNationalNumber(nationalSignificantNumber, {\r\n\t\t\t\t\t\tcountries: ambiguousCountries,\r\n\t\t\t\t\t\tdefaultCountry: this.defaultCountry,\r\n\t\t\t\t\t\tmetadata: this.metadata.metadata\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (exactCountry) {\r\n\t\t\t\t\t\tcountry = exactCountry\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst phoneNumber = new PhoneNumber(\r\n\t\t\tcountry || callingCode,\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (carrierCode) {\r\n\t\t\tphoneNumber.carrierCode = carrierCode\r\n\t\t}\r\n\t\t// Phone number extensions are not supported by \"As You Type\" formatter.\r\n\t\treturn phoneNumber\r\n\t}\r\n\r\n\t/**\r\n\t * Returns `true` if the phone number is \"possible\".\r\n\t * Is just a shortcut for `PhoneNumber.isPossible()`.\r\n\t * @return {boolean}\r\n\t */\r\n\tisPossible() {\r\n\t\tconst phoneNumber = this.getNumber()\r\n\t\tif (!phoneNumber) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\treturn phoneNumber.isPossible()\r\n\t}\r\n\r\n\t/**\r\n\t * Returns `true` if the phone number is \"valid\".\r\n\t * Is just a shortcut for `PhoneNumber.isValid()`.\r\n\t * @return {boolean}\r\n\t */\r\n\tisValid() {\r\n\t\tconst phoneNumber = this.getNumber()\r\n\t\tif (!phoneNumber) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\treturn phoneNumber.isValid()\r\n\t}\r\n\r\n\t/**\r\n\t * @deprecated\r\n\t * This method is used in `react-phone-number-input/source/input-control.js`\r\n\t * in versions before `3.0.16`.\r\n\t */\r\n\tgetNationalNumber() {\r\n\t\treturn this.state.nationalSignificantNumber\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the phone number characters entered by the user.\r\n\t * @return {string}\r\n\t */\r\n\tgetChars() {\r\n\t\treturn (this.state.international ? '+' : '') + this.state.digits\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the template for the formatted phone number.\r\n\t * @return {string}\r\n\t */\r\n\tgetTemplate() {\r\n\t\treturn this.formatter.getTemplate(this.state) || this.getNonFormattedTemplate() || ''\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,kBAAkB,IAAIC,iBAAiB,QAAQ,yBAAyB;AAC/E,OAAOC,eAAe,IAAIC,6BAA6B,QAAQ,sBAAsB;AACrF,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,0BAA0B,MAAM,yCAAyC;AAChF,OAAOC,QAAQ,MAAM,uBAAuB;AAE5C,IAAMC,+BAA+B,GAAG,KAAK;AAAA,IAExBC,SAAS;EAC7B;AACD;AACA;AACA;EACC,SAAAA,UAAYC,uBAAuB,EAAEC,QAAQ,EAAE;IAAAC,eAAA,OAAAH,SAAA;IAC9C,IAAI,CAACE,QAAQ,GAAG,IAAIb,QAAQ,CAACa,QAAQ,CAAC;IACtC,IAAAE,qBAAA,GAA6C,IAAI,CAACC,wBAAwB,CAACJ,uBAAuB,CAAC;MAAAK,sBAAA,GAAAC,cAAA,CAAAH,qBAAA;MAA5FI,cAAc,GAAAF,sBAAA;MAAEG,kBAAkB,GAAAH,sBAAA;IACzC;IACA;IACA;IACA,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,KAAK,CAAC,CAAC;EACb;EAAC,OAAAC,YAAA,CAAAX,SAAA;IAAAY,GAAA;IAAAC,KAAA,EAED,SAAAR,wBAAwBA,CAACJ,uBAAuB,EAAE;MACjD;MACA,IAAIO,cAAc;MAClB,IAAIC,kBAAkB;MACtB;MACA,IAAIR,uBAAuB,EAAE;QAC5B,IAAIH,QAAQ,CAACG,uBAAuB,CAAC,EAAE;UACtCO,cAAc,GAAGP,uBAAuB,CAACO,cAAc;UACvDC,kBAAkB,GAAGR,uBAAuB,CAACQ,kBAAkB;QAChE,CAAC,MAAM;UACND,cAAc,GAAGP,uBAAuB;QACzC;MACD;MACA,IAAIO,cAAc,IAAI,CAAC,IAAI,CAACN,QAAQ,CAACY,UAAU,CAACN,cAAc,CAAC,EAAE;QAChEA,cAAc,GAAGO,SAAS;MAC3B;MACA,IAAIN,kBAAkB,EAAE;QACvB;QACA,IAAIV,+BAA+B,EAAE;UACpC,IAAI,IAAI,CAACG,QAAQ,CAACc,0BAA0B,CAACP,kBAAkB,CAAC,EAAE;YACjED,cAAc,GAAG,KAAK;UACvB;QACD;MACD;MACA,OAAO,CAACA,cAAc,EAAEC,kBAAkB,CAAC;IAC5C;;IAEA;AACD;AACA;AACA;AACA;EAJC;IAAAG,GAAA;IAAAC,KAAA,EAKA,SAAAI,KAAKA,CAACC,IAAI,EAAE;MACX,IAAAC,kBAAA,GAGI,IAAI,CAACC,MAAM,CAACH,KAAK,CAACC,IAAI,EAAE,IAAI,CAACG,KAAK,CAAC;QAFtCC,MAAM,GAAAH,kBAAA,CAANG,MAAM;QACNC,eAAe,GAAAJ,kBAAA,CAAfI,eAAe;MAEhB,IAAIA,eAAe,EAAE;QACpB,IAAI,CAACC,eAAe,GAAG,GAAG;MAC3B,CAAC,MAAM,IAAIF,MAAM,EAAE;QAClB,IAAI,CAACG,2BAA2B,CAAC,CAAC;QAClC;QACA,IAAI,IAAI,CAACJ,KAAK,CAACK,yBAAyB,EAAE;UACzC,IAAI,CAACC,SAAS,CAACC,yBAAyB,CAAC,IAAI,CAACP,KAAK,CAAC;QACrD;QACA,IAAIQ,uBAAuB;QAC3B,IAAI,IAAI,CAAC3B,QAAQ,CAAC4B,wBAAwB,CAAC,CAAC,EAAE;UAC7CD,uBAAuB,GAAG,IAAI,CAACF,SAAS,CAACI,MAAM,CAACT,MAAM,EAAE,IAAI,CAACD,KAAK,CAAC;QACpE;QACA,IAAIQ,uBAAuB,KAAKd,SAAS,EAAE;UAC1C;UACA,IAAI,IAAI,CAACK,MAAM,CAACY,kCAAkC,CAAC,IAAI,CAACX,KAAK,CAAC,EAAE;YAC/D,IAAI,CAACI,2BAA2B,CAAC,CAAC;YAClC;YACA,IAAMQ,cAAc,GAAG,IAAI,CAACZ,KAAK,CAACa,iBAAiB,CAAC,CAAC;YACrD,IAAID,cAAc,EAAE;cACnBJ,uBAAuB,GAAG,IAAI,CAACF,SAAS,CAACI,MAAM,CAACE,cAAc,EAAE,IAAI,CAACZ,KAAK,CAAC;YAC5E;UACD;QACD;QACA,IAAI,CAACG,eAAe,GAAGK,uBAAuB,GAC3C,IAAI,CAACM,aAAa,CAACN,uBAAuB,CAAC,GAC3C,IAAI,CAACO,qBAAqB,CAAC,CAAC;MAChC;MACA,OAAO,IAAI,CAACZ,eAAe;IAC5B;EAAC;IAAAZ,GAAA;IAAAC,KAAA,EAED,SAAAH,KAAKA,CAAA,EAAG;MAAA,IAAA2B,KAAA;MACP,IAAI,CAAChB,KAAK,GAAG,IAAI9B,cAAc,CAAC;QAC/B+C,eAAe,EAAE,SAAjBA,eAAeA,CAAGC,OAAO,EAAK;UAC7B;UACA;UACA;UACA;UACA;UACAF,KAAI,CAACE,OAAO,GAAGA,OAAO;QACvB,CAAC;QACDC,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAGC,WAAW,EAAEF,OAAO,EAAK;UAC9CF,KAAI,CAACnC,QAAQ,CAACwC,mBAAmB,CAACH,OAAO,EAAEE,WAAW,CAAC;UACvDJ,KAAI,CAACV,SAAS,CAACjB,KAAK,CAAC2B,KAAI,CAACnC,QAAQ,CAACyC,aAAa,EAAEN,KAAI,CAAChB,KAAK,CAAC;UAC7DgB,KAAI,CAACjB,MAAM,CAACV,KAAK,CAAC2B,KAAI,CAACnC,QAAQ,CAACyC,aAAa,CAAC;QAC/C;MACD,CAAC,CAAC;MACF,IAAI,CAAChB,SAAS,GAAG,IAAInC,kBAAkB,CAAC;QACvC6B,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBnB,QAAQ,EAAE,IAAI,CAACA;MAChB,CAAC,CAAC;MACF,IAAI,CAACkB,MAAM,GAAG,IAAI1B,eAAe,CAAC;QACjCc,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCC,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;QAC3CP,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBmB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBuB,iCAAiC,EAAE,SAAnCA,iCAAiCA,CAAA,EAAQ;UACxCP,KAAI,CAACZ,2BAA2B,CAAC,CAAC;UAClCY,KAAI,CAACV,SAAS,CAACjB,KAAK,CAAC2B,KAAI,CAACnC,QAAQ,CAACyC,aAAa,EAAEN,KAAI,CAAChB,KAAK,CAAC;QAC9D;MACD,CAAC,CAAC;MACF,IAAI,CAACA,KAAK,CAACX,KAAK,CAAC;QAChB6B,OAAO,EAAE,IAAI,CAAC/B,cAAc;QAC5BiC,WAAW,EAAE,IAAI,CAAChC;MACnB,CAAC,CAAC;MACF,IAAI,CAACe,eAAe,GAAG,EAAE;MACzB,OAAO,IAAI;IACZ;;IAEA;AACD;AACA;AACA;AACA;EAJC;IAAAZ,GAAA;IAAAC,KAAA,EAKA,SAAAgC,eAAeA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACxB,KAAK,CAACyB,aAAa;IAChC;;IAEA;AACD;AACA;AACA;AACA;AACA;EALC;IAAAlC,GAAA;IAAAC,KAAA,EAMA,SAAAkC,cAAcA,CAAA,EAAG;MACf;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACD,IAAI,IAAI,CAACF,eAAe,CAAC,CAAC,EAAE;QAC3B,OAAO,IAAI,CAACxB,KAAK,CAACoB,WAAW;MAC9B;IACD;;IAEA;EAAA;IAAA7B,GAAA;IAAAC,KAAA,EACA,SAAAmC,qBAAqBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACD,cAAc,CAAC,CAAC;IAC7B;;IAEA;AACD;AACA;AACA;AACA;AACA;EALC;IAAAnC,GAAA;IAAAC,KAAA,EAMA,SAAAoC,UAAUA,CAAA,EAAG;MACZ,IAAQ3B,MAAM,GAAK,IAAI,CAACD,KAAK,CAArBC,MAAM;MACd;MACA,IAAIA,MAAM,EAAE;QACX,OAAO,IAAI,CAAC4B,WAAW,CAAC,CAAC;MAC1B;IACD;;IAEA;AACD;AACA;AACA;AACA;EAJC;IAAAtC,GAAA;IAAAC,KAAA,EAKA,SAAAqC,WAAWA,CAAA,EAAG;MACb,IAAQX,OAAO,GAAK,IAAI,CAAClB,KAAK,CAAtBkB,OAAO;MACf;MACA,IAAIxC,+BAA+B,EAAE;QACpC;QACA;QACA,IAAIwC,OAAO,KAAK,KAAK,EAAE;UACtB;QACD;MACD;MACA,OAAOA,OAAO;IACf;EAAC;IAAA3B,GAAA;IAAAC,KAAA,EAED,SAAAY,2BAA2BA,CAAA,EAAG;MAC7B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACJ,KAAK,CAACkB,OAAO,IAAI,IAAI,CAACY,6BAA6B,CAAC,CAAC,EAAE;QAChE,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC3B;IACD;;IAEA;EAAA;IAAAxC,GAAA;IAAAC,KAAA,EACA,SAAAsB,aAAaA,CAACN,uBAAuB,EAAE;MAAA,IAAAwB,MAAA;MACtC,IAAI,IAAI,CAACR,eAAe,CAAC,CAAC,EAAE;QAC3B,IAAMS,MAAM,GAAG,SAATA,MAAMA,CAAIpC,IAAI;UAAA,OAAKmC,MAAI,CAAC1B,SAAS,CAAC4B,8CAA8C,CAACF,MAAI,CAAChC,KAAK,EAAE;YAClGmC,OAAO,EAAEtC,IAAI,GAAG,IAAI,GAAG;UACxB,CAAC,CAAC,GAAGA,IAAI;QAAA;QACT,IAAQuB,WAAW,GAAK,IAAI,CAACpB,KAAK,CAA1BoB,WAAW;QACnB,IAAI,CAACA,WAAW,EAAE;UACjB,OAAOa,MAAM,IAAAG,MAAA,CAAI,IAAI,CAACpC,KAAK,CAACqC,mCAAmC,CAAC,CAAC,CAAE,CAAC;QACrE;QACA,IAAI,CAAC7B,uBAAuB,EAAE;UAC7B,OAAOyB,MAAM,CAACb,WAAW,CAAC;QAC3B;QACA,OAAOa,MAAM,IAAAG,MAAA,CAAIhB,WAAW,OAAAgB,MAAA,CAAI5B,uBAAuB,CAAE,CAAC;MAC3D;MACA,OAAOA,uBAAuB;IAC/B;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAED,SAAA8C,uCAAuCA,CAAA,EAAG;MACzC,IAAAC,WAAA,GAII,IAAI,CAACvC,KAAK;QAHbK,yBAAyB,GAAAkC,WAAA,CAAzBlC,yBAAyB;QACzBmC,4CAA4C,GAAAD,WAAA,CAA5CC,4CAA4C;QAC5CC,cAAc,GAAAF,WAAA,CAAdE,cAAc;MAEf,IAAIC,MAAM,GAAGrC,yBAAyB;MACtC,IAAM4B,MAAM,GAAGO,4CAA4C,IAAIC,cAAc;MAC7E,IAAIR,MAAM,EAAE;QACXS,MAAM,GAAGT,MAAM,GAAGS,MAAM;MACzB;MACA,OAAOA,MAAM;IACd;EAAC;IAAAnD,GAAA;IAAAC,KAAA,EAED,SAAAuB,qBAAqBA,CAAA,EAAG;MACvB,IAAQ4B,qCAAqC,GAAK,IAAI,CAAC3C,KAAK,CAApD2C,qCAAqC;MAC7C,OAAO,IAAI,CAAC7B,aAAa,CACxB6B,qCAAqC,GAClC,IAAI,CAACL,uCAAuC,CAAC,CAAC,GAC9C,IAAI,CAACtC,KAAK,CAACa,iBAAiB,CAAC,CACjC,CAAC;IACF;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAED,SAAAoD,uBAAuBA,CAAA,EAAG;MACzB,IAAMF,MAAM,GAAG,IAAI,CAAC3B,qBAAqB,CAAC,CAAC;MAC3C,IAAI2B,MAAM,EAAE;QACX,OAAOA,MAAM,CAACG,OAAO,CAAC,SAAS,EAAEzE,iBAAiB,CAAC;MACpD;IACD;EAAC;IAAAmB,GAAA;IAAAC,KAAA,EAED,SAAAsC,6BAA6BA,CAAA,EAAG;MAC/B,IAAQV,WAAW,GAAK,IAAI,CAACpB,KAAK,CAA1BoB,WAAW;MACnB,IAAM0B,YAAY,GAAG,IAAI,CAACjE,QAAQ,CAACkE,6BAA6B,CAAC3B,WAAW,CAAC;MAC7E,OAAO0B,YAAY,IAAIA,YAAY,CAACE,MAAM,GAAG,CAAC;IAC/C;;IAEA;IACA;IACA;EAAA;IAAAzD,GAAA;IAAAC,KAAA,EACA,SAAAuC,mBAAmBA,CAAA,EAAG;MACrB,IAAI,CAAC/B,KAAK,CAACiD,UAAU,CAAC1E,uBAAuB,CAC5C,IAAI,CAACiD,eAAe,CAAC,CAAC,GAAG,IAAI,CAACxB,KAAK,CAACoB,WAAW,GAAG,IAAI,CAAChC,kBAAkB,EACzE;QACC8D,cAAc,EAAE,IAAI,CAAClD,KAAK,CAACK,yBAAyB;QACpDlB,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCN,QAAQ,EAAE,IAAI,CAACA;MAChB,CACD,CAAC,CAAC;IACH;;IAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAjBC;IAAAU,GAAA;IAAAC,KAAA,EAkBA,SAAA2D,cAAcA,CAAA,EAAG;MAChB,IAAAC,YAAA,GAKI,IAAI,CAACpD,KAAK;QAJbC,MAAM,GAAAmD,YAAA,CAANnD,MAAM;QACNmB,WAAW,GAAAgC,YAAA,CAAXhC,WAAW;QACXF,OAAO,GAAAkC,YAAA,CAAPlC,OAAO;QACPb,yBAAyB,GAAA+C,YAAA,CAAzB/C,yBAAyB;;MAGzB;MACD,IAAI,CAACJ,MAAM,EAAE;QACZ;MACD;MAEA,IAAI,IAAI,CAACuB,eAAe,CAAC,CAAC,EAAE;QAC3B,IAAIJ,WAAW,EAAE;UAChB,OAAO,GAAG,GAAGA,WAAW,GAAGf,yBAAyB;QACrD,CAAC,MAAM;UACN,OAAO,GAAG,GAAGJ,MAAM;QACpB;MACD,CAAC,MAAM;QACN,IAAIiB,OAAO,IAAIE,WAAW,EAAE;UAC3B,IAAMiC,YAAY,GAAGnC,OAAO,GAAG,IAAI,CAACrC,QAAQ,CAACyE,kBAAkB,CAAC,CAAC,GAAGlC,WAAW;UAC/E,OAAO,GAAG,GAAGiC,YAAY,GAAGhD,yBAAyB;QACtD;MACD;IACD;;IAEA;AACD;AACA;AACA;AACA;AACA;EALC;IAAAd,GAAA;IAAAC,KAAA,EAMA,SAAA+D,SAASA,CAAA,EAAG;MACX,IAAAC,YAAA,GAII,IAAI,CAACxD,KAAK;QAHbK,yBAAyB,GAAAmD,YAAA,CAAzBnD,yBAAyB;QACzBoD,WAAW,GAAAD,YAAA,CAAXC,WAAW;QACXrC,WAAW,GAAAoC,YAAA,CAAXpC,WAAW;;MAGZ;MACA;MACA;MACA,IAAIF,OAAO,GAAG,IAAI,CAACW,WAAW,CAAC,CAAC;MAEhC,IAAI,CAACxB,yBAAyB,EAAE;QAC/B;MACD;;MAEA;MACA;MACA;;MAEA,IAAI,CAACa,OAAO,IAAI,CAACE,WAAW,EAAE;QAC7B;MACD;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIF,OAAO,EAAE;QACZ,IAAIA,OAAO,KAAK,IAAI,CAAC/B,cAAc,EAAE;UACpC;UACA;UACA;UACA,IAAMN,QAAQ,GAAG,IAAIb,QAAQ,CAAC,IAAI,CAACa,QAAQ,CAACA,QAAQ,CAAC;UACrDA,QAAQ,CAACwC,mBAAmB,CAACH,OAAO,CAAC;UACrC,IAAME,YAAW,GAAGvC,QAAQ,CAACyC,aAAa,CAACF,WAAW,CAAC,CAAC;UACxD,IAAMsC,kBAAkB,GAAG,IAAI,CAAC7E,QAAQ,CAACkE,6BAA6B,CAAC3B,YAAW,CAAC;UACnF,IAAIsC,kBAAkB,CAACV,MAAM,GAAG,CAAC,EAAE;YAClC,IAAMW,YAAY,GAAGnF,0BAA0B,CAAC6B,yBAAyB,EAAE;cAC1EuD,SAAS,EAAEF,kBAAkB;cAC7BvE,cAAc,EAAE,IAAI,CAACA,cAAc;cACnCN,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAACA;YACzB,CAAC,CAAC;YACF,IAAI8E,YAAY,EAAE;cACjBzC,OAAO,GAAGyC,YAAY;YACvB;UACD;QACD;MACD;MAEA,IAAME,WAAW,GAAG,IAAI5F,WAAW,CAClCiD,OAAO,IAAIE,WAAW,EACtBf,yBAAyB,EACzB,IAAI,CAACxB,QAAQ,CAACA,QACf,CAAC;MACD,IAAI4E,WAAW,EAAE;QAChBI,WAAW,CAACJ,WAAW,GAAGA,WAAW;MACtC;MACA;MACA,OAAOI,WAAW;IACnB;;IAEA;AACD;AACA;AACA;AACA;EAJC;IAAAtE,GAAA;IAAAC,KAAA,EAKA,SAAAsE,UAAUA,CAAA,EAAG;MACZ,IAAMD,WAAW,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;MACpC,IAAI,CAACM,WAAW,EAAE;QACjB,OAAO,KAAK;MACb;MACA,OAAOA,WAAW,CAACC,UAAU,CAAC,CAAC;IAChC;;IAEA;AACD;AACA;AACA;AACA;EAJC;IAAAvE,GAAA;IAAAC,KAAA,EAKA,SAAAuE,OAAOA,CAAA,EAAG;MACT,IAAMF,WAAW,GAAG,IAAI,CAACN,SAAS,CAAC,CAAC;MACpC,IAAI,CAACM,WAAW,EAAE;QACjB,OAAO,KAAK;MACb;MACA,OAAOA,WAAW,CAACE,OAAO,CAAC,CAAC;IAC7B;;IAEA;AACD;AACA;AACA;AACA;EAJC;IAAAxE,GAAA;IAAAC,KAAA,EAKA,SAAAwE,iBAAiBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAAChE,KAAK,CAACK,yBAAyB;IAC5C;;IAEA;AACD;AACA;AACA;EAHC;IAAAd,GAAA;IAAAC,KAAA,EAIA,SAAAyE,QAAQA,CAAA,EAAG;MACV,OAAO,CAAC,IAAI,CAACjE,KAAK,CAACyB,aAAa,GAAG,GAAG,GAAG,EAAE,IAAI,IAAI,CAACzB,KAAK,CAACC,MAAM;IACjE;;IAEA;AACD;AACA;AACA;EAHC;IAAAV,GAAA;IAAAC,KAAA,EAIA,SAAA0E,WAAWA,CAAA,EAAG;MACb,OAAO,IAAI,CAAC5D,SAAS,CAAC4D,WAAW,CAAC,IAAI,CAAClE,KAAK,CAAC,IAAI,IAAI,CAAC4C,uBAAuB,CAAC,CAAC,IAAI,EAAE;IACtF;EAAC;AAAA;AAAA,SAhcmBjE,SAAS,IAAAwF,OAAA", "ignoreList": []}