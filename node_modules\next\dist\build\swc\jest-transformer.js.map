{"version": 3, "sources": ["../../../src/build/swc/jest-transformer.ts"], "names": ["isSupportEsm", "vm", "getJestConfig", "jestConfig", "config", "isEsm", "isEsmProject", "filename", "test", "extensionsToTreatAsEsm", "some", "ext", "endsWith", "createTransformer", "inputOptions", "process", "src", "jestOptions", "swcTransformOpts", "getJestSWCOptions", "isServer", "testEnvironment", "includes", "jsConfig", "resolvedBaseUrl", "pagesDir", "serverComponents", "modularizeImports", "swcPlugins", "compilerOptions", "esm", "Boolean", "transformSync", "module", "exports"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;2DAEe;uBACe;yBACI;;;;;;AAsBlC,6FAA6F;AAC7F,mDAAmD;AACnD,MAAMA,eAAe,YAAYC,WAAE;AAEnC,SAASC,cACPC,UAAmD;IAEnD,OAAO,YAAYA,aAEfA,WAAWC,MAAM,GAEhBD;AACP;AAEA,SAASE,MACPC,YAAqB,EACrBC,QAAgB,EAChBJ,UAAgC;QAI9BA;IAFF,OACE,AAAC,UAAUK,IAAI,CAACD,aAAaD,kBAC7BH,qCAAAA,WAAWM,sBAAsB,qBAAjCN,mCAAmCO,IAAI,CAAC,CAACC,MACvCJ,SAASK,QAAQ,CAACD;AAGxB;AAEA,MAAME,oBAGF,CAACC,eAAkB,CAAA;QACrBC,SAAQC,GAAG,EAAET,QAAQ,EAAEU,WAAW;YAChC,MAAMd,aAAaD,cAAce;YAEjC,MAAMC,mBAAmBC,IAAAA,0BAAiB,EAAC;gBACzCC,UACEjB,WAAWkB,eAAe,KAAK,UAC/BlB,WAAWkB,eAAe,CAACC,QAAQ,CAAC;gBACtCf;gBACAgB,QAAQ,EAAET,gCAAAA,aAAcS,QAAQ;gBAChCC,eAAe,EAAEV,gCAAAA,aAAcU,eAAe;gBAC9CC,QAAQ,EAAEX,gCAAAA,aAAcW,QAAQ;gBAChCC,gBAAgB,EAAEZ,gCAAAA,aAAcY,gBAAgB;gBAChDC,iBAAiB,EAAEb,gCAAAA,aAAca,iBAAiB;gBAClDC,UAAU,EAAEd,gCAAAA,aAAcc,UAAU;gBACpCC,eAAe,EAAEf,gCAAAA,aAAce,eAAe;gBAC9CC,KACE9B,gBACAK,MAAM0B,QAAQjB,gCAAAA,aAAcR,YAAY,GAAGC,UAAUJ;YACzD;YAEA,OAAO6B,IAAAA,oBAAa,EAAChB,KAAK;gBAAE,GAAGE,gBAAgB;gBAAEX;YAAS;QAC5D;IACF,CAAA;AAEA0B,OAAOC,OAAO,GAAG;IAAErB;AAAkB"}