{"version": 3, "file": "IsISO8601.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISO8601.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,wEAAyD;AAG5C,QAAA,UAAU,GAAG,WAAW,CAAC;AAEtC;;;;GAIG;AACH,SAAgB,SAAS,CAAC,KAAc,EAAE,OAAsC;IAC9E,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,mBAAkB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACzE,CAAC;AAFD,8BAEC;AAED;;;;GAIG;AACH,SAAgB,SAAS,CACvB,OAAsC,EACtC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,kBAAU;QAChB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC1E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,gDAAgD,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAlBD,8BAkBC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIso8601Validator from 'validator/lib/isISO8601';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_ISO8601 = 'isIso8601';\n\n/**\n * Checks if the string is a valid ISO 8601 date.\n * If given value is not a string, then it returns false.\n * Use the option strict = true for additional checks for a valid date, e.g. invalidates dates like 2019-02-29.\n */\nexport function isISO8601(value: unknown, options?: ValidatorJS.IsISO8601Options): boolean {\n  return typeof value === 'string' && isIso8601Validator(value, options);\n}\n\n/**\n * Checks if the string is a valid ISO 8601 date.\n * If given value is not a string, then it returns false.\n * Use the option strict = true for additional checks for a valid date, e.g. invalidates dates like 2019-02-29.\n */\nexport function IsISO8601(\n  options?: ValidatorJS.IsISO8601Options,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISO8601,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isISO8601(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid ISO 8601 date string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}