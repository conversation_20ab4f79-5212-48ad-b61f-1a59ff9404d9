{"version": 3, "file": "isValid.js", "names": ["_metadata", "_interopRequireDefault", "require", "_matchesEntirely", "_getNumberType", "e", "__esModule", "isValidNumber", "input", "options", "metadata", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "country", "countryCallingCode", "hasTypes", "getNumberType", "undefined", "nationalNumber", "v2", "phone", "matchesEntirely", "nationalNumberPattern"], "sources": ["../source/isValid.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\nimport matchesEntirely from './helpers/matchesEntirely.js'\r\nimport getNumberType from './helpers/getNumberType.js'\r\n\r\n/**\r\n * Checks if a given phone number is valid.\r\n *\r\n * isValid(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * If the `number` is a string, it will be parsed to an object,\r\n * but only if it contains only valid phone number characters (including punctuation).\r\n * If the `number` is an object, it is used as is.\r\n *\r\n * The optional `defaultCountry` argument is the default country.\r\n * I.e. it does not restrict to just that country,\r\n * e.g. in those cases where several countries share\r\n * the same phone numbering rules (NANPA, Britain, etc).\r\n * For example, even though the number `07624 369230`\r\n * belongs to the Isle of Man (\"IM\" country code)\r\n * calling `isValidNumber('07624369230', 'GB', metadata)`\r\n * still returns `true` because the country is not restricted to `GB`,\r\n * it's just that `GB` is the default one for the phone numbering rules.\r\n * For restricting the country see `isValidNumberForRegion()`\r\n * though restricting a country might not be a good idea.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isValidNumber('+78005553535', metadata)\r\n * isValidNumber('8005553535', 'RU', metadata)\r\n * isValidNumber('88005553535', 'RU', metadata)\r\n * isValidNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\r\nexport default function isValidNumber(input, options, metadata)\r\n{\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tmetadata.selectNumberingPlan(input.country, input.countryCallingCode)\r\n\r\n\t// By default, countries only have type regexps when it's required for\r\n\t// distinguishing different countries having the same `countryCallingCode`.\r\n\tif (metadata.hasTypes()) {\r\n\t\treturn getNumberType(input, options, metadata.metadata) !== undefined\r\n\t}\r\n\r\n\t// If there are no type regexps for this country in metadata then use\r\n\t// `nationalNumberPattern` as a \"better than nothing\" replacement.\r\n\tconst nationalNumber = options.v2 ? input.nationalNumber : input.phone\r\n\treturn matchesEntirely(nationalNumber, metadata.nationalNumberPattern())\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,cAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAsD,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASE,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAC9D;EACC;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvBC,QAAQ,GAAG,IAAIC,oBAAQ,CAACD,QAAQ,CAAC;EAEjCA,QAAQ,CAACE,mBAAmB,CAACJ,KAAK,CAACK,OAAO,EAAEL,KAAK,CAACM,kBAAkB,CAAC;;EAErE;EACA;EACA,IAAIJ,QAAQ,CAACK,QAAQ,CAAC,CAAC,EAAE;IACxB,OAAO,IAAAC,yBAAa,EAACR,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAACA,QAAQ,CAAC,KAAKO,SAAS;EACtE;;EAEA;EACA;EACA,IAAMC,cAAc,GAAGT,OAAO,CAACU,EAAE,GAAGX,KAAK,CAACU,cAAc,GAAGV,KAAK,CAACY,KAAK;EACtE,OAAO,IAAAC,2BAAe,EAACH,cAAc,EAAER,QAAQ,CAACY,qBAAqB,CAAC,CAAC,CAAC;AACzE", "ignoreList": []}