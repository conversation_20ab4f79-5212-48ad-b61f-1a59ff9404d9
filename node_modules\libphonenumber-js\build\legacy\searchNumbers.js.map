{"version": 3, "file": "searchNumbers.js", "names": ["_normalizeArguments2", "_interopRequireDefault", "require", "_PhoneNumberMatcher", "e", "__esModule", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_defineProperty", "r", "t", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "value", "enumerable", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "searchNumbers", "_normalizeArguments", "normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "PhoneNumberMatcher", "next", "hasNext", "done"], "sources": ["../../source/legacy/searchNumbers.js"], "sourcesContent": ["import normalizeArguments from '../normalizeArguments.js'\r\nimport PhoneNumberMatcher from '../PhoneNumberMatcher.js'\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport default function searchNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\r\n\tconst matcher = new PhoneNumberMatcher(text, options, metadata)\r\n\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (matcher.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: matcher.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;AAAA,IAAAA,oBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAyD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,gBAAAR,CAAA,EAAAS,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAE,cAAA,CAAAF,CAAA,MAAAT,CAAA,GAAAY,MAAA,CAAAC,cAAA,CAAAb,CAAA,EAAAS,CAAA,IAAAK,KAAA,EAAAJ,CAAA,EAAAK,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAAjB,CAAA,CAAAS,CAAA,IAAAC,CAAA,EAAAV,CAAA;AAAA,SAAAW,eAAAD,CAAA,QAAAQ,CAAA,GAAAC,YAAA,CAAAT,CAAA,gCAAAR,OAAA,CAAAgB,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAT,CAAA,EAAAD,CAAA,oBAAAP,OAAA,CAAAQ,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAV,CAAA,GAAAU,CAAA,CAAAN,MAAA,CAAAgB,WAAA,kBAAApB,CAAA,QAAAkB,CAAA,GAAAlB,CAAA,CAAAqB,IAAA,CAAAX,CAAA,EAAAD,CAAA,gCAAAP,OAAA,CAAAgB,CAAA,UAAAA,CAAA,YAAAI,SAAA,yEAAAb,CAAA,GAAAc,MAAA,GAAAC,MAAA,EAAAd,CAAA;AAEzD;AACA;AACA;AACe,SAASe,aAAaA,CAAA,EACrC;EACC,IAAAC,mBAAA,GAAoC,IAAAC,+BAAkB,EAACC,SAAS,CAAC;IAAzDC,IAAI,GAAAH,mBAAA,CAAJG,IAAI;IAAEC,OAAO,GAAAJ,mBAAA,CAAPI,OAAO;IAAEC,QAAQ,GAAAL,mBAAA,CAARK,QAAQ;EAE/B,IAAMC,OAAO,GAAG,IAAIC,8BAAkB,CAACJ,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAE/D,OAAAvB,eAAA,KACEJ,MAAM,CAACC,QAAQ,cAAI;IACnB,OAAO;MACH6B,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAQ;QACX,IAAIF,OAAO,CAACG,OAAO,CAAC,CAAC,EAAE;UACzB,OAAO;YACNC,IAAI,EAAE,KAAK;YACXtB,KAAK,EAAEkB,OAAO,CAACE,IAAI,CAAC;UACrB,CAAC;QACF;QACA,OAAO;UACNE,IAAI,EAAE;QACP,CAAC;MACC;IACJ,CAAC;EACF,CAAC;AAEH", "ignoreList": []}