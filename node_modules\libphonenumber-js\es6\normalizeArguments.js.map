{"version": 3, "file": "normalizeArguments.js", "names": ["isObject", "normalizeArguments", "args", "_Array$prototype$slic", "Array", "prototype", "slice", "call", "_Array$prototype$slic2", "_slicedToArray", "arg_1", "arg_2", "arg_3", "arg_4", "text", "options", "metadata", "TypeError", "undefined", "_objectSpread", "defaultCountry", "Error", "concat"], "sources": ["../source/normalizeArguments.js"], "sourcesContent": ["import isObject from './helpers/isObject.js'\r\n\r\n// Extracts the following properties from function arguments:\r\n// * input `text`\r\n// * `options` object\r\n// * `metadata` JSON\r\nexport default function normalizeArguments(args) {\r\n\tconst [arg_1, arg_2, arg_3, arg_4] = Array.prototype.slice.call(args)\r\n\r\n\tlet text\r\n\tlet options\r\n\tlet metadata\r\n\r\n\t// If the phone number is passed as a string.\r\n\t// `parsePhoneNumber('88005553535', ...)`.\r\n\tif (typeof arg_1 === 'string') {\r\n\t\ttext = arg_1\r\n\t}\r\n\telse throw new TypeError('A text for parsing must be a string.')\r\n\r\n\t// If \"default country\" argument is being passed then move it to `options`.\r\n\t// `parsePhoneNumber('88005553535', 'RU', [options], metadata)`.\r\n\tif (!arg_2 || typeof arg_2 === 'string')\r\n\t{\r\n\t\tif (arg_4) {\r\n\t\t\toptions = arg_3\r\n\t\t\tmetadata = arg_4\r\n\t\t} else {\r\n\t\t\toptions = undefined\r\n\t\t\tmetadata = arg_3\r\n\t\t}\r\n\r\n\t\tif (arg_2) {\r\n\t\t\toptions = { defaultCountry: arg_2, ...options }\r\n\t\t}\r\n\t}\r\n\t// `defaultCountry` is not passed.\r\n\t// Example: `parsePhoneNumber('+78005553535', [options], metadata)`.\r\n\telse if (isObject(arg_2))\r\n\t{\r\n\t\tif (arg_3) {\r\n\t\t\toptions  = arg_2\r\n\t\t\tmetadata = arg_3\r\n\t\t} else {\r\n\t\t\tmetadata = arg_2\r\n\t\t}\r\n\t}\r\n\telse throw new Error(`Invalid second argument: ${arg_2}`)\r\n\r\n\treturn {\r\n\t\ttext,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;AAAA,OAAOA,QAAQ,MAAM,uBAAuB;;AAE5C;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EAChD,IAAAC,qBAAA,GAAqCC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACL,IAAI,CAAC;IAAAM,sBAAA,GAAAC,cAAA,CAAAN,qBAAA;IAA9DO,KAAK,GAAAF,sBAAA;IAAEG,KAAK,GAAAH,sBAAA;IAAEI,KAAK,GAAAJ,sBAAA;IAAEK,KAAK,GAAAL,sBAAA;EAEjC,IAAIM,IAAI;EACR,IAAIC,OAAO;EACX,IAAIC,QAAQ;;EAEZ;EACA;EACA,IAAI,OAAON,KAAK,KAAK,QAAQ,EAAE;IAC9BI,IAAI,GAAGJ,KAAK;EACb,CAAC,MACI,MAAM,IAAIO,SAAS,CAAC,sCAAsC,CAAC;;EAEhE;EACA;EACA,IAAI,CAACN,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACvC;IACC,IAAIE,KAAK,EAAE;MACVE,OAAO,GAAGH,KAAK;MACfI,QAAQ,GAAGH,KAAK;IACjB,CAAC,MAAM;MACNE,OAAO,GAAGG,SAAS;MACnBF,QAAQ,GAAGJ,KAAK;IACjB;IAEA,IAAID,KAAK,EAAE;MACVI,OAAO,GAAAI,aAAA;QAAKC,cAAc,EAAET;MAAK,GAAKI,OAAO,CAAE;IAChD;EACD;EACA;EACA;EAAA,KACK,IAAIf,QAAQ,CAACW,KAAK,CAAC,EACxB;IACC,IAAIC,KAAK,EAAE;MACVG,OAAO,GAAIJ,KAAK;MAChBK,QAAQ,GAAGJ,KAAK;IACjB,CAAC,MAAM;MACNI,QAAQ,GAAGL,KAAK;IACjB;EACD,CAAC,MACI,MAAM,IAAIU,KAAK,6BAAAC,MAAA,CAA6BX,KAAK,CAAE,CAAC;EAEzD,OAAO;IACNG,IAAI,EAAJA,IAAI;IACJC,OAAO,EAAPA,OAAO;IACPC,QAAQ,EAARA;EACD,CAAC;AACF", "ignoreList": []}