{"version": 3, "sources": ["../../src/lib/verify-root-layout.ts"], "names": ["path", "Log", "promises", "fs", "bold", "APP_DIR_ALIAS", "globOrig", "require", "glob", "cwd", "pattern", "Promise", "resolve", "reject", "err", "files", "getRootLayout", "isTs", "verifyRootLayout", "dir", "appDir", "tsconfigPath", "pagePath", "pageExtensions", "rootLayoutPath", "layoutFiles", "join", "isFileUnderAppDir", "startsWith", "normalizedPagePath", "replace", "pagePathSegments", "split", "availableDir", "length", "firstSegmentValue", "pop", "currentSegments", "segment", "push", "some", "file", "resolvedTsConfigPath", "hasTsConfig", "access", "then", "writeFile", "warn", "e", "console", "error"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,YAAYC,EAAE,QAAQ,KAAI;AACnC,SAASC,IAAI,QAAQ,eAAc;AACnC,SAASC,aAAa,QAAQ,cAAa;AAG3C,MAAMC,WACJC,QAAQ;AACV,MAAMC,OAAO,CAACC,KAAaC;IACzB,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BP,SAASI,SAAS;YAAED;QAAI,GAAG,CAACK,KAAKC;YAC/B,IAAID,KAAK;gBACP,OAAOD,OAAOC;YAChB;YACAF,QAAQG;QACV;IACF;AACF;AAEA,SAASC,cAAcC,IAAa;IAClC,IAAIA,MAAM;QACR,OAAO,CAAC;;;;;;;;;;;;;;;;AAgBZ,CAAC;IACC;IAEA,OAAO,CAAC;;;;;;;;;;;;AAYV,CAAC;AACD;AAEA,OAAO,eAAeC,iBAAiB,EACrCC,GAAG,EACHC,MAAM,EACNC,YAAY,EACZC,QAAQ,EACRC,cAAc,EAOf;IACC,IAAIC;IACJ,IAAI;QACF,MAAMC,cAAc,MAAMjB,KACxBY,QACA,CAAC,WAAW,EAAEG,eAAeG,IAAI,CAAC,KAAK,CAAC,CAAC;QAE3C,MAAMC,oBAAoBL,SAASM,UAAU,CAAC,CAAC,EAAEvB,cAAc,CAAC,CAAC;QACjE,MAAMwB,qBAAqBP,SAASQ,OAAO,CAAC,CAAC,EAAEzB,cAAc,CAAC,CAAC,EAAE;QACjE,MAAM0B,mBAAmBF,mBAAmBG,KAAK,CAAC;QAElD,oGAAoG;QACpG,iDAAiD;QACjD,IAAIC;QAEJ,IAAIN,mBAAmB;YACrB,IAAIF,YAAYS,MAAM,KAAK,GAAG;gBAC5B,+EAA+E;gBAC/E,kGAAkG;gBAClG,uDAAuD;gBACvD,MAAMC,oBAAoBJ,gBAAgB,CAAC,EAAE;gBAC7CE,eAAeE,kBAAkBP,UAAU,CAAC,OACxCO,oBACA;YACN,OAAO;gBACLJ,iBAAiBK,GAAG,GAAG,gCAAgC;;gBAEvD,IAAIC,kBAA4B,EAAE;gBAClC,KAAK,MAAMC,WAAWP,iBAAkB;oBACtCM,gBAAgBE,IAAI,CAACD;oBACrB,8FAA8F;oBAC9F,IACE,CAACb,YAAYe,IAAI,CAAC,CAACC,OACjBA,KAAKb,UAAU,CAACS,gBAAgBX,IAAI,CAAC,QAEvC;wBACAO,eAAeI,gBAAgBX,IAAI,CAAC;wBACpC;oBACF;gBACF;YACF;QACF,OAAO;YACLO,eAAe;QACjB;QAEA,IAAI,OAAOA,iBAAiB,UAAU;YACpC,MAAMS,uBAAuB1C,KAAK0B,IAAI,CAACP,KAAKE;YAC5C,MAAMsB,cAAc,MAAMxC,GAAGyC,MAAM,CAACF,sBAAsBG,IAAI,CAC5D,IAAM,MACN,IAAM;YAGRrB,iBAAiBxB,KAAK0B,IAAI,CACxBN,QACAa,cACA,CAAC,OAAO,EAAEU,cAAc,QAAQ,KAAK,CAAC;YAExC,MAAMxC,GAAG2C,SAAS,CAACtB,gBAAgBR,cAAc2B;YAEjD1C,IAAI8C,IAAI,CACN,CAAC,UAAU,EAAE3C,KACX,CAAC,IAAI,EAAEyB,mBAAmB,CAAC,EAC3B,wCAAwC,EAAEzB,KAC1C,CAAC,GAAG,EAAEoB,eAAeM,OAAO,CAACV,QAAQ,IAAI,CAAC,EAC1C,SAAS,CAAC;YAGd,sBAAsB;YACtB,OAAO;gBAAC;gBAAMI;aAAe;QAC/B;IACF,EAAE,OAAOwB,GAAG;QACVC,QAAQC,KAAK,CAACF;IAChB;IAEA,4BAA4B;IAC5B,OAAO;QAAC;QAAOxB;KAAe;AAChC"}