{"version": 3, "file": "getCountryCallingCode.test.js", "names": ["_metadataMin", "_interopRequireDefault", "require", "_getCountryCallingCode", "e", "__esModule", "describe", "it", "expect", "getCountryCallingCode", "metadata", "to", "equal"], "sources": ["../source/getCountryCallingCode.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\nimport getCountryCallingCode from './getCountryCallingCode.js'\r\n\r\ndescribe('getCountryCallingCode', () => {\r\n\tit('should get country calling code', () => {\r\n\t\texpect(getCountryCallingCode('US', metadata)).to.equal('1')\r\n\t})\r\n\r\n\tit('should throw if country is unknown', () => {\r\n\t\texpect(() => getCountryCallingCode('ZZ', metadata)).to.throw('Unknown country: ZZ')\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,sBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA8D,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE9DE,QAAQ,CAAC,uBAAuB,EAAE,YAAM;EACvCC,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3CC,MAAM,CAAC,IAAAC,iCAAqB,EAAC,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EAC5D,CAAC,CAAC;EAEFL,EAAE,CAAC,oCAAoC,EAAE,YAAM;IAC9CC,MAAM,CAAC;MAAA,OAAM,IAAAC,iCAAqB,EAAC,IAAI,EAAEC,uBAAQ,CAAC;IAAA,EAAC,CAACC,EAAE,SAAM,CAAC,qBAAqB,CAAC;EACpF,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}