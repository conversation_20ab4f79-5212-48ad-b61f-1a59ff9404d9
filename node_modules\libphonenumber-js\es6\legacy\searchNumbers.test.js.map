{"version": 3, "file": "searchNumbers.test.js", "names": ["searchNumbers", "metadata", "type", "describe", "it", "expectedNumbers", "country", "phone", "startsAt", "endsAt", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "number", "value", "expect", "to", "deep", "equal", "shift", "length"], "sources": ["../../source/legacy/searchNumbers.test.js"], "sourcesContent": ["import searchNumbers from './searchNumbers.js'\r\nimport metadata from '../../metadata.min.json' with { type: 'json' }\r\n\r\ndescribe('searchNumbers', () => {\r\n\tit('should iterate', () => {\r\n\t\tconst expectedNumbers =[{\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\t// number   : '(*************',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}]\r\n\r\n\t\tfor (const number of searchNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\texpect(number).to.deep.equal(expectedNumbers.shift())\r\n\t\t}\r\n\r\n\t\texpect(expectedNumbers.length).to.equal(0)\r\n\t})\r\n})"], "mappings": ";;;AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,QAAQ,MAAM,yBAAyB,QAAQC,IAAI,EAAE,MAAM;AAElEC,QAAQ,CAAC,eAAe,EAAE,YAAM;EAC/BC,EAAE,CAAC,gBAAgB,EAAE,YAAM;IAC1B,IAAMC,eAAe,GAAE,CAAC;MACvBC,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtB;MACAC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,EAAE;MACFH,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtB;MACAC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC;IAEF,SAAAC,SAAA,GAAAC,+BAAA,CAAqBX,aAAa,CAAC,qFAAqF,EAAE,IAAI,EAAEC,QAAQ,CAAC,GAAAW,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAE;MAAA,IAAhIC,MAAM,GAAAF,KAAA,CAAAG,KAAA;MAChBC,MAAM,CAACF,MAAM,CAAC,CAACG,EAAE,CAACC,IAAI,CAACC,KAAK,CAACd,eAAe,CAACe,KAAK,CAAC,CAAC,CAAC;IACtD;IAEAJ,MAAM,CAACX,eAAe,CAACgB,MAAM,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}