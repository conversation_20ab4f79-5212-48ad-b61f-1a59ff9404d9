{"name": "libphonenumber-js", "version": "1.12.12", "description": "A simpler (and smaller) rewrite of Google Android's libphonenumber library in javascript", "main": "index.cjs", "module": "index.js", "type": "module", "exports": {".": {"import": "./index.js", "require": "./index.cjs"}, "./min": {"import": "./min/index.js", "require": "./min/index.cjs"}, "./max": {"import": "./max/index.js", "require": "./max/index.cjs"}, "./mobile": {"import": "./mobile/index.js", "require": "./mobile/index.cjs"}, "./core": {"import": "./core/index.js", "require": "./core/index.cjs"}, "./min/metadata": {"import": "./metadata.min.json.js", "require": "./metadata.min.json"}, "./metadata.min": {"import": "./metadata.min.json.js", "require": "./metadata.min.json"}, "./metadata.min.json": {"import": "./metadata.min.json.js", "require": "./metadata.min.json"}, "./metadata.full": {"import": "./metadata.max.json.js", "require": "./metadata.max.json"}, "./metadata.full.json": {"import": "./metadata.max.json.js", "require": "./metadata.max.json"}, "./max/metadata": {"import": "./metadata.max.json.js", "require": "./metadata.max.json"}, "./metadata.max": {"import": "./metadata.max.json.js", "require": "./metadata.max.json"}, "./metadata.max.json": {"import": "./metadata.max.json.js", "require": "./metadata.max.json"}, "./mobile/metadata": {"import": "./metadata.mobile.json.js", "require": "./metadata.mobile.json"}, "./metadata.mobile": {"import": "./metadata.mobile.json.js", "require": "./metadata.mobile.json"}, "./metadata.mobile.json": {"import": "./metadata.mobile.json.js", "require": "./metadata.mobile.json"}, "./mobile/examples": {"import": "./examples.mobile.json.js", "require": "./examples.mobile.json"}, "./examples.mobile": {"import": "./examples.mobile.json.js", "require": "./examples.mobile.json"}, "./examples.mobile.json": {"import": "./examples.mobile.json.js", "require": "./examples.mobile.json"}, "./package.json": "./package.json"}, "sideEffects": false, "devDependencies": {"@babel/cli": "^7.28.0", "@babel/core": "^7.28.0", "@babel/plugin-syntax-import-assertions": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/preset-env": "^7.28.0", "@babel/register": "^7.27.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-terser": "^0.4.4", "babel-plugin-istanbul": "^7.0.0", "chai": "^5.2.1", "cpy-cli": "^5.0.0", "crlf": "^1.1.1", "cross-env": "^10.0.0", "istanbul": "^1.1.0-alpha.1", "jest": "^30.0.5", "jest-codemods": "^0.34.2", "libphonenumber-metadata-generator": "^1.1.0", "minimist": "^1.2.8", "mocha": "^10.0.0", "npm-run-all": "^4.1.5", "nyc": "^17.1.0", "renamer": "^5.0.2", "replace-in-file": "^8.3.0", "rimraf": "^6.0.1", "rollup": "^4.46.2", "tslib": "^2.8.1"}, "scripts": {"metadata:update:job": "git reset --hard && git pull && npm install && npm run metadata:update:release", "metadata:pull-request": "node runnable/metadata-pull-request", "metadata:branch": "node runnable/metadata-branch", "metadata:unbranch": "node runnable/metadata-unbranch", "metadata:publish": "npm version patch && npm publish && git push", "metadata:update:release": "npm run metadata:download && node runnable/metadata-update-and-release", "metadata:update:branch": "npm run metadata:branch && npm run metadata:download && node runnable/metadata-update-and-push", "metadata:update:pull-request": "npm run metadata:branch && npm run metadata:download && node runnable/metadata-update-and-push-and-pull-request", "metadata:generate": "npm-run-all metadata:generate:min metadata:generate:full metadata:generate:max metadata:generate:mobile metadata:generate:min:js metadata:generate:full:js metadata:generate:max:js metadata:generate:mobile:js metadata:generate:mobile:examples:js", "metadata:generate:min": "node runnable/generate ../PhoneNumberMetadata.xml ../metadata.min.json --examples mobile", "metadata:generate:full": "node runnable/generate ../PhoneNumberMetadata.xml ../metadata.full.json --extended --debug", "metadata:generate:max": "node runnable/generate ../PhoneNumberMetadata.xml ../metadata.max.json --extended --debug", "metadata:generate:mobile": "node runnable/generate ../PhoneNumberMetadata.xml ../metadata.mobile.json --types mobile", "metadata:generate:min:js": "node runnable/json-to-js ./metadata.min.json", "metadata:generate:full:js": "node runnable/json-to-js ./metadata.full.json", "metadata:generate:max:js": "node runnable/json-to-js ./metadata.max.json", "metadata:generate:mobile:js": "node runnable/json-to-js ./metadata.mobile.json", "metadata:generate:mobile:examples:js": "node runnable/json-to-js ./examples.mobile.json", "metadata:download": "node runnable/download https://raw.githubusercontent.com/googlei18n/libphonenumber/master/resources/PhoneNumberMetadata.xml PhoneNumberMetadata.xml", "generate-country-codes": "node --experimental-json-modules runnable/generate-country-codes", "transform-mocha-tests-into-jest-tests": "npx jest-codemods", "test": "npm run test--mocha", "test--jest": "npm-run-all test:except-exports--jest test:exports--mocha", "test:except-exports--jest": "jest", "test:exports--mocha": "mocha --colors --bail --reporter spec \"test/exports.test.js\" \"test/exports.*.test.js\" --recursive", "test--mocha": "mocha --colors --bail --reporter spec --require ./test/globals.js \"source/**/*.test.js\" \"test/**/*.test.js\" --recursive", "test-coverage": "npm run test-coverage--mocha", "test-coverage--jest": "jest --coverage", "test-coverage--mocha": "npm-run-all build:commonjs test-coverage:commonjs--mocha", "test-coverage:commonjs--mocha": "istanbul cover -x \"*.test.js\" -x \"build/findNumbers/Leniency.js\" -x \"build/findNumbers/matchPhoneNumberStringAgainstPhoneNumber.js\" -x \"build/findNumbers/RegExpCache.js\" -x \"build/findNumbers/LRUCache.js\" -x \"build/PhoneNumberMatcher.js\" -x \"build/tools/semver-compare.js\" node_modules/mocha/bin/_mocha -- --colors --reporter dot --require ./test/globals.js \"build/**/*.test.js\" --recursive", "test-coverage:esm--mocha--does-not-seem-to-work": "istanbul cover -x \"build/**\" -x \"es6/**\" -x \"*.test.js\" -x \"source/findNumbers/Leniency.js\" -x \"source/findNumbers/matchPhoneNumberStringAgainstPhoneNumber.js\" -x \"source/findNumbers/RegExpCache.js\" -x \"source/findNumbers/LRUCache.js\" -x \"source/PhoneNumberMatcher.js\" -x \"source/tools/semver-compare.js\" node_modules/mocha/bin/_mocha -- --colors --reporter dot --require ./test/globals.js \"source/**/*.test.js\" \"test/**/*.test.js\" --recursive", "test:esm--nyc--does-not-seem-to-work": "cross-env nyc mocha --bail --require @babel/register --require ./test/globals.js \"source/**/*.test.js\" \"test/**/*.test.js\"", "test-coverage--nyc--does-not-seem-to-work": "nyc report --reporter=text-lcov", "test-travis": "node --experimental-json-modules node_modules/istanbul/lib/cli.js cover -x \"build/**\" -x \"es6/**\" -x \"*.test.js\" -x \"source/findNumbers/Leniency.js\" -x \"source/findNumbers/matchPhoneNumberStringAgainstPhoneNumber.js\" -x \"source/findNumbers/RegExpCache.js\" -x \"source/findNumbers/LRUCache.js\" -x \"source/PhoneNumberMatcher.js\" -x \"source/tools/semver-compare.js\" node_modules/mocha/bin/_mocha --report lcovonly -- --colors --reporter spec --require ./test/globals.js \"source/**/*.test.js\" \"test/**/*.test.js\" --recursive", "clean": "rimraf --glob ./build/**/* ./es6/**/*", "build:commonjs": "npm-run-all build:commonjs:with-tests build:commonjs:package.json build:commonjs:create-typescript-definitions build:commonjs:patch-typescript-definitions", "build:commonjs:before-es-modules": "cross-env BABEL_ENV=commonjs babel ./source --out-dir ./build --source-maps --ignore test.js", "build:commonjs:with-tests": "cross-env BABEL_ENV=commonjs babel ./source --out-dir ./build --source-maps", "build:commonjs:package.json": "node runnable/create-commonjs-package-json.js", "build:commonjs:create-typescript-definitions": "rimraf --verbose --glob ./*.d.cts \"./!(node_modules)/**/*.d.cts\" && cpy **/{index,metadata*,examples*,types}.d.ts . --rename={{basename}}.cts && renamer --find d.cts.ts --replace d.cts ./*.d.cts.ts \"./!(node_modules)/**/*.d.cts.ts\"", "build:commonjs:patch-typescript-definitions": "replace-in-file \".d.js';\" \".d.cjs';\" **/*.d.cts", "build:modules:copy-typescript-definitions": "cpy --flat min/index.d.ts max && cpy --flat min/index.d.ts mobile", "build:copy-metadata-files-to-subpackage-folders:min": "cpy --flat metadata.min.json metadata.min.json.js metadata.min.json.d.ts min/metadata", "build:copy-metadata-files-to-subpackage-folders:max": "cpy --flat metadata.max.json metadata.max.json.js metadata.max.json.d.ts max/metadata", "build:copy-metadata-files-to-subpackage-folders:mobile": "cpy --flat metadata.mobile.json metadata.mobile.json.js metadata.mobile.json.d.ts mobile/metadata", "build:copy-metadata-files-to-subpackage-folders": "npm-run-all build:copy-metadata-files-to-subpackage-folders:max build:copy-metadata-files-to-subpackage-folders:min build:copy-metadata-files-to-subpackage-folders:mobile", "build:copy-examples-files-to-subpackage-folders:mobile": "cpy --flat examples.mobile.json examples.mobile.json.js examples.mobile.json.d.ts mobile/examples", "build:copy-examples-files-to-subpackage-folders": "npm-run-all build:copy-examples-files-to-subpackage-folders:mobile", "build:modules": "cross-env BABEL_ENV=es6 babel ./source --out-dir ./es6 --source-maps --ignore test.js", "build:bundle": "rollup --config rollup.config.mjs", "build": "npm-run-all clean build:modules:copy-typescript-definitions build:modules build:commonjs build:bundle build:copy-metadata-files-to-subpackage-folders build:copy-examples-files-to-subpackage-folders", "prepublishOnly": "npm run metadata:generate && npm run generate-country-codes && crlf --set=LF metadata.*.json && npm-run-all build test"}, "repository": {"type": "git", "url": "git+https://gitlab.com/catamphetamine/libphonenumber-js.git"}, "keywords": ["telephone", "phone", "number", "input", "mobile", "libphonenumber"], "author": "catamphetamine <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://gitlab.com/catamphetamine/libphonenumber-js/issues"}, "homepage": "https://gitlab.com/catamphetamine/libphonenumber-js#readme"}