{"version": 3, "sources": ["../../src/lib/recursive-delete.ts"], "names": ["promises", "join", "isAbsolute", "dirname", "isError", "wait", "unlinkPath", "p", "isDir", "t", "rmdir", "unlink", "e", "code", "recursiveDelete", "dir", "exclude", "previousPath", "result", "readdir", "withFileTypes", "Promise", "all", "map", "part", "absolutePath", "name", "isDirectory", "isSymlink", "isSymbolicLink", "linkPath", "readlink", "stats", "stat", "pp", "isNotExcluded", "test"], "mappings": "AACA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAM;AAChD,OAAOC,aAAa,aAAY;AAChC,SAASC,IAAI,QAAQ,SAAQ;AAE7B,MAAMC,aAAa,OAAOC,GAAWC,QAAQ,KAAK,EAAEC,IAAI,CAAC;IACvD,IAAI;QACF,IAAID,OAAO;YACT,MAAMR,SAASU,KAAK,CAACH;QACvB,OAAO;YACL,MAAMP,SAASW,MAAM,CAACJ;QACxB;IACF,EAAE,OAAOK,GAAG;QACV,MAAMC,OAAOT,QAAQQ,MAAMA,EAAEC,IAAI;QACjC,IACE,AAACA,CAAAA,SAAS,WACRA,SAAS,eACTA,SAAS,WACTA,SAAS,QAAO,KAClBJ,IAAI,GACJ;YACA,MAAMJ,KAAKI,IAAI;YACf,OAAOH,WAAWC,GAAGC,OAAOC;QAC9B;QAEA,IAAII,SAAS,UAAU;YACrB;QACF;QAEA,MAAMD;IACR;AACF;AAEA;;CAEC,GACD,OAAO,eAAeE,gBACpB,wCAAwC,GACxCC,GAAW,EACX,wCAAwC,GACxCC,OAAgB,EAChB,sEAAsE,GACtEC,eAAuB,EAAE;IAEzB,IAAIC;IACJ,IAAI;QACFA,SAAS,MAAMlB,SAASmB,OAAO,CAACJ,KAAK;YAAEK,eAAe;QAAK;IAC7D,EAAE,OAAOR,GAAG;QACV,IAAIR,QAAQQ,MAAMA,EAAEC,IAAI,KAAK,UAAU;YACrC;QACF;QACA,MAAMD;IACR;IAEA,MAAMS,QAAQC,GAAG,CACfJ,OAAOK,GAAG,CAAC,OAAOC;QAChB,MAAMC,eAAexB,KAAKc,KAAKS,KAAKE,IAAI;QAExC,yCAAyC;QACzC,mDAAmD;QACnD,IAAIC,cAAcH,KAAKG,WAAW;QAClC,MAAMC,YAAYJ,KAAKK,cAAc;QAErC,IAAID,WAAW;YACb,MAAME,WAAW,MAAM9B,SAAS+B,QAAQ,CAACN;YAEzC,IAAI;gBACF,MAAMO,QAAQ,MAAMhC,SAASiC,IAAI,CAC/B/B,WAAW4B,YACPA,WACA7B,KAAKE,QAAQsB,eAAeK;gBAElCH,cAAcK,MAAML,WAAW;YACjC,EAAE,OAAM,CAAC;QACX;QAEA,MAAMO,KAAKjC,KAAKgB,cAAcO,KAAKE,IAAI;QACvC,MAAMS,gBAAgB,CAACnB,WAAW,CAACA,QAAQoB,IAAI,CAACF;QAEhD,IAAIC,eAAe;YACjB,IAAIR,aAAa;gBACf,MAAMb,gBAAgBW,cAAcT,SAASkB;YAC/C;YACA,OAAO5B,WAAWmB,cAAc,CAACG,aAAaD;QAChD;IACF;AAEJ"}