{"version": 3, "file": "formatIncompletePhoneNumber.js", "names": ["AsYouType", "formatIncompletePhoneNumber", "value", "optionsOrDefaultCountry", "metadata", "undefined", "input"], "sources": ["../source/formatIncompletePhoneNumber.js"], "sourcesContent": ["import AsYouType from './AsYouType.js'\r\n\r\n/**\r\n * Formats a (possibly incomplete) phone number.\r\n * The phone number can be either in E.164 format\r\n * or in a form of national number digits.\r\n * @param {string} value - A possibly incomplete phone number. Either in E.164 format or in a form of national number digits.\r\n * @param {string|object} [optionsOrDefaultCountry] - A two-letter (\"ISO 3166-1 alpha-2\") country code, or an object of shape `{ defaultCountry?: string, defaultCallingCode?: string }`.\r\n * @return {string} Formatted (possibly incomplete) phone number.\r\n */\r\nexport default function formatIncompletePhoneNumber(value, optionsOrDefaultCountry, metadata) {\r\n\tif (!metadata) {\r\n\t\tmetadata = optionsOrDefaultCountry\r\n\t\toptionsOrDefaultCountry = undefined\r\n\t}\r\n\treturn new AsYouType(optionsOrDefaultCountry, metadata).input(value)\r\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,2BAA2BA,CAACC,KAAK,EAAEC,uBAAuB,EAAEC,QAAQ,EAAE;EAC7F,IAAI,CAACA,QAAQ,EAAE;IACdA,QAAQ,GAAGD,uBAAuB;IAClCA,uBAAuB,GAAGE,SAAS;EACpC;EACA,OAAO,IAAIL,SAAS,CAACG,uBAAuB,EAAEC,QAAQ,CAAC,CAACE,KAAK,CAACJ,KAAK,CAAC;AACrE", "ignoreList": []}