{"version": 3, "sources": ["../../src/client/page-loader.ts"], "names": ["addBasePath", "interpolateAs", "getAssetPathFromRoute", "addLocale", "isDynamicRoute", "parseRelativeUrl", "removeTrailingSlash", "createRouteLoader", "getClientBuildManifest", "DEV_CLIENT_PAGES_MANIFEST", "DEV_MIDDLEWARE_MANIFEST", "<PERSON><PERSON><PERSON><PERSON>", "getPageList", "process", "env", "NODE_ENV", "then", "manifest", "sortedPages", "window", "__DEV_PAGES_MANIFEST", "pages", "promisedDevPagesManifest", "fetch", "assetPrefix", "credentials", "res", "json", "catch", "err", "console", "log", "Error", "getMiddleware", "middlewareMatchers", "__NEXT_MIDDLEWARE_MATCHERS", "__MIDDLEWARE_MATCHERS", "undefined", "__DEV_MIDDLEWARE_MATCHERS", "promisedMiddlewareMatchers", "buildId", "matchers", "getDataHref", "params", "<PERSON><PERSON><PERSON>", "href", "locale", "pathname", "hrefPathname", "query", "search", "asPathname", "route", "getHrefForSlug", "path", "dataRoute", "skipInterpolation", "result", "_isSsg", "promisedSsgManifest", "has", "loadPage", "routeLoader", "loadRoute", "page", "component", "mod", "exports", "styleSheets", "styles", "map", "o", "text", "content", "error", "prefetch", "constructor", "Promise", "resolve", "__SSG_MANIFEST", "__SSG_MANIFEST_CB"], "mappings": "AAGA,SAASA,WAAW,QAAQ,kBAAiB;AAC7C,SAASC,aAAa,QAAQ,4CAA2C;AACzE,OAAOC,2BAA2B,uDAAsD;AACxF,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,cAAc,QAAQ,wCAAuC;AACtE,SAASC,gBAAgB,QAAQ,gDAA+C;AAChF,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,iBAAgB;AAC1E,SACEC,yBAAyB,EACzBC,uBAAuB,QAClB,0BAAyB;AAkBjB,MAAMC;IA0BnBC,cAAc;QACZ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,OAAOP,yBAAyBQ,IAAI,CAAC,CAACC,WAAaA,SAASC,WAAW;QACzE,OAAO;YACL,IAAIC,OAAOC,oBAAoB,EAAE;gBAC/B,OAAOD,OAAOC,oBAAoB,CAACC,KAAK;YAC1C,OAAO;gBACL,IAAI,CAACC,6BAAL,IAAI,CAACA,2BAA6BC,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,+BAA4Bf,2BAChD;oBAAEgB,aAAa;gBAAc,GAE5BT,IAAI,CAAC,CAACU,MAAQA,IAAIC,IAAI,IACtBX,IAAI,CAAC,CAACC;oBACLE,OAAOC,oBAAoB,GAAGH;oBAC9B,OAAOA,SAASI,KAAK;gBACvB,GACCO,KAAK,CAAC,CAACC;oBACNC,QAAQC,GAAG,CAAE,qCAAoCF;oBACjD,MAAM,IAAIG,MACR,AAAC,0FACC;gBAEN;gBACF,OAAO,IAAI,CAACV,wBAAwB;YACtC;QACF;IACF;IAEAW,gBAAgB;QACd,IAAIpB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAMmB,qBAAqBrB,QAAQC,GAAG,CAACqB,0BAA0B;YACjEhB,OAAOiB,qBAAqB,GAAGF,qBAC1BA,qBACDG;YACJ,OAAOlB,OAAOiB,qBAAqB;QACrC,OAAO;YACL,IAAIjB,OAAOmB,yBAAyB,EAAE;gBACpC,OAAOnB,OAAOmB,yBAAyB;YACzC,OAAO;gBACL,IAAI,CAAC,IAAI,CAACC,0BAA0B,EAAE;oBACpC,2EAA2E;oBAC3E,aAAa;oBACb,IAAI,CAACA,0BAA0B,GAAGhB,MAChC,AAAG,IAAI,CAACC,WAAW,GAAC,mBAAgB,IAAI,CAACgB,OAAO,GAAC,MAAG9B,yBACpD;wBAAEe,aAAa;oBAAc,GAE5BT,IAAI,CAAC,CAACU,MAAQA,IAAIC,IAAI,IACtBX,IAAI,CAAC,CAACyB;wBACLtB,OAAOmB,yBAAyB,GAAGG;wBACnC,OAAOA;oBACT,GACCb,KAAK,CAAC,CAACC;wBACNC,QAAQC,GAAG,CAAE,0CAAyCF;oBACxD;gBACJ;gBACA,wDAAwD;gBACxD,OAAO,IAAI,CAACU,0BAA0B;YACxC;QACF;IACF;IAEAG,YAAYC,MAKX,EAAU;QACT,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE,GAAGH;QACjC,MAAM,EAAEI,UAAUC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAG7C,iBAAiBwC;QACnE,MAAM,EAAEE,UAAUI,UAAU,EAAE,GAAG9C,iBAAiBuC;QAClD,MAAMQ,QAAQ9C,oBAAoB0C;QAClC,IAAII,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,MAAM,IAAIpB,MAAM,AAAC,8CAA2CoB,QAAM;QACpE;QAEA,MAAMC,iBAAiB,CAACC;YACtB,MAAMC,YAAYrD,sBAChBI,oBAAoBH,UAAUmD,MAAMR,UACpC;YAEF,OAAO9C,YACL,AAAC,iBAAc,IAAI,CAACwC,OAAO,GAAGe,YAAYL,QAC1C;QAEJ;QAEA,OAAOG,eACLV,OAAOa,iBAAiB,GACpBL,aACA/C,eAAegD,SACfnD,cAAc+C,cAAcG,YAAYF,OAAOQ,MAAM,GACrDL;IAER;IAEAM,OACE,iCAAiC,GACjCN,KAAa,EACK;QAClB,OAAO,IAAI,CAACO,mBAAmB,CAAC3C,IAAI,CAAC,CAACC,WAAaA,SAAS2C,GAAG,CAACR;IAClE;IAEAS,SAAST,KAAa,EAA0B;QAC9C,OAAO,IAAI,CAACU,WAAW,CAACC,SAAS,CAACX,OAAOpC,IAAI,CAAC,CAACU;YAC7C,IAAI,eAAeA,KAAK;gBACtB,OAAO;oBACLsC,MAAMtC,IAAIuC,SAAS;oBACnBC,KAAKxC,IAAIyC,OAAO;oBAChBC,aAAa1C,IAAI2C,MAAM,CAACC,GAAG,CAAC,CAACC,IAAO,CAAA;4BAClC1B,MAAM0B,EAAE1B,IAAI;4BACZ2B,MAAMD,EAAEE,OAAO;wBACjB,CAAA;gBACF;YACF;YACA,MAAM/C,IAAIgD,KAAK;QACjB;IACF;IAEAC,SAASvB,KAAa,EAAiB;QACrC,OAAO,IAAI,CAACU,WAAW,CAACa,QAAQ,CAACvB;IACnC;IAzIAwB,YAAYpC,OAAe,EAAEhB,WAAmB,CAAE;QAChD,IAAI,CAACsC,WAAW,GAAGvD,kBAAkBiB;QAErC,IAAI,CAACgB,OAAO,GAAGA;QACf,IAAI,CAAChB,WAAW,GAAGA;QAEnB,IAAI,CAACmC,mBAAmB,GAAG,IAAIkB,QAAQ,CAACC;YACtC,IAAI3D,OAAO4D,cAAc,EAAE;gBACzBD,QAAQ3D,OAAO4D,cAAc;YAC/B,OAAO;gBACL5D,OAAO6D,iBAAiB,GAAG;oBACzBF,QAAQ3D,OAAO4D,cAAc;gBAC/B;YACF;QACF;IACF;AA2HF;AAnJA,SAAqBpE,wBAmJpB"}