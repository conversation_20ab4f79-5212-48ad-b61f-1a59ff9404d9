{"version": 3, "file": "AsyncAction.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/AsyncAction.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,mCAAkC;AAIlC,uDAAsD;AACtD,+CAA8C;AAG9C;IAAoC,+BAAS;IAO3C,qBAAsB,SAAyB,EAAY,IAAmD;QAA9G,YACE,kBAAM,SAAS,EAAE,IAAI,CAAC,SACvB;QAFqB,eAAS,GAAT,SAAS,CAAgB;QAAY,UAAI,GAAJ,IAAI,CAA+C;QAFpG,aAAO,GAAY,KAAK,CAAC;;IAInC,CAAC;IAEM,8BAAQ,GAAf,UAAgB,KAAS,EAAE,KAAiB;;QAAjB,sBAAA,EAAA,SAAiB;QAC1C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QAGD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACnB,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAuBjC,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;SACrD;QAID,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,IAAI,CAAC,EAAE,GAAG,MAAA,IAAI,CAAC,EAAE,mCAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAEpE,OAAO,IAAI,CAAC;IACd,CAAC;IAES,oCAAc,GAAxB,UAAyB,SAAyB,EAAE,GAAiB,EAAE,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACtF,OAAO,mCAAgB,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACpF,CAAC;IAES,oCAAc,GAAxB,UAAyB,UAA0B,EAAE,EAAgB,EAAE,KAAwB;QAAxB,sBAAA,EAAA,SAAwB;QAE7F,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;YACnE,OAAO,EAAE,CAAC;SACX;QAGD,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,mCAAgB,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;SACpC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAMM,6BAAO,GAAd,UAAe,KAAQ,EAAE,KAAa;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SAClD;QAED,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC1C,IAAI,KAAK,EAAE;YACT,OAAO,KAAK,CAAC;SACd;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE;YAcpD,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;SAC9D;IACH,CAAC;IAES,8BAAQ,GAAlB,UAAmB,KAAQ,EAAE,MAAc;QACzC,IAAI,OAAO,GAAY,KAAK,CAAC;QAC7B,IAAI,UAAe,CAAC;QACpB,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,GAAG,IAAI,CAAC;YAIf,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;SACtE;QACD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO,UAAU,CAAC;SACnB;IACH,CAAC;IAED,iCAAW,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACV,IAAA,KAAoB,IAAI,EAAtB,EAAE,QAAA,EAAE,SAAS,eAAS,CAAC;YACvB,IAAA,OAAO,GAAK,SAAS,QAAd,CAAe;YAE9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,IAAK,CAAC;YAChD,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YAErB,qBAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACzB,IAAI,EAAE,IAAI,IAAI,EAAE;gBACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;aACpD;YAED,IAAI,CAAC,KAAK,GAAG,IAAK,CAAC;YACnB,iBAAM,WAAW,WAAE,CAAC;SACrB;IACH,CAAC;IACH,kBAAC;AAAD,CAAC,AA9ID,CAAoC,eAAM,GA8IzC;AA9IY,kCAAW"}