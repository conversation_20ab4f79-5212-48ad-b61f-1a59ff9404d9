{"version": 3, "file": "IsHexColor.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsHexColor.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,mBAAmB,MAAM,0BAA0B,CAAC;AAE3D,MAAM,CAAC,IAAM,YAAY,GAAG,YAAY,CAAC;AAEzC;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,KAAc;IACvC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,iBAAqC;IAC9D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,UAAU,CAAC,KAAK,CAAC,EAAjB,CAAiB;YACrD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,uCAAuC,EAApD,CAAoD,EAClE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isHexColorValidator from 'validator/lib/isHexColor';\n\nexport const IS_HEX_COLOR = 'isHexColor';\n\n/**\n * Checks if the string is a hexadecimal color.\n * If given value is not a string, then it returns false.\n */\nexport function isHexColor(value: unknown): boolean {\n  return typeof value === 'string' && isHexColorValidator(value);\n}\n\n/**\n * Checks if the string is a hexadecimal color.\n * If given value is not a string, then it returns false.\n */\nexport function IsHexColor(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_HEX_COLOR,\n      validator: {\n        validate: (value, args): boolean => isHexColor(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a hexadecimal color',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}