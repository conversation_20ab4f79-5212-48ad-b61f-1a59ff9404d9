{"version": 3, "file": "ParseError.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "_Error", "code", "_this", "_classCallCheck", "_callSuper", "Object", "setPrototypeOf", "prototype", "name", "constructor", "_inherits", "_createClass", "_wrapNativeSuper", "Error", "default"], "sources": ["../source/ParseError.js"], "sourcesContent": ["// https://stackoverflow.com/a/46971044/970769\r\n// \"Breaking changes in Typescript 2.1\"\r\n// \"Extending built-ins like <PERSON>rror, Array, and Map may no longer work.\"\r\n// \"As a recommendation, you can manually adjust the prototype immediately after any super(...) calls.\"\r\n// https://github.com/Microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\r\nexport default class ParseError extends Error {\r\n  constructor(code) {\r\n    super(code)\r\n    // Set the prototype explicitly.\r\n    // Any subclass of FooError will have to manually set the prototype as well.\r\n    Object.setPrototypeOf(this, ParseError.prototype)\r\n    this.name = this.constructor.name\r\n  }\r\n}"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AAAA,IACqBA,UAAU,0BAAAC,MAAA;EAC7B,SAAAD,WAAYE,IAAI,EAAE;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAJ,UAAA;IAChBG,KAAA,GAAAE,UAAA,OAAAL,UAAA,GAAME,IAAI;IACV;IACA;IACAI,MAAM,CAACC,cAAc,CAAAJ,KAAA,EAAOH,UAAU,CAACQ,SAAS,CAAC;IACjDL,KAAA,CAAKM,IAAI,GAAGN,KAAA,CAAKO,WAAW,CAACD,IAAI;IAAA,OAAAN,KAAA;EACnC;EAACQ,SAAA,CAAAX,UAAA,EAAAC,MAAA;EAAA,OAAAW,YAAA,CAAAZ,UAAA;AAAA,eAAAa,gBAAA,CAPqCC,KAAK;AAAA,SAAxBd,UAAU,IAAAe,OAAA", "ignoreList": []}