{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/loader.ts"], "names": ["getTargets", "getImportCode", "getModuleCode", "getExportCode", "getFilter", "getPreRequester", "isDataUrl", "isUrlRequestable", "requestify", "resolveRequests", "stringifyRequest", "<PERSON><PERSON><PERSON><PERSON>", "encoder", "TextEncoder", "moduleRegExp", "createUrlAndImportVisitor", "visitorOptions", "apis", "imports", "replacements", "replacedUrls", "replacedImportUrls", "importUrlToNameMap", "Map", "hasUrlImportHelper", "urlToNameMap", "urlToReplacementMap", "urlIndex", "importUrlIndex", "handleUrl", "u", "url", "<PERSON><PERSON><PERSON>", "url<PERSON><PERSON><PERSON>", "set", "query", "hash<PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "queryParts", "prefix", "length", "pop", "join", "hash", "push", "type", "importName", "JSON", "stringify", "require", "resolve", "index", "newUrl", "get", "size", "needQuotes", "<PERSON><PERSON><PERSON>", "replacement<PERSON>ame", "loc", "Rule", "import", "node", "importFilter", "value", "media", "mediaQueries", "undefined", "isRequestable", "importUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Url", "createIcssVisitor", "replacementIndex", "Declaration", "composes", "property", "specifier", "from", "icss", "dedupe", "newNames", "localName", "names", "LOADER_NAME", "Lightning<PERSON>s<PERSON><PERSON>der", "source", "prevMap", "options", "done", "async", "getOptions", "implementation", "targets", "userTargets", "opts", "modules", "transformCss", "TypeError", "exports", "icssImports", "exportOnlyLocals", "unshift", "loadBindings", "transform", "css", "lightning", "icssReplacedUrls", "urlImportVisitor", "importLoaders", "resourcePath", "context", "icssVisitor", "visitor", "code", "map", "moduleExports", "cssModules", "test", "pattern", "process", "env", "__NEXT_TEST_MODE", "filename", "encode", "sourceMap", "key", "loader", "inputSourceMap", "include", "cssCodeAsString", "toString", "name", "Object", "prototype", "hasOwnProperty", "call", "v", "compose", "urlResolver", "getResolve", "conditionNames", "mainFields", "mainFiles", "extensions", "entries", "pathname", "request", "rootContext", "resolvedUrl", "Set", "importItem", "replace", "importResolver", "restrictions", "icssResolver", "importCode", "moduleCode", "exportCode", "esCode", "parse", "error", "console", "raw"], "mappings": "AACA,SAASA,UAAU,QAAQ,UAAS;AACpC,SACEC,aAAa,EAKbC,aAAa,EACbC,aAAa,QACR,YAAW;AAClB,SACEC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,gBAAgB,EAChBC,UAAU,EACVC,eAAe,QACV,6BAA4B;AACnC,SAASC,gBAAgB,QAAQ,6BAA4B;AAC7D,SAASC,SAAS,QAAQ,cAAa;AAEvC,MAAMC,UAAU,IAAIC;AAEpB,MAAMC,eAAe;AAErB,SAASC,0BACPC,cAAmB,EACnBC,IAAgB,EAChBC,OAAoB,EACpBC,YAA8B,EAC9BC,YAAiC,EACjCC,kBAAuC;IAEvC,MAAMC,qBAAqB,IAAIC;IAE/B,IAAIC,qBAAqB;IACzB,MAAMC,eAAe,IAAIF;IACzB,MAAMG,sBAAsB,IAAIH;IAChC,IAAII,WAAW,CAAC;IAChB,IAAIC,iBAAiB,CAAC;IAEtB,SAASC,UAAUC,CAAgC;QACjD,IAAIC,MAAMD,EAAEC,GAAG;QACf,MAAMC,WAAWhB,eAAeiB,SAAS,CAACF;QAE1C,IAAI,CAACC,UAAU;YACb,OAAOF;QACT;QAEA,IAAIxB,UAAUyB,MAAM;YAClB,OAAOD;QACT;QAEAH;QAEAP,aAAac,GAAG,CAACP,UAAUI;QAC3BA,MAAM,CAAC,uCAAuC,EAAEJ,SAAS,EAAE,CAAC;QAE5D,MAAM,GAAGQ,OAAOC,YAAY,GAAGL,IAAIM,KAAK,CAAC,UAAU;QAEnD,MAAMC,aAAaP,IAAIM,KAAK,CAAC;QAC7B,IAAIE;QAEJ,IAAID,WAAWE,MAAM,GAAG,GAAG;YACzBT,MAAMO,WAAWG,GAAG;YACpBF,SAASD,WAAWI,IAAI,CAAC;QAC3B;QAEA,IAAIC,OAAOR,QAAQ,MAAM;QACzBQ,QAAQP,cAAc,CAAC,CAAC,EAAEA,YAAY,CAAC,GAAG;QAE1C,IAAI,CAACZ,oBAAoB;YACvBN,QAAQ0B,IAAI,CAAC;gBACXC,MAAM;gBACNC,YAAY;gBACZf,KAAKgB,KAAKC,SAAS,CACjBC,QAAQC,OAAO,CAAC;gBAElBC,OAAO,CAAC;YACV;YAEA3B,qBAAqB;QACvB;QAEA,MAAM4B,SAASb,SAAS,CAAC,EAAEA,OAAO,CAAC,EAAER,IAAI,CAAC,GAAGA;QAC7C,IAAIe,aAAarB,aAAa4B,GAAG,CAACD;QAElC,IAAI,CAACN,YAAY;YACfA,aAAa,CAAC,yBAAyB,EAAErB,aAAa6B,IAAI,CAAC,GAAG,CAAC;YAC/D7B,aAAaS,GAAG,CAACkB,QAAQN;YAEzB5B,QAAQ0B,IAAI,CAAC;gBACXC,MAAM;gBACNC;gBACAf,KAAKgB,KAAKC,SAAS,CAACI;gBACpBD,OAAOxB;YACT;QACF;QACA,mDAAmD;QACnD,MAAM4B,aAAa;QAEnB,MAAMC,iBAAiBT,KAAKC,SAAS,CAAC;YAAEI;YAAQT;YAAMY;QAAW;QACjE,IAAIE,kBAAkB/B,oBAAoB2B,GAAG,CAACG;QAE9C,IAAI,CAACC,iBAAiB;YACpBA,kBAAkB,CAAC,8BAA8B,EAAE/B,oBAAoB4B,IAAI,CAAC,GAAG,CAAC;YAChF5B,oBAAoBQ,GAAG,CAACsB,gBAAgBC;YAExCtC,aAAayB,IAAI,CAAC;gBAChBa;gBACAX;gBACAH;gBACAY;YACF;QACF;QAEA,OAAO;YACLG,KAAK5B,EAAE4B,GAAG;YACV3B,KAAK0B;QACP;IACF;IAEA,OAAO;QACLE,MAAM;YACJC,QAAOC,IAAS;gBACd,IAAI7C,eAAe8C,YAAY,EAAE;oBAC/B,MAAM9B,WAAWhB,eAAe8C,YAAY,CAC1CD,KAAKE,KAAK,CAAChC,GAAG,EACd8B,KAAKE,KAAK,CAACC,KAAK;oBAGlB,IAAI,CAAChC,UAAU;wBACb,OAAO6B;oBACT;gBACF;gBACA,IAAI9B,MAAM8B,KAAKE,KAAK,CAAChC,GAAG;gBAExBH;gBAEAP,mBAAmBa,GAAG,CAACN,gBAAgBG;gBACvCA,MAAM,CAAC,8CAA8C,EAAEH,eAAe,EAAE,CAAC;gBAEzE,uDAAuD;gBACvD,MAAMoC,QAAQH,KAAKE,KAAK,CAACC,KAAK,CAACC,YAAY,CAACzB,MAAM,GAC9CO,KAAKC,SAAS,CAACa,KAAKE,KAAK,CAACC,KAAK,CAACC,YAAY,IAC5CC;gBACJ,MAAMC,gBAAgB5D,iBAAiBwB;gBACvC,IAAIQ;gBACJ,IAAI4B,eAAe;oBACjB,MAAM7B,aAAaP,IAAIM,KAAK,CAAC;oBAC7B,IAAIC,WAAWE,MAAM,GAAG,GAAG;wBACzBT,MAAMO,WAAWG,GAAG;wBACpBF,SAASD,WAAWI,IAAI,CAAC;oBAC3B;gBACF;gBACA,IAAI,CAACyB,eAAe;oBAClBlD,KAAK2B,IAAI,CAAC;wBAAEb;wBAAKiC;oBAAM;oBACvB,sBAAsB;oBACtB,OAAO;wBAAEnB,MAAM;wBAAWkB,OAAO;oBAAG;gBACtC;gBACA,MAAMX,SAASb,SAAS,CAAC,EAAEA,OAAO,CAAC,EAAER,IAAI,CAAC,GAAGA;gBAC7C,IAAIe,aAAaxB,mBAAmB+B,GAAG,CAACD;gBACxC,IAAI,CAACN,YAAY;oBACfA,aAAa,CAAC,6BAA6B,EAAExB,mBAAmBgC,IAAI,CAAC,GAAG,CAAC;oBACzEhC,mBAAmBY,GAAG,CAACkB,QAAQN;oBAE/B,MAAMsB,YAAYpD,eAAeqD,UAAU,CAACjB;oBAC5ClC,QAAQ0B,IAAI,CAAC;wBACXC,MAAM;wBACNC;wBACAf,KAAKqC;oBACP;gBACF;gBACAnD,KAAK2B,IAAI,CAAC;oBAAEE;oBAAYkB;gBAAM;gBAC9B,sBAAsB;gBACtB,OAAO;oBAAEnB,MAAM;oBAAWkB,OAAO;gBAAG;YACtC;QACF;QACAO,KAAIT,IAAS;YACX,OAAOhC,UAAUgC;QACnB;IACF;AACF;AAEA,SAASU,kBAAkB,EACzBtD,IAAI,EACJC,OAAO,EACPC,YAAY,EACZC,YAAY,EACZiD,UAAU,EAOX;IACC,IAAIlB,QAAQ,CAAC;IACb,IAAIqB,mBAAmB,CAAC;IAExB,OAAO;QACLC,aAAa;YACXC,UAASb,IAAS;gBAChB,IAAIA,KAAKc,QAAQ,KAAK,YAAY;oBAChC;gBACF;gBAEA,MAAMC,YAAYf,KAAKE,KAAK,CAACc,IAAI;gBAEjC,IAAID,CAAAA,6BAAAA,UAAW/B,IAAI,MAAK,QAAQ;oBAC9B;gBACF;gBAEA,IAAId,MAAM6C,UAAUb,KAAK;gBACzB,IAAI,CAAChC,KAAK;oBACR;gBACF;gBAEAoB;gBAEA/B,aAAac,GAAG,CAACiB,OAAOpB;gBACxBA,MAAM,CAAC,4CAA4C,EAAEoB,MAAM,EAAE,CAAC;gBAE9D,MAAML,aAAa,CAAC,0BAA0B,EAAE5B,QAAQsB,MAAM,CAAC,GAAG,CAAC;gBACnEtB,QAAQ0B,IAAI,CAAC;oBACXC,MAAM;oBACNC;oBACAgC,MAAM;oBACN/C,KAAKsC,WAAWtC;oBAChBoB;gBACF;gBAEAlC,KAAK2B,IAAI,CAAC;oBAAEE;oBAAYiC,QAAQ;oBAAM5B;gBAAM;gBAE5C,MAAM6B,WAAqB,EAAE;gBAE7B,KAAK,MAAMC,aAAapB,KAAKE,KAAK,CAACmB,KAAK,CAAE;oBACxCV;oBACA,MAAMf,kBAAkB,CAAC,0BAA0B,EAAEN,MAAM,aAAa,EAAEqB,iBAAiB,GAAG,CAAC;oBAE/FrD,aAAayB,IAAI,CAAC;wBAChBa;wBACAX;wBACAmC;oBACF;oBACAD,SAASpC,IAAI,CAACa;gBAChB;gBAEA,OAAO;oBACLkB,UAAU;oBACVZ,OAAO;wBACLL,KAAKG,KAAKE,KAAK,CAACL,GAAG;wBACnBwB,OAAOF;wBACPH,MAAMD;oBACR;gBACF;YACF;QACF;IACF;AACF;AAEA,MAAMO,cAAc,CAAC,mBAAmB,CAAC;AACzC,OAAO,eAAeC,mBAEpBC,MAAc,EACdC,OAA6B;QAuBzBC;IArBJ,MAAMC,OAAO,IAAI,CAACC,KAAK;IACvB,MAAMF,UAAU,IAAI,CAACG,UAAU;IAC/B,MAAM,EAAEC,cAAc,EAAEC,SAASC,WAAW,EAAE,GAAGC,MAAM,GAAGP;IAE1DA,QAAQQ,OAAO,KAAK,CAAC;IAErB,IAAIJ,kBAAkB,OAAOA,eAAeK,YAAY,KAAK,YAAY;QACvER,KACE,IAAIS,UACF,CAAC,CAAC,EAAEd,YAAY,8FAA8F,EAAE,OAAOQ,eAAeK,YAAY,CAAC,CAAC;QAGxJ;IACF;IAEA,MAAME,UAAuB,EAAE;IAC/B,MAAMhF,UAAuB,EAAE;IAC/B,MAAMiF,cAA2B,EAAE;IACnC,MAAMlF,OAAmB,EAAE;IAC3B,MAAME,eAAiC,EAAE;IAEzC,IAAIoE,EAAAA,mBAAAA,QAAQQ,OAAO,qBAAfR,iBAAiBa,gBAAgB,MAAK,MAAM;QAC9ClF,QAAQmF,OAAO,CAAC;YACdxD,MAAM;YACNC,YAAY;YACZf,KAAKrB,iBACH,IAAI,EACJuC,QAAQC,OAAO,CAAC;QAEpB;IACF;IACA,MAAM,EAAEoD,YAAY,EAAE,GAAGrD,QAAQ;IAEjC,MAAMsD,YACJZ,CAAAA,kCAAAA,eAAgBK,YAAY,KAC5B,AAAC,CAAA,MAAMM,cAAa,EAAGE,GAAG,CAACC,SAAS,CAACF,SAAS;IAEhD,MAAMnF,eAAe,IAAIG;IACzB,MAAMmF,mBAAmB,IAAInF;IAC7B,MAAMF,qBAAqB,IAAIE;IAE/B,MAAMoF,mBAAmB5F,0BACvB;QACEsD,YAAY,CAACtC,MACXrB,iBACE,IAAI,EACJL,gBAAgB,IAAI,EAAEkF,QAAQqB,aAAa,IAAI,KAAK7E;QAExDE,WAAW7B,UAAUmF,QAAQxD,GAAG,EAAE,IAAI,CAAC8E,YAAY;QACnD/C,cAAc1D,UAAUmF,QAAQ3B,MAAM,EAAE,IAAI,CAACiD,YAAY;QAEzDC,SAAS,IAAI,CAACA,OAAO;IACvB,GACA7F,MACAC,SACAC,cACAC,cACAC;IAGF,MAAM0F,cAAcxC,kBAAkB;QACpCtD;QACAC,SAASiF;QACThF;QACAC,cAAcsF;QACdrC,YAAY,CAACtC,MACXrB,iBACE,IAAI,EACJL,gBAAgB,IAAI,EAAEkF,QAAQqB,aAAa,IAAI7E;IAErD;IAEA,uDAAuD;IACvD,gGAAgG;IAChG,iCAAiC;IACjC,MAAMiF,UAAU;QACd,GAAGL,gBAAgB;QACnB,GAAGI,WAAW;IAChB;IAEA,IAAI;QACF,MAAM,EACJE,IAAI,EACJC,GAAG,EACHhB,SAASiB,aAAa,EACvB,GAAGZ,UAAU;YACZ,GAAGT,IAAI;YACPkB;YACAI,YACE7B,QAAQQ,OAAO,IAAIjF,aAAauG,IAAI,CAAC,IAAI,CAACR,YAAY,IAClD;gBACES,SAASC,QAAQC,GAAG,CAACC,gBAAgB,GACjC,oBACA;YACN,IACAvD;YACNwD,UAAU,IAAI,CAACb,YAAY;YAC3BI,MAAMrG,QAAQ+G,MAAM,CAACtC;YACrBuC,WAAW,IAAI,CAACA,SAAS;YACzBhC,SAAS5F,WAAW;gBAAE4F,SAASC;gBAAagC,KAAKlH,UAAUmH,MAAM;YAAC;YAClEC,gBACE,IAAI,CAACH,SAAS,IAAItC,UAAUvC,KAAKC,SAAS,CAACsC,WAAWpB;YACxD8D,SAAS;QACX;QACA,IAAIC,kBAAkBhB,KAAKiB,QAAQ;QAEnC,IAAIf,eAAe;YACjB,IAAK,MAAMgB,QAAQhB,cAAe;gBAChC,IAAIiB,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACpB,eAAegB,OAAO;oBAC7D,MAAMK,IAAIrB,aAAa,CAACgB,KAAK;oBAC7B,IAAIpE,QAAQyE,EAAEL,IAAI;oBAClB,KAAK,MAAMM,WAAWD,EAAE9D,QAAQ,CAAE;wBAChCX,SAAS,CAAC,CAAC,EAAE0E,QAAQN,IAAI,CAAC,CAAC;oBAC7B;oBAEAjC,QAAQtD,IAAI,CAAC;wBACXuF;wBACApE;oBACF;gBACF;YACF;QACF;QAEA,IAAI3C,aAAakC,IAAI,KAAK,GAAG;YAC3B,MAAMoF,cAAc,IAAI,CAACC,UAAU,CAAC;gBAClCC,gBAAgB;oBAAC;iBAAQ;gBACzBC,YAAY;oBAAC;iBAAQ;gBACrBC,WAAW,EAAE;gBACbC,YAAY,EAAE;YAChB;YAEA,KAAK,MAAM,CAAC5F,OAAOpB,IAAI,IAAIX,aAAa4H,OAAO,GAAI;gBACjD,MAAM,CAACC,SAAY,GAAGlH,IAAIM,KAAK,CAAC,UAAU;gBAE1C,MAAM6G,UAAU1I,WAAWyI,UAAU,IAAI,CAACE,WAAW;gBACrD,MAAMC,cAAc,MAAM3I,gBAAgBiI,aAAa,IAAI,CAAC5B,OAAO,EAAE;uBAChE,IAAIuC,IAAI;wBAACH;wBAASnH;qBAAI;iBAC1B;gBAED,KAAK,MAAMuH,cAAcpI,QAAS;oBAChCoI,WAAWvH,GAAG,GAAGuH,WAAWvH,GAAG,CAACwH,OAAO,CACrC,CAAC,uCAAuC,EAAEpG,MAAM,EAAE,CAAC,EACnDiG,eAAerH;gBAEnB;YACF;QACF;QAEA,IAAIV,mBAAmBiC,IAAI,KAAK,GAAG;YACjC,MAAMkG,iBAAiB,IAAI,CAACb,UAAU,CAAC;gBACrCC,gBAAgB;oBAAC;iBAAQ;gBACzBG,YAAY;oBAAC;iBAAO;gBACpBF,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;gBAC3BW,cAAc;oBAAC;iBAAU;YAC3B;YAEA,KAAK,MAAM,CAACtG,OAAOpB,IAAI,IAAIV,mBAAmB2H,OAAO,GAAI;gBACvD,MAAM,CAACC,SAAY,GAAGlH,IAAIM,KAAK,CAAC,UAAU;gBAE1C,MAAM6G,UAAU1I,WAAWyI,UAAU,IAAI,CAACE,WAAW;gBACrD,MAAMC,cAAc,MAAM3I,gBACxB+I,gBACA,IAAI,CAAC1C,OAAO,EACZ;uBAAI,IAAIuC,IAAI;wBAACH;wBAASnH;qBAAI;iBAAE;gBAG9B,KAAK,MAAMuH,cAAcpI,QAAS;oBAChCoI,WAAWvH,GAAG,GAAGuH,WAAWvH,GAAG,CAACwH,OAAO,CACrC,CAAC,8CAA8C,EAAEpG,MAAM,EAAE,CAAC,EAC1DiG,eAAerH;gBAEnB;YACF;QACF;QACA,IAAI2E,iBAAiBpD,IAAI,KAAK,GAAG;YAC/B,MAAMoG,eAAe,IAAI,CAACf,UAAU,CAAC;gBACnCC,gBAAgB;oBAAC;iBAAQ;gBACzBG,YAAY,EAAE;gBACdF,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;YAC7B;YAEA,KAAK,MAAM,CAAC3F,OAAOpB,IAAI,IAAI2E,iBAAiBsC,OAAO,GAAI;gBACrD,MAAM,CAACC,SAAY,GAAGlH,IAAIM,KAAK,CAAC,UAAU;gBAE1C,MAAM6G,UAAU1I,WAAWyI,UAAU,IAAI,CAACE,WAAW;gBACrD,MAAMC,cAAc,MAAM3I,gBAAgBiJ,cAAc,IAAI,CAAC5C,OAAO,EAAE;uBACjE,IAAIuC,IAAI;wBAACtH;wBAAKmH;qBAAQ;iBAC1B;gBAED,KAAK,MAAMI,cAAcnD,YAAa;oBACpCmD,WAAWvH,GAAG,GAAGuH,WAAWvH,GAAG,CAACwH,OAAO,CACrC,CAAC,4CAA4C,EAAEpG,MAAM,EAAE,CAAC,EACxDiG,eAAerH;gBAEnB;YACF;QACF;QAEAb,QAAQ0B,IAAI,IAAIuD;QAEhB,MAAMwD,aAAa1J,cAAciB,SAASqE;QAC1C,MAAMqE,aAAa1J,cACjB;YAAEsG,KAAKyB;YAAiBf;QAAI,GAC5BjG,MACAE,cACAoE,SACA,IAAI;QAEN,MAAMsE,aAAa1J,cAAc+F,SAAS/E,cAAcoE;QAExD,MAAMuE,SAAS,CAAC,EAAEH,WAAW,EAAEC,WAAW,EAAEC,WAAW,CAAC;QAExDrE,KAAK,MAAMsE,QAAQ5C,OAAOnE,KAAKgH,KAAK,CAAC7C,IAAIgB,QAAQ;IACnD,EAAE,OAAO8B,OAAgB;QACvBC,QAAQD,KAAK,CAAC,6BAA6BA;QAC3CxE,KAAKwE;IACP;AACF;AAEA,OAAO,MAAME,MAAM,KAAI"}