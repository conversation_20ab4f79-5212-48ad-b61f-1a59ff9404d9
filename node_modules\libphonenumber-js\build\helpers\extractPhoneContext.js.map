{"version": 3, "file": "extractPhoneContext.js", "names": ["_constants", "require", "PLUS_SIGN", "exports", "RFC3966_VISUAL_SEPARATOR_", "RFC3966_PHONE_DIGIT_", "VALID_DIGITS", "RFC3966_GLOBAL_NUMBER_DIGITS_", "RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_", "RegExp", "ALPHANUM_", "RFC3966_DOMAINLABEL_", "VALID_ALPHA_", "RFC3966_TOPLABEL_", "RFC3966_DOMAINNAME_", "RFC3966_DOMAINNAME_PATTERN_", "RFC3966_PREFIX_", "RFC3966_PHONE_CONTEXT_", "RFC3966_ISDN_SUBADDRESS_", "extractPhoneContext", "numberToExtractFrom", "indexOfPhoneContext", "indexOf", "phoneContextStart", "length", "phoneContextEnd", "substring", "isPhoneContextValid", "phoneContext", "test"], "sources": ["../../source/helpers/extractPhoneContext.js"], "sourcesContent": ["// When phone numbers are written in `RFC3966` format — `\"tel:+12133734253\"` —\r\n// they can have their \"calling code\" part written separately in a `phone-context` parameter.\r\n// Example: `\"tel:12133734253;phone-context=+1\"`.\r\n// This function parses the full phone number from the local number and the `phone-context`\r\n// when the `phone-context` contains a `+` sign.\r\n\r\nimport {\r\n  VALID_DIGITS,\r\n  // PLUS_CHARS\r\n} from '../constants.js'\r\n\r\nexport const PLUS_SIGN = '+'\r\n\r\nconst RFC3966_VISUAL_SEPARATOR_ = '[\\\\-\\\\.\\\\(\\\\)]?'\r\n\r\nconst RFC3966_PHONE_DIGIT_ = '(' + '[' + VALID_DIGITS + ']' + '|' + RFC3966_VISUAL_SEPARATOR_ + ')'\r\n\r\nconst RFC3966_GLOBAL_NUMBER_DIGITS_ =\r\n\t'^' +\r\n\t'\\\\' +\r\n\tPLUS_SIGN +\r\n\tRFC3966_PHONE_DIGIT_ +\r\n\t'*' +\r\n\t'[' + VALID_DIGITS +  ']' +\r\n\tRFC3966_PHONE_DIGIT_ +\r\n\t'*' +\r\n\t'$'\r\n\r\n/**\r\n * Regular expression of valid global-number-digits for the phone-context\r\n * parameter, following the syntax defined in RFC3966.\r\n */\r\nconst RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_ = new RegExp(RFC3966_GLOBAL_NUMBER_DIGITS_, 'g')\r\n\r\n// In this port of Google's library, we don't accept alpha characters in phone numbers.\r\n// const ALPHANUM_ = VALID_ALPHA_ + VALID_DIGITS\r\nconst ALPHANUM_ = VALID_DIGITS\r\n\r\nconst RFC3966_DOMAINLABEL_ = '[' + ALPHANUM_ + ']+((\\\\-)*[' + ALPHANUM_ + '])*'\r\n\r\nconst VALID_ALPHA_ = 'a-zA-Z'\r\nconst RFC3966_TOPLABEL_ = '[' + VALID_ALPHA_ + ']+((\\\\-)*[' + ALPHANUM_ + '])*'\r\n\r\nconst RFC3966_DOMAINNAME_ = '^(' + RFC3966_DOMAINLABEL_ + '\\\\.)*' + RFC3966_TOPLABEL_ + '\\\\.?$'\r\n\r\n/**\r\n * Regular expression of valid domainname for the phone-context parameter,\r\n * following the syntax defined in RFC3966.\r\n */\r\nconst RFC3966_DOMAINNAME_PATTERN_ = new RegExp(RFC3966_DOMAINNAME_, 'g')\r\n\r\nexport const RFC3966_PREFIX_ = 'tel:'\r\nexport const RFC3966_PHONE_CONTEXT_ = ';phone-context='\r\nexport const RFC3966_ISDN_SUBADDRESS_ = ';isub='\r\n\r\n/**\r\n * Extracts the value of the phone-context parameter of `numberToExtractFrom`,\r\n * following the syntax defined in RFC3966.\r\n *\r\n * @param {string} numberToExtractFrom\r\n * @return {string|null} the extracted string (possibly empty), or `null` if no phone-context parameter is found.\r\n */\r\nexport default function extractPhoneContext(numberToExtractFrom) {\r\n\tconst indexOfPhoneContext = numberToExtractFrom.indexOf(RFC3966_PHONE_CONTEXT_)\r\n\t// If no phone-context parameter is present\r\n\tif (indexOfPhoneContext < 0) {\r\n\t\treturn null\r\n\t}\r\n\r\n\tconst phoneContextStart = indexOfPhoneContext + RFC3966_PHONE_CONTEXT_.length\r\n\t// If phone-context parameter is empty\r\n\tif (phoneContextStart >= numberToExtractFrom.length) {\r\n\t\treturn ''\r\n\t}\r\n\r\n\tconst phoneContextEnd = numberToExtractFrom.indexOf(';', phoneContextStart)\r\n\t// If phone-context is not the last parameter\r\n\tif (phoneContextEnd >= 0) {\r\n\t\treturn numberToExtractFrom.substring(phoneContextStart, phoneContextEnd)\r\n\t} else {\r\n\t\treturn numberToExtractFrom.substring(phoneContextStart)\r\n\t}\r\n}\r\n\r\n/**\r\n * Returns whether the value of phoneContext follows the syntax defined in RFC3966.\r\n *\r\n * @param {string|null} phoneContext\r\n * @return {boolean}\r\n */\r\nexport function isPhoneContextValid(phoneContext) {\r\n\tif (phoneContext === null) {\r\n\t\treturn true\r\n\t}\r\n\r\n\tif (phoneContext.length === 0) {\r\n\t\treturn false\r\n\t}\r\n\r\n\t// Does phone-context value match pattern of global-number-digits or domainname.\r\n\treturn RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_.test(phoneContext) ||\r\n\t\tRFC3966_DOMAINNAME_PATTERN_.test(phoneContext)\r\n}"], "mappings": ";;;;;;;;AAMA,IAAAA,UAAA,GAAAC,OAAA;AANA;AACA;AACA;AACA;AACA;;AAOO,IAAMC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG,GAAG;AAE5B,IAAME,yBAAyB,GAAG,iBAAiB;AAEnD,IAAMC,oBAAoB,GAAG,GAAG,GAAG,GAAG,GAAGC,uBAAY,GAAG,GAAG,GAAG,GAAG,GAAGF,yBAAyB,GAAG,GAAG;AAEnG,IAAMG,6BAA6B,GAClC,GAAG,GACH,IAAI,GACJL,SAAS,GACTG,oBAAoB,GACpB,GAAG,GACH,GAAG,GAAGC,uBAAY,GAAI,GAAG,GACzBD,oBAAoB,GACpB,GAAG,GACH,GAAG;;AAEJ;AACA;AACA;AACA;AACA,IAAMG,qCAAqC,GAAG,IAAIC,MAAM,CAACF,6BAA6B,EAAE,GAAG,CAAC;;AAE5F;AACA;AACA,IAAMG,SAAS,GAAGJ,uBAAY;AAE9B,IAAMK,oBAAoB,GAAG,GAAG,GAAGD,SAAS,GAAG,YAAY,GAAGA,SAAS,GAAG,KAAK;AAE/E,IAAME,YAAY,GAAG,QAAQ;AAC7B,IAAMC,iBAAiB,GAAG,GAAG,GAAGD,YAAY,GAAG,YAAY,GAAGF,SAAS,GAAG,KAAK;AAE/E,IAAMI,mBAAmB,GAAG,IAAI,GAAGH,oBAAoB,GAAG,OAAO,GAAGE,iBAAiB,GAAG,OAAO;;AAE/F;AACA;AACA;AACA;AACA,IAAME,2BAA2B,GAAG,IAAIN,MAAM,CAACK,mBAAmB,EAAE,GAAG,CAAC;AAEjE,IAAME,eAAe,GAAAb,OAAA,CAAAa,eAAA,GAAG,MAAM;AAC9B,IAAMC,sBAAsB,GAAAd,OAAA,CAAAc,sBAAA,GAAG,iBAAiB;AAChD,IAAMC,wBAAwB,GAAAf,OAAA,CAAAe,wBAAA,GAAG,QAAQ;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASC,mBAAmBA,CAACC,mBAAmB,EAAE;EAChE,IAAMC,mBAAmB,GAAGD,mBAAmB,CAACE,OAAO,CAACL,sBAAsB,CAAC;EAC/E;EACA,IAAII,mBAAmB,GAAG,CAAC,EAAE;IAC5B,OAAO,IAAI;EACZ;EAEA,IAAME,iBAAiB,GAAGF,mBAAmB,GAAGJ,sBAAsB,CAACO,MAAM;EAC7E;EACA,IAAID,iBAAiB,IAAIH,mBAAmB,CAACI,MAAM,EAAE;IACpD,OAAO,EAAE;EACV;EAEA,IAAMC,eAAe,GAAGL,mBAAmB,CAACE,OAAO,CAAC,GAAG,EAAEC,iBAAiB,CAAC;EAC3E;EACA,IAAIE,eAAe,IAAI,CAAC,EAAE;IACzB,OAAOL,mBAAmB,CAACM,SAAS,CAACH,iBAAiB,EAAEE,eAAe,CAAC;EACzE,CAAC,MAAM;IACN,OAAOL,mBAAmB,CAACM,SAAS,CAACH,iBAAiB,CAAC;EACxD;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,mBAAmBA,CAACC,YAAY,EAAE;EACjD,IAAIA,YAAY,KAAK,IAAI,EAAE;IAC1B,OAAO,IAAI;EACZ;EAEA,IAAIA,YAAY,CAACJ,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAO,KAAK;EACb;;EAEA;EACA,OAAOhB,qCAAqC,CAACqB,IAAI,CAACD,YAAY,CAAC,IAC9Db,2BAA2B,CAACc,IAAI,CAACD,YAAY,CAAC;AAChD", "ignoreList": []}