{"version": 3, "sources": ["../../../src/client/components/redirect.ts"], "names": ["getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "permanentRedirect", "redirect", "REDIRECT_ERROR_CODE", "RedirectType", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "error", "Error", "digest", "requestStore", "requestAsyncStorage", "getStore", "mutableCookies", "actionStore", "actionAsyncStorage", "isAction", "<PERSON><PERSON><PERSON>", "PermanentRedirect", "errorCode", "destination", "status", "split", "Number", "isNaN"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAiBgBA,gBAAgB;eAAhBA;;IAoIAC,8BAA8B;eAA9BA;;IAVAC,wBAAwB;eAAxBA;;IARAC,uBAAuB;eAAvBA;;IAnCAC,eAAe;eAAfA;;IAzBAC,iBAAiB;eAAjBA;;IA7BAC,QAAQ;eAARA;;;6CA1CoB;4CAED;oCACA;AAEnC,MAAMC,sBAAsB;;UAEhBC;;;GAAAA,iBAAAA;AAUL,SAASR,iBACdS,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,uBAAAA,aAAiCC,sCAAkB,CAACC,iBAAiB;IAErE,MAAMC,QAAQ,IAAIC,MAAMR;IACxBO,MAAME,MAAM,GAAG,AAAGT,sBAAoB,MAAGG,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,MAAMM,eAAeC,gDAAmB,CAACC,QAAQ;IACjD,IAAIF,cAAc;QAChBH,MAAMM,cAAc,GAAGH,aAAaG,cAAc;IACpD;IACA,OAAON;AACT;AAaO,SAASR,SACd,2BAA2B,GAC3BG,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA;IAEA,MAAMW,cAAcC,8CAAkB,CAACH,QAAQ;IAC/C,MAAMnB,iBACJS,KACAC,MACA,uDAAuD;IACvD,4DAA4D;IAC5D,kDAAkD;IAClDW,CAAAA,+BAAAA,YAAaE,QAAQ,IACjBX,sCAAkB,CAACY,QAAQ,GAC3BZ,sCAAkB,CAACC,iBAAiB;AAE5C;AAaO,SAASR,kBACd,2BAA2B,GAC3BI,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,iBAAAA;IAEA,MAAMW,cAAcC,8CAAkB,CAACH,QAAQ;IAC/C,MAAMnB,iBACJS,KACAC,MACA,uDAAuD;IACvD,4DAA4D;IAC5D,kDAAkD;IAClDW,CAAAA,+BAAAA,YAAaE,QAAQ,IACjBX,sCAAkB,CAACY,QAAQ,GAC3BZ,sCAAkB,CAACa,iBAAiB;AAE5C;AASO,SAASrB,gBACdU,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAME,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAM,CAACU,WAAWhB,MAAMiB,aAAaC,OAAO,GAAGd,MAAME,MAAM,CAACa,KAAK,CAAC,KAAK;IAEvE,MAAMlB,aAAamB,OAAOF;IAE1B,OACEF,cAAcnB,uBACbG,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOiB,gBAAgB,YACvB,CAACI,MAAMpB,eACPA,cAAcC,sCAAkB;AAEpC;AAYO,SAAST,wBAAwBW,KAAc;IACpD,IAAI,CAACV,gBAAgBU,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACa,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAAS3B,yBACdY,KAAuB;IAEvB,IAAI,CAACV,gBAAgBU,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOD,MAAME,MAAM,CAACa,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAAS5B,+BACda,KAAuB;IAEvB,IAAI,CAACV,gBAAgBU,QAAQ;QAC3B,MAAM,IAAIC,MAAM;IAClB;IAEA,OAAOe,OAAOhB,MAAME,MAAM,CAACa,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AAC7C"}