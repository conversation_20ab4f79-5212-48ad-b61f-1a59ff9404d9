{"version": 3, "file": "IsPassportNumber.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsPassportNumber.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,yBAAyB,MAAM,gCAAgC,CAAC;AAEvE,MAAM,CAAC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC;AAErD;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAc,EAAE,WAAmB;IAClE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,yBAAyB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACpF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,WAAmB,EAAE,iBAAqC;IACzF,OAAO,UAAU,CACf;QACE,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,CAAC,WAAW,CAAC;QAC1B,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACjF,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,yCAAyC,EACpE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isPassportNumberValidator from 'validator/lib/isPassportNumber';\n\nexport const IS_PASSPORT_NUMBER = 'isPassportNumber';\n\n/**\n * Check if the string is a valid passport number relative to a specific country code.\n * If given value is not a string, then it returns false.\n */\nexport function isPassportNumber(value: unknown, countryCode: string): boolean {\n  return typeof value === 'string' && isPassportNumberValidator(value, countryCode);\n}\n\n/**\n * Check if the string is a valid passport number relative to a specific country code.\n * If given value is not a string, then it returns false.\n */\nexport function IsPassportNumber(countryCode: string, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_PASSPORT_NUMBER,\n      constraints: [countryCode],\n      validator: {\n        validate: (value, args): boolean => isPassportNumber(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be valid passport number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}