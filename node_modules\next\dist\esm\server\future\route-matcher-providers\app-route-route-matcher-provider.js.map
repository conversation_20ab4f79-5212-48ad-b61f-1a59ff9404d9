{"version": 3, "sources": ["../../../../src/server/future/route-matcher-providers/app-route-route-matcher-provider.ts"], "names": ["isAppRouteRoute", "APP_PATHS_MANIFEST", "RouteKind", "AppRouteRouteMatcher", "ManifestRouteMatcherProvider", "AppNormalizers", "AppRouteRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "normalizers", "transform", "manifest", "pages", "Object", "keys", "filter", "page", "matchers", "filename", "normalize", "pathname", "bundlePath", "push", "kind", "APP_ROUTE"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kCAAiC;AACjE,SAASC,kBAAkB,QAAQ,gCAA+B;AAClE,SAASC,SAAS,QAAQ,gBAAe;AACzC,SAASC,oBAAoB,QAAQ,4CAA2C;AAKhF,SAASC,4BAA4B,QAAQ,oCAAmC;AAChF,SAASC,cAAc,QAAQ,2BAA0B;AAEzD,OAAO,MAAMC,qCAAqCF;IAGhDG,YAAYC,OAAe,EAAEC,cAA8B,CAAE;QAC3D,KAAK,CAACR,oBAAoBQ;QAE1B,IAAI,CAACC,WAAW,GAAG,IAAIL,eAAeG;IACxC;IAEA,MAAgBG,UACdC,QAAkB,EAC4B;QAC9C,wCAAwC;QACxC,MAAMC,QAAQC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,OAASjB,gBAAgBiB;QAErE,qBAAqB;QACrB,MAAMC,WAAwC,EAAE;QAChD,KAAK,MAAMD,QAAQJ,MAAO;YACxB,MAAMM,WAAW,IAAI,CAACT,WAAW,CAACS,QAAQ,CAACC,SAAS,CAACR,QAAQ,CAACK,KAAK;YACnE,MAAMI,WAAW,IAAI,CAACX,WAAW,CAACW,QAAQ,CAACD,SAAS,CAACH;YACrD,MAAMK,aAAa,IAAI,CAACZ,WAAW,CAACY,UAAU,CAACF,SAAS,CAACH;YAEzDC,SAASK,IAAI,CACX,IAAIpB,qBAAqB;gBACvBqB,MAAMtB,UAAUuB,SAAS;gBACzBJ;gBACAJ;gBACAK;gBACAH;YACF;QAEJ;QAEA,OAAOD;IACT;AACF"}