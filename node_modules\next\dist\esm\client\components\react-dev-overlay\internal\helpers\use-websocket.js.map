{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-websocket.ts"], "names": ["useCallback", "useContext", "useEffect", "useRef", "GlobalLayoutRouterContext", "getSocketUrl", "useWebsocket", "assetPrefix", "webSocketRef", "current", "url", "window", "WebSocket", "useSendMessage", "sendMessage", "data", "socket", "readyState", "OPEN", "send", "useTurbopack", "onUpdateError", "turbopackState", "init", "queue", "callback", "undefined", "processTurbopackMessage", "msg", "push", "initCurrent", "then", "connect", "addMessageListener", "cb", "useWebsocketPing", "websocketRef", "tree", "interval", "setInterval", "JSON", "stringify", "event", "appDirRoute", "clearInterval"], "mappings": "AAAA,SAASA,WAAW,EAAEC,UAAU,EAAEC,SAAS,EAAEC,MAAM,QAAQ,QAAO;AAClE,SAASC,yBAAyB,QAAQ,8DAA6D;AACvG,SAASC,YAAY,QAAQ,mBAAkB;AAG/C,OAAO,SAASC,aAAaC,WAAmB;IAC9C,MAAMC,eAAeL;IAErBD,UAAU;QACR,IAAIM,aAAaC,OAAO,EAAE;YACxB;QACF;QAEA,MAAMC,MAAML,aAAaE;QAEzBC,aAAaC,OAAO,GAAG,IAAIE,OAAOC,SAAS,CAAC,AAAC,KAAEF,MAAI;IACrD,GAAG;QAACH;KAAY;IAEhB,OAAOC;AACT;AAEA,OAAO,SAASK,eAAeL,YAA6C;IAC1E,MAAMM,cAAcd,YAClB,CAACe;QACC,MAAMC,SAASR,aAAaC,OAAO;QACnC,IAAI,CAACO,UAAUA,OAAOC,UAAU,KAAKD,OAAOE,IAAI,EAAE;YAChD;QACF;QACA,OAAOF,OAAOG,IAAI,CAACJ;IACrB,GACA;QAACP;KAAa;IAEhB,OAAOM;AACT;AAEA,OAAO,SAASM,aACdN,WAA8C,EAC9CO,aAAqC;IAErC,MAAMC,iBAAiBnB,OAIpB;QACDoB,MAAM;QACN,0FAA0F;QAC1FC,OAAO,EAAE;QACTC,UAAUC;IACZ;IAEA,MAAMC,0BAA0B3B,YAAY,CAAC4B;QAC3C,MAAM,EAAEH,QAAQ,EAAED,KAAK,EAAE,GAAGF,eAAeb,OAAO;QAClD,IAAIgB,UAAU;YACZA,SAASG;QACX,OAAO;YACLJ,MAAOK,IAAI,CAACD;QACd;IACF,GAAG,EAAE;IAEL1B,UAAU;QACR,MAAM,EAAEO,SAASqB,WAAW,EAAE,GAAGR;QACjC,2DAA2D;QAC3D,IAAIQ,YAAYP,IAAI,EAAE;YACpB;QACF;QACAO,YAAYP,IAAI,GAAG;QAEnB,MAAM,CACJ,gGAAgG;QAChG,iEACAQ,IAAI,CAAC;gBAAC,EAAEC,OAAO,EAAE;YACjB,MAAM,EAAEvB,OAAO,EAAE,GAAGa;YACpBU,QAAQ;gBACNC,oBAAmBC,EAAwC;oBACzDzB,QAAQgB,QAAQ,GAAGS;oBAEnB,iFAAiF;oBACjF,KAAK,MAAMN,OAAOnB,QAAQe,KAAK,CAAG;wBAChCU,GAAGN;oBACL;oBACAnB,QAAQe,KAAK,GAAGE;gBAClB;gBACAZ;gBACAO;YACF;QACF;IACF,GAAG;QAACP;QAAaO;KAAc;IAE/B,OAAOM;AACT;AAEA,OAAO,SAASQ,iBACdC,YAA6C;IAE7C,MAAMtB,cAAcD,eAAeuB;IACnC,MAAM,EAAEC,IAAI,EAAE,GAAGpC,WAAWG;IAE5BF,UAAU;QACR,yCAAyC;QACzC,MAAMoC,WAAWC,YAAY;YAC3BzB,YACE0B,KAAKC,SAAS,CAAC;gBACbC,OAAO;gBACPL;gBACAM,aAAa;YACf;QAEJ,GAAG;QACH,OAAO,IAAMC,cAAcN;IAC7B,GAAG;QAACD;QAAMvB;KAAY;AACxB"}