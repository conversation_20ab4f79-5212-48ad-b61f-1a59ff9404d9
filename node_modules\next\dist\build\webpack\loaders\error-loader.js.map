{"version": 3, "sources": ["../../../../src/build/webpack/loaders/error-loader.ts"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "getOptions", "reason", "resource", "_module", "issuer", "context", "rootContext", "_compiler", "path", "relative", "err", "Error", "cyan", "emitError"], "mappings": ";;;;+BAwBA;;;eAAA;;;4BAxBqB;6DACJ;;;;;;AAGjB,MAAMA,cAAgD;QAOnC,sBAAA,eACmB;IAPpC,oBAAoB;IACpB,MAAMC,UAAU,IAAI,CAACC,UAAU,MAAO,CAAC;IAEvC,MAAM,EAAEC,SAAS,+BAA+B,EAAE,GAAGF;IAErD,mBAAmB;IACnB,MAAMG,WAAW,EAAA,gBAAA,IAAI,CAACC,OAAO,sBAAZ,uBAAA,cAAcC,MAAM,qBAApB,qBAAsBF,QAAQ,KAAI;IACnD,MAAMG,UAAU,IAAI,CAACC,WAAW,MAAI,kBAAA,IAAI,CAACC,SAAS,qBAAd,gBAAgBF,OAAO;IAE3D,MAAMD,SAASF,WACXG,UACEG,aAAI,CAACC,QAAQ,CAACJ,SAASH,YACv<PERSON>,WACF;IAEJ,MAAMQ,MAAM,IAAIC,MAAMV,SAAUG,CAAAA,SAAS,CAAC,YAAY,EAAEQ,IAAAA,gBAAI,EAACR,QAAQ,CAAC,GAAG,EAAC;IAC1E,IAAI,CAACS,SAAS,CAACH;AACjB;MAEA,WAAeZ"}