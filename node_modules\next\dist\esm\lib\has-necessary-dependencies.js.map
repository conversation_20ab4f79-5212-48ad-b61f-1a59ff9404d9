{"version": 3, "sources": ["../../src/lib/has-necessary-dependencies.ts"], "names": ["existsSync", "promises", "fs", "resolveFrom", "dirname", "join", "relative", "hasNecessaryDependencies", "baseDir", "requiredPackages", "resolutions", "Map", "missingPackages", "Promise", "all", "map", "p", "pkgPath", "realpath", "pkg", "pkgDir", "exportsRestrict", "fileNameToVerify", "file", "fileToVerify", "set", "push", "_", "resolved", "missing"], "mappings": "AAAA,SAASA,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,OAAM;AAa9C,OAAO,eAAeC,yBACpBC,OAAe,EACfC,gBAAqC;IAErC,IAAIC,cAAc,IAAIC;IACtB,MAAMC,kBAAuC,EAAE;IAE/C,MAAMC,QAAQC,GAAG,CACfL,iBAAiBM,GAAG,CAAC,OAAOC;QAC1B,IAAI;YACF,MAAMC,UAAU,MAAMf,GAAGgB,QAAQ,CAC/Bf,YAAYK,SAAS,CAAC,EAAEQ,EAAEG,GAAG,CAAC,aAAa,CAAC;YAE9C,MAAMC,SAAShB,QAAQa;YAEvB,IAAID,EAAEK,eAAe,EAAE;gBACrB,MAAMC,mBAAmBhB,SAASU,EAAEG,GAAG,EAAEH,EAAEO,IAAI;gBAC/C,IAAID,kBAAkB;oBACpB,MAAME,eAAenB,KAAKe,QAAQE;oBAClC,IAAItB,WAAWwB,eAAe;wBAC5Bd,YAAYe,GAAG,CAACT,EAAEG,GAAG,EAAEK;oBACzB,OAAO;wBACL,OAAOZ,gBAAgBc,IAAI,CAACV;oBAC9B;gBACF,OAAO;oBACLN,YAAYe,GAAG,CAACT,EAAEG,GAAG,EAAEF;gBACzB;YACF,OAAO;gBACLP,YAAYe,GAAG,CAACT,EAAEG,GAAG,EAAEhB,YAAYK,SAASQ,EAAEO,IAAI;YACpD;QACF,EAAE,OAAOI,GAAG;YACV,OAAOf,gBAAgBc,IAAI,CAACV;QAC9B;IACF;IAGF,OAAO;QACLY,UAAUlB;QACVmB,SAASjB;IACX;AACF"}