{"version": 3, "sources": ["../../src/lib/detect-typo.ts"], "names": ["minDistance", "a", "b", "threshold", "m", "length", "n", "previousRow", "Array", "from", "_", "i", "s1", "currentRow", "j", "s2", "insertions", "deletions", "substitutions", "Number", "push", "Math", "min", "detectTypo", "input", "options", "potentialTypos", "map", "o", "option", "distance", "filter", "sort"], "mappings": "AAAA,6EAA6E;AAC7E,SAASA,YAAYC,CAAS,EAAEC,CAAS,EAAEC,SAAiB;IAC1D,MAAMC,IAAIH,EAAEI,MAAM;IAClB,MAAMC,IAAIJ,EAAEG,MAAM;IAElB,IAAID,IAAIE,GAAG;QACT,OAAON,YAAYE,GAAGD,GAAGE;IAC3B;IAEA,IAAIG,MAAM,GAAG;QACX,OAAOF;IACT;IAEA,IAAIG,cAAcC,MAAMC,IAAI,CAAC;QAAEJ,QAAQC,IAAI;IAAE,GAAG,CAACI,GAAGC,IAAMA;IAE1D,IAAK,IAAIA,IAAI,GAAGA,IAAIP,GAAGO,IAAK;QAC1B,MAAMC,KAAKX,CAAC,CAACU,EAAE;QACf,IAAIE,aAAa;YAACF,IAAI;SAAE;QACxB,IAAK,IAAIG,IAAI,GAAGA,IAAIR,GAAGQ,IAAK;YAC1B,MAAMC,KAAKb,CAAC,CAACY,EAAE;YACf,MAAME,aAAaT,WAAW,CAACO,IAAI,EAAE,GAAG;YACxC,MAAMG,YAAYJ,UAAU,CAACC,EAAE,GAAG;YAClC,MAAMI,gBAAgBX,WAAW,CAACO,EAAE,GAAGK,OAAOP,OAAOG;YACrDF,WAAWO,IAAI,CAACC,KAAKC,GAAG,CAACN,YAAYC,WAAWC;QAClD;QACAX,cAAcM;IAChB;IACA,OAAON,WAAW,CAACA,YAAYF,MAAM,GAAG,EAAE;AAC5C;AAEA,OAAO,SAASkB,WAAWC,KAAa,EAAEC,OAAiB,EAAEtB,YAAY,CAAC;IACxE,MAAMuB,iBAAiBD,QACpBE,GAAG,CAAC,CAACC,IAAO,CAAA;YACXC,QAAQD;YACRE,UAAU9B,YAAY4B,GAAGJ,OAAOrB;QAClC,CAAA,GACC4B,MAAM,CAAC,CAAC,EAAED,QAAQ,EAAE,GAAKA,YAAY3B,aAAa2B,WAAW,GAC7DE,IAAI,CAAC,CAAC/B,GAAGC,IAAMD,EAAE6B,QAAQ,GAAG5B,EAAE4B,QAAQ;IAEzC,IAAIJ,eAAerB,MAAM,EAAE;QACzB,OAAOqB,cAAc,CAAC,EAAE,CAACG,MAAM;IACjC;IACA,OAAO;AACT"}