{"version": 3, "file": "findNumbers.test.js", "names": ["findNumbers", "metadata", "type", "describe", "it", "expect", "to", "deep", "equal", "phone", "country", "startsAt", "endsAt", "leniency", "ext", "phoneNumbers", "v2", "length", "number", "nationalNumber", "countryCallingCode", "thrower", "numbers", "defaultCountry", "possibleNumbers", "extended"], "sources": ["../../source/legacy/findNumbers.test.js"], "sourcesContent": ["import findNumbers from './findNumbers.js'\r\nimport metadata from '../../metadata.max.json' with { type: 'json' }\r\n\r\ndescribe('findNumbers', () => {\r\n\tit('should find numbers', () => {\r\n\t\texpect(findNumbers('2133734253', 'US', metadata)).to.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 10\r\n\t\t}])\r\n\r\n\t\texpect(findNumbers('(*************', 'US', metadata)).to.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 14\r\n\t\t}])\r\n\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// Opening parenthesis issue.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/252\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 and not (************* (that\\'s not even in the same country!) as written in the document.', 'US', metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// No default country.\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 as written in the document.', metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options` and default country.\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 as written in the document.', 'US', { leniency: 'VALID' }, metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options`.\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 as written in the document.', { leniency: 'VALID' }, metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Not a phone number and a phone number.\r\n\t\texpect(\r\n            findNumbers('Digits 12 are not a number, but +7 (800) 555-35-35 is.', { leniency: 'VALID' }, metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 32,\r\n\t\t\tendsAt   : 50\r\n\t\t}])\r\n\r\n\t\t// Phone number extension.\r\n\t\texpect(\r\n            findNumbers('Date 02/17/2018 is not a number, but +7 (800) 555-35-35 ext. 123 is.', { leniency: 'VALID' }, metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\text      : '123',\r\n\t\t\tstartsAt : 37,\r\n\t\t\tendsAt   : 64\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should find numbers (v2)', () => {\r\n\t\tconst phoneNumbers = findNumbers('The number is +7 (800) 555-35-35 ext. 1234 and not (************* as written in the document.', 'US', { v2: true }, metadata)\r\n\r\n\t\texpect(phoneNumbers.length).to.equal(2)\r\n\r\n\t\texpect(phoneNumbers[0].startsAt).to.equal(14)\r\n\t\texpect(phoneNumbers[0].endsAt).to.equal(42)\r\n\r\n\t\texpect(phoneNumbers[0].number.number).to.equal('+78005553535')\r\n\t\texpect(phoneNumbers[0].number.nationalNumber).to.equal('8005553535')\r\n\t\texpect(phoneNumbers[0].number.country).to.equal('RU')\r\n\t\texpect(phoneNumbers[0].number.countryCallingCode).to.equal('7')\r\n\t\texpect(phoneNumbers[0].number.ext).to.equal('1234')\r\n\r\n\t\texpect(phoneNumbers[1].startsAt).to.equal(51)\r\n\t\texpect(phoneNumbers[1].endsAt).to.equal(65)\r\n\r\n\t\texpect(phoneNumbers[1].number.number).to.equal('+12133734253')\r\n\t\texpect(phoneNumbers[1].number.nationalNumber).to.equal('2133734253')\r\n\t\texpect(phoneNumbers[1].number.country).to.equal('US')\r\n\t\texpect(phoneNumbers[1].number.countryCallingCode).to.equal('1')\r\n\t})\r\n\r\n\tit('shouldn\\'t find non-valid numbers', () => {\r\n\t\t// Not a valid phone number for US.\r\n\t\texpect(findNumbers('1111111111', 'US', metadata)).to.deep.equal([])\r\n\t})\r\n\r\n\tit('should find non-European digits', () => {\r\n\t\t// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n\t\texpect(findNumbers('العَرَبِيَّة‎ +٤٤٣٣٣٣٣٣٣٣٣٣عَرَبِيّ‎', metadata)).to.deep.equal([{\r\n\t\t\tcountry  : 'GB',\r\n\t\t\tphone    : '3333333333',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 27\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No input\r\n\t\texpect(findNumbers('', metadata)).to.deep.equal([])\r\n\r\n\t\t// // No country metadata for this `require` country code\r\n\t\t// thrower = () => findNumbers('123', 'ZZ', metadata)\r\n\t\t// thrower.should.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => findNumbers(2141111111, 'US')\r\n\t\texpect(thrower).to.throw('A text for parsing must be a string.')\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => findNumbers('')\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// No metadata, no default country, no phone numbers.\r\n\t\texpect(findNumbers('')).to.deep.equal([])\r\n\t})\r\n\r\n\tit('should find international numbers when passed a non-existent default country', () => {\r\n\t\tconst numbers = findNumbers('Phone: +7 (800) 555 35 35. National: 8 (800) 555-55-55', { defaultCountry: 'XX', v2: true }, metadata)\r\n\t\texpect(numbers.length).to.equal(1)\r\n\t\texpect(numbers[0].number.nationalNumber).to.equal('8005553535')\r\n\t})\r\n\r\n\tit('shouldn\\'t find phone numbers which are not phone numbers', () => {\r\n\t\t// A timestamp.\r\n\t\texpect(findNumbers('2012-01-02 08:00', 'US', metadata)).to.deep.equal([])\r\n\r\n\t\t// A valid number (not a complete timestamp).\r\n\t\texpect(findNumbers('2012-01-02 08', 'US', metadata)).to.deep.equal([{\r\n\t\t\tcountry  : 'US',\r\n\t\t\tphone    : '2012010208',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 13\r\n\t\t}])\r\n\r\n\t\t// Invalid parens.\r\n\t\texpect(findNumbers('213(3734253', 'US', metadata)).to.deep.equal([])\r\n\r\n\t\t// Letters after phone number.\r\n\t\texpect(findNumbers('2133734253a', 'US', metadata)).to.deep.equal([])\r\n\r\n\t\t// Valid phone (same as the one found in the UUID below).\r\n\t\texpect(findNumbers('The phone number is 231354125.', 'FR', metadata)).to.deep.equal([{\r\n\t\t\tcountry  : 'FR',\r\n\t\t\tphone    : '231354125',\r\n\t\t\tstartsAt : 20,\r\n\t\t\tendsAt   : 29\r\n\t\t}])\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Should parse in `{ extended: true }` mode.\r\n\t\tconst possibleNumbers = findNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', { extended: true }, metadata)\r\n\t\texpect(possibleNumbers.length).to.equal(1)\r\n\t\texpect(possibleNumbers[0].country).to.equal('FR')\r\n\t\texpect(possibleNumbers[0].phone).to.equal('231354125')\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Shouldn't parse by default.\r\n\t\texpect(\r\n            findNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', metadata)\r\n        ).to.deep.equal([])\r\n\t})\r\n\r\n\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/merge_requests/4\r\n\tit('should return correct `startsAt` and `endsAt` when matching \"inner\" candidates in a could-be-a-candidate substring', () => {\r\n\t\texpect(findNumbers('39945926 77200596 16533084', 'ID', metadata)).to.deep.equal([{\r\n\t\t\t\tcountry: 'ID',\r\n\t\t\t\tphone: '77200596',\r\n\t\t\t\tstartsAt: 9,\r\n\t\t\t\tendsAt: 17\r\n\t\t\t}])\r\n\t})\r\n})\r\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,QAAQ,MAAM,yBAAyB,QAAQC,IAAI,EAAE,MAAM;AAElEC,QAAQ,CAAC,aAAa,EAAE,YAAM;EAC7BC,EAAE,CAAC,qBAAqB,EAAE,YAAM;IAC/BC,MAAM,CAACL,WAAW,CAAC,YAAY,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MAChEC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,CAAC;MACZC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;IAEHP,MAAM,CAACL,WAAW,CAAC,gBAAgB,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACpEC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,CAAC;MACZC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;IAEHP,MAAM,CACIL,WAAW,CAAC,qFAAqF,EAAE,IAAI,EAAEC,QAAQ,CACrH,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,EAAE;MACFH,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACA;IACAP,MAAM,CACIL,WAAW,CAAC,6HAA6H,EAAE,IAAI,EAAEC,QAAQ,CAC7J,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,EAAE;MACFH,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAP,MAAM,CACIL,WAAW,CAAC,8DAA8D,EAAEC,QAAQ,CACxF,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAP,MAAM,CACIL,WAAW,CAAC,8DAA8D,EAAE,IAAI,EAAE;MAAEa,QAAQ,EAAE;IAAQ,CAAC,EAAEZ,QAAQ,CACrH,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAP,MAAM,CACIL,WAAW,CAAC,8DAA8D,EAAE;MAAEa,QAAQ,EAAE;IAAQ,CAAC,EAAEZ,QAAQ,CAC/G,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAP,MAAM,CACIL,WAAW,CAAC,wDAAwD,EAAE;MAAEa,QAAQ,EAAE;IAAQ,CAAC,EAAEZ,QAAQ,CACzG,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAP,MAAM,CACIL,WAAW,CAAC,sEAAsE,EAAE;MAAEa,QAAQ,EAAE;IAAQ,CAAC,EAAEZ,QAAQ,CACvH,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfI,GAAG,EAAQ,KAAK;MAChBH,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,EAAE,CAAC,0BAA0B,EAAE,YAAM;IACpC,IAAMW,YAAY,GAAGf,WAAW,CAAC,+FAA+F,EAAE,IAAI,EAAE;MAAEgB,EAAE,EAAE;IAAK,CAAC,EAAEf,QAAQ,CAAC;IAE/JI,MAAM,CAACU,YAAY,CAACE,MAAM,CAAC,CAACX,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;IAEvCH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAACL,EAAE,CAACE,KAAK,CAAC,EAAE,CAAC;IAC7CH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACH,MAAM,CAAC,CAACN,EAAE,CAACE,KAAK,CAAC,EAAE,CAAC;IAE3CH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACA,MAAM,CAAC,CAACZ,EAAE,CAACE,KAAK,CAAC,cAAc,CAAC;IAC9DH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACC,cAAc,CAAC,CAACb,EAAE,CAACE,KAAK,CAAC,YAAY,CAAC;IACpEH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACR,OAAO,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC;IACrDH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACE,kBAAkB,CAAC,CAACd,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;IAC/DH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACJ,GAAG,CAAC,CAACR,EAAE,CAACE,KAAK,CAAC,MAAM,CAAC;IAEnDH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAACL,EAAE,CAACE,KAAK,CAAC,EAAE,CAAC;IAC7CH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACH,MAAM,CAAC,CAACN,EAAE,CAACE,KAAK,CAAC,EAAE,CAAC;IAE3CH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACA,MAAM,CAAC,CAACZ,EAAE,CAACE,KAAK,CAAC,cAAc,CAAC;IAC9DH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACC,cAAc,CAAC,CAACb,EAAE,CAACE,KAAK,CAAC,YAAY,CAAC;IACpEH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACR,OAAO,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC;IACrDH,MAAM,CAACU,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACE,kBAAkB,CAAC,CAACd,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;EAChE,CAAC,CAAC;EAEFJ,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7C;IACAC,MAAM,CAACL,WAAW,CAAC,YAAY,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;EACpE,CAAC,CAAC;EAEFJ,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3C;IACAC,MAAM,CAACL,WAAW,CAAC,sCAAsC,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACpFE,OAAO,EAAI,IAAI;MACfD,KAAK,EAAM,YAAY;MACvBE,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFR,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC,IAAIiB,OAAO;;IAEX;IACAhB,MAAM,CAACL,WAAW,CAAC,EAAE,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;;IAEnD;IACA;IACA;;IAEA;IACAa,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASrB,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC;IAAA;IAC7CK,MAAM,CAACgB,OAAO,CAAC,CAACf,EAAE,SAAM,CAAC,sCAAsC,CAAC;;IAEhE;IACA;IACA;;IAEA;IACAD,MAAM,CAACL,WAAW,CAAC,EAAE,CAAC,CAAC,CAACM,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;EAC1C,CAAC,CAAC;EAEFJ,EAAE,CAAC,8EAA8E,EAAE,YAAM;IACxF,IAAMkB,OAAO,GAAGtB,WAAW,CAAC,wDAAwD,EAAE;MAAEuB,cAAc,EAAE,IAAI;MAAEP,EAAE,EAAE;IAAK,CAAC,EAAEf,QAAQ,CAAC;IACnII,MAAM,CAACiB,OAAO,CAACL,MAAM,CAAC,CAACX,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;IAClCH,MAAM,CAACiB,OAAO,CAAC,CAAC,CAAC,CAACJ,MAAM,CAACC,cAAc,CAAC,CAACb,EAAE,CAACE,KAAK,CAAC,YAAY,CAAC;EAChE,CAAC,CAAC;EAEFJ,EAAE,CAAC,2DAA2D,EAAE,YAAM;IACrE;IACAC,MAAM,CAACL,WAAW,CAAC,kBAAkB,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;;IAEzE;IACAH,MAAM,CAACL,WAAW,CAAC,eAAe,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACnEE,OAAO,EAAI,IAAI;MACfD,KAAK,EAAM,YAAY;MACvBE,QAAQ,EAAG,CAAC;MACZC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAP,MAAM,CAACL,WAAW,CAAC,aAAa,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;;IAEpE;IACAH,MAAM,CAACL,WAAW,CAAC,aAAa,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;;IAEpE;IACAH,MAAM,CAACL,WAAW,CAAC,gCAAgC,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACpFE,OAAO,EAAI,IAAI;MACfD,KAAK,EAAM,WAAW;MACtBE,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACA;IACA,IAAMY,eAAe,GAAGxB,WAAW,CAAC,iDAAiD,EAAE,IAAI,EAAE;MAAEyB,QAAQ,EAAE;IAAK,CAAC,EAAExB,QAAQ,CAAC;IAC1HI,MAAM,CAACmB,eAAe,CAACP,MAAM,CAAC,CAACX,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;IAC1CH,MAAM,CAACmB,eAAe,CAAC,CAAC,CAAC,CAACd,OAAO,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC;IACjDH,MAAM,CAACmB,eAAe,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC,CAACH,EAAE,CAACE,KAAK,CAAC,WAAW,CAAC;;IAEtD;IACA;IACAH,MAAM,CACIL,WAAW,CAAC,iDAAiD,EAAE,IAAI,EAAEC,QAAQ,CACjF,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;EAC1B,CAAC,CAAC;;EAEF;EACAJ,EAAE,CAAC,oHAAoH,EAAE,YAAM;IAC9HC,MAAM,CAACL,WAAW,CAAC,4BAA4B,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MAC/EE,OAAO,EAAE,IAAI;MACbD,KAAK,EAAE,UAAU;MACjBE,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACT,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}