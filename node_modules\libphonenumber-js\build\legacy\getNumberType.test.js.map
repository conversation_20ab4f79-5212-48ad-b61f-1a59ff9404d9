{"version": 3, "file": "getNumberType.test.js", "names": ["_metadataMax", "_interopRequireDefault", "require", "_metadata", "_getNumberType2", "e", "__esModule", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "getNumberType", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "metadata", "_getNumberType", "apply", "describe", "it", "expect", "to", "equal", "thrower", "phone", "country", "type", "something"], "sources": ["../../source/legacy/getNumberType.test.js"], "sourcesContent": ["import metadata from '../../metadata.max.json' with { type: 'json' }\r\nimport Metadata from '../metadata.js'\r\nimport _getNumberType from './getNumberType.js'\r\n\r\nfunction getNumberType(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _getNumberType.apply(this, parameters)\r\n}\r\n\r\ndescribe('getNumberType', () => {\r\n\tit('should infer phone number type MOBILE', () => {\r\n\t\texpect(getNumberType('9150000000', 'RU')).to.equal('MOBILE')\r\n\t\texpect(getNumberType('7912345678', 'GB')).to.equal('MOBILE')\r\n\t\texpect(getNumberType('51234567', 'EE')).to.equal('MOBILE')\r\n\t})\r\n\r\n\tit('should infer phone number types', () =>  {\r\n\t\texpect(getNumberType('88005553535', 'RU')).to.equal('TOLL_FREE')\r\n\t\texpect(getNumberType('8005553535', 'RU')).to.equal('TOLL_FREE')\r\n\t\texpect(getNumberType('4957777777', 'RU')).to.equal('FIXED_LINE')\r\n\t\texpect(getNumberType('8030000000', 'RU')).to.equal('PREMIUM_RATE')\r\n\r\n\t\texpect(getNumberType('2133734253', 'US')).to.equal('FIXED_LINE_OR_MOBILE')\r\n\t\texpect(getNumberType('5002345678', 'US')).to.equal('PERSONAL_NUMBER')\r\n\t})\r\n\r\n\tit('should work when no country is passed', () => {\r\n\t\texpect(getNumberType('+79150000000')).to.equal('MOBILE')\r\n\t})\r\n\r\n\tit('should return FIXED_LINE_OR_MOBILE when there is ambiguity', () => {\r\n\t\t// (no such country in the metadata, therefore no unit test for this `if`)\r\n\t})\r\n\r\n\tit('should work in edge cases', function() {\r\n\t\tlet thrower\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => _getNumberType({ phone: '+78005553535' })\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// Parsed phone number\r\n\t\texpect(getNumberType({ phone: '8005553535', country: 'RU' })).to.equal('TOLL_FREE')\r\n\r\n\t\t// Invalid phone number\r\n\t\texpect(type(getNumberType('123', 'RU'))).to.equal('undefined')\r\n\r\n\t\t// Invalid country\r\n\t\tthrower = () => getNumberType({ phone: '8005553535', country: 'RUS' })\r\n\t\texpect(thrower).to.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => getNumberType(89150000000, 'RU')\r\n\t\texpect(thrower).to.throw(\r\n            'A phone number must either be a string or an object of shape { phone, [country] }.'\r\n        )\r\n\r\n\t\t// When `options` argument is passed.\r\n\t\texpect(getNumberType('8005553535', 'RU', {})).to.equal('TOLL_FREE')\r\n\t\texpect(getNumberType('+78005553535', {})).to.equal('TOLL_FREE')\r\n\t\texpect(getNumberType({ phone: '8005553535', country: 'RU' }, {})).to.equal('TOLL_FREE')\r\n\t})\r\n})\r\n\r\nfunction type(something) {\r\n\treturn typeof something\r\n}"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,eAAA,GAAAH,sBAAA,CAAAC,OAAA;AAA+C,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAE/C,SAASK,aAAaA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACnCF,UAAU,CAACG,IAAI,CAACC,uBAAQ,CAAC;EACzB,OAAOC,0BAAc,CAACC,KAAK,CAAC,IAAI,EAAEN,UAAU,CAAC;AAC9C;AAEAO,QAAQ,CAAC,eAAe,EAAE,YAAM;EAC/BC,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjDC,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC5DF,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC5DF,MAAM,CAACb,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;EAC3D,CAAC,CAAC;EAEFH,EAAE,CAAC,iCAAiC,EAAE,YAAO;IAC5CC,MAAM,CAACb,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAChEF,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC/DF,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IAChEF,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAElEF,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,sBAAsB,CAAC;IAC1EF,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EACtE,CAAC,CAAC;EAEFH,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjDC,MAAM,CAACb,aAAa,CAAC,cAAc,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;EACzD,CAAC,CAAC;EAEFH,EAAE,CAAC,4DAA4D,EAAE,YAAM;IACtE;EAAA,CACA,CAAC;EAEFA,EAAE,CAAC,2BAA2B,EAAE,YAAW;IAC1C,IAAII,OAAO;;IAEX;IACA;IACA;;IAEA;IACAH,MAAM,CAACb,aAAa,CAAC;MAAEiB,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;;IAEnF;IACAF,MAAM,CAACM,IAAI,CAACnB,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;;IAE9D;IACAC,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAShB,aAAa,CAAC;QAAEiB,KAAK,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAAA;IACtEL,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,iBAAiB,CAAC;;IAE3C;IACAE,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAShB,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC;IAAA;IAChDa,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CACd,oFACJ,CAAC;;IAEP;IACAD,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACnEF,MAAM,CAACb,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC/DF,MAAM,CAACb,aAAa,CAAC;MAAEiB,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAE;IAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EACxF,CAAC,CAAC;AACH,CAAC,CAAC;AAEF,SAASI,IAAIA,CAACC,SAAS,EAAE;EACxB,OAAA1B,OAAA,CAAc0B,SAAS;AACxB", "ignoreList": []}