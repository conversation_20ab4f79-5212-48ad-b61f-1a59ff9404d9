{"version": 3, "file": "getNumberType.test.js", "names": ["getNumberType", "oldMetadata", "type", "<PERSON><PERSON><PERSON>", "describe", "it", "expect", "nationalNumber", "country", "v2", "to", "equal", "be", "undefined"], "sources": ["../../source/helpers/getNumberType.test.js"], "sourcesContent": ["import getNumberType from './getNumberType.js'\r\n\r\nimport oldMetadata from '../../test/metadata/1.0.0/metadata.min.json' with { type: 'json' }\r\n\r\nimport Metadata from '../metadata.js'\r\n\r\ndescribe('getNumberType', function() {\r\n\tit('should get number type when using old metadata', function() {\r\n\t\texpect(getNumberType(\r\n\t\t\t{\r\n\t\t\t\tnationalNumber: '2133734253',\r\n\t\t\t\tcountry: 'US'\r\n\t\t\t},\r\n\t\t\t{ v2: true },\r\n\t\t\toldMetadata\r\n\t\t)).to.equal('FIXED_LINE_OR_MOBILE')\r\n\t})\r\n\r\n\tit('should return `undefined` when the phone number is a malformed one', function() {\r\n\t\texpect(getNumberType(\r\n\t\t\t{},\r\n\t\t\t{ v2: true },\r\n\t\t\toldMetadata\r\n\t\t)).to.be.undefined\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAE9C,OAAOC,WAAW,MAAM,6CAA6C,QAAQC,IAAI,EAAE,MAAM;AAEzF,OAAOC,QAAQ,MAAM,gBAAgB;AAErCC,QAAQ,CAAC,eAAe,EAAE,YAAW;EACpCC,EAAE,CAAC,gDAAgD,EAAE,YAAW;IAC/DC,MAAM,CAACN,aAAa,CACnB;MACCO,cAAc,EAAE,YAAY;MAC5BC,OAAO,EAAE;IACV,CAAC,EACD;MAAEC,EAAE,EAAE;IAAK,CAAC,EACZR,WACD,CAAC,CAAC,CAACS,EAAE,CAACC,KAAK,CAAC,sBAAsB,CAAC;EACpC,CAAC,CAAC;EAEFN,EAAE,CAAC,oEAAoE,EAAE,YAAW;IACnFC,MAAM,CAACN,aAAa,CACnB,CAAC,CAAC,EACF;MAAES,EAAE,EAAE;IAAK,CAAC,EACZR,WACD,CAAC,CAAC,CAACS,EAAE,CAACE,EAAE,CAACC,SAAS;EACnB,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}