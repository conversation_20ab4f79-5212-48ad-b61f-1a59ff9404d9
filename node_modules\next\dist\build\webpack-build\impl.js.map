{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "names": ["webpackBuildImpl", "worker<PERSON>ain", "debug", "origDebug", "isTelemetryPlugin", "plugin", "TelemetryPlugin", "isTraceEntryPointsPlugin", "TraceEntryPointsPlugin", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "NextBuildContext", "dir", "config", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "createEntrypoints", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "<PERSON><PERSON><PERSON>", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "loadProjectInfo", "dev", "Promise", "all", "getBaseWebpackConfig", "middlewareMatchers", "compilerType", "COMPILER_NAMES", "client", "server", "edgeServer", "edgePreviewProps", "__NEXT_PREVIEW_MODE_ID", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY", "previewModeEncryptionKey", "__NEXT_PREVIEW_MODE_SIGNING_KEY", "previewModeSigningKey", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "Log", "warn", "process", "hrtime", "inputFileSystem", "clientResult", "serverResult", "edgeServerResult", "start", "Date", "now", "runCompiler", "pluginState", "getPluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "APP_CLIENT_INTERNALS", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "import", "layer", "WEBPACK_LAYERS", "appPagesBrowser", "dependOn", "purge", "filter", "nonNullable", "traceFn", "formatWebpackMessages", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "red", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "event", "duration", "buildTraceContext", "telemetryState", "usages", "packagesUsedInServerSideProps", "workerData", "telemetry", "Telemetry", "distDir", "buildContext", "setGlobal", "Object", "assign", "initializeTraceState", "traceState", "resumePluginState", "loadConfig", "PHASE_PRODUCTION_BUILD", "trace", "entriesTrace", "chunksTrace", "entryNameMap", "depModArray", "entryEntries", "entryNameFilesMap", "stop", "debugTraceEvents", "getTraceEvents"], "mappings": ";;;;;;;;;;;;;;;IAkEsBA,gBAAgB;eAAhBA;;IA6RAC,UAAU;eAAVA;;;4BA9VF;8EACc;6BACN;2BAOrB;0BACqB;6DACP;uEACiC;iCAK/C;8BAKA;yBAC2B;+DACX;uBAQhB;4BACwB;4CACQ;8DAIjB;yBACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1B,MAAMC,QAAQC,IAAAA,cAAS,EAAC;AAcxB,SAASC,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBC,gCAAe;AAC1C;AAEA,SAASC,yBACPF,MAAe;IAEf,OAAOA,kBAAkBG,kDAAsB;AACjD;AAEO,eAAeR,iBACpBS,YAAkD;QAqN1B,uBAIO;IAlN/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBC,8BAAgB,CAACD,aAAa;IACpD,MAAME,MAAMD,8BAAgB,CAACC,GAAG;IAChC,MAAMC,SAASF,8BAAgB,CAACE,MAAM;IAEtC,MAAMC,iBAAiBJ,cAAcK,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAMN,cACvBK,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZC,IAAAA,0BAAiB,EAAC;YAChBC,SAASR,8BAAgB,CAACQ,OAAO;YACjCN,QAAQA;YACRO,UAAUT,8BAAgB,CAACU,cAAc;YACzCC,OAAO;YACPC,SAASX;YACTY,gBAAgBX,OAAOW,cAAc;YACrCC,UAAUd,8BAAgB,CAACc,QAAQ;YACnCC,QAAQf,8BAAgB,CAACe,MAAM;YAC/BC,OAAOhB,8BAAgB,CAACiB,WAAW;YACnCC,UAAUlB,8BAAgB,CAACmB,cAAc;YACzCC,aAAapB,8BAAgB,CAACqB,YAAY;YAC1CC,WAAWtB,8BAAgB,CAACuB,eAAe;YAC3CC,wBAAwBxB,8BAAgB,CAACwB,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVlB,SAASR,8BAAgB,CAACQ,OAAO;QACjCmB,eAAe3B,8BAAgB,CAAC2B,aAAa;QAC7CzB,QAAQA;QACRa,QAAQf,8BAAgB,CAACe,MAAM;QAC/BD,UAAUd,8BAAgB,CAACc,QAAQ;QACnCc,UAAU5B,8BAAgB,CAAC4B,QAAQ;QACnCC,kBAAkB7B,8BAAgB,CAAC6B,gBAAgB;QACnDC,mBAAmB9B,8BAAgB,CAAC8B,iBAAiB;QACrDC,0BAA0B/B,8BAAgB,CAAC+B,wBAAwB;QACnEC,YAAYhC,8BAAgB,CAACgC,UAAU;QACvCC,qBAAqBjC,8BAAgB,CAACiC,mBAAmB;QACzDC,eAAelC,8BAAgB,CAACkC,aAAa;QAC7CC,6BAA6BnC,8BAAgB,CAACmC,2BAA2B;QACzEC,qBAAqBpC,8BAAgB,CAACoC,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMlC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAMgC,OAAO,MAAMC,IAAAA,8BAAe,EAAC;YACjCtC;YACAC,QAAQuB,qBAAqBvB,MAAM;YACnCsC,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjBC,IAAAA,sBAAoB,EAAC1C,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBmB,oBAAoBvC,YAAYuC,kBAAkB;gBAClDzC;gBACA0C,cAAcC,yBAAc,CAACC,MAAM;gBACnC1C,aAAaA,YAAY0C,MAAM;gBAC/B,GAAGT,IAAI;YACT;YACAK,IAAAA,sBAAoB,EAAC1C,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBtB;gBACAyC,oBAAoBvC,YAAYuC,kBAAkB;gBAClDC,cAAcC,yBAAc,CAACE,MAAM;gBACnC3C,aAAaA,YAAY2C,MAAM;gBAC/B,GAAGV,IAAI;YACT;YACAK,IAAAA,sBAAoB,EAAC1C,KAAK;gBACxB,GAAGwB,oBAAoB;gBACvBtB;gBACAyC,oBAAoBvC,YAAYuC,kBAAkB;gBAClDC,cAAcC,yBAAc,CAACG,UAAU;gBACvC5C,aAAaA,YAAY4C,UAAU;gBACnCC,kBAAkB;oBAChBC,wBACEnD,8BAAgB,CAACqB,YAAY,CAAEa,aAAa;oBAC9CkB,oCACEpD,8BAAgB,CAACqB,YAAY,CAAEgC,wBAAwB;oBACzDC,iCACEtD,8BAAgB,CAACqB,YAAY,CAAEkC,qBAAqB;gBACxD;gBACA,GAAGjB,IAAI;YACT;SACD;IACH;IAEF,MAAMkB,eAAenB,OAAO,CAAC,EAAE;IAC/B,MAAMoB,eAAepB,OAAO,CAAC,EAAE;IAC/B,MAAMqB,aAAarB,OAAO,CAAC,EAAE;IAE7B,IACEmB,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACAC,KAAIC,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEAlE,oBAAoBmE,QAAQC,MAAM;IAElChF,MAAM,CAAC,iBAAiB,CAAC,EAAEO;IAC3B,+EAA+E;IAC/E,MAAMU,eAAeG,YAAY,CAAC;YAsEhC6D;QArEA,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIC,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIH;QAEJ,IAAI,CAAC1E,gBAAgBA,iBAAiB,UAAU;YAC9CP,MAAM;YACN,MAAMqF,QAAQC,KAAKC,GAAG;YACrB,CAACJ,cAAcF,gBAAgB,GAAG,MAAMO,IAAAA,qBAAW,EAACjB,cAAc;gBACjEtD;gBACAgE;YACF;YACAjF,MAAM,CAAC,yBAAyB,EAAEsF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC1D;QAEA,IAAI,CAAC9E,gBAAgBA,iBAAiB,eAAe;YACnDP,MAAM;YACN,MAAMqF,QAAQC,KAAKC,GAAG;YACrB,CAACH,kBAAkBH,gBAAgB,GAAGT,aACnC,MAAMgB,IAAAA,qBAAW,EAAChB,YAAY;gBAAEvD;gBAAgBgE;YAAgB,KAChE;gBAAC;aAAK;YACVjF,MAAM,CAAC,8BAA8B,EAAEsF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC/D;QAEA,wCAAwC;QACxC,IAAI,EAACF,gCAAAA,aAAczE,MAAM,CAACkE,MAAM,KAAI,EAACQ,oCAAAA,iBAAkB1E,MAAM,CAACkE,MAAM,GAAE;YACpE,MAAMa,cAAcC,IAAAA,4BAAc;YAClC,IAAK,MAAMC,OAAOF,YAAYG,qBAAqB,CAAE;gBACnD,MAAMC,QAAQJ,YAAYG,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAcxB,aAAayB,KAAK;gBACtC,IAAIJ,QAAQK,+BAAoB,EAAE;oBAChCF,WAAW,CAACG,+CAAoC,CAAC,GAAG;wBAClDC,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFJ,WAAW,CAACG,+CAAoC,CAAC,CAACC,MAAM;4BAC3DL;yBACD;wBACDM,OAAOC,0BAAc,CAACC,eAAe;oBACvC;gBACF,OAAO;oBACLP,WAAW,CAACH,IAAI,GAAG;wBACjBW,UAAU;4BAACL,+CAAoC;yBAAC;wBAChDC,QAAQL;wBACRM,OAAOC,0BAAc,CAACC,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAAC9F,gBAAgBA,iBAAiB,UAAU;gBAC9CP,MAAM;gBACN,MAAMqF,QAAQC,KAAKC,GAAG;gBACrB,CAACL,cAAcD,gBAAgB,GAAG,MAAMO,IAAAA,qBAAW,EAAClB,cAAc;oBACjErD;oBACAgE;gBACF;gBACAjF,MAAM,CAAC,yBAAyB,EAAEsF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;YAC1D;QACF;QAEAJ,oCAAAA,yBAAAA,gBAAiBsB,KAAK,qBAAtBtB,4BAAAA;QAEAzE,SAAS;YACPC,UAAU;mBACJyE,CAAAA,gCAAAA,aAAczE,QAAQ,KAAI,EAAE;mBAC5B0E,CAAAA,gCAAAA,aAAc1E,QAAQ,KAAI,EAAE;mBAC5B2E,CAAAA,oCAAAA,iBAAkB3E,QAAQ,KAAI,EAAE;aACrC,CAAC+F,MAAM,CAACC,wBAAW;YACpB/F,QAAQ;mBACFwE,CAAAA,gCAAAA,aAAcxE,MAAM,KAAI,EAAE;mBAC1ByE,CAAAA,gCAAAA,aAAczE,MAAM,KAAI,EAAE;mBAC1B0E,CAAAA,oCAAAA,iBAAkB1E,MAAM,KAAI,EAAE;aACnC,CAAC8F,MAAM,CAACC,wBAAW;YACpB9F,OAAO;gBACLuE,gCAAAA,aAAcvE,KAAK;gBACnBwE,gCAAAA,aAAcxE,KAAK;gBACnByE,oCAAAA,iBAAkBzE,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNK,UAAU,CAAC,2BACXwF,OAAO,CAAC,IAAMC,IAAAA,8BAAqB,EAACnG,QAAQ;IAE/C,MAAMoG,mBAAkB,wBAAA,AAACtC,aAAuCuC,OAAO,qBAA/C,sBAAiDC,IAAI,CAC3E5G;IAGF,MAAM6G,0BAAyB,wBAAA,AAC7BxC,aACAsC,OAAO,qBAFsB,sBAEpBC,IAAI,CAACzG;IAEhB,MAAM2G,kBAAkBjC,QAAQC,MAAM,CAACpE;IAEvC,IAAIJ,OAAOE,MAAM,CAACkE,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAIpE,OAAOE,MAAM,CAACkE,MAAM,GAAG,GAAG;YAC5BpE,OAAOE,MAAM,CAACkE,MAAM,GAAG;QACzB;QACA,IAAIqC,QAAQzG,OAAOE,MAAM,CAAC8F,MAAM,CAACU,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAACI,IAAAA,eAAG,EAAC;QAElB,IACEJ,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACR;YACpC,MAAMS,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC;QAE5L;QAEAN,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMK,OAAO,CAAC,wBAAwB,CAAC,KACvCL,MAAMK,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,IAAID,MACd;YAEFC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,IAAID,MAAM;QACtBC,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,IAAIrH,OAAOC,QAAQ,CAACmE,MAAM,GAAG,GAAG;YAC9BC,KAAIC,IAAI,CAAC;YACTsC,QAAQtC,IAAI,CAACtE,OAAOC,QAAQ,CAAC+F,MAAM,CAACU,SAASC,IAAI,CAAC;YAClDC,QAAQtC,IAAI;QACd,OAAO,IAAI,CAACvE,cAAc;YACxBsE,KAAIkD,KAAK,CAAC;QACZ;QAEA,OAAO;YACLC,UAAUhB,eAAe,CAAC,EAAE;YAC5BiB,iBAAiB,EAAElB,0CAAAA,uBAAwBkB,iBAAiB;YAC5DxC,aAAaC,IAAAA,4BAAc;YAC3BwC,gBAAgB;gBACdC,QAAQvB,CAAAA,mCAAAA,gBAAiBuB,MAAM,OAAM,EAAE;gBACvCC,+BACExB,CAAAA,mCAAAA,gBAAiBwB,6BAA6B,OAAM,EAAE;YAC1D;QACF;IACF;AACF;AAGO,eAAerI,WAAWsI,UAIhC;IAKC,iCAAiC;IACjC,MAAMC,YAAY,IAAIC,kBAAS,CAAC;QAC9BC,SAASH,WAAWI,YAAY,CAACzH,MAAM,CAAEwH,OAAO;IAClD;IACAE,IAAAA,gBAAS,EAAC,aAAaJ;IACvB,0EAA0E;IAC1EK,OAAOC,MAAM,CAAC9H,8BAAgB,EAAEuH,WAAWI,YAAY;IAEvD,0CAA0C;IAC1CI,IAAAA,2BAAoB,EAACR,WAAWS,UAAU;IAE1C,sBAAsB;IACtBC,IAAAA,+BAAiB,EAACjI,8BAAgB,CAAC2E,WAAW;IAE9C,iDAAiD;IACjD3E,8BAAgB,CAACE,MAAM,GAAG,MAAMgI,IAAAA,eAAU,EACxCC,iCAAsB,EACtBnI,8BAAgB,CAACC,GAAG;IAEtBD,8BAAgB,CAACD,aAAa,GAAGqI,IAAAA,YAAK,EACpC,CAAC,YAAY,EAAEb,WAAW9H,YAAY,CAAC,CAAC;IAG1C,MAAMC,SAAS,MAAMV,iBAAiBuI,WAAW9H,YAAY;IAC7D,MAAM,EAAE4I,YAAY,EAAEC,WAAW,EAAE,GAAG5I,OAAOyH,iBAAiB,IAAI,CAAC;IACnE,IAAIkB,cAAc;QAChB,MAAM,EAAEE,YAAY,EAAEC,WAAW,EAAE,GAAGH;QACtC,IAAIG,aAAa;YACf9I,OAAOyH,iBAAiB,CAAEkB,YAAY,CAAEG,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeF;YACrB7I,OAAOyH,iBAAiB,CAAEkB,YAAY,CAAEE,YAAY,GAAGE;QACzD;IACF;IACA,IAAIH,+BAAAA,YAAaI,iBAAiB,EAAE;QAClC,MAAMA,oBAAoBJ,YAAYI,iBAAiB;QACvDhJ,OAAOyH,iBAAiB,CAAEmB,WAAW,CAAEI,iBAAiB,GAAGA;IAC7D;IACA1I,8BAAgB,CAACD,aAAa,CAAC4I,IAAI;IACnC,OAAO;QAAE,GAAGjJ,MAAM;QAAEkJ,kBAAkBC,IAAAA,qBAAc;IAAG;AACzD"}