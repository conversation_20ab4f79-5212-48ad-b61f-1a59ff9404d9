{"version": 3, "sources": ["../../src/lib/format-server-error.ts"], "names": ["invalidServerComponentReactHooks", "setMessage", "error", "message", "stack", "lines", "split", "join", "getStackWithoutErrorMessage", "replace", "formatServerError", "includes", "addedMessage", "clientHook", "regex", "RegExp", "test"], "mappings": "AAAA,MAAMA,mCAAmC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,WAAWC,KAAY,EAAEC,OAAe;IAC/CD,MAAMC,OAAO,GAAGA;IAChB,IAAID,MAAME,KAAK,EAAE;QACf,MAAMC,QAAQH,MAAME,KAAK,CAACE,KAAK,CAAC;QAChCD,KAAK,CAAC,EAAE,GAAGF;QACXD,MAAME,KAAK,GAAGC,MAAME,IAAI,CAAC;IAC3B;AACF;AAEA;;;;;;;;;CASC,GACD,OAAO,SAASC,4BAA4BN,KAAY;IACtD,MAAME,QAAQF,MAAME,KAAK;IACzB,IAAI,CAACA,OAAO,OAAO;IACnB,OAAOA,MAAMK,OAAO,CAAC,aAAa;AACpC;AAEA,OAAO,SAASC,kBAAkBR,KAAY;IAC5C,IAAI,QAAOA,yBAAAA,MAAOC,OAAO,MAAK,UAAU;IAExC,IACED,MAAMC,OAAO,CAACQ,QAAQ,CACpB,+DAEF;QACA,MAAMC,eACJ;QAEF,qEAAqE;QACrE,IAAIV,MAAMC,OAAO,CAACQ,QAAQ,CAACC,eAAe;QAE1CX,WACEC,OACA,CAAC,EAAEA,MAAMC,OAAO,CAAC;;AAEvB,EAAES,aAAa,CAAC;QAEZ;IACF;IAEA,IAAIV,MAAMC,OAAO,CAACQ,QAAQ,CAAC,oCAAoC;QAC7DV,WACEC,OACA;QAEF;IACF;IAEA,KAAK,MAAMW,cAAcb,iCAAkC;QACzD,MAAMc,QAAQ,IAAIC,OAAO,CAAC,GAAG,EAAEF,WAAW,sBAAsB,CAAC;QACjE,IAAIC,MAAME,IAAI,CAACd,MAAMC,OAAO,GAAG;YAC7BF,WACEC,OACA,CAAC,EAAEW,WAAW,oLAAoL,CAAC;YAErM;QACF;IACF;AACF"}