{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Dialog/Dialog.tsx"], "names": ["React", "useOnClickOutside", "Dialog", "children", "type", "onClose", "props", "dialog", "setDialog", "useState", "role", "setRole", "document", "hasFocus", "undefined", "onDialog", "useCallback", "node", "e", "preventDefault", "useEffect", "root", "getRootNode", "ShadowRoot", "shadowRoot", "handler", "el", "activeElement", "key", "HTMLElement", "getAttribute", "stopPropagation", "click", "handleFocus", "addEventListener", "window", "removeEventListener", "div", "ref", "data-nextjs-dialog", "tabIndex", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "aria-modal", "data-nextjs-dialog-banner", "className"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,iBAAiB,QAAQ,mCAAkC;AAUpE,MAAMC,SAAgC,SAASA,OAAO,KAKrD;IALqD,IAAA,EACpDC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACP,GAAGC,OACJ,GALqD;IAMpD,MAAM,CAACC,QAAQC,UAAU,GAAGR,MAAMS,QAAQ,CAAwB;IAClE,MAAM,CAACC,MAAMC,QAAQ,GAAGX,MAAMS,QAAQ,CACpC,OAAOG,aAAa,eAAeA,SAASC,QAAQ,KAChD,WACAC;IAEN,MAAMC,WAAWf,MAAMgB,WAAW,CAAC,CAACC;QAClCT,UAAUS;IACZ,GAAG,EAAE;IACLhB,kBAAkBM,QAAQ,CAACW;QACzBA,EAAEC,cAAc;QAChB,OAAOd,2BAAAA;IACT;IAEA,uEAAuE;IACvE,0BAA0B;IAC1BL,MAAMoB,SAAS,CAAC;QACd,IAAIb,UAAU,MAAM;YAClB;QACF;QAEA,MAAMc,OAAOd,OAAOe,WAAW;QAC/B,8CAA8C;QAC9C,IAAI,CAAED,CAAAA,gBAAgBE,UAAS,GAAI;YACjC;QACF;QACA,MAAMC,aAAaH;QACnB,SAASI,QAAQP,CAAgB;YAC/B,MAAMQ,KAAKF,WAAWG,aAAa;YACnC,IACET,EAAEU,GAAG,KAAK,WACVF,cAAcG,eACdH,GAAGI,YAAY,CAAC,YAAY,QAC5B;gBACAZ,EAAEC,cAAc;gBAChBD,EAAEa,eAAe;gBAEjBL,GAAGM,KAAK;YACV;QACF;QAEA,SAASC;YACP,2GAA2G;YAC3G,6EAA6E;YAC7EtB,QAAQC,SAASC,QAAQ,KAAK,WAAWC;QAC3C;QAEAU,WAAWU,gBAAgB,CAAC,WAAWT;QACvCU,OAAOD,gBAAgB,CAAC,SAASD;QACjCE,OAAOD,gBAAgB,CAAC,QAAQD;QAChC,OAAO;YACLT,WAAWY,mBAAmB,CAAC,WAAWX;YAC1CU,OAAOC,mBAAmB,CAAC,SAASH;YACpCE,OAAOC,mBAAmB,CAAC,QAAQH;QACrC;IACF,GAAG;QAAC1B;KAAO;IAEX,qBACE,MAAC8B;QACCC,KAAKvB;QACLwB,oBAAkB;QAClBC,UAAU,CAAC;QACX9B,MAAMA;QACN+B,mBAAiBnC,KAAK,CAAC,kBAAkB;QACzCoC,oBAAkBpC,KAAK,CAAC,mBAAmB;QAC3CqC,cAAW;;0BAEX,KAACN;gBAAIO,2BAAyB;gBAACC,WAAW,AAAC,YAASzC;;YACnDD;;;AAGP;AAEA,SAASD,MAAM,GAAE"}