export interface ObjectUnsubscribedError extends Error {
}
export interface ObjectUnsubscribedErrorCtor {
    /**
     * @deprecated Internal implementation detail. Do not construct error instances.
     * Cannot be tagged as internal: https://github.com/ReactiveX/rxjs/issues/6269
     */
    new (): ObjectUnsubscribedError;
}
/**
 * An error thrown when an action is invalid because the object has been
 * unsubscribed.
 *
 * @see {@link Subject}
 * @see {@link BehaviorSubject}
 *
 * @class ObjectUnsubscribedError
 */
export declare const ObjectUnsubscribedError: ObjectUnsubscribedErrorCtor;
//# sourceMappingURL=ObjectUnsubscribedError.d.ts.map