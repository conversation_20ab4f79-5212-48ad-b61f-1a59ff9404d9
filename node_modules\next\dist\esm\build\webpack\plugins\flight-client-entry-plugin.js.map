{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-client-entry-plugin.ts"], "names": ["webpack", "stringify", "path", "sources", "getInvalidator", "getEntries", "EntryTypes", "getEntry<PERSON>ey", "WEBPACK_LAYERS", "APP_CLIENT_INTERNALS", "BARREL_OPTIMIZATION_PREFIX", "COMPILER_NAMES", "DEFAULT_RUNTIME_WEBPACK", "EDGE_RUNTIME_WEBPACK", "SERVER_REFERENCE_MANIFEST", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "getActionsFromBuildInfo", "generateActionId", "isClientComponentEntryModule", "isCSSMod", "regexCSS", "traverseModules", "forEachEntryModule", "formatBarrelOptimizedResource", "getModuleReferencesInOrder", "normalizePathSep", "getProxiedPluginState", "PAGE_TYPES", "isWebpackServerOnlyLayer", "getModuleBuildInfo", "getAssumedSourceType", "PLUGIN_NAME", "pluginState", "serverActions", "edgeServerActions", "actionModServerId", "actionModEdgeServerId", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "injectedClientEntries", "deduplicateCSSImportsForEntry", "mergedCSSimports", "sortedCSSImports", "Object", "entries", "sort", "a", "b", "a<PERSON><PERSON>", "bPath", "a<PERSON><PERSON><PERSON>", "split", "length", "b<PERSON><PERSON><PERSON>", "aName", "parse", "name", "bName", "indexA", "indexOf", "indexB", "dedupedCSSImports", "trackedCSSImports", "Set", "entryName", "cssImports", "cssImport", "has", "filename", "includes", "add", "push", "FlightClientEntryPlugin", "constructor", "options", "dev", "appDir", "isEdgeServer", "assetPrefix", "<PERSON><PERSON><PERSON>", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "finishMake", "tapPromise", "createClientEntries", "afterCompile", "recordModule", "modId", "mod", "modPath", "matchResource", "resourceResolveData", "mod<PERSON><PERSON><PERSON>", "query", "modResource", "startsWith", "resource", "layer", "serverSideRendering", "ssrNamedModuleId", "relative", "context", "replace", "_chunk", "_chunkGroup", "moduleGraph", "isAsync", "make", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createActionAssets", "addClientEntryAndSSRModulesList", "createdSSRDependenciesForEntry", "addActionEntryList", "actionMapsPerEntry", "createdActions", "entryModule", "internalClientComponentEntryImports", "actionEntryImports", "Map", "clientEntriesToInject", "connection", "entryRequest", "dependency", "request", "clientComponentImports", "actionImports", "collectComponentInfoFromServerEntryDependency", "resolvedModule", "for<PERSON>ach", "dep", "names", "isAbsoluteRequest", "isAbsolute", "keys", "value", "relativeRequest", "bundlePath", "assign", "absolutePagePath", "clientEntryToInject", "injected", "injectClientEntryAndSSRModules", "clientImports", "reduce", "res", "curr", "size", "actionNames", "actionName", "injectActionEntry", "actions", "invalidator", "outputPath", "some", "shouldInvalidate", "invalidate", "client", "Promise", "all", "map", "addClientEntryAndSSRModules", "addedClientActionEntryList", "actionMapsPerClientEntry", "ssrEntryDependencies", "collectClientActionsFromDependencies", "remainingClientImportedActions", "remainingActionEntryImports", "remainingActionNames", "id", "fromClient", "collectedActions", "visitedModule", "visitedEntry", "collectActions", "collectActionsInDep", "getModuleResource", "entryDependency", "ssrEntryModule", "getResolvedModule", "depModule", "visited", "CSSImports", "filterClientComponents", "importedIdentifiers", "isCSS", "addClientImport", "webpackRuntime", "sideEffectFree", "factoryMeta", "unused", "getExportsInfo", "isModuleUsed", "dependencyIds", "ids", "Array", "from", "loaderOptions", "modules", "test", "localeCompare", "clientImportPath", "server", "clientBrowserLoader", "sep", "x", "JSON", "clientSSRLoader", "page<PERSON><PERSON>", "APP", "type", "CHILD_ENTRY", "parentEntries", "absoluteEntryFilePath", "dispose", "lastActiveTime", "Date", "now", "entryData", "clientComponentEntryDep", "EntryPlugin", "createDependency", "addEntry", "actionsArray", "actionLoader", "__client_imported__", "currentCompilerServerActions", "p", "workers", "<PERSON><PERSON><PERSON><PERSON>", "reactServerComponents", "actionEntryDep", "resolve", "reject", "entry", "get", "includeDependencies", "call", "addModuleTree", "contextInfo", "issuer<PERSON><PERSON>er", "err", "module", "failedEntry", "<PERSON><PERSON><PERSON><PERSON>", "chunkGroup", "mapping", "action", "serverManifest", "node", "edge", "edgeServerManifest", "json", "undefined", "edgeJson", "RawSource", "modRequest", "isFirstVisitModule", "clientEntryType", "rsc", "isCjsModule", "assumedSourceType", "clientImportsSet", "isAutoModuleSourceType", "isCjsDefaultImport", "identifier"], "mappings": "AAMA,SAASA,OAAO,QAAQ,qCAAoC;AAC5D,SAASC,SAAS,QAAQ,cAAa;AACvC,OAAOC,UAAU,OAAM;AACvB,SAASC,OAAO,QAAQ,qCAAoC;AAC5D,SACEC,cAAc,EACdC,UAAU,EACVC,UAAU,EACVC,WAAW,QACN,8CAA6C;AACpD,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,yBAAyB,EACzBC,gCAAgC,QAC3B,gCAA+B;AACtC,SACEC,uBAAuB,EACvBC,gBAAgB,EAChBC,4BAA4B,EAC5BC,QAAQ,EACRC,QAAQ,QACH,mBAAkB;AACzB,SACEC,eAAe,EACfC,kBAAkB,EAClBC,6BAA6B,EAC7BC,0BAA0B,QACrB,WAAU;AACjB,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,UAAU,QAAQ,0BAAyB;AACpD,SAASC,wBAAwB,QAAQ,cAAa;AACtD,SAASC,kBAAkB,QAAQ,mCAAkC;AACrE,SAASC,oBAAoB,QAAQ,gCAA+B;AASpE,MAAMC,cAAc;AAqBpB,MAAMC,cAAcN,sBAAsB;IACxC,gDAAgD;IAChDO,eAAe,CAAC;IAChBC,mBAAmB,CAAC;IAEpBC,mBAAmB,CAAC;IAOpBC,uBAAuB,CAAC;IAQxB,gEAAgE;IAChEC,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IAEtB,6DAA6D;IAC7D,wEAAwE;IACxE,qFAAqF;IACrF,uCAAuC;IACvCC,sBAAsB,CAAC;IAEvBC,uBAAuB,CAAC;AAC1B;AAEA,SAASC,8BAA8BC,gBAA4B;IACjE,uEAAuE;IACvE,oEAAoE;IACpE,wEAAwE;IACxE,+DAA+D;IAC/D,sEAAsE;IACtE,uEAAuE;IACvE,wEAAwE;IACxE,UAAU;IACV,qEAAqE;IACrE,qEAAqE;IACrE,mEAAmE;IACnE,yEAAyE;IACzE,uFAAuF;IAEvF,2CAA2C;IAC3C,MAAMC,mBAAmBC,OAAOC,OAAO,CAACH,kBAAkBI,IAAI,CAAC,CAACC,GAAGC;QACjE,MAAM,CAACC,MAAM,GAAGF;QAChB,MAAM,CAACG,MAAM,GAAGF;QAEhB,MAAMG,SAASF,MAAMG,KAAK,CAAC,KAAKC,MAAM;QACtC,MAAMC,SAASJ,MAAME,KAAK,CAAC,KAAKC,MAAM;QAEtC,IAAIF,WAAWG,QAAQ;YACrB,OAAOH,SAASG;QAClB;QAEA,MAAMC,QAAQrD,KAAKsD,KAAK,CAACP,OAAOQ,IAAI;QACpC,MAAMC,QAAQxD,KAAKsD,KAAK,CAACN,OAAOO,IAAI;QAEpC,MAAME,SAAS;YAAC;YAAY;SAAS,CAACC,OAAO,CAACL;QAC9C,MAAMM,SAAS;YAAC;YAAY;SAAS,CAACD,OAAO,CAACF;QAE9C,IAAIC,WAAW,CAAC,GAAG,OAAO;QAC1B,IAAIE,WAAW,CAAC,GAAG,OAAO,CAAC;QAC3B,OAAOF,SAASE;IAClB;IAEA,MAAMC,oBAAgC,CAAC;IACvC,MAAMC,oBAAoB,IAAIC;IAC9B,KAAK,MAAM,CAACC,WAAWC,WAAW,IAAIvB,iBAAkB;QACtD,KAAK,MAAMwB,aAAaD,WAAY;YAClC,IAAIH,kBAAkBK,GAAG,CAACD,YAAY;YAEtC,iEAAiE;YACjE,MAAME,WAAWnE,KAAKsD,KAAK,CAACS,WAAWR,IAAI;YAC3C,IAAI;gBAAC;gBAAY;aAAS,CAACa,QAAQ,CAACD,WAAW;gBAC7CN,kBAAkBQ,GAAG,CAACJ;YACxB;YAEA,IAAI,CAACL,iBAAiB,CAACG,UAAU,EAAE;gBACjCH,iBAAiB,CAACG,UAAU,GAAG,EAAE;YACnC;YACAH,iBAAiB,CAACG,UAAU,CAACO,IAAI,CAACL;QACpC;IACF;IAEA,OAAOL;AACT;AAEA,OAAO,MAAMW;IAOXC,YAAYC,OAAgB,CAAE;QAC5B,IAAI,CAACC,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,YAAY,GAAGH,QAAQG,YAAY;QACxC,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACH,GAAG,IAAI,CAAC,IAAI,CAACE,YAAY,GAAG,QAAQ;QAC7D,IAAI,CAACE,aAAa,GAAGL,QAAQK,aAAa;IAC5C;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5BtD,aACA,CAACqD,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjCxF,QAAQyF,YAAY,CAACC,gBAAgB,EACrCJ;YAEFF,YAAYO,mBAAmB,CAACH,GAAG,CACjCxF,QAAQyF,YAAY,CAACC,gBAAgB,EACrC,IAAI1F,QAAQyF,YAAY,CAACG,cAAc,CAACC,QAAQ;QAEpD;QAGFX,SAASC,KAAK,CAACW,UAAU,CAACC,UAAU,CAAChE,aAAa,CAACqD,cACjD,IAAI,CAACY,mBAAmB,CAACd,UAAUE;QAGrCF,SAASC,KAAK,CAACc,YAAY,CAACZ,GAAG,CAACtD,aAAa,CAACqD;YAC5C,MAAMc,eAAe,CAACC,OAAeC;oBAGEA,0BACpBA;gBAHjB,yFAAyF;gBACzF,2DAA2D;gBAC3D,MAAMC,UAAUD,IAAIE,aAAa,MAAIF,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBlG,IAAI;gBAClE,MAAMsG,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;gBACnD,wCAAwC;gBACxC,gFAAgF;gBAChF,MAAMC,cAAcL,UAChBA,QAAQM,UAAU,CAACjG,8BACjBa,8BAA8B6E,IAAIQ,QAAQ,EAAEP,WAC5CA,UAAUG,WACZJ,IAAIQ,QAAQ;gBAEhB,IAAIR,IAAIS,KAAK,KAAKrG,eAAesG,mBAAmB,EAAE;oBACpD;gBACF;gBAEA,yHAAyH;gBACzH,IAAI,OAAOX,UAAU,eAAeO,aAAa;oBAC/C,4EAA4E;oBAC5E,6EAA6E;oBAC7E,sBAAsB;oBACtB,IAAIK,mBAAmB7G,KAAK8G,QAAQ,CAAC9B,SAAS+B,OAAO,EAAEP;oBAEvD,IAAI,CAACK,iBAAiBJ,UAAU,CAAC,MAAM;wBACrC,+BAA+B;wBAC/BI,mBAAmB,CAAC,EAAE,EAAEtF,iBAAiBsF,kBAAkB,CAAC;oBAC9D;oBAEA,IAAI,IAAI,CAACjC,YAAY,EAAE;wBACrB9C,YAAYM,mBAAmB,CAC7ByE,iBAAiBG,OAAO,CAAC,uBAAuB,eACjD,GAAGf;oBACN,OAAO;wBACLnE,YAAYK,eAAe,CAAC0E,iBAAiB,GAAGZ;oBAClD;gBACF;YACF;YAEA9E,gBAAgB+D,aAAa,CAACgB,KAAKe,QAAQC,aAAajB;gBACtD,IAAIC,OAAOA,IAAIQ,QAAQ,IAAI,CAAChF,yBAAyBwE,IAAIS,KAAK,GAAG;oBAC/D,IAAIzB,YAAYiC,WAAW,CAACC,OAAO,CAAClB,MAAM;wBACxC,0FAA0F;wBAC1F,4FAA4F;wBAC5FpE,YAAYO,oBAAoB,CAAC6D,IAAIQ,QAAQ,CAAC,GAAG;oBACnD;gBACF;gBAEA,IAAIT,OAAOD,aAAaC,OAAOC;YACjC;QACF;QAEAlB,SAASC,KAAK,CAACoC,IAAI,CAAClC,GAAG,CAACtD,aAAa,CAACqD;YACpCA,YAAYD,KAAK,CAACqC,aAAa,CAACzB,UAAU,CACxC;gBACEtC,MAAM1B;gBACN0F,OAAOzH,QAAQ0H,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,kBAAkB,CAACzC,aAAawC;QAErD;IACF;IAEA,MAAM5B,oBACJd,QAA0B,EAC1BE,WAAgC,EAChC;QACA,MAAM0C,kCAEF,EAAE;QACN,MAAMC,iCAGF,CAAC;QAEL,MAAMC,qBACJ,EAAE;QACJ,MAAMC,qBAA4D,CAAC;QACnE,MAAMC,iBAAiB,IAAIlE;QAE3B,4EAA4E;QAC5E,0BAA0B;QAC1B1C,mBAAmB8D,aAAa,CAAC,EAAE3B,IAAI,EAAE0E,WAAW,EAAE;YACpD,MAAMC,sCAA8D,CAAC;YACrE,MAAMC,qBAAqB,IAAIC;YAC/B,MAAMC,wBAAwB,EAAE;YAChC,MAAM7F,mBAA+B,CAAC;YAEtC,KAAK,MAAM8F,cAAchH,2BACvB2G,aACA/C,YAAYiC,WAAW,EACtB;gBACD,uFAAuF;gBACvF,MAAMoB,eAAe,AACnBD,WAAWE,UAAU,CACrBC,OAAO;gBAET,MAAM,EAAEC,sBAAsB,EAAEC,aAAa,EAAE3E,UAAU,EAAE,GACzD,IAAI,CAAC4E,6CAA6C,CAAC;oBACjDL;oBACArD;oBACA2D,gBAAgBP,WAAWO,cAAc;gBAC3C;gBAEFF,cAAcG,OAAO,CAAC,CAAC,CAACC,KAAKC,MAAM,GACjCb,mBAAmB7C,GAAG,CAACyD,KAAKC;gBAG9B,MAAMC,oBAAoBjJ,KAAKkJ,UAAU,CAACX;gBAE1C,mDAAmD;gBACnD,IAAI,CAACU,mBAAmB;oBACtBvG,OAAOyG,IAAI,CAACT,wBAAwBI,OAAO,CACzC,CAACM,QAAWlB,mCAAmC,CAACkB,MAAM,GAAG,IAAItF;oBAE/D;gBACF;gBAEA,2HAA2H;gBAC3H,4DAA4D;gBAC5D,kEAAkE;gBAClE,aAAa;gBACb,IAAI;gBAEJ,MAAMuF,kBAAkBJ,oBACpBjJ,KAAK8G,QAAQ,CAAC5B,YAAYT,OAAO,CAACsC,OAAO,EAAGwB,gBAC5CA;gBAEJ,8CAA8C;gBAC9C,MAAMe,aAAa/H,iBACjB8H,gBAAgBrC,OAAO,CAAC,eAAe,IAAIA,OAAO,CAAC,aAAa;gBAGlEtE,OAAO6G,MAAM,CAAC/G,kBAAkBwB;gBAChCqE,sBAAsB/D,IAAI,CAAC;oBACzBU;oBACAE;oBACAnB,WAAWR;oBACXmF;oBACAY;oBACAE,kBAAkBjB;gBACpB;gBAEA,uKAAuK;gBACvK,wGAAwG;gBACxG,8IAA8I;gBAC9I,IACEhF,SAAS,CAAC,GAAG,EAAE1C,iCAAiC,CAAC,IACjDyI,eAAe,iBACf;oBACAjB,sBAAsB/D,IAAI,CAAC;wBACzBU;wBACAE;wBACAnB,WAAWR;wBACXmF,wBAAwB,CAAC;wBACzBY,YAAY,CAAC,GAAG,EAAEzI,iCAAiC,CAAC;wBACpD2I,kBAAkBjB;oBACpB;gBACF;YACF;YAEA,2EAA2E;YAC3E,mBAAmB;YACnB,MAAM3E,oBAAoBrB,8BAA8BC;YACxD,KAAK,MAAMiH,uBAAuBpB,sBAAuB;gBACvD,MAAMqB,WAAW,IAAI,CAACC,8BAA8B,CAAC;oBACnD,GAAGF,mBAAmB;oBACtBG,eAAe;wBACb,GAAGH,oBAAoBf,sBAAsB;wBAC7C,GAAG,AACD9E,CAAAA,iBAAiB,CAAC6F,oBAAoBD,gBAAgB,CAAC,IAAI,EAAE,AAAD,EAC5DK,MAAM,CAAyB,CAACC,KAAKC;4BACrCD,GAAG,CAACC,KAAK,GAAG,IAAIjG;4BAChB,OAAOgG;wBACT,GAAG,CAAC,EAAE;oBACR;gBACF;gBAEA,2EAA2E;gBAC3E,IAAI,CAACjC,8BAA8B,CAAC4B,oBAAoB1F,SAAS,CAAC,EAAE;oBAClE8D,8BAA8B,CAAC4B,oBAAoB1F,SAAS,CAAC,GAAG,EAAE;gBACpE;gBACA8D,8BAA8B,CAAC4B,oBAAoB1F,SAAS,CAAC,CAACO,IAAI,CAChEoF,QAAQ,CAAC,EAAE;gBAGb9B,gCAAgCtD,IAAI,CAACoF;YACvC;YAEA,sBAAsB;YACtB9B,gCAAgCtD,IAAI,CAClC,IAAI,CAACqF,8BAA8B,CAAC;gBAClC3E;gBACAE;gBACAnB,WAAWR;gBACXqG,eAAe;oBAAE,GAAG1B,mCAAmC;gBAAC;gBACxDoB,YAAY/I;YACd;YAGF,IAAI4H,mBAAmB6B,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACjC,kBAAkB,CAACxE,KAAK,EAAE;oBAC7BwE,kBAAkB,CAACxE,KAAK,GAAG,IAAI6E;gBACjC;gBACAL,kBAAkB,CAACxE,KAAK,GAAG,IAAI6E,IAAI;uBAC9BL,kBAAkB,CAACxE,KAAK;uBACxB4E;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAAC5E,MAAM4E,mBAAmB,IAAIzF,OAAOC,OAAO,CACrDoF,oBACC;YACD,KAAK,MAAM,CAACgB,KAAKkB,YAAY,IAAI9B,mBAAoB;gBACnD,KAAK,MAAM+B,cAAcD,YAAa;oBACpCjC,eAAe3D,GAAG,CAACd,OAAO,MAAMwF,MAAM,MAAMmB;gBAC9C;YACF;YACApC,mBAAmBxD,IAAI,CACrB,IAAI,CAAC6F,iBAAiB,CAAC;gBACrBnF;gBACAE;gBACAkF,SAASjC;gBACTpE,WAAWR;gBACX+F,YAAY/F;YACd;QAEJ;QAEA,qDAAqD;QACrD,MAAM8G,cAAcnK,eAAe8E,SAASsF,UAAU;QACtD,4DAA4D;QAC5D,IACED,eACAzC,gCAAgC2C,IAAI,CAClC,CAAC,CAACC,iBAAiB,GAAKA,qBAAqB,OAE/C;YACAH,YAAYI,UAAU,CAAC;gBAAChK,eAAeiK,MAAM;aAAC;QAChD;QAEA,qGAAqG;QACrG,6EAA6E;QAC7E,MAAMC,QAAQC,GAAG,CACfhD,gCAAgCiD,GAAG,CACjC,CAACC,8BAAgCA,2BAA2B,CAAC,EAAE;QAInE,uCAAuC;QACvC,MAAMH,QAAQC,GAAG,CAAC9C;QAElB,MAAMiD,6BAA6C,EAAE;QACrD,MAAMC,2BAAkE,CAAC;QAEzE,mEAAmE;QACnE,gBAAgB;QAChB,yEAAyE;QACzE,KAAK,MAAM,CAACzH,MAAM0H,qBAAqB,IAAIvI,OAAOC,OAAO,CACvDkF,gCACC;YACD,qEAAqE;YACrE,sBAAsB;YACtB,MAAMM,qBAAqB,IAAI,CAAC+C,oCAAoC,CAAC;gBACnEhG;gBACAK,cAAc0F;YAChB;YAEA,IAAI9C,mBAAmB6B,IAAI,GAAG,GAAG;gBAC/B,IAAI,CAACgB,wBAAwB,CAACzH,KAAK,EAAE;oBACnCyH,wBAAwB,CAACzH,KAAK,GAAG,IAAI6E;gBACvC;gBACA4C,wBAAwB,CAACzH,KAAK,GAAG,IAAI6E,IAAI;uBACpC4C,wBAAwB,CAACzH,KAAK;uBAC9B4E;iBACJ;YACH;QACF;QAEA,KAAK,MAAM,CAAC5E,MAAM4E,mBAAmB,IAAIzF,OAAOC,OAAO,CACrDqI,0BACC;YACD,uEAAuE;YACvE,+CAA+C;YAC/C,uEAAuE;YACvE,mBAAmB;YACnB,IAAIG,iCAAiC;YACrC,MAAMC,8BAA8B,IAAIhD;YACxC,KAAK,MAAM,CAACW,KAAKkB,YAAY,IAAI9B,mBAAoB;gBACnD,MAAMkD,uBAAuB,EAAE;gBAC/B,KAAK,MAAMnB,cAAcD,YAAa;oBACpC,MAAMqB,KAAK/H,OAAO,MAAMwF,MAAM,MAAMmB;oBACpC,IAAI,CAAClC,eAAe9D,GAAG,CAACoH,KAAK;wBAC3BD,qBAAqB/G,IAAI,CAAC4F;oBAC5B;gBACF;gBACA,IAAImB,qBAAqBlI,MAAM,GAAG,GAAG;oBACnCiI,4BAA4B9F,GAAG,CAACyD,KAAKsC;oBACrCF,iCAAiC;gBACnC;YACF;YAEA,IAAIA,gCAAgC;gBAClCJ,2BAA2BzG,IAAI,CAC7B,IAAI,CAAC6F,iBAAiB,CAAC;oBACrBnF;oBACAE;oBACAkF,SAASgB;oBACTrH,WAAWR;oBACX+F,YAAY/F;oBACZgI,YAAY;gBACd;YAEJ;QACF;QAEA,MAAMZ,QAAQC,GAAG,CAACG;IACpB;IAEAG,qCAAqC,EACnChG,WAAW,EACXK,YAAY,EAIb,EAAE;QACD,mCAAmC;QACnC,MAAMiG,mBAAmB,IAAIpD;QAE7B,gFAAgF;QAChF,MAAMqD,gBAAgB,IAAI3H;QAC1B,MAAM4H,eAAe,IAAI5H;QAEzB,MAAM6H,iBAAiB,CAAC,EACtBpD,YAAY,EACZM,cAAc,EAIf;YACC,MAAM+C,sBAAsB,CAAC1F;gBAC3B,IAAI,CAACA,KAAK;gBAEV,MAAMM,cAAcqF,kBAAkB3F;gBAEtC,IAAI,CAACM,eAAeiF,cAAcvH,GAAG,CAACsC,cAAc;gBACpDiF,cAAcpH,GAAG,CAACmC;gBAElB,MAAM4D,UAAUtJ,wBAAwBoF;gBACxC,IAAIkE,SAAS;oBACXoB,iBAAiBlG,GAAG,CAACkB,aAAa4D;gBACpC;gBAEA9I,2BAA2B4E,KAAKhB,YAAYiC,WAAW,EAAE2B,OAAO,CAC9D,CAACR;oBACCsD,oBACEtD,WAAWO,cAAc;gBAE7B;YAEJ;YAEA,yEAAyE;YACzE,IACEN,gBACA,CAACA,aAAanE,QAAQ,CAAC,oCACvB;gBACA,2DAA2D;gBAC3DwH,oBAAoB/C;YACtB;QACF;QAEA,KAAK,MAAMiD,mBAAmBvG,aAAc;YAC1C,MAAMwG,iBACJ7G,YAAYiC,WAAW,CAAC6E,iBAAiB,CAACF;YAC5C,KAAK,MAAMxD,cAAchH,2BACvByK,gBACA7G,YAAYiC,WAAW,EACtB;gBACD,MAAM8E,YAAY3D,WAAWE,UAAU;gBACvC,MAAMC,UAAU,AAACwD,UAA8CxD,OAAO;gBAEtE,oEAAoE;gBACpE,oEAAoE;gBACpE,IAAIiD,aAAaxH,GAAG,CAACuE,UAAU;gBAC/BiD,aAAarH,GAAG,CAACoE;gBAEjBkD,eAAe;oBACbpD,cAAcE;oBACdI,gBAAgBP,WAAWO,cAAc;gBAC3C;YACF;QACF;QAEA,OAAO2C;IACT;IAEA5C,8CAA8C,EAC5CL,YAAY,EACZrD,WAAW,EACX2D,cAAc,EAKf,EAIC;QACA,gFAAgF;QAChF,MAAMqD,UAAU,IAAIpI;QAEpB,mBAAmB;QACnB,MAAM4E,yBAAiD,CAAC;QACxD,MAAMC,gBAAsC,EAAE;QAC9C,MAAMwD,aAAa,IAAIrI;QAEvB,MAAMsI,yBAAyB,CAC7BlG,KACAmG;YAEA,IAAI,CAACnG,KAAK;YAEV,MAAMoG,QAAQrL,SAASiF;YACvB,MAAMM,cAAcqF,kBAAkB3F;YAEtC,IAAI,CAACM,aAAa;YAClB,IAAI0F,QAAQhI,GAAG,CAACsC,cAAc;gBAC5B,IAAIkC,sBAAsB,CAAClC,YAAY,EAAE;oBACvC+F,gBACErG,KACAM,aACAkC,wBACA2D,qBACA;gBAEJ;gBACA;YACF;YACAH,QAAQ7H,GAAG,CAACmC;YAEZ,MAAM4D,UAAUtJ,wBAAwBoF;YACxC,IAAIkE,SAAS;gBACXzB,cAAcrE,IAAI,CAAC;oBAACkC;oBAAa4D;iBAAQ;YAC3C;YAEA,MAAMoC,iBAAiB,IAAI,CAAC5H,YAAY,GACpCjE,uBACAD;YAEJ,IAAI4L,OAAO;gBACT,MAAMG,iBACJvG,IAAIwG,WAAW,IAAI,AAACxG,IAAIwG,WAAW,CAASD,cAAc;gBAE5D,IAAIA,gBAAgB;oBAClB,MAAME,SAAS,CAACzH,YAAYiC,WAAW,CACpCyF,cAAc,CAAC1G,KACf2G,YAAY,CAACL;oBAEhB,IAAIG,QAAQ;gBACd;gBAEAR,WAAW9H,GAAG,CAACmC;YACjB,OAAO,IAAIxF,6BAA6BkF,MAAM;gBAC5C,IAAI,CAACwC,sBAAsB,CAAClC,YAAY,EAAE;oBACxCkC,sBAAsB,CAAClC,YAAY,GAAG,IAAI1C;gBAC5C;gBACAyI,gBACErG,KACAM,aACAkC,wBACA2D,qBACA;gBAGF;YACF;YAEA/K,2BAA2B4E,KAAKhB,YAAYiC,WAAW,EAAE2B,OAAO,CAC9D,CAACR;oBAKKA;gBAJJ,IAAIwE,gBAA0B,EAAE;gBAEhC,mEAAmE;gBACnE,6CAA6C;gBAC7C,KAAIxE,yBAAAA,WAAWE,UAAU,qBAArBF,uBAAuByE,GAAG,EAAE;oBAC9BD,cAAcxI,IAAI,IAAIgE,WAAWE,UAAU,CAACuE,GAAG;gBACjD,OAAO;oBACLD,gBAAgB;wBAAC;qBAAI;gBACvB;gBAEAV,uBAAuB9D,WAAWO,cAAc,EAAEiE;YACpD;QAEJ;QAEA,2DAA2D;QAC3DV,uBAAuBvD,gBAAgB,EAAE;QAEzC,OAAO;YACLH;YACA1E,YAAYmI,WAAWnC,IAAI,GACvB;gBACE,CAACzB,aAAa,EAAEyE,MAAMC,IAAI,CAACd;YAC7B,IACA,CAAC;YACLxD;QACF;IACF;IAEAgB,+BAA+B,EAC7B3E,QAAQ,EACRE,WAAW,EACXnB,SAAS,EACT6F,aAAa,EACbN,UAAU,EACVE,gBAAgB,EAQjB,EAIC;QACA,IAAIgB,mBAAmB;QAEvB,MAAM0C,gBAGF;YACFC,SAASzK,OAAOyG,IAAI,CAACS,eAClBhH,IAAI,CAAC,CAACC,GAAGC,IAAO5B,SAASkM,IAAI,CAACtK,KAAK,IAAID,EAAEwK,aAAa,CAACvK,IACvD+H,GAAG,CAAC,CAACyC,mBAAsB,CAAA;oBAC1B7E,SAAS6E;oBACTP,KAAK;2BAAInD,aAAa,CAAC0D,iBAAiB;qBAAC;gBAC3C,CAAA;YACFC,QAAQ;QACV;QAEA,uEAAuE;QACvE,0EAA0E;QAC1E,gBAAgB;QAChB,MAAMC,sBAAsB,CAAC,gCAAgC,EAAEzN,UAAU;YACvEoN,SAAS,AAAC,CAAA,IAAI,CAACvI,YAAY,GACvBsI,cAAcC,OAAO,CAACtC,GAAG,CAAC,CAAC,EAAEpC,OAAO,EAAEsE,GAAG,EAAE,GAAM,CAAA;oBAC/CtE,SAASA,QAAQzB,OAAO,CACtB,mCACA,cAAcA,OAAO,CAAC,OAAOhH,KAAKyN,GAAG;oBAEvCV;gBACF,CAAA,KACAG,cAAcC,OAAO,AAAD,EACtBtC,GAAG,CAAC,CAAC6C,IAAMC,KAAK5N,SAAS,CAAC2N;YAC5BH,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,MAAMK,kBAAkB,CAAC,gCAAgC,EAAE7N,UAAU;YACnEoN,SAASD,cAAcC,OAAO,CAACtC,GAAG,CAAC,CAAC6C,IAAMC,KAAK5N,SAAS,CAAC2N;YACzDH,QAAQ;QACV,GAAG,CAAC,CAAC;QAEL,iCAAiC;QACjC,2CAA2C;QAC3C,IAAI,IAAI,CAAC7I,GAAG,EAAE;YACZ,MAAM/B,UAAUxC,WAAW6E,SAASsF,UAAU;YAC9C,MAAMuD,UAAUxN,YACdI,eAAeiK,MAAM,EACrBjJ,WAAWqM,GAAG,EACdxE;YAGF,IAAI,CAAC3G,OAAO,CAACkL,QAAQ,EAAE;gBACrBlL,OAAO,CAACkL,QAAQ,GAAG;oBACjBE,MAAM3N,WAAW4N,WAAW;oBAC5BC,eAAe,IAAInK,IAAI;wBAACC;qBAAU;oBAClCmK,uBAAuB1E;oBACvBF;oBACAb,SAAS+E;oBACTW,SAAS;oBACTC,gBAAgBC,KAAKC,GAAG;gBAC1B;gBACA9D,mBAAmB;YACrB,OAAO;gBACL,MAAM+D,YAAY5L,OAAO,CAACkL,QAAQ;gBAClC,mCAAmC;gBACnC,IAAIU,UAAU9F,OAAO,KAAK+E,qBAAqB;oBAC7Ce,UAAU9F,OAAO,GAAG+E;oBACpBhD,mBAAmB;gBACrB;gBACA,IAAI+D,UAAUR,IAAI,KAAK3N,WAAW4N,WAAW,EAAE;oBAC7CO,UAAUN,aAAa,CAAC5J,GAAG,CAACN;gBAC9B;gBACAwK,UAAUJ,OAAO,GAAG;gBACpBI,UAAUH,cAAc,GAAGC,KAAKC,GAAG;YACrC;QACF,OAAO;YACLxM,YAAYQ,qBAAqB,CAACgH,WAAW,GAAGkE;QAClD;QAEA,qDAAqD;QACrD,MAAMgB,0BAA0B1O,QAAQ2O,WAAW,CAACC,gBAAgB,CAClEd,iBACA;YACErK,MAAM+F;QACR;QAGF,OAAO;YACLkB;YACA,6CAA6C;YAC7C,gGAAgG;YAChG,qEAAqE;YACrE,IAAI,CAACmE,QAAQ,CACXzJ,aACA,6BAA6B;YAC7BF,SAAS+B,OAAO,EAChByH,yBACA;gBACE,+BAA+B;gBAC/BjL,MAAMQ;gBACN,6CAA6C;gBAC7C,iEAAiE;gBACjE4C,OAAOrG,eAAesG,mBAAmB;YAC3C;YAEF4H;SACD;IACH;IAEArE,kBAAkB,EAChBnF,QAAQ,EACRE,WAAW,EACXkF,OAAO,EACPrG,SAAS,EACTuF,UAAU,EACViC,UAAU,EAQX,EAAE;QACD,MAAMqD,eAAe5B,MAAMC,IAAI,CAAC7C,QAAQzH,OAAO;QAE/C,MAAMkM,eAAe,CAAC,gCAAgC,EAAE9O,UAAU;YAChEqK,SAASuD,KAAK5N,SAAS,CAAC6O;YACxBE,qBAAqBvD;QACvB,GAAG,CAAC,CAAC;QAEL,MAAMwD,+BAA+B,IAAI,CAACnK,YAAY,GAClD9C,YAAYE,iBAAiB,GAC7BF,YAAYC,aAAa;QAC7B,KAAK,MAAM,CAACiN,GAAGhG,MAAM,IAAI4F,aAAc;YACrC,KAAK,MAAMrL,QAAQyF,MAAO;gBACxB,MAAMsC,KAAKvK,iBAAiBiO,GAAGzL;gBAC/B,IAAI,OAAOwL,4BAA4B,CAACzD,GAAG,KAAK,aAAa;oBAC3DyD,4BAA4B,CAACzD,GAAG,GAAG;wBACjC2D,SAAS,CAAC;wBACVtI,OAAO,CAAC;oBACV;gBACF;gBACAoI,4BAA4B,CAACzD,GAAG,CAAC2D,OAAO,CAAC3F,WAAW,GAAG;gBACvDyF,4BAA4B,CAACzD,GAAG,CAAC3E,KAAK,CAAC2C,WAAW,GAAGiC,aACjDjL,eAAe4O,aAAa,GAC5B5O,eAAe6O,qBAAqB;YAC1C;QACF;QAEA,0CAA0C;QAC1C,MAAMC,iBAAiBtP,QAAQ2O,WAAW,CAACC,gBAAgB,CAACG,cAAc;YACxEtL,MAAM+F;QACR;QAEA,OAAO,IAAI,CAACqF,QAAQ,CAClBzJ,aACA,6BAA6B;QAC7BF,SAAS+B,OAAO,EAChBqI,gBACA;YACE7L,MAAMQ;YACN4C,OAAO4E,aACHjL,eAAe4O,aAAa,GAC5B5O,eAAe6O,qBAAqB;QAC1C;IAEJ;IAEAR,SACEzJ,WAAgB,EAChB6B,OAAe,EACfyB,UAA8B,EAC9B/D,OAA6B,EACf,mBAAmB,GAAG;QACpC,OAAO,IAAIkG,QAAQ,CAAC0E,SAASC;YAC3B,MAAMC,QAAQrK,YAAYvC,OAAO,CAAC6M,GAAG,CAAC/K,QAAQlB,IAAI;YAClDgM,MAAME,mBAAmB,CAACnL,IAAI,CAACkE;YAC/BtD,YAAYD,KAAK,CAAC0J,QAAQ,CAACe,IAAI,CAACH,OAAO9K;YACvCS,YAAYyK,aAAa,CACvB;gBACE5I;gBACAyB;gBACAoH,aAAa;oBAAEC,aAAapL,QAAQkC,KAAK;gBAAC;YAC5C,GACA,CAACmJ,KAAwBC;gBACvB,IAAID,KAAK;oBACP5K,YAAYD,KAAK,CAAC+K,WAAW,CAACN,IAAI,CAAClH,YAAY/D,SAASqL;oBACxD,OAAOR,OAAOQ;gBAChB;gBAEA5K,YAAYD,KAAK,CAACgL,YAAY,CAACP,IAAI,CAAClH,YAAY/D,SAASsL;gBACzD,OAAOV,QAAQU;YACjB;QAEJ;IACF;IAEA,MAAMpI,mBACJzC,WAAgC,EAChCwC,MAAqC,EACrC;QACA,MAAM3F,gBAAwC,CAAC;QAC/C,MAAMC,oBAA4C,CAAC;QAEnDb,gBAAgB+D,aAAa,CAACgB,KAAKe,QAAQiJ,YAAYjK;YACrD,yEAAyE;YACzE,IACEiK,WAAW3M,IAAI,IACf2C,IAAIuC,OAAO,IACXxC,SACA,kCAAkCmH,IAAI,CAAClH,IAAIuC,OAAO,GAClD;gBACA,MAAM8C,aAAa,4BAA4B6B,IAAI,CAAClH,IAAIuC,OAAO;gBAE/D,MAAM0H,UAAU,IAAI,CAACvL,YAAY,GAC7B9C,YAAYI,qBAAqB,GACjCJ,YAAYG,iBAAiB;gBAEjC,IAAI,CAACkO,OAAO,CAACD,WAAW3M,IAAI,CAAC,EAAE;oBAC7B4M,OAAO,CAACD,WAAW3M,IAAI,CAAC,GAAG,CAAC;gBAC9B;gBACA4M,OAAO,CAACD,WAAW3M,IAAI,CAAC,CAACgI,aAAa,WAAW,SAAS,GAAGtF;YAC/D;QACF;QAEA,IAAK,IAAIqF,MAAMxJ,YAAYC,aAAa,CAAE;YACxC,MAAMqO,SAAStO,YAAYC,aAAa,CAACuJ,GAAG;YAC5C,IAAK,IAAI/H,QAAQ6M,OAAOnB,OAAO,CAAE;gBAC/B,MAAMhJ,QACJnE,YAAYG,iBAAiB,CAACsB,KAAK,CACjC6M,OAAOzJ,KAAK,CAACpD,KAAK,KAAKjD,eAAe4O,aAAa,GAC/C,WACA,SACL;gBACHkB,OAAOnB,OAAO,CAAC1L,KAAK,GAAG0C;YACzB;YACAlE,aAAa,CAACuJ,GAAG,GAAG8E;QACtB;QAEA,IAAK,IAAI9E,MAAMxJ,YAAYE,iBAAiB,CAAE;YAC5C,MAAMoO,SAAStO,YAAYE,iBAAiB,CAACsJ,GAAG;YAChD,IAAK,IAAI/H,QAAQ6M,OAAOnB,OAAO,CAAE;gBAC/B,MAAMhJ,QACJnE,YAAYI,qBAAqB,CAACqB,KAAK,CACrC6M,OAAOzJ,KAAK,CAACpD,KAAK,KAAKjD,eAAe4O,aAAa,GAC/C,WACA,SACL;gBACHkB,OAAOnB,OAAO,CAAC1L,KAAK,GAAG0C;YACzB;YACAjE,iBAAiB,CAACsJ,GAAG,GAAG8E;QAC1B;QAEA,MAAMC,iBAAiB;YACrBC,MAAMvO;YACNwO,MAAMvO;YACN8C,eAAe,IAAI,CAACA,aAAa;QACnC;QACA,MAAM0L,qBAAqB;YACzB,GAAGH,cAAc;YACjBvL,eAAe;QACjB;QAEA,MAAM2L,OAAO9C,KAAK5N,SAAS,CAACsQ,gBAAgB,MAAM,IAAI,CAAC3L,GAAG,GAAG,IAAIgM;QACjE,MAAMC,WAAWhD,KAAK5N,SAAS,CAC7ByQ,oBACA,MACA,IAAI,CAAC9L,GAAG,GAAG,IAAIgM;QAGjBhJ,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC7C,WAAW,CAAC,EAAEjE,0BAA0B,GAAG,CAAC,CAAC,GAC1D,IAAIX,QAAQ2Q,SAAS,CACnB,CAAC,2BAA2B,EAAEjD,KAAK5N,SAAS,CAAC4Q,UAAU,CAAC;QAE5DjJ,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC7C,WAAW,CAAC,EAAEjE,0BAA0B,KAAK,CAAC,CAAC,GAC5D,IAAIX,QAAQ2Q,SAAS,CAACH;IAC1B;AACF;AAEA,SAASlE,gBACPrG,GAAyB,EACzB2K,UAAkB,EAClBnI,sBAA8C,EAC9C2D,mBAA6B,EAC7ByE,kBAA2B;QAEHnP;IAAxB,MAAMoP,mBAAkBpP,0BAAAA,mBAAmBuE,KAAK8K,GAAG,qBAA3BrP,wBAA6BoP,eAAe;IACpE,MAAME,cAAcF,oBAAoB;IACxC,MAAMG,oBAAoBtP,qBACxBsE,KACA+K,cAAc,aAAa;IAG7B,MAAME,mBAAmBzI,sBAAsB,CAACmI,WAAW;IAE3D,IAAIxE,mBAAmB,CAAC,EAAE,KAAK,KAAK;QAClC,kEAAkE;QAClE,qDAAqD;QACrD,sCAAsC;QACtC,IAAI,CAACyE,sBAAsB;eAAIK;SAAiB,CAAC,EAAE,KAAK,KAAK;YAC3DzI,sBAAsB,CAACmI,WAAW,GAAG,IAAI/M,IAAI;gBAAC;aAAI;QACpD;IACF,OAAO;QACL,MAAMsN,yBAAyBF,sBAAsB;QACrD,IAAIE,wBAAwB;YAC1B1I,sBAAsB,CAACmI,WAAW,GAAG,IAAI/M,IAAI;gBAAC;aAAI;QACpD,OAAO;YACL,gGAAgG;YAChG,oEAAoE;YACpE,KAAK,MAAMP,QAAQ8I,oBAAqB;gBACtC,mEAAmE;gBACnE,MAAMgF,qBAAqBJ,eAAe1N,SAAS;gBAEnD,kEAAkE;gBAClE,4DAA4D;gBAC5D,IAAI8N,oBAAoB;oBACtB3I,sBAAsB,CAACmI,WAAW,CAACxM,GAAG,CAAC;gBACzC;gBAEAqE,sBAAsB,CAACmI,WAAW,CAACxM,GAAG,CAACd;YACzC;QACF;IACF;AACF;AAEA,SAASsI,kBAAkB3F,GAAyB;QAC1BA,0BACPA,2BAebA;IAhBJ,MAAMC,UAAkBD,EAAAA,2BAAAA,IAAIG,mBAAmB,qBAAvBH,yBAAyBlG,IAAI,KAAI;IACzD,MAAMsG,WAAWJ,EAAAA,4BAAAA,IAAIG,mBAAmB,qBAAvBH,0BAAyBK,KAAK,KAAI;IACnD,mEAAmE;IACnE,yEAAyE;IACzE,0EAA0E;IAC1E,IAAIC,cAAsBL,UAAUG;IAEpC,6EAA6E;IAC7E,IAAIJ,IAAI1B,WAAW,CAACjB,IAAI,KAAK,iBAAiB;QAC5CiD,cAAcN,IAAIoL,UAAU;IAC9B;IAEA,yEAAyE;IACzE,yEAAyE;IACzE,0EAA0E;IAC1E,wEAAwE;IACxE,KAAIpL,qBAAAA,IAAIE,aAAa,qBAAjBF,mBAAmBO,UAAU,CAACjG,6BAA6B;QAC7DgG,cAAcN,IAAIE,aAAa,GAAG,MAAMI;IAC1C;IACA,OAAOA;AACT"}