{"version": 3, "file": "ArrayNotContains.js", "sourceRoot": "", "sources": ["../../../../src/decorator/array/ArrayNotContains.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,kBAAkB,GAAG,kBAAkB,CAAC;AAErD;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAc,EAAE,MAAa;IAC5D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IAExC,OAAO,MAAM,CAAC,KAAK,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAA3B,CAA2B,CAAC,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,MAAa,EAAE,iBAAqC;IACnF,OAAO,UAAU,CACf;QACE,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,gBAAgB,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAA7C,CAA6C;YACjF,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,kDAAkD,EAA/D,CAA+D,EAC7E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const ARRAY_NOT_CONTAINS = 'arrayNotContains';\n\n/**\n * Checks if array does not contain any of the given values.\n * If null or undefined is given then this function returns false.\n */\nexport function arrayNotContains(array: unknown, values: any[]): boolean {\n  if (!Array.isArray(array)) return false;\n\n  return values.every(value => array.indexOf(value) === -1);\n}\n\n/**\n * Checks if array does not contain any of the given values.\n * If null or undefined is given then this function returns false.\n */\nexport function ArrayNotContains(values: any[], validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: ARRAY_NOT_CONTAINS,\n      constraints: [values],\n      validator: {\n        validate: (value, args): boolean => arrayNotContains(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property should not contain $constraint1 values',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}