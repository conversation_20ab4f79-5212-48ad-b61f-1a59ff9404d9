{"version": 3, "file": "isValidPhoneNumber.test.js", "names": ["_isValidPhoneNumber", "metadata", "type", "isValidPhoneNumber", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "apply", "describe", "it", "expect", "to", "equal", "defaultCountry"], "sources": ["../source/isValidPhoneNumber.test.js"], "sourcesContent": ["import _isValidPhoneNumber from './isValidPhoneNumber.js'\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\nfunction isValidPhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isValidPhoneNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidPhoneNumber', () => {\r\n\tit('should detect whether a phone number is valid', () => {\r\n\t\texpect(isValidPhoneNumber('8 (800) 555 35 35', 'RU')).to.equal(true)\r\n\t\texpect(isValidPhoneNumber('8 (800) 555 35 35 0', 'RU')).to.equal(false)\r\n\t\texpect(isValidPhoneNumber('Call: 8 (800) 555 35 35', 'RU')).to.equal(false)\r\n\t\texpect(isValidPhoneNumber('8 (800) 555 35 35', { defaultCountry: 'RU' })).to.equal(true)\r\n\t\texpect(isValidPhoneNumber('+7 (800) 555 35 35')).to.equal(true)\r\n\t\texpect(isValidPhoneNumber('**** (800) 555 35 35')).to.equal(false)\r\n\t\texpect(isValidPhoneNumber(' +7 (800) 555 35 35')).to.equal(false)\r\n\t\texpect(isValidPhoneNumber(' ')).to.equal(false)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAE/D,SAASC,kBAAkBA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACxCF,UAAU,CAACG,IAAI,CAACT,QAAQ,CAAC;EACzB,OAAOD,mBAAmB,CAACW,KAAK,CAAC,IAAI,EAAEJ,UAAU,CAAC;AACnD;AAEAK,QAAQ,CAAC,oBAAoB,EAAE,YAAM;EACpCC,EAAE,CAAC,+CAA+C,EAAE,YAAM;IACzDC,MAAM,CAACX,kBAAkB,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACpEF,MAAM,CAACX,kBAAkB,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACvEF,MAAM,CAACX,kBAAkB,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC3EF,MAAM,CAACX,kBAAkB,CAAC,mBAAmB,EAAE;MAAEc,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACxFF,MAAM,CAACX,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC/DF,MAAM,CAACX,kBAAkB,CAAC,sBAAsB,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClEF,MAAM,CAACX,kBAAkB,CAAC,qBAAqB,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACjEF,MAAM,CAACX,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAChD,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}