{"version": 3, "sources": ["../../../../src/server/lib/squoosh/image_data.ts"], "names": ["ImageData", "from", "input", "data", "_data", "width", "height", "Object", "prototype", "toString", "call", "<PERSON><PERSON><PERSON>", "values", "Uint8Array", "Uint8ClampedArray", "Error", "constructor"], "mappings": "AAAA,eAAe,MAAMA;IACnB,OAAOC,KAAKC,KAAgB,EAAa;QACvC,OAAO,IAAIF,UAAUE,MAAMC,IAAI,IAAID,MAAME,KAAK,EAAEF,MAAMG,KAAK,EAAEH,MAAMI,MAAM;IAC3E;IAMA,IAAIH,OAAe;QACjB,IAAII,OAAOC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACN,KAAK,MAAM,mBAAmB;YACpE,OAAOO,OAAOV,IAAI,CAACM,OAAOK,MAAM,CAAC,IAAI,CAACR,KAAK;QAC7C;QACA,IACE,IAAI,CAACA,KAAK,YAAYO,UACtB,IAAI,CAACP,KAAK,YAAYS,cACtB,IAAI,CAACT,KAAK,YAAYU,mBACtB;YACA,OAAOH,OAAOV,IAAI,CAAC,IAAI,CAACG,KAAK;QAC/B;QACA,MAAM,IAAIW,MAAM;IAClB;IAEAC,YACEb,IAA6C,EAC7CE,KAAa,EACbC,MAAc,CACd;QACA,IAAI,CAACF,KAAK,GAAGD;QACb,IAAI,CAACE,KAAK,GAAGA;QACb,IAAI,CAACC,MAAM,GAAGA;IAChB;AACF"}