{"version": 3, "file": "IsAscii.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsAscii.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,gBAAgB,MAAM,uBAAuB,CAAC;AAErD,MAAM,CAAC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAElC;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,KAAc;IACpC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC9D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,iBAAqC;IAC3D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,OAAO,CAAC,KAAK,CAAC,EAAd,CAAc;YAClD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,8CAA8C,EAA3D,CAA2D,EACzE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isAsciiValidator from 'validator/lib/isAscii';\n\nexport const IS_ASCII = 'isAscii';\n\n/**\n * Checks if the string contains ASCII chars only.\n * If given value is not a string, then it returns false.\n */\nexport function isAscii(value: unknown): boolean {\n  return typeof value === 'string' && isAsciiValidator(value);\n}\n\n/**\n * Checks if the string contains ASCII chars only.\n * If given value is not a string, then it returns false.\n */\nexport function IsAscii(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ASCII,\n      validator: {\n        validate: (value, args): boolean => isAscii(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain only ASCII characters',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}