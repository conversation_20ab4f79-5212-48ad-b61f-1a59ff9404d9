{"version": 3, "file": "NotEquals.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/NotEquals.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,UAAU,GAAG,WAAW,CAAC;AAEtC;;GAEG;AACH,SAAgB,SAAS,CAAC,KAAc,EAAE,UAAmB;IAC3D,OAAO,KAAK,KAAK,UAAU,CAAC;AAC9B,CAAC;AAFD,8BAEC;AAED;;GAEG;AACH,SAAgB,SAAS,CAAC,UAAe,EAAE,iBAAqC;IAC9E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,kBAAU;QAChB,WAAW,EAAE,CAAC,UAAU,CAAC;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC1E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,+CAA+C,EAC1E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,8BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const NOT_EQUALS = 'notEquals';\n\n/**\n * Checks if value does not match (\"!==\") the comparison.\n */\nexport function notEquals(value: unknown, comparison: unknown): boolean {\n  return value !== comparison;\n}\n\n/**\n * Checks if value does not match (\"!==\") the comparison.\n */\nexport function NotEquals(comparison: any, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: NOT_EQUALS,\n      constraints: [comparison],\n      validator: {\n        validate: (value, args): boolean => notEquals(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property should not be equal to $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}