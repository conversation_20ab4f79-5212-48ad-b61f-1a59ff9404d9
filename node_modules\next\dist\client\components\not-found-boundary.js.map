{"version": 3, "sources": ["../../../src/client/components/not-found-boundary.tsx"], "names": ["NotFoundBoundary", "NotFoundErrorBoundary", "React", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "has", "warningMessage", "size", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "warnOnce", "getDerivedStateFromError", "error", "isNotFoundError", "notFoundTriggered", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "render", "meta", "name", "content", "notFoundStyles", "notFound", "children", "constructor", "asNotFound", "usePathname", "useContext", "MissingSlotContext"], "mappings": "AAAA;;;;;+BA8GgBA;;;eAAAA;;;;;iEA5GkB;4BACN;0BACI;0BACP;+CACU;AAmBnC,MAAMC,8BAA8BC,cAAK,CAACC,SAAS;IAYjDC,oBAA0B;QACxB,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,4EAA4E;QAC5E,CAAC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,GAAG,CAAC,aAC7B;YACA,IAAIC,iBACF,4HACA;YAEF,IAAI,IAAI,CAACH,KAAK,CAACC,YAAY,CAACG,IAAI,GAAG,GAAG;gBACpC,MAAMC,iBAAiBC,MAAMC,IAAI,CAAC,IAAI,CAACP,KAAK,CAACC,YAAY,EACtDO,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,OAAS,AAAC,MAAGA,MAClBC,IAAI,CAAC;gBAERX,kBAAkB,oBAAoBE;YACxC;YAEAU,IAAAA,kBAAQ,EAACZ;QACX;IACF;IAEA,OAAOa,yBAAyBC,KAAU,EAAE;QAC1C,IAAIC,IAAAA,yBAAe,EAACD,QAAQ;YAC1B,OAAO;gBACLE,mBAAmB;YACrB;QACF;QACA,mCAAmC;QACnC,MAAMF;IACR;IAEA,OAAOG,yBACLpB,KAAiC,EACjCqB,KAAiC,EACE;QACnC;;;;;KAKC,GACD,IAAIrB,MAAMsB,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMF,iBAAiB,EAAE;YACxE,OAAO;gBACLA,mBAAmB;gBACnBI,kBAAkBvB,MAAMsB,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,mBAAmBE,MAAMF,iBAAiB;YAC1CI,kBAAkBvB,MAAMsB,QAAQ;QAClC;IACF;IAEAE,SAAS;QACP,IAAI,IAAI,CAACH,KAAK,CAACF,iBAAiB,EAAE;YAChC,qBACE;;kCACE,qBAACM;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3B9B,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAAC0B;wBAAKC,MAAK;wBAAaC,SAAQ;;oBAEjC,IAAI,CAAC3B,KAAK,CAAC4B,cAAc;oBACzB,IAAI,CAAC5B,KAAK,CAAC6B,QAAQ;;;QAG1B;QAEA,OAAO,IAAI,CAAC7B,KAAK,CAAC8B,QAAQ;IAC5B;IA9EAC,YAAY/B,KAAiC,CAAE;QAC7C,KAAK,CAACA;QACN,IAAI,CAACqB,KAAK,GAAG;YACXF,mBAAmB,CAAC,CAACnB,MAAMgC,UAAU;YACrCT,kBAAkBvB,MAAMsB,QAAQ;QAClC;IACF;AAyEF;AAEO,SAAS9B,iBAAiB,KAKT;IALS,IAAA,EAC/BqC,QAAQ,EACRD,cAAc,EACdI,UAAU,EACVF,QAAQ,EACc,GALS;IAM/B,MAAMR,WAAWW,IAAAA,uBAAW;IAC5B,MAAMhC,eAAeiC,IAAAA,iBAAU,EAACC,iDAAkB;IAClD,OAAON,yBACL,qBAACpC;QACC6B,UAAUA;QACVO,UAAUA;QACVD,gBAAgBA;QAChBI,YAAYA;QACZ/B,cAAcA;kBAEb6B;uBAGH;kBAAGA;;AAEP"}