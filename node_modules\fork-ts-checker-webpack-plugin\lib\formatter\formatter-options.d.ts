import type { Formatter, FormatterPathType } from './formatter';
import type { BabelCodeFrameOptions } from './types/babel__code-frame';
declare type FormatterType = 'basic' | 'codeframe';
declare type BasicFormatterOptions = {
    type: 'basic';
    pathType?: FormatterPathType;
};
declare type CodeframeFormatterOptions = {
    type: 'codeframe';
    pathType?: FormatterPathType;
    options?: BabelCodeFrameOptions;
};
declare type FormatterOptions = undefined | FormatterType | BasicFormatterOptions | CodeframeFormatterOptions | Formatter;
export { FormatterOptions, FormatterType, BasicFormatterOptions, CodeframeFormatterOptions };
