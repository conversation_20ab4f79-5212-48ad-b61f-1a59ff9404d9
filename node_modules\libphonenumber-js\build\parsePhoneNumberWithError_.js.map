{"version": 3, "file": "parsePhoneNumberWithError_.js", "names": ["_parse", "_interopRequireDefault", "require", "e", "__esModule", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "parsePhoneNumberWithError", "text", "options", "metadata", "parse", "v2"], "sources": ["../source/parsePhoneNumberWithError_.js"], "sourcesContent": ["import parse from './parse.js'\r\n\r\nexport default function parsePhoneNumberWithError(text, options, metadata) {\r\n\treturn parse(text, { ...options, v2: true }, metadata)\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA8B,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,QAAAR,CAAA,EAAAS,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAZ,CAAA,OAAAW,MAAA,CAAAE,qBAAA,QAAAV,CAAA,GAAAQ,MAAA,CAAAE,qBAAA,CAAAb,CAAA,GAAAS,CAAA,KAAAN,CAAA,GAAAA,CAAA,CAAAW,MAAA,WAAAL,CAAA,WAAAE,MAAA,CAAAI,wBAAA,CAAAf,CAAA,EAAAS,CAAA,EAAAO,UAAA,OAAAN,CAAA,CAAAO,IAAA,CAAAC,KAAA,CAAAR,CAAA,EAAAP,CAAA,YAAAO,CAAA;AAAA,SAAAS,cAAAnB,CAAA,aAAAS,CAAA,MAAAA,CAAA,GAAAW,SAAA,CAAAC,MAAA,EAAAZ,CAAA,UAAAC,CAAA,WAAAU,SAAA,CAAAX,CAAA,IAAAW,SAAA,CAAAX,CAAA,QAAAA,CAAA,OAAAD,OAAA,CAAAG,MAAA,CAAAD,CAAA,OAAAY,OAAA,WAAAb,CAAA,IAAAc,eAAA,CAAAvB,CAAA,EAAAS,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAAzB,CAAA,EAAAW,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAF,OAAA,CAAAG,MAAA,CAAAD,CAAA,GAAAY,OAAA,WAAAb,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAA1B,CAAA,EAAAS,CAAA,EAAAE,MAAA,CAAAI,wBAAA,CAAAL,CAAA,EAAAD,CAAA,iBAAAT,CAAA;AAAA,SAAAuB,gBAAAvB,CAAA,EAAAS,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAkB,cAAA,CAAAlB,CAAA,MAAAT,CAAA,GAAAW,MAAA,CAAAe,cAAA,CAAA1B,CAAA,EAAAS,CAAA,IAAAmB,KAAA,EAAAlB,CAAA,EAAAM,UAAA,MAAAa,YAAA,MAAAC,QAAA,UAAA9B,CAAA,CAAAS,CAAA,IAAAC,CAAA,EAAAV,CAAA;AAAA,SAAA2B,eAAAjB,CAAA,QAAAqB,CAAA,GAAAC,YAAA,CAAAtB,CAAA,gCAAAR,OAAA,CAAA6B,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAtB,CAAA,EAAAD,CAAA,oBAAAP,OAAA,CAAAQ,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAV,CAAA,GAAAU,CAAA,CAAAN,MAAA,CAAA6B,WAAA,kBAAAjC,CAAA,QAAA+B,CAAA,GAAA/B,CAAA,CAAAkC,IAAA,CAAAxB,CAAA,EAAAD,CAAA,gCAAAP,OAAA,CAAA6B,CAAA,UAAAA,CAAA,YAAAI,SAAA,yEAAA1B,CAAA,GAAA2B,MAAA,GAAAC,MAAA,EAAA3B,CAAA;AAEf,SAAS4B,yBAAyBA,CAACC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC1E,OAAO,IAAAC,iBAAK,EAACH,IAAI,EAAApB,aAAA,CAAAA,aAAA,KAAOqB,OAAO;IAAEG,EAAE,EAAE;EAAI,IAAIF,QAAQ,CAAC;AACvD", "ignoreList": []}