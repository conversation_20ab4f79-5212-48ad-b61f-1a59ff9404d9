{"version": 3, "sources": ["../../../../src/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.ts"], "names": ["fillLazyItemsTillLeafWithHead", "newCache", "existingCache", "routerState", "cacheNodeSeedData", "head", "prefetchEntry", "isLastSegment", "Object", "keys", "length", "key", "parallelRouteState", "segmentForParallelRoute", "cache<PERSON>ey", "createRouterCache<PERSON>ey", "parallelSeedData", "undefined", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "hasReusablePrefetch", "kind", "status", "PrefetchCacheEntryStatus", "reusable", "parallelRouteCacheNode", "Map", "existingCacheNode", "newCacheNode", "seedNode", "loading", "lazyData", "rsc", "prefetchRsc", "prefetchHead", "lazyDataResolved", "set", "existingParallelRoutes"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;sCANqB;oCAI9B;AAEA,SAASA,8BACdC,QAAmB,EACnBC,aAAoC,EACpCC,WAA8B,EAC9BC,iBAA2C,EAC3CC,IAAqB,EACrBC,aAAkC;IAElC,MAAMC,gBAAgBC,OAAOC,IAAI,CAACN,WAAW,CAAC,EAAE,EAAEO,MAAM,KAAK;IAC7D,IAAIH,eAAe;QACjBN,SAASI,IAAI,GAAGA;QAChB;IACF;IACA,uFAAuF;IACvF,IAAK,MAAMM,OAAOR,WAAW,CAAC,EAAE,CAAE;QAChC,MAAMS,qBAAqBT,WAAW,CAAC,EAAE,CAACQ,IAAI;QAC9C,MAAME,0BAA0BD,kBAAkB,CAAC,EAAE;QACrD,MAAME,WAAWC,IAAAA,0CAAoB,EAACF;QAEtC,4EAA4E;QAC5E,2EAA2E;QAC3E,wEAAwE;QACxE,wEAAwE;QACxE,0EAA0E;QAC1E,qBAAqB;QACrB,EAAE;QACF,0EAA0E;QAC1E,wEAAwE;QACxE,kEAAkE;QAClE,MAAMG,mBACJZ,sBAAsB,QAAQA,iBAAiB,CAAC,EAAE,CAACO,IAAI,KAAKM,YACxDb,iBAAiB,CAAC,EAAE,CAACO,IAAI,GACzB;QACN,IAAIT,eAAe;YACjB,MAAMgB,kCACJhB,cAAciB,cAAc,CAACC,GAAG,CAACT;YACnC,IAAIO,iCAAiC;gBACnC,MAAMG,sBACJf,CAAAA,iCAAAA,cAAegB,IAAI,MAAK,UACxBhB,cAAciB,MAAM,KAAKC,4CAAwB,CAACC,QAAQ;gBAE5D,IAAIC,yBAAyB,IAAIC,IAAIT;gBACrC,MAAMU,oBAAoBF,uBAAuBN,GAAG,CAACN;gBACrD,IAAIe;gBACJ,IAAIb,qBAAqB,MAAM;oBAC7B,qCAAqC;oBACrC,MAAMc,WAAWd,gBAAgB,CAAC,EAAE;oBACpC,MAAMe,UAAUf,gBAAgB,CAAC,EAAE;oBACnCa,eAAe;wBACbG,UAAU;wBACVC,KAAKH;wBACL,kEAAkE;wBAClE,oEAAoE;wBACpE,2DAA2D;wBAC3D,kEAAkE;wBAClE,+BAA+B;wBAC/BI,aAAa;wBACb7B,MAAM;wBACN8B,cAAc;wBACdJ;wBACAZ,gBAAgB,IAAIQ,IAAIC,qCAAAA,kBAAmBT,cAAc;wBACzDiB,kBAAkB;oBACpB;gBACF,OAAO,IAAIf,uBAAuBO,mBAAmB;oBACnD,oEAAoE;oBACpE,2CAA2C;oBAC3CC,eAAe;wBACbG,UAAUJ,kBAAkBI,QAAQ;wBACpCC,KAAKL,kBAAkBK,GAAG;wBAC1B,oEAAoE;wBACpE,kEAAkE;wBAClE,2BAA2B;wBAC3BC,aAAaN,kBAAkBM,WAAW;wBAC1C7B,MAAMuB,kBAAkBvB,IAAI;wBAC5B8B,cAAcP,kBAAkBO,YAAY;wBAC5ChB,gBAAgB,IAAIQ,IAAIC,kBAAkBT,cAAc;wBACxDiB,kBAAkBR,kBAAkBQ,gBAAgB;wBACpDL,SAASH,kBAAkBG,OAAO;oBACpC;gBACF,OAAO;oBACL,kEAAkE;oBAClE,iBAAiB;oBACjBF,eAAe;wBACbG,UAAU;wBACVC,KAAK;wBACLC,aAAa;wBACb7B,MAAM;wBACN8B,cAAc;wBACdhB,gBAAgB,IAAIQ,IAAIC,qCAAAA,kBAAmBT,cAAc;wBACzDiB,kBAAkB;wBAClBL,SAAS;oBACX;gBACF;gBAEA,mDAAmD;gBACnDL,uBAAuBW,GAAG,CAACvB,UAAUe;gBACrC,qEAAqE;gBACrE7B,8BACE6B,cACAD,mBACAhB,oBACAI,mBAAmBA,mBAAmB,MACtCX,MACAC;gBAGFL,SAASkB,cAAc,CAACkB,GAAG,CAAC1B,KAAKe;gBACjC;YACF;QACF;QAEA,IAAIG;QACJ,IAAIb,qBAAqB,MAAM;YAC7B,qCAAqC;YACrC,MAAMc,WAAWd,gBAAgB,CAAC,EAAE;YACpC,MAAMe,UAAUf,gBAAgB,CAAC,EAAE;YACnCa,eAAe;gBACbG,UAAU;gBACVC,KAAKH;gBACLI,aAAa;gBACb7B,MAAM;gBACN8B,cAAc;gBACdhB,gBAAgB,IAAIQ;gBACpBS,kBAAkB;gBAClBL;YACF;QACF,OAAO;YACL,kEAAkE;YAClE,iBAAiB;YACjBF,eAAe;gBACbG,UAAU;gBACVC,KAAK;gBACLC,aAAa;gBACb7B,MAAM;gBACN8B,cAAc;gBACdhB,gBAAgB,IAAIQ;gBACpBS,kBAAkB;gBAClBL,SAAS;YACX;QACF;QAEA,MAAMO,yBAAyBrC,SAASkB,cAAc,CAACC,GAAG,CAACT;QAC3D,IAAI2B,wBAAwB;YAC1BA,uBAAuBD,GAAG,CAACvB,UAAUe;QACvC,OAAO;YACL5B,SAASkB,cAAc,CAACkB,GAAG,CAAC1B,KAAK,IAAIgB,IAAI;gBAAC;oBAACb;oBAAUe;iBAAa;aAAC;QACrE;QAEA7B,8BACE6B,cACAZ,WACAL,oBACAI,kBACAX,MACAC;IAEJ;AACF"}