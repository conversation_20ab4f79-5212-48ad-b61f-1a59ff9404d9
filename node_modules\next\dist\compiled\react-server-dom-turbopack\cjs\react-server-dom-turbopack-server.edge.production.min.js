/*
 React
 react-server-dom-turbopack-server.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var ba=require("react"),ca=require("react-dom"),m=null,n=0;function p(a,b){if(0!==b.byteLength)if(2048<b.byteLength)0<n&&(a.enqueue(new Uint8Array(m.buffer,0,n)),m=new Uint8Array(2048),n=0),a.enqueue(b);else{var c=m.length-n;c<b.byteLength&&(0===c?a.enqueue(m):(m.set(b.subarray(0,c),n),a.enqueue(m),b=b.subarray(c)),m=new Uint8Array(2048),n=0);m.set(b,n);n+=b.byteLength}return!0}var q=new TextEncoder;function da(a,b){"function"===typeof a.error?a.error(b):a.close()}
var r=Symbol.for("react.client.reference"),t=Symbol.for("react.server.reference");function v(a,b,c){return Object.defineProperties(a,{$$typeof:{value:r},$$id:{value:b},$$async:{value:c}})}var ea=Function.prototype.bind,fa=Array.prototype.slice;function ha(){var a=ea.apply(this,arguments);if(this.$$typeof===t){var b=fa.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ha}})}return a}
var ia=Promise.prototype,ja={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function ka(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "__esModule":var c=a.$$id;a.default=v(function(){throw Error("Attempted to call the default export of "+c+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var d=v({},a.$$id,!0),e=new Proxy(d,la);a.status="fulfilled";a.value=e;return a.then=v(function(f){return Promise.resolve(f(e))},a.$$id+"#then",!1)}if("symbol"===typeof b)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");d=a[b];d||(d=v(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(d,"name",{value:b}),d=a[b]=new Proxy(d,ja));return d}
var la={get:function(a,b){return ka(a,b)},getOwnPropertyDescriptor:function(a,b){var c=Object.getOwnPropertyDescriptor(a,b);c||(c={value:ka(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,c));return c},getPrototypeOf:function(){return ia},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ta={prefetchDNS:ma,preconnect:na,preload:oa,preloadModule:pa,preinitStyle:qa,preinitScript:ra,preinitModuleScript:sa};
function ma(a){if("string"===typeof a&&a){var b=w();if(b){var c=b.hints,d="D|"+a;c.has(d)||(c.add(d),x(b,"D",a))}}}function na(a,b){if("string"===typeof a){var c=w();if(c){var d=c.hints,e="C|"+(null==b?"null":b)+"|"+a;d.has(e)||(d.add(e),"string"===typeof b?x(c,"C",[a,b]):x(c,"C",a))}}}
function oa(a,b,c){if("string"===typeof a){var d=w();if(d){var e=d.hints,f="L";if("image"===b&&c){var g=c.imageSrcSet,k=c.imageSizes,h="";"string"===typeof g&&""!==g?(h+="["+g+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;f+="[image]"+h}else f+="["+b+"]"+a;e.has(f)||(e.add(f),(c=y(c))?x(d,"L",[a,b,c]):x(d,"L",[a,b]))}}}function pa(a,b){if("string"===typeof a){var c=w();if(c){var d=c.hints,e="m|"+a;if(!d.has(e))return d.add(e),(b=y(b))?x(c,"m",[a,b]):x(c,"m",a)}}}
function qa(a,b,c){if("string"===typeof a){var d=w();if(d){var e=d.hints,f="S|"+a;if(!e.has(f))return e.add(f),(c=y(c))?x(d,"S",[a,"string"===typeof b?b:0,c]):"string"===typeof b?x(d,"S",[a,b]):x(d,"S",a)}}}function ra(a,b){if("string"===typeof a){var c=w();if(c){var d=c.hints,e="X|"+a;if(!d.has(e))return d.add(e),(b=y(b))?x(c,"X",[a,b]):x(c,"X",a)}}}function sa(a,b){if("string"===typeof a){var c=w();if(c){var d=c.hints,e="M|"+a;if(!d.has(e))return d.add(e),(b=y(b))?x(c,"M",[a,b]):x(c,"M",a)}}}
function y(a){if(null==a)return null;var b=!1,c={},d;for(d in a)null!=a[d]&&(b=!0,c[d]=a[d]);return b?c:null}var ua=ca.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,A="function"===typeof AsyncLocalStorage,va=A?new AsyncLocalStorage:null;"object"===typeof async_hooks?async_hooks.createHook:function(){return{enable:function(){},disable:function(){}}};"object"===typeof async_hooks?async_hooks.executionAsyncId:null;
var B=Symbol.for("react.element"),wa=Symbol.for("react.fragment"),xa=Symbol.for("react.context"),ya=Symbol.for("react.forward_ref"),za=Symbol.for("react.suspense"),Aa=Symbol.for("react.suspense_list"),Ba=Symbol.for("react.memo"),D=Symbol.for("react.lazy"),Ca=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.postpone");var Da=Symbol.iterator,Ea=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Fa(){}function Ga(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Fa,Fa),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}E=b;throw Ea;}}var E=null;
function Ha(){if(null===E)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=E;E=null;return a}var F=null,Ia=0,G=null;function Ja(){var a=G||[];G=null;return a}
var Oa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:H,useTransition:H,readContext:Ka,useContext:Ka,useReducer:H,useRef:H,useState:H,useInsertionEffect:H,useLayoutEffect:H,useImperativeHandle:H,useEffect:H,useId:La,useSyncExternalStore:H,useCacheRefresh:function(){return Ma},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=Ca;return b},use:Na};
function H(){throw Error("This Hook is not supported in Server Components.");}function Ma(){throw Error("Refreshing the cache is not supported in Server Components.");}function Ka(){throw Error("Cannot read a Client Context from a Server Component.");}function La(){if(null===F)throw Error("useId can only be used while React is rendering");var a=F.identifierCount++;return":"+F.identifierPrefix+"S"+a.toString(32)+":"}
function Na(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ia;Ia+=1;null===G&&(G=[]);return Ga(G,a,b)}a.$$typeof===xa&&Ka()}if(a.$$typeof===r){if(null!=a.value&&a.value.$$typeof===xa)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.");}throw Error("An unsupported type was passed to use(): "+String(a));}function Pa(){return(new AbortController).signal}
function Qa(){var a=w();return a?a.cache:new Map}var Ra={getCacheSignal:function(){var a=Qa(),b=a.get(Pa);void 0===b&&(b=Pa(),a.set(Pa,b));return b},getCacheForType:function(a){var b=Qa(),c=b.get(a);void 0===c&&(c=a(),b.set(a,c));return c}},Sa=Array.isArray,Ta=Object.getPrototypeOf;function Ua(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,c){return c})}
function Va(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Sa(a))return"[...]";if(null!==a&&a.$$typeof===Wa)return"client";a=Ua(a);return"Object"===a?"{...}":a;case "function":return a.$$typeof===Wa?"client":(a=a.displayName||a.name)?"function "+a:"function";default:return String(a)}}
function I(a){if("string"===typeof a)return a;switch(a){case za:return"Suspense";case Aa:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case ya:return I(a.render);case Ba:return I(a.type);case D:var b=a._payload;a=a._init;try{return I(a(b))}catch(c){}}return""}var Wa=Symbol.for("react.client.reference");
function J(a,b){var c=Ua(a);if("Object"!==c&&"Array"!==c)return c;c=-1;var d=0;if(Sa(a)){var e="[";for(var f=0;f<a.length;f++){0<f&&(e+=", ");var g=a[f];g="object"===typeof g&&null!==g?J(g):Va(g);""+f===b?(c=e.length,d=g.length,e+=g):e=10>g.length&&40>e.length+g.length?e+g:e+"..."}e+="]"}else if(a.$$typeof===B)e="<"+I(a.type)+"/>";else{if(a.$$typeof===Wa)return"client";e="{";f=Object.keys(a);for(g=0;g<f.length;g++){0<g&&(e+=", ");var k=f[g],h=JSON.stringify(k);e+=('"'+k+'"'===h?k:h)+": ";h=a[k];h=
"object"===typeof h&&null!==h?J(h):Va(h);k===b?(c=e.length,d=h.length,e+=h):e=10>h.length&&40>e.length+h.length?e+h:e+"..."}e+="}"}return void 0===b?e:-1<c&&0<d?(a=" ".repeat(c)+"^".repeat(d),"\n  "+e+"\n  "+a):"\n  "+e}var Xa=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Ya=ba.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!Ya)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var Za=Object.prototype,K=JSON.stringify,$a=Ya.ReactCurrentCache,ab=Xa.ReactCurrentDispatcher;function bb(a){console.error(a)}function cb(){}
function db(a,b,c,d,e){if(null!==$a.current&&$a.current!==Ra)throw Error("Currently React only supports one RSC renderer at a time.");ua.current=ta;$a.current=Ra;var f=new Set,g=[],k=new Set;b={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:k,abortableTasks:f,pingedTasks:g,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenObjects:new WeakMap,identifierPrefix:d||"",identifierCount:1,taintCleanupQueue:[],onError:void 0===c?bb:c,onPostpone:void 0===e?cb:e};a=L(b,a,null,!1,f);g.push(a);return b}var M=null;function w(){if(M)return M;if(A){var a=va.getStore();if(a)return a}return null}
function eb(a,b,c){var d=L(a,null,b.keyPath,b.implicitSlot,a.abortableTasks);switch(c.status){case "fulfilled":return d.model=c.value,fb(a,d),d.id;case "rejected":return b=N(a,c.reason),O(a,d.id,b),d.id;default:"string"!==typeof c.status&&(c.status="pending",c.then(function(e){"pending"===c.status&&(c.status="fulfilled",c.value=e)},function(e){"pending"===c.status&&(c.status="rejected",c.reason=e)}))}c.then(function(e){d.model=e;fb(a,d)},function(e){d.status=4;e=N(a,e);O(a,d.id,e);a.abortableTasks.delete(d);
null!==a.destination&&P(a,a.destination)});return d.id}function x(a,b,c){c=K(c);var d=a.nextChunkId++;b="H"+b;b=d.toString(16)+":"+b;c=q.encode(b+c+"\n");a.completedHintChunks.push(c);gb(a)}function hb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}
function ib(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:D,_payload:a,_init:hb}}
function jb(a,b,c,d,e){var f=b.thenableState;b.thenableState=null;Ia=0;G=f;d=d(e,void 0);if("object"===typeof d&&null!==d&&"function"===typeof d.then){e=d;if("fulfilled"===e.status)return e.value;d=ib(d)}e=b.keyPath;f=b.implicitSlot;null!==c?b.keyPath=null===e?c:e+","+c:null===e&&(b.implicitSlot=!0);a=Q(a,b,R,"",d);b.keyPath=e;b.implicitSlot=f;return a}
function kb(a,b,c,d,e,f){if(null!==e&&void 0!==e)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof c)return c.$$typeof===r?[B,c,d,f]:jb(a,b,d,c,f);if("string"===typeof c)return[B,c,d,f];if("symbol"===typeof c)return c===wa&&null===d?(d=b.implicitSlot,null===b.keyPath&&(b.implicitSlot=!0),a=Q(a,b,R,"",f.children),b.implicitSlot=d,a):[B,c,d,f];if(null!=c&&"object"===typeof c){if(c.$$typeof===r)return[B,c,d,f];switch(c.$$typeof){case D:var g=
c._init;c=g(c._payload);return kb(a,b,c,d,e,f);case ya:return jb(a,b,d,c.render,f);case Ba:return kb(a,b,c.type,d,e,f)}}throw Error("Unsupported Server Component type: "+Va(c));}function fb(a,b){var c=a.pingedTasks;c.push(b);1===c.length&&(a.flushScheduled=null!==a.destination,setTimeout(function(){return lb(a)},0))}
function L(a,b,c,d,e){a.pendingChunks++;var f=a.nextChunkId++;"object"===typeof b&&null!==b&&a.writtenObjects.set(b,f);var g={id:f,status:0,model:b,keyPath:c,implicitSlot:d,ping:function(){return fb(a,g)},toJSON:function(k,h){var l=g.keyPath,z=g.implicitSlot;try{var u=Q(a,g,this,k,h)}catch(aa){if(k=aa===Ea?Ha():aa,h=g.model,h="object"===typeof h&&null!==h&&(h.$$typeof===B||h.$$typeof===D),"object"===typeof k&&null!==k&&"function"===typeof k.then){u=L(a,g.model,g.keyPath,g.implicitSlot,a.abortableTasks);
var C=u.ping;k.then(C,C);u.thenableState=Ja();g.keyPath=l;g.implicitSlot=z;u=h?"$L"+u.id.toString(16):S(u.id)}else if(g.keyPath=l,g.implicitSlot=z,h)a.pendingChunks++,l=a.nextChunkId++,z=N(a,k),O(a,l,z),u="$L"+l.toString(16);else throw k;}return u},thenableState:null};e.add(g);return g}function S(a){return"$"+a.toString(16)}function mb(a,b,c){a=K(c);b=b.toString(16)+":"+a+"\n";return q.encode(b)}
function nb(a,b,c,d){var e=d.$$async?d.$$id+"#async":d.$$id,f=a.writtenClientReferences,g=f.get(e);if(void 0!==g)return b[0]===B&&"1"===c?"$L"+g.toString(16):S(g);try{var k=a.bundlerConfig,h=d.$$id;g="";var l=k[h];if(l)g=l.name;else{var z=h.lastIndexOf("#");-1!==z&&(g=h.slice(z+1),l=k[h.slice(0,z)]);if(!l)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var u=!0===d.$$async?[l.id,l.chunks,g,1]:[l.id,l.chunks,
g];a.pendingChunks++;var C=a.nextChunkId++,aa=K(u),Eb=C.toString(16)+":I"+aa+"\n",Fb=q.encode(Eb);a.completedImportChunks.push(Fb);f.set(e,C);return b[0]===B&&"1"===c?"$L"+C.toString(16):S(C)}catch(Gb){return a.pendingChunks++,b=a.nextChunkId++,c=N(a,Gb),O(a,b,c),S(b)}}function T(a,b){b=L(a,b,null,!1,a.abortableTasks);ob(a,b);return b.id}var U=!1;
function Q(a,b,c,d,e){b.model=e;if(e===B)return"$";if(null===e)return null;if("object"===typeof e){switch(e.$$typeof){case B:c=a.writtenObjects;d=c.get(e);if(void 0!==d)if(U===e)U=null;else return-1===d?(a=T(a,e),S(a)):S(d);else c.set(e,-1);return kb(a,b,e.type,e.key,e.ref,e.props);case D:return b.thenableState=null,c=e._init,e=c(e._payload),Q(a,b,R,"",e)}if(e.$$typeof===r)return nb(a,c,d,e);c=a.writtenObjects;d=c.get(e);if("function"===typeof e.then){if(void 0!==d)if(U===e)U=null;else return"$@"+
d.toString(16);a=eb(a,b,e);c.set(e,a);return"$@"+a.toString(16)}if(void 0!==d)if(U===e)U=null;else return-1===d?(a=T(a,e),S(a)):S(d);else c.set(e,-1);if(Sa(e))return e;if(e instanceof Map){e=Array.from(e);for(b=0;b<e.length;b++)c=e[b][0],"object"===typeof c&&null!==c&&(d=a.writtenObjects,void 0===d.get(c)&&d.set(c,-1));return"$Q"+T(a,e).toString(16)}if(e instanceof Set){e=Array.from(e);for(b=0;b<e.length;b++)c=e[b],"object"===typeof c&&null!==c&&(d=a.writtenObjects,void 0===d.get(c)&&d.set(c,-1));
return"$W"+T(a,e).toString(16)}null===e||"object"!==typeof e?a=null:(a=Da&&e[Da]||e["@@iterator"],a="function"===typeof a?a:null);if(a)return a=Array.from(e),a;a=Ta(e);if(a!==Za&&(null===a||null!==Ta(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");return e}if("string"===typeof e){if("Z"===e[e.length-1]&&c[d]instanceof Date)return"$D"+e;if(1024<=e.length)return a.pendingChunks+=2,b=
a.nextChunkId++,e=q.encode(e),c=e.byteLength,c=b.toString(16)+":T"+c.toString(16)+",",c=q.encode(c),a.completedRegularChunks.push(c,e),S(b);a="$"===e[0]?"$"+e:e;return a}if("boolean"===typeof e)return e;if("number"===typeof e)return Number.isFinite(e)?0===e&&-Infinity===1/e?"$-0":e:Infinity===e?"$Infinity":-Infinity===e?"$-Infinity":"$NaN";if("undefined"===typeof e)return"$undefined";if("function"===typeof e){if(e.$$typeof===r)return nb(a,c,d,e);if(e.$$typeof===t)return b=a.writtenServerReferences,
c=b.get(e),void 0!==c?a="$F"+c.toString(16):(c=e.$$bound,c={id:e.$$id,bound:c?Promise.resolve(c):null},a=T(a,c),b.set(e,a),a="$F"+a.toString(16)),a;if(/^on[A-Z]/.test(d))throw Error("Event handlers cannot be passed to Client Component props."+J(c,d)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+
J(c,d));}if("symbol"===typeof e){b=a.writtenSymbols;var f=b.get(e);if(void 0!==f)return S(f);f=e.description;if(Symbol.for(f)!==e)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(e.description+") cannot be found among global symbols.")+J(c,d));a.pendingChunks++;c=a.nextChunkId++;d=mb(a,c,"$S"+f);a.completedImportChunks.push(d);b.set(e,c);return S(c)}if("bigint"===typeof e)return"$n"+e.toString(10);throw Error("Type "+typeof e+
" is not supported in Client Component props."+J(c,d));}function N(a,b){var c=M;M=null;try{var d=a.onError;var e=A?va.run(void 0,d,b):d(b)}finally{M=c}if(null!=e&&"string"!==typeof e)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e||""}
function pb(a,b){null!==a.destination?(a.status=2,da(a.destination,b)):(a.status=1,a.fatalError=b)}function O(a,b,c){c={digest:c};b=b.toString(16)+":E"+K(c)+"\n";b=q.encode(b);a.completedErrorChunks.push(b)}var R={};
function ob(a,b){if(0===b.status)try{U=b.model;var c=Q(a,b,R,"",b.model);U=c;b.keyPath=null;b.implicitSlot=!1;var d="object"===typeof c&&null!==c?K(c,b.toJSON):K(c),e=b.id.toString(16)+":"+d+"\n",f=q.encode(e);a.completedRegularChunks.push(f);a.abortableTasks.delete(b);b.status=1}catch(l){var g=l===Ea?Ha():l;if("object"===typeof g&&null!==g&&"function"===typeof g.then){var k=b.ping;g.then(k,k);b.thenableState=Ja()}else{a.abortableTasks.delete(b);b.status=4;var h=N(a,g);O(a,b.id,h)}}finally{}}
function lb(a){var b=ab.current;ab.current=Oa;var c=M;F=M=a;try{var d=a.pingedTasks;a.pingedTasks=[];for(var e=0;e<d.length;e++)ob(a,d[e]);null!==a.destination&&P(a,a.destination)}catch(f){N(a,f),pb(a,f)}finally{ab.current=b,F=null,M=c}}
function P(a,b){m=new Uint8Array(2048);n=0;try{for(var c=a.completedImportChunks,d=0;d<c.length;d++)a.pendingChunks--,p(b,c[d]);c.splice(0,d);var e=a.completedHintChunks;for(d=0;d<e.length;d++)p(b,e[d]);e.splice(0,d);var f=a.completedRegularChunks;for(d=0;d<f.length;d++)a.pendingChunks--,p(b,f[d]);f.splice(0,d);var g=a.completedErrorChunks;for(d=0;d<g.length;d++)a.pendingChunks--,p(b,g[d]);g.splice(0,d)}finally{a.flushScheduled=!1,m&&0<n&&(b.enqueue(new Uint8Array(m.buffer,0,n)),m=null,n=0)}0===a.pendingChunks&&
b.close()}function qb(a){a.flushScheduled=null!==a.destination;A?setTimeout(function(){return va.run(a,lb,a)},0):setTimeout(function(){return lb(a)},0)}function gb(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){var b=a.destination;a.flushScheduled=!0;setTimeout(function(){return P(a,b)},0)}}
function rb(a,b){try{var c=a.abortableTasks;if(0<c.size){a.pendingChunks++;var d=a.nextChunkId++,e=void 0===b?Error("The render was aborted by the server without a reason."):b,f=N(a,e);O(a,d,f,e);c.forEach(function(g){g.status=3;var k=S(d);g=mb(a,g.id,k);a.completedErrorChunks.push(g)});c.clear()}null!==a.destination&&P(a,a.destination)}catch(g){N(a,g),pb(a,g)}}
function sb(a,b){var c="",d=a[b];if(d)c=d.name;else{var e=b.lastIndexOf("#");-1!==e&&(c=b.slice(e+1),d=a[b.slice(0,e)]);if(!d)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[d.id,d.chunks,c]}var tb=new Map;
function ub(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function vb(){}
function wb(a){for(var b=a[1],c=[],d=0;d<b.length;d++){var e=b[d],f=tb.get(e);if(void 0===f){f=globalThis.__next_chunk_load__(e);c.push(f);var g=tb.set.bind(tb,e,null);f.then(g,vb);tb.set(e,f)}else null!==f&&c.push(f)}return 4===a.length?0===c.length?ub(a[0]):Promise.all(c).then(function(){return ub(a[0])}):0<c.length?Promise.all(c):null}
function V(a){var b=globalThis.__next_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function W(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}W.prototype=Object.create(Promise.prototype);
W.prototype.then=function(a,b){switch(this.status){case "resolved_model":xb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function yb(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}
function zb(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&yb(c,b)}}function Ab(a,b,c,d,e,f){var g=sb(a._bundlerConfig,b);a=wb(g);if(c)c=Promise.all([c,a]).then(function(k){k=k[0];var h=V(g);return h.bind.apply(h,[null].concat(k))});else if(a)c=Promise.resolve(a).then(function(){return V(g)});else return V(g);c.then(Bb(d,e,f),Cb(d));return null}var X=null,Y=null;
function xb(a){var b=X,c=Y;X=a;Y=null;try{var d=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=d,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=d)}catch(e){a.status="rejected",a.reason=e}finally{X=b,Y=c}}function Db(a,b){a._closed=!0;a._closedReason=b;a._chunks.forEach(function(c){"pending"===c.status&&zb(c,b)})}
function Z(a,b){var c=a._chunks,d=c.get(b);d||(d=a._formData.get(a._prefix+b),d=null!=d?new W("resolved_model",d,null,a):a._closed?new W("rejected",null,a._closedReason,a):new W("pending",null,null,a),c.set(b,d));return d}function Bb(a,b,c){if(Y){var d=Y;d.deps++}else d=Y={deps:1,value:null};return function(e){b[c]=e;d.deps--;0===d.deps&&"blocked"===a.status&&(e=a.value,a.status="fulfilled",a.value=d.value,null!==e&&yb(e,d.value))}}function Cb(a){return function(b){return zb(a,b)}}
function Hb(a,b){a=Z(a,b);"resolved_model"===a.status&&xb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Ib(a,b,c,d){if("$"===d[0])switch(d[1]){case "$":return d.slice(1);case "@":return b=parseInt(d.slice(2),16),Z(a,b);case "S":return Symbol.for(d.slice(2));case "F":return d=parseInt(d.slice(2),16),d=Hb(a,d),Ab(a,d.id,d.bound,X,b,c);case "Q":return b=parseInt(d.slice(2),16),a=Hb(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=Hb(a,b),new Set(a);case "K":b=d.slice(2);var e=a._prefix+b+"_",f=new FormData;a._formData.forEach(function(g,k){k.startsWith(e)&&f.append(k.slice(e.length),
g)});return f;case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=Z(a,d);switch(a.status){case "resolved_model":xb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return d=X,a.then(Bb(d,b,c),Cb(d)),null;default:throw a.reason;}}return d}
function Jb(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,d=new Map,e={_bundlerConfig:a,_prefix:b,_formData:c,_chunks:d,_fromJSON:function(f,g){return"string"===typeof g?Ib(e,this,f,g):g},_closed:!1,_closedReason:null};return e}function Kb(a){Db(a,Error("Connection closed."))}
function Lb(a,b,c){var d=sb(a,b);a=wb(d);return c?Promise.all([c,a]).then(function(e){e=e[0];var f=V(d);return f.bind.apply(f,[null].concat(e))}):a?Promise.resolve(a).then(function(){return V(d)}):Promise.resolve(V(d))}function Mb(a,b,c){a=Jb(b,c,a);Kb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=v({},a,!1);return new Proxy(a,la)};
exports.decodeAction=function(a,b){var c=new FormData,d=null;a.forEach(function(e,f){f.startsWith("$ACTION_")?f.startsWith("$ACTION_REF_")?(e="$ACTION_"+f.slice(12)+":",e=Mb(a,b,e),d=Lb(b,e.id,e.bound)):f.startsWith("$ACTION_ID_")&&(e=f.slice(11),d=Lb(b,e,null)):c.append(f,e)});return null===d?null:d.then(function(e){return e.bind(null,c)})};exports.decodeReply=function(a,b){if("string"===typeof a){var c=new FormData;c.append("0",a);a=c}a=Jb(b,"",a);b=Z(a,0);Kb(a);return b};
exports.registerClientReference=function(a,b,c){return v(a,b+"#"+c,!1)};exports.registerServerReference=function(a,b,c){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:null===c?b:b+"#"+c,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:ha,configurable:!0}})};
exports.renderToReadableStream=function(a,b,c){var d=db(a,b,c?c.onError:void 0,c?c.identifierPrefix:void 0,c?c.onPostpone:void 0);if(c&&c.signal){var e=c.signal;if(e.aborted)rb(d,e.reason);else{var f=function(){rb(d,e.reason);e.removeEventListener("abort",f)};e.addEventListener("abort",f)}}return new ReadableStream({type:"bytes",start:function(){qb(d)},pull:function(g){if(1===d.status)d.status=2,da(g,d.fatalError);else if(2!==d.status&&null===d.destination){d.destination=g;try{P(d,g)}catch(k){N(d,
k),pb(d,k)}}},cancel:function(){}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-turbopack-server.edge.production.min.js.map
