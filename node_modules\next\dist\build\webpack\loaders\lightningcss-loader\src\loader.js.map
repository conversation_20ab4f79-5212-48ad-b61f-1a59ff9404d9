{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/loader.ts"], "names": ["Lightning<PERSON>s<PERSON><PERSON>der", "raw", "encoder", "TextEncoder", "moduleRegExp", "createUrlAndImportVisitor", "visitorOptions", "apis", "imports", "replacements", "replacedUrls", "replacedImportUrls", "importUrlToNameMap", "Map", "hasUrlImportHelper", "urlToNameMap", "urlToReplacementMap", "urlIndex", "importUrlIndex", "handleUrl", "u", "url", "<PERSON><PERSON><PERSON>", "url<PERSON><PERSON><PERSON>", "isDataUrl", "set", "query", "hash<PERSON><PERSON><PERSON><PERSON><PERSON>", "split", "queryParts", "prefix", "length", "pop", "join", "hash", "push", "type", "importName", "JSON", "stringify", "require", "resolve", "index", "newUrl", "get", "size", "needQuotes", "<PERSON><PERSON><PERSON>", "replacement<PERSON>ame", "loc", "Rule", "import", "node", "importFilter", "value", "media", "mediaQueries", "undefined", "isRequestable", "isUrlRequestable", "importUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Url", "createIcssVisitor", "replacementIndex", "Declaration", "composes", "property", "specifier", "from", "icss", "dedupe", "newNames", "localName", "names", "LOADER_NAME", "source", "prevMap", "options", "done", "async", "getOptions", "implementation", "targets", "userTargets", "opts", "modules", "transformCss", "TypeError", "exports", "icssImports", "exportOnlyLocals", "unshift", "stringifyRequest", "loadBindings", "transform", "css", "lightning", "icssReplacedUrls", "urlImportVisitor", "getPreRequester", "importLoaders", "getFilter", "resourcePath", "context", "icssVisitor", "visitor", "code", "map", "moduleExports", "cssModules", "test", "pattern", "process", "env", "__NEXT_TEST_MODE", "filename", "encode", "sourceMap", "getTargets", "key", "<PERSON><PERSON><PERSON><PERSON>", "loader", "inputSourceMap", "include", "cssCodeAsString", "toString", "name", "Object", "prototype", "hasOwnProperty", "call", "v", "compose", "urlResolver", "getResolve", "conditionNames", "mainFields", "mainFiles", "extensions", "entries", "pathname", "request", "requestify", "rootContext", "resolvedUrl", "resolveRequests", "Set", "importItem", "replace", "importResolver", "restrictions", "icssResolver", "importCode", "getImportCode", "moduleCode", "getModuleCode", "exportCode", "getExportCode", "esCode", "parse", "error", "console"], "mappings": ";;;;;;;;;;;;;;;IAuQsBA,kBAAkB;eAAlBA;;IAkOTC,GAAG;eAAHA;;;uBAxec;yBASpB;wBAQA;kCAC0B;2BACP;AAE1B,MAAMC,UAAU,IAAIC;AAEpB,MAAMC,eAAe;AAErB,SAASC,0BACPC,cAAmB,EACnBC,IAAgB,EAChBC,OAAoB,EACpBC,YAA8B,EAC9BC,YAAiC,EACjCC,kBAAuC;IAEvC,MAAMC,qBAAqB,IAAIC;IAE/B,IAAIC,qBAAqB;IACzB,MAAMC,eAAe,IAAIF;IACzB,MAAMG,sBAAsB,IAAIH;IAChC,IAAII,WAAW,CAAC;IAChB,IAAIC,iBAAiB,CAAC;IAEtB,SAASC,UAAUC,CAAgC;QACjD,IAAIC,MAAMD,EAAEC,GAAG;QACf,MAAMC,WAAWhB,eAAeiB,SAAS,CAACF;QAE1C,IAAI,CAACC,UAAU;YACb,OAAOF;QACT;QAEA,IAAII,IAAAA,iBAAS,EAACH,MAAM;YAClB,OAAOD;QACT;QAEAH;QAEAP,aAAae,GAAG,CAACR,UAAUI;QAC3BA,MAAM,CAAC,uCAAuC,EAAEJ,SAAS,EAAE,CAAC;QAE5D,MAAM,GAAGS,OAAOC,YAAY,GAAGN,IAAIO,KAAK,CAAC,UAAU;QAEnD,MAAMC,aAAaR,IAAIO,KAAK,CAAC;QAC7B,IAAIE;QAEJ,IAAID,WAAWE,MAAM,GAAG,GAAG;YACzBV,MAAMQ,WAAWG,GAAG;YACpBF,SAASD,WAAWI,IAAI,CAAC;QAC3B;QAEA,IAAIC,OAAOR,QAAQ,MAAM;QACzBQ,QAAQP,cAAc,CAAC,CAAC,EAAEA,YAAY,CAAC,GAAG;QAE1C,IAAI,CAACb,oBAAoB;YACvBN,QAAQ2B,IAAI,CAAC;gBACXC,MAAM;gBACNC,YAAY;gBACZhB,KAAKiB,KAAKC,SAAS,CACjBC,QAAQC,OAAO,CAAC;gBAElBC,OAAO,CAAC;YACV;YAEA5B,qBAAqB;QACvB;QAEA,MAAM6B,SAASb,SAAS,CAAC,EAAEA,OAAO,CAAC,EAAET,IAAI,CAAC,GAAGA;QAC7C,IAAIgB,aAAatB,aAAa6B,GAAG,CAACD;QAElC,IAAI,CAACN,YAAY;YACfA,aAAa,CAAC,yBAAyB,EAAEtB,aAAa8B,IAAI,CAAC,GAAG,CAAC;YAC/D9B,aAAaU,GAAG,CAACkB,QAAQN;YAEzB7B,QAAQ2B,IAAI,CAAC;gBACXC,MAAM;gBACNC;gBACAhB,KAAKiB,KAAKC,SAAS,CAACI;gBACpBD,OAAOzB;YACT;QACF;QACA,mDAAmD;QACnD,MAAM6B,aAAa;QAEnB,MAAMC,iBAAiBT,KAAKC,SAAS,CAAC;YAAEI;YAAQT;YAAMY;QAAW;QACjE,IAAIE,kBAAkBhC,oBAAoB4B,GAAG,CAACG;QAE9C,IAAI,CAACC,iBAAiB;YACpBA,kBAAkB,CAAC,8BAA8B,EAAEhC,oBAAoB6B,IAAI,CAAC,GAAG,CAAC;YAChF7B,oBAAoBS,GAAG,CAACsB,gBAAgBC;YAExCvC,aAAa0B,IAAI,CAAC;gBAChBa;gBACAX;gBACAH;gBACAY;YACF;QACF;QAEA,OAAO;YACLG,KAAK7B,EAAE6B,GAAG;YACV5B,KAAK2B;QACP;IACF;IAEA,OAAO;QACLE,MAAM;YACJC,QAAOC,IAAS;gBACd,IAAI9C,eAAe+C,YAAY,EAAE;oBAC/B,MAAM/B,WAAWhB,eAAe+C,YAAY,CAC1CD,KAAKE,KAAK,CAACjC,GAAG,EACd+B,KAAKE,KAAK,CAACC,KAAK;oBAGlB,IAAI,CAACjC,UAAU;wBACb,OAAO8B;oBACT;gBACF;gBACA,IAAI/B,MAAM+B,KAAKE,KAAK,CAACjC,GAAG;gBAExBH;gBAEAP,mBAAmBc,GAAG,CAACP,gBAAgBG;gBACvCA,MAAM,CAAC,8CAA8C,EAAEH,eAAe,EAAE,CAAC;gBAEzE,uDAAuD;gBACvD,MAAMqC,QAAQH,KAAKE,KAAK,CAACC,KAAK,CAACC,YAAY,CAACzB,MAAM,GAC9CO,KAAKC,SAAS,CAACa,KAAKE,KAAK,CAACC,KAAK,CAACC,YAAY,IAC5CC;gBACJ,MAAMC,gBAAgBC,IAAAA,wBAAgB,EAACtC;gBACvC,IAAIS;gBACJ,IAAI4B,eAAe;oBACjB,MAAM7B,aAAaR,IAAIO,KAAK,CAAC;oBAC7B,IAAIC,WAAWE,MAAM,GAAG,GAAG;wBACzBV,MAAMQ,WAAWG,GAAG;wBACpBF,SAASD,WAAWI,IAAI,CAAC;oBAC3B;gBACF;gBACA,IAAI,CAACyB,eAAe;oBAClBnD,KAAK4B,IAAI,CAAC;wBAAEd;wBAAKkC;oBAAM;oBACvB,sBAAsB;oBACtB,OAAO;wBAAEnB,MAAM;wBAAWkB,OAAO;oBAAG;gBACtC;gBACA,MAAMX,SAASb,SAAS,CAAC,EAAEA,OAAO,CAAC,EAAET,IAAI,CAAC,GAAGA;gBAC7C,IAAIgB,aAAazB,mBAAmBgC,GAAG,CAACD;gBACxC,IAAI,CAACN,YAAY;oBACfA,aAAa,CAAC,6BAA6B,EAAEzB,mBAAmBiC,IAAI,CAAC,GAAG,CAAC;oBACzEjC,mBAAmBa,GAAG,CAACkB,QAAQN;oBAE/B,MAAMuB,YAAYtD,eAAeuD,UAAU,CAAClB;oBAC5CnC,QAAQ2B,IAAI,CAAC;wBACXC,MAAM;wBACNC;wBACAhB,KAAKuC;oBACP;gBACF;gBACArD,KAAK4B,IAAI,CAAC;oBAAEE;oBAAYkB;gBAAM;gBAC9B,sBAAsB;gBACtB,OAAO;oBAAEnB,MAAM;oBAAWkB,OAAO;gBAAG;YACtC;QACF;QACAQ,KAAIV,IAAS;YACX,OAAOjC,UAAUiC;QACnB;IACF;AACF;AAEA,SAASW,kBAAkB,EACzBxD,IAAI,EACJC,OAAO,EACPC,YAAY,EACZC,YAAY,EACZmD,UAAU,EAOX;IACC,IAAInB,QAAQ,CAAC;IACb,IAAIsB,mBAAmB,CAAC;IAExB,OAAO;QACLC,aAAa;YACXC,UAASd,IAAS;gBAChB,IAAIA,KAAKe,QAAQ,KAAK,YAAY;oBAChC;gBACF;gBAEA,MAAMC,YAAYhB,KAAKE,KAAK,CAACe,IAAI;gBAEjC,IAAID,CAAAA,6BAAAA,UAAWhC,IAAI,MAAK,QAAQ;oBAC9B;gBACF;gBAEA,IAAIf,MAAM+C,UAAUd,KAAK;gBACzB,IAAI,CAACjC,KAAK;oBACR;gBACF;gBAEAqB;gBAEAhC,aAAae,GAAG,CAACiB,OAAOrB;gBACxBA,MAAM,CAAC,4CAA4C,EAAEqB,MAAM,EAAE,CAAC;gBAE9D,MAAML,aAAa,CAAC,0BAA0B,EAAE7B,QAAQuB,MAAM,CAAC,GAAG,CAAC;gBACnEvB,QAAQ2B,IAAI,CAAC;oBACXC,MAAM;oBACNC;oBACAiC,MAAM;oBACNjD,KAAKwC,WAAWxC;oBAChBqB;gBACF;gBAEAnC,KAAK4B,IAAI,CAAC;oBAAEE;oBAAYkC,QAAQ;oBAAM7B;gBAAM;gBAE5C,MAAM8B,WAAqB,EAAE;gBAE7B,KAAK,MAAMC,aAAarB,KAAKE,KAAK,CAACoB,KAAK,CAAE;oBACxCV;oBACA,MAAMhB,kBAAkB,CAAC,0BAA0B,EAAEN,MAAM,aAAa,EAAEsB,iBAAiB,GAAG,CAAC;oBAE/FvD,aAAa0B,IAAI,CAAC;wBAChBa;wBACAX;wBACAoC;oBACF;oBACAD,SAASrC,IAAI,CAACa;gBAChB;gBAEA,OAAO;oBACLmB,UAAU;oBACVb,OAAO;wBACLL,KAAKG,KAAKE,KAAK,CAACL,GAAG;wBACnByB,OAAOF;wBACPH,MAAMD;oBACR;gBACF;YACF;QACF;IACF;AACF;AAEA,MAAMO,cAAc,CAAC,mBAAmB,CAAC;AAClC,eAAe3E,mBAEpB4E,MAAc,EACdC,OAA6B;QAuBzBC;IArBJ,MAAMC,OAAO,IAAI,CAACC,KAAK;IACvB,MAAMF,UAAU,IAAI,CAACG,UAAU;IAC/B,MAAM,EAAEC,cAAc,EAAEC,SAASC,WAAW,EAAE,GAAGC,MAAM,GAAGP;IAE1DA,QAAQQ,OAAO,KAAK,CAAC;IAErB,IAAIJ,kBAAkB,OAAOA,eAAeK,YAAY,KAAK,YAAY;QACvER,KACE,IAAIS,UACF,CAAC,CAAC,EAAEb,YAAY,8FAA8F,EAAE,OAAOO,eAAeK,YAAY,CAAC,CAAC;QAGxJ;IACF;IAEA,MAAME,WAAuB,EAAE;IAC/B,MAAMjF,UAAuB,EAAE;IAC/B,MAAMkF,cAA2B,EAAE;IACnC,MAAMnF,OAAmB,EAAE;IAC3B,MAAME,eAAiC,EAAE;IAEzC,IAAIqE,EAAAA,mBAAAA,QAAQQ,OAAO,qBAAfR,iBAAiBa,gBAAgB,MAAK,MAAM;QAC9CnF,QAAQoF,OAAO,CAAC;YACdxD,MAAM;YACNC,YAAY;YACZhB,KAAKwE,IAAAA,kCAAgB,EACnB,IAAI,EACJrD,QAAQC,OAAO,CAAC;QAEpB;IACF;IACA,MAAM,EAAEqD,YAAY,EAAE,GAAGtD,QAAQ;IAEjC,MAAMuD,YACJb,CAAAA,kCAAAA,eAAgBK,YAAY,KAC5B,AAAC,CAAA,MAAMO,cAAa,EAAGE,GAAG,CAACC,SAAS,CAACF,SAAS;IAEhD,MAAMrF,eAAe,IAAIG;IACzB,MAAMqF,mBAAmB,IAAIrF;IAC7B,MAAMF,qBAAqB,IAAIE;IAE/B,MAAMsF,mBAAmB9F,0BACvB;QACEwD,YAAY,CAACxC,MACXwE,IAAAA,kCAAgB,EACd,IAAI,EACJO,IAAAA,uBAAe,EAAC,IAAI,EAAEtB,QAAQuB,aAAa,IAAI,KAAKhF;QAExDE,WAAW+E,IAAAA,iBAAS,EAACxB,QAAQzD,GAAG,EAAE,IAAI,CAACkF,YAAY;QACnDlD,cAAciD,IAAAA,iBAAS,EAACxB,QAAQ3B,MAAM,EAAE,IAAI,CAACoD,YAAY;QAEzDC,SAAS,IAAI,CAACA,OAAO;IACvB,GACAjG,MACAC,SACAC,cACAC,cACAC;IAGF,MAAM8F,cAAc1C,kBAAkB;QACpCxD;QACAC,SAASkF;QACTjF;QACAC,cAAcwF;QACdrC,YAAY,CAACxC,MACXwE,IAAAA,kCAAgB,EACd,IAAI,EACJO,IAAAA,uBAAe,EAAC,IAAI,EAAEtB,QAAQuB,aAAa,IAAIhF;IAErD;IAEA,uDAAuD;IACvD,gGAAgG;IAChG,iCAAiC;IACjC,MAAMqF,UAAU;QACd,GAAGP,gBAAgB;QACnB,GAAGM,WAAW;IAChB;IAEA,IAAI;QACF,MAAM,EACJE,IAAI,EACJC,GAAG,EACHnB,SAASoB,aAAa,EACvB,GAAGd,UAAU;YACZ,GAAGV,IAAI;YACPqB;YACAI,YACEhC,QAAQQ,OAAO,IAAIlF,aAAa2G,IAAI,CAAC,IAAI,CAACR,YAAY,IAClD;gBACES,SAASC,QAAQC,GAAG,CAACC,gBAAgB,GACjC,oBACA;YACN,IACA1D;YACN2D,UAAU,IAAI,CAACb,YAAY;YAC3BI,MAAMzG,QAAQmH,MAAM,CAACzC;YACrB0C,WAAW,IAAI,CAACA,SAAS;YACzBnC,SAASoC,IAAAA,iBAAU,EAAC;gBAAEpC,SAASC;gBAAaoC,KAAKC,oBAAS,CAACC,MAAM;YAAC;YAClEC,gBACE,IAAI,CAACL,SAAS,IAAIzC,UAAUvC,KAAKC,SAAS,CAACsC,WAAWpB;YACxDmE,SAAS;QACX;QACA,IAAIC,kBAAkBlB,KAAKmB,QAAQ;QAEnC,IAAIjB,eAAe;YACjB,IAAK,MAAMkB,QAAQlB,cAAe;gBAChC,IAAImB,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACtB,eAAekB,OAAO;oBAC7D,MAAMK,IAAIvB,aAAa,CAACkB,KAAK;oBAC7B,IAAIzE,QAAQ8E,EAAEL,IAAI;oBAClB,KAAK,MAAMM,WAAWD,EAAElE,QAAQ,CAAE;wBAChCZ,SAAS,CAAC,CAAC,EAAE+E,QAAQN,IAAI,CAAC,CAAC;oBAC7B;oBAEAtC,SAAQtD,IAAI,CAAC;wBACX4F;wBACAzE;oBACF;gBACF;YACF;QACF;QAEA,IAAI5C,aAAamC,IAAI,KAAK,GAAG;YAC3B,MAAMyF,cAAc,IAAI,CAACC,UAAU,CAAC;gBAClCC,gBAAgB;oBAAC;iBAAQ;gBACzBC,YAAY;oBAAC;iBAAQ;gBACrBC,WAAW,EAAE;gBACbC,YAAY,EAAE;YAChB;YAEA,KAAK,MAAM,CAACjG,OAAOrB,IAAI,IAAIX,aAAakI,OAAO,GAAI;gBACjD,MAAM,CAACC,SAAY,GAAGxH,IAAIO,KAAK,CAAC,UAAU;gBAE1C,MAAMkH,UAAUC,IAAAA,kBAAU,EAACF,UAAU,IAAI,CAACG,WAAW;gBACrD,MAAMC,cAAc,MAAMC,IAAAA,uBAAe,EAACZ,aAAa,IAAI,CAAC9B,OAAO,EAAE;uBAChE,IAAI2C,IAAI;wBAACL;wBAASzH;qBAAI;iBAC1B;gBAED,KAAK,MAAM+H,cAAc5I,QAAS;oBAChC4I,WAAW/H,GAAG,GAAG+H,WAAW/H,GAAG,CAACgI,OAAO,CACrC,CAAC,uCAAuC,EAAE3G,MAAM,EAAE,CAAC,EACnDuG,eAAe5H;gBAEnB;YACF;QACF;QAEA,IAAIV,mBAAmBkC,IAAI,KAAK,GAAG;YACjC,MAAMyG,iBAAiB,IAAI,CAACf,UAAU,CAAC;gBACrCC,gBAAgB;oBAAC;iBAAQ;gBACzBG,YAAY;oBAAC;iBAAO;gBACpBF,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;gBAC3Ba,cAAc;oBAAC;iBAAU;YAC3B;YAEA,KAAK,MAAM,CAAC7G,OAAOrB,IAAI,IAAIV,mBAAmBiI,OAAO,GAAI;gBACvD,MAAM,CAACC,SAAY,GAAGxH,IAAIO,KAAK,CAAC,UAAU;gBAE1C,MAAMkH,UAAUC,IAAAA,kBAAU,EAACF,UAAU,IAAI,CAACG,WAAW;gBACrD,MAAMC,cAAc,MAAMC,IAAAA,uBAAe,EACvCI,gBACA,IAAI,CAAC9C,OAAO,EACZ;uBAAI,IAAI2C,IAAI;wBAACL;wBAASzH;qBAAI;iBAAE;gBAG9B,KAAK,MAAM+H,cAAc5I,QAAS;oBAChC4I,WAAW/H,GAAG,GAAG+H,WAAW/H,GAAG,CAACgI,OAAO,CACrC,CAAC,8CAA8C,EAAE3G,MAAM,EAAE,CAAC,EAC1DuG,eAAe5H;gBAEnB;YACF;QACF;QACA,IAAI6E,iBAAiBrD,IAAI,KAAK,GAAG;YAC/B,MAAM2G,eAAe,IAAI,CAACjB,UAAU,CAAC;gBACnCC,gBAAgB;oBAAC;iBAAQ;gBACzBG,YAAY,EAAE;gBACdF,YAAY;oBAAC;oBAAO;oBAAS;oBAAQ;iBAAM;gBAC3CC,WAAW;oBAAC;oBAAS;iBAAM;YAC7B;YAEA,KAAK,MAAM,CAAChG,OAAOrB,IAAI,IAAI6E,iBAAiB0C,OAAO,GAAI;gBACrD,MAAM,CAACC,SAAY,GAAGxH,IAAIO,KAAK,CAAC,UAAU;gBAE1C,MAAMkH,UAAUC,IAAAA,kBAAU,EAACF,UAAU,IAAI,CAACG,WAAW;gBACrD,MAAMC,cAAc,MAAMC,IAAAA,uBAAe,EAACM,cAAc,IAAI,CAAChD,OAAO,EAAE;uBACjE,IAAI2C,IAAI;wBAAC9H;wBAAKyH;qBAAQ;iBAC1B;gBAED,KAAK,MAAMM,cAAc1D,YAAa;oBACpC0D,WAAW/H,GAAG,GAAG+H,WAAW/H,GAAG,CAACgI,OAAO,CACrC,CAAC,4CAA4C,EAAE3G,MAAM,EAAE,CAAC,EACxDuG,eAAe5H;gBAEnB;YACF;QACF;QAEAb,QAAQ2B,IAAI,IAAIuD;QAEhB,MAAM+D,aAAaC,IAAAA,sBAAa,EAAClJ,SAASsE;QAC1C,MAAM6E,aAAaC,IAAAA,sBAAa,EAC9B;YAAE5D,KAAK6B;YAAiBjB;QAAI,GAC5BrG,MACAE,cACAqE,SACA,IAAI;QAEN,MAAM+E,aAAaC,IAAAA,sBAAa,EAACrE,UAAShF,cAAcqE;QAExD,MAAMiF,SAAS,CAAC,EAAEN,WAAW,EAAEE,WAAW,EAAEE,WAAW,CAAC;QAExD9E,KAAK,MAAMgF,QAAQnD,OAAOtE,KAAK0H,KAAK,CAACpD,IAAIkB,QAAQ;IACnD,EAAE,OAAOmC,OAAgB;QACvBC,QAAQD,KAAK,CAAC,6BAA6BA;QAC3ClF,KAAKkF;IACP;AACF;AAEO,MAAMhK,MAAM"}