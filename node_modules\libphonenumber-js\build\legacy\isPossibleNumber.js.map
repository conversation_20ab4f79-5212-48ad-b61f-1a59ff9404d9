{"version": 3, "file": "isPossibleNumber.js", "names": ["_getNumberType", "require", "_isPossible", "_interopRequireDefault", "e", "__esModule", "isPossibleNumber", "_normalizeArguments", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "v2", "_isPossibleNumber"], "sources": ["../../source/legacy/isPossibleNumber.js"], "sourcesContent": ["import { normalizeArguments } from './getNumberType.js'\r\nimport _isPossibleNumber from '../isPossible.js'\r\n\r\n/**\r\n * Checks if a given phone number is possible.\r\n * Which means it only checks phone number length\r\n * and doesn't test any regular expressions.\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isPossibleNumber('+78005553535', metadata)\r\n * isPossibleNumber('8005553535', 'RU', metadata)\r\n * isPossibleNumber('88005553535', 'RU', metadata)\r\n * isPossibleNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\r\nexport default function isPossibleNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone && !(options && options.v2)) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isPossibleNumber(input, options, metadata)\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAgD,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASE,gBAAgBA,CAAA,EAAG;EAC1C,IAAAC,mBAAA,GAAqC,IAAAC,iCAAkB,EAACC,SAAS,CAAC;IAA1DC,KAAK,GAAAH,mBAAA,CAALG,KAAK;IAAEC,OAAO,GAAAJ,mBAAA,CAAPI,OAAO;IAAEC,QAAQ,GAAAL,mBAAA,CAARK,QAAQ;EAChC;EACA,IAAI,CAACF,KAAK,CAACG,KAAK,IAAI,EAAEF,OAAO,IAAIA,OAAO,CAACG,EAAE,CAAC,EAAE;IAC7C,OAAO,KAAK;EACb;EACA,OAAO,IAAAC,sBAAiB,EAACL,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACnD", "ignoreList": []}