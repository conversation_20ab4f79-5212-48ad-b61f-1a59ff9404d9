{"version": 3, "file": "applyInternationalSeparatorStyle.test.js", "names": ["applyInternationalSeparatorStyle", "describe", "it", "expect", "to", "equal"], "sources": ["../../source/helpers/applyInternationalSeparatorStyle.test.js"], "sourcesContent": ["import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'\r\n\r\ndescribe('applyInternationalSeparatorStyle', () => {\r\n\tit('should change Google\\'s international format style', () => {\r\n\t\texpect(applyInternationalSeparatorStyle('(xxx) xxx-xx-xx')).to.equal('xxx xxx xx xx')\r\n\t\texpect(applyInternationalSeparatorStyle('(xxx)xxx')).to.equal('xxx xxx')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,gCAAgC,MAAM,uCAAuC;AAEpFC,QAAQ,CAAC,kCAAkC,EAAE,YAAM;EAClDC,EAAE,CAAC,oDAAoD,EAAE,YAAM;IAC9DC,MAAM,CAACH,gCAAgC,CAAC,iBAAiB,CAAC,CAAC,CAACI,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IACrFF,MAAM,CAACH,gCAAgC,CAAC,UAAU,CAAC,CAAC,CAACI,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;EACzE,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}