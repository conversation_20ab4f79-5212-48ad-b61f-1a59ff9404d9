{"version": 3, "sources": ["../../../../../../../src/build/webpack/config/blocks/css/loaders/next-font.ts"], "names": ["getNextFontLoader", "ctx", "postcss", "fontLoaderPath", "loaders", "isClient", "push", "getClientStyleLoader", "hasAppDir", "isDevelopment", "assetPrefix", "loader", "require", "resolve", "options", "importLoaders", "esModule", "url", "resourcePath", "cssFileResolve", "experimental", "urlImports", "import", "_", "modules", "exportLocalsConvention", "exportOnlyLocals", "isServer", "mode", "getLocalIdent", "_context", "_localIdentName", "exportName", "_options", "meta", "fontFamilyHash", "fontLoader", "isDev"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;wBAHqB;6BACN;AAExB,SAASA,kBACdC,GAAyB,EACzBC,OAAY,EACZC,cAAsB;IAEtB,MAAMC,UAAoC,EAAE;IAE5C,IAAIH,IAAII,QAAQ,EAAE;QAChB,4DAA4D;QAC5D,SAAS;QACTD,QAAQE,IAAI,CACVC,IAAAA,4BAAoB,EAAC;YACnBC,WAAWP,IAAIO,SAAS;YACxBC,eAAeR,IAAIQ,aAAa;YAChCC,aAAaT,IAAIS,WAAW;QAC9B;IAEJ;IAEAN,QAAQE,IAAI,CAAC;QACXK,QAAQC,QAAQC,OAAO,CAAC;QACxBC,SAAS;YACPZ;YACAa,eAAe;YACf,4CAA4C;YAC5CC,UAAU;YACVC,KAAK,CAACA,KAAaC,eACjBC,IAAAA,2BAAc,EAACF,KAAKC,cAAcjB,IAAImB,YAAY,CAACC,UAAU;YAC/DC,QAAQ,CAACL,KAAaM,GAAQL,eAC5BC,IAAAA,2BAAc,EAACF,KAAKC,cAAcjB,IAAImB,YAAY,CAACC,UAAU;YAC/DG,SAAS;gBACP,mEAAmE;gBACnEC,wBAAwB;gBACxB,2CAA2C;gBAC3CC,kBAAkBzB,IAAI0B,QAAQ;gBAC9B,6DAA6D;gBAC7D,iCAAiC;gBACjCC,MAAM;gBACNC,eAAe,CACbC,UACAC,iBACAC,YACAC,UACAC;oBAEA,6BAA6B;oBAC7B,OAAO,CAAC,EAAE,EAAEF,WAAW,CAAC,EAAEE,KAAKC,cAAc,CAAC,CAAC;gBACjD;YACF;YACAC,YAAY;QACd;IACF;IAEAhC,QAAQE,IAAI,CAAC;QACXK,QAAQ;QACRG,SAAS;YACPuB,OAAOpC,IAAIQ,aAAa;YACxBkB,UAAU1B,IAAI0B,QAAQ;YACtBjB,aAAaT,IAAIS,WAAW;YAC5BP;YACAD;QACF;IACF;IAEA,OAAOE;AACT"}