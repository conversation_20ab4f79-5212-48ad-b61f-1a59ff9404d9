{"version": 3, "file": "IsISBN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISBN.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAItC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,SAAgB,MAAM,CAAC,KAAc,EAAE,OAAuB;IAC5D,+EAA+E;IAC/E,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,CAAE,GAAG,OAAO,EAAkB,CAAC,CAAC,CAAC,SAAS,CAAC;IACvE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACzE,CAAC;AAJD,wBAIC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,OAAuB,EAAE,iBAAqC;IACnF,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,2BAA2B,EAAE,iBAAiB,CAAC;SACxG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAZD,wBAYC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIsbnValidator from 'validator/lib/isISBN';\n\nexport type IsISBNVersion = '10' | '13' | 10 | 13;\n\nexport const IS_ISBN = 'isIsbn';\n\n/**\n * Checks if the string is an ISBN (version 10 or 13).\n * If given value is not a string, then it returns false.\n */\nexport function isISBN(value: unknown, version?: IsISBNVersion): boolean {\n  /* eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion */\n  const versionStr = version ? (`${version}` as '10' | '13') : undefined;\n  return typeof value === 'string' && isIsbnValidator(value, versionStr);\n}\n\n/**\n * Checks if the string is an ISBN (version 10 or 13).\n * If given value is not a string, then it returns false.\n */\nexport function IsISBN(version?: IsISBNVersion, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISBN,\n      constraints: [version],\n      validator: {\n        validate: (value, args): boolean => isISBN(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an ISBN', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}