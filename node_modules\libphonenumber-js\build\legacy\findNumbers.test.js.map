{"version": 3, "file": "findNumbers.test.js", "names": ["_findNumbers", "_interopRequireDefault", "require", "_metadataMax", "e", "__esModule", "describe", "it", "expect", "findNumbers", "metadata", "to", "deep", "equal", "phone", "country", "startsAt", "endsAt", "leniency", "ext", "phoneNumbers", "v2", "length", "number", "nationalNumber", "countryCallingCode", "thrower", "numbers", "defaultCountry", "possibleNumbers", "extended"], "sources": ["../../source/legacy/findNumbers.test.js"], "sourcesContent": ["import findNumbers from './findNumbers.js'\r\nimport metadata from '../../metadata.max.json' with { type: 'json' }\r\n\r\ndescribe('findNumbers', () => {\r\n\tit('should find numbers', () => {\r\n\t\texpect(findNumbers('2133734253', 'US', metadata)).to.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 10\r\n\t\t}])\r\n\r\n\t\texpect(findNumbers('(*************', 'US', metadata)).to.deep.equal([{\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 14\r\n\t\t}])\r\n\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// Opening parenthesis issue.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/252\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 and not (************* (that\\'s not even in the same country!) as written in the document.', 'US', metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tphone    : '2133734253',\r\n\t\t\tcountry  : 'US',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}])\r\n\r\n\t\t// No default country.\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 as written in the document.', metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options` and default country.\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 as written in the document.', 'US', { leniency: 'VALID' }, metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Passing `options`.\r\n\t\texpect(\r\n            findNumbers('The number is +7 (800) 555-35-35 as written in the document.', { leniency: 'VALID' }, metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}])\r\n\r\n\t\t// Not a phone number and a phone number.\r\n\t\texpect(\r\n            findNumbers('Digits 12 are not a number, but +7 (800) 555-35-35 is.', { leniency: 'VALID' }, metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\tstartsAt : 32,\r\n\t\t\tendsAt   : 50\r\n\t\t}])\r\n\r\n\t\t// Phone number extension.\r\n\t\texpect(\r\n            findNumbers('Date 02/17/2018 is not a number, but +7 (800) 555-35-35 ext. 123 is.', { leniency: 'VALID' }, metadata)\r\n        ).to.deep.equal([{\r\n\t\t\tphone    : '8005553535',\r\n\t\t\tcountry  : 'RU',\r\n\t\t\text      : '123',\r\n\t\t\tstartsAt : 37,\r\n\t\t\tendsAt   : 64\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should find numbers (v2)', () => {\r\n\t\tconst phoneNumbers = findNumbers('The number is +7 (800) 555-35-35 ext. 1234 and not (************* as written in the document.', 'US', { v2: true }, metadata)\r\n\r\n\t\texpect(phoneNumbers.length).to.equal(2)\r\n\r\n\t\texpect(phoneNumbers[0].startsAt).to.equal(14)\r\n\t\texpect(phoneNumbers[0].endsAt).to.equal(42)\r\n\r\n\t\texpect(phoneNumbers[0].number.number).to.equal('+78005553535')\r\n\t\texpect(phoneNumbers[0].number.nationalNumber).to.equal('8005553535')\r\n\t\texpect(phoneNumbers[0].number.country).to.equal('RU')\r\n\t\texpect(phoneNumbers[0].number.countryCallingCode).to.equal('7')\r\n\t\texpect(phoneNumbers[0].number.ext).to.equal('1234')\r\n\r\n\t\texpect(phoneNumbers[1].startsAt).to.equal(51)\r\n\t\texpect(phoneNumbers[1].endsAt).to.equal(65)\r\n\r\n\t\texpect(phoneNumbers[1].number.number).to.equal('+12133734253')\r\n\t\texpect(phoneNumbers[1].number.nationalNumber).to.equal('2133734253')\r\n\t\texpect(phoneNumbers[1].number.country).to.equal('US')\r\n\t\texpect(phoneNumbers[1].number.countryCallingCode).to.equal('1')\r\n\t})\r\n\r\n\tit('shouldn\\'t find non-valid numbers', () => {\r\n\t\t// Not a valid phone number for US.\r\n\t\texpect(findNumbers('1111111111', 'US', metadata)).to.deep.equal([])\r\n\t})\r\n\r\n\tit('should find non-European digits', () => {\r\n\t\t// E.g. in Iraq they don't write `+442323234` but rather `+٤٤٢٣٢٣٢٣٤`.\r\n\t\texpect(findNumbers('العَرَبِيَّة‎ +٤٤٣٣٣٣٣٣٣٣٣٣عَرَبِيّ‎', metadata)).to.deep.equal([{\r\n\t\t\tcountry  : 'GB',\r\n\t\t\tphone    : '3333333333',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 27\r\n\t\t}])\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No input\r\n\t\texpect(findNumbers('', metadata)).to.deep.equal([])\r\n\r\n\t\t// // No country metadata for this `require` country code\r\n\t\t// thrower = () => findNumbers('123', 'ZZ', metadata)\r\n\t\t// thrower.should.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => findNumbers(2141111111, 'US')\r\n\t\texpect(thrower).to.throw('A text for parsing must be a string.')\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => findNumbers('')\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// No metadata, no default country, no phone numbers.\r\n\t\texpect(findNumbers('')).to.deep.equal([])\r\n\t})\r\n\r\n\tit('should find international numbers when passed a non-existent default country', () => {\r\n\t\tconst numbers = findNumbers('Phone: +7 (800) 555 35 35. National: 8 (800) 555-55-55', { defaultCountry: 'XX', v2: true }, metadata)\r\n\t\texpect(numbers.length).to.equal(1)\r\n\t\texpect(numbers[0].number.nationalNumber).to.equal('8005553535')\r\n\t})\r\n\r\n\tit('shouldn\\'t find phone numbers which are not phone numbers', () => {\r\n\t\t// A timestamp.\r\n\t\texpect(findNumbers('2012-01-02 08:00', 'US', metadata)).to.deep.equal([])\r\n\r\n\t\t// A valid number (not a complete timestamp).\r\n\t\texpect(findNumbers('2012-01-02 08', 'US', metadata)).to.deep.equal([{\r\n\t\t\tcountry  : 'US',\r\n\t\t\tphone    : '2012010208',\r\n\t\t\tstartsAt : 0,\r\n\t\t\tendsAt   : 13\r\n\t\t}])\r\n\r\n\t\t// Invalid parens.\r\n\t\texpect(findNumbers('213(3734253', 'US', metadata)).to.deep.equal([])\r\n\r\n\t\t// Letters after phone number.\r\n\t\texpect(findNumbers('2133734253a', 'US', metadata)).to.deep.equal([])\r\n\r\n\t\t// Valid phone (same as the one found in the UUID below).\r\n\t\texpect(findNumbers('The phone number is 231354125.', 'FR', metadata)).to.deep.equal([{\r\n\t\t\tcountry  : 'FR',\r\n\t\t\tphone    : '231354125',\r\n\t\t\tstartsAt : 20,\r\n\t\t\tendsAt   : 29\r\n\t\t}])\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Should parse in `{ extended: true }` mode.\r\n\t\tconst possibleNumbers = findNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', { extended: true }, metadata)\r\n\t\texpect(possibleNumbers.length).to.equal(1)\r\n\t\texpect(possibleNumbers[0].country).to.equal('FR')\r\n\t\texpect(possibleNumbers[0].phone).to.equal('231354125')\r\n\r\n\t\t// Not a phone number (part of a UUID).\r\n\t\t// Shouldn't parse by default.\r\n\t\texpect(\r\n            findNumbers('The UUID is CA801c26f98cd16e231354125ad046e40b.', 'FR', metadata)\r\n        ).to.deep.equal([])\r\n\t})\r\n\r\n\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/merge_requests/4\r\n\tit('should return correct `startsAt` and `endsAt` when matching \"inner\" candidates in a could-be-a-candidate substring', () => {\r\n\t\texpect(findNumbers('39945926 77200596 16533084', 'ID', metadata)).to.deep.equal([{\r\n\t\t\t\tcountry: 'ID',\r\n\t\t\t\tphone: '77200596',\r\n\t\t\t\tstartsAt: 9,\r\n\t\t\t\tendsAt: 17\r\n\t\t\t}])\r\n\t})\r\n})\r\n"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAoE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEpEE,QAAQ,CAAC,aAAa,EAAE,YAAM;EAC7BC,EAAE,CAAC,qBAAqB,EAAE,YAAM;IAC/BC,MAAM,CAAC,IAAAC,uBAAW,EAAC,YAAY,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MAChEC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,CAAC;MACZC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;IAEHT,MAAM,CAAC,IAAAC,uBAAW,EAAC,gBAAgB,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACpEC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,CAAC;MACZC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;IAEHT,MAAM,CACI,IAAAC,uBAAW,EAAC,qFAAqF,EAAE,IAAI,EAAEC,uBAAQ,CACrH,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,EAAE;MACFH,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACA;IACAT,MAAM,CACI,IAAAC,uBAAW,EAAC,6HAA6H,EAAE,IAAI,EAAEC,uBAAQ,CAC7J,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,EAAE;MACFH,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAT,MAAM,CACI,IAAAC,uBAAW,EAAC,8DAA8D,EAAEC,uBAAQ,CACxF,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAT,MAAM,CACI,IAAAC,uBAAW,EAAC,8DAA8D,EAAE,IAAI,EAAE;MAAES,QAAQ,EAAE;IAAQ,CAAC,EAAER,uBAAQ,CACrH,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAT,MAAM,CACI,IAAAC,uBAAW,EAAC,8DAA8D,EAAE;MAAES,QAAQ,EAAE;IAAQ,CAAC,EAAER,uBAAQ,CAC/G,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAT,MAAM,CACI,IAAAC,uBAAW,EAAC,wDAAwD,EAAE;MAAES,QAAQ,EAAE;IAAQ,CAAC,EAAER,uBAAQ,CACzG,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAT,MAAM,CACI,IAAAC,uBAAW,EAAC,sEAAsE,EAAE;MAAES,QAAQ,EAAE;IAAQ,CAAC,EAAER,uBAAQ,CACvH,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACtBC,KAAK,EAAM,YAAY;MACvBC,OAAO,EAAI,IAAI;MACfI,GAAG,EAAQ,KAAK;MAChBH,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFV,EAAE,CAAC,0BAA0B,EAAE,YAAM;IACpC,IAAMa,YAAY,GAAG,IAAAX,uBAAW,EAAC,+FAA+F,EAAE,IAAI,EAAE;MAAEY,EAAE,EAAE;IAAK,CAAC,EAAEX,uBAAQ,CAAC;IAE/JF,MAAM,CAACY,YAAY,CAACE,MAAM,CAAC,CAACX,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;IAEvCL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAACL,EAAE,CAACE,KAAK,CAAC,EAAE,CAAC;IAC7CL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACH,MAAM,CAAC,CAACN,EAAE,CAACE,KAAK,CAAC,EAAE,CAAC;IAE3CL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACA,MAAM,CAAC,CAACZ,EAAE,CAACE,KAAK,CAAC,cAAc,CAAC;IAC9DL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACC,cAAc,CAAC,CAACb,EAAE,CAACE,KAAK,CAAC,YAAY,CAAC;IACpEL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACR,OAAO,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC;IACrDL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACE,kBAAkB,CAAC,CAACd,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;IAC/DL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACJ,GAAG,CAAC,CAACR,EAAE,CAACE,KAAK,CAAC,MAAM,CAAC;IAEnDL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAACL,EAAE,CAACE,KAAK,CAAC,EAAE,CAAC;IAC7CL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACH,MAAM,CAAC,CAACN,EAAE,CAACE,KAAK,CAAC,EAAE,CAAC;IAE3CL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACA,MAAM,CAAC,CAACZ,EAAE,CAACE,KAAK,CAAC,cAAc,CAAC;IAC9DL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACC,cAAc,CAAC,CAACb,EAAE,CAACE,KAAK,CAAC,YAAY,CAAC;IACpEL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACR,OAAO,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC;IACrDL,MAAM,CAACY,YAAY,CAAC,CAAC,CAAC,CAACG,MAAM,CAACE,kBAAkB,CAAC,CAACd,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;EAChE,CAAC,CAAC;EAEFN,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7C;IACAC,MAAM,CAAC,IAAAC,uBAAW,EAAC,YAAY,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;EACpE,CAAC,CAAC;EAEFN,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3C;IACAC,MAAM,CAAC,IAAAC,uBAAW,EAAC,sCAAsC,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACpFE,OAAO,EAAI,IAAI;MACfD,KAAK,EAAM,YAAY;MACvBE,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFV,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC,IAAImB,OAAO;;IAEX;IACAlB,MAAM,CAAC,IAAAC,uBAAW,EAAC,EAAE,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;;IAEnD;IACA;IACA;;IAEA;IACAa,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAAjB,uBAAW,EAAC,UAAU,EAAE,IAAI,CAAC;IAAA;IAC7CD,MAAM,CAACkB,OAAO,CAAC,CAACf,EAAE,SAAM,CAAC,sCAAsC,CAAC;;IAEhE;IACA;IACA;;IAEA;IACAH,MAAM,CAAC,IAAAC,uBAAW,EAAC,EAAE,CAAC,CAAC,CAACE,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;EAC1C,CAAC,CAAC;EAEFN,EAAE,CAAC,8EAA8E,EAAE,YAAM;IACxF,IAAMoB,OAAO,GAAG,IAAAlB,uBAAW,EAAC,wDAAwD,EAAE;MAAEmB,cAAc,EAAE,IAAI;MAAEP,EAAE,EAAE;IAAK,CAAC,EAAEX,uBAAQ,CAAC;IACnIF,MAAM,CAACmB,OAAO,CAACL,MAAM,CAAC,CAACX,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;IAClCL,MAAM,CAACmB,OAAO,CAAC,CAAC,CAAC,CAACJ,MAAM,CAACC,cAAc,CAAC,CAACb,EAAE,CAACE,KAAK,CAAC,YAAY,CAAC;EAChE,CAAC,CAAC;EAEFN,EAAE,CAAC,2DAA2D,EAAE,YAAM;IACrE;IACAC,MAAM,CAAC,IAAAC,uBAAW,EAAC,kBAAkB,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;;IAEzE;IACAL,MAAM,CAAC,IAAAC,uBAAW,EAAC,eAAe,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACnEE,OAAO,EAAI,IAAI;MACfD,KAAK,EAAM,YAAY;MACvBE,QAAQ,EAAG,CAAC;MACZC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAT,MAAM,CAAC,IAAAC,uBAAW,EAAC,aAAa,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;;IAEpE;IACAL,MAAM,CAAC,IAAAC,uBAAW,EAAC,aAAa,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;;IAEpE;IACAL,MAAM,CAAC,IAAAC,uBAAW,EAAC,gCAAgC,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MACpFE,OAAO,EAAI,IAAI;MACfD,KAAK,EAAM,WAAW;MACtBE,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC,CAAC;;IAEH;IACA;IACA,IAAMY,eAAe,GAAG,IAAApB,uBAAW,EAAC,iDAAiD,EAAE,IAAI,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,EAAEpB,uBAAQ,CAAC;IAC1HF,MAAM,CAACqB,eAAe,CAACP,MAAM,CAAC,CAACX,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;IAC1CL,MAAM,CAACqB,eAAe,CAAC,CAAC,CAAC,CAACd,OAAO,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC;IACjDL,MAAM,CAACqB,eAAe,CAAC,CAAC,CAAC,CAACf,KAAK,CAAC,CAACH,EAAE,CAACE,KAAK,CAAC,WAAW,CAAC;;IAEtD;IACA;IACAL,MAAM,CACI,IAAAC,uBAAW,EAAC,iDAAiD,EAAE,IAAI,EAAEC,uBAAQ,CACjF,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,EAAE,CAAC;EAC1B,CAAC,CAAC;;EAEF;EACAN,EAAE,CAAC,oHAAoH,EAAE,YAAM;IAC9HC,MAAM,CAAC,IAAAC,uBAAW,EAAC,4BAA4B,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC;MAC/EE,OAAO,EAAE,IAAI;MACbD,KAAK,EAAE,UAAU;MACjBE,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE;IACT,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}