{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/css/plugins.ts"], "names": ["bold", "red", "underline", "yellow", "findConfig", "genericErrorText", "getError_NullConfig", "pluginName", "isIgnoredPlugin", "pluginPath", "ignoredRegex", "match", "exec", "plugin", "pop", "console", "warn", "createLazyPostCssPlugin", "fn", "result", "undefined", "args", "postcss", "loadPlugin", "dir", "options", "error", "Error", "require", "resolve", "paths", "Object", "keys", "length", "getDefaultPlugins", "supportedBrowsers", "disablePostcssPresetEnv", "browsers", "autoprefixer", "flexbox", "stage", "features", "filter", "Boolean", "getPostCssPlugins", "config", "plugins", "<PERSON><PERSON><PERSON>", "find", "key", "Array", "isArray", "pc", "reduce", "acc", "curr", "p", "push", "parsed", "for<PERSON>ach", "pluginConfig", "resolved", "Promise", "all", "map", "filtered"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gCAA+B;AAC5E,SAASC,UAAU,QAAQ,iCAAgC;AAY3D,MAAMC,mBAAmB;AAEzB,SAASC,oBAAoBC,UAAkB;IAC7C,OAAO,CAAC,EAAEN,IACRD,KAAK,UACL,kCAAkC,EAAEO,WAAW,cAAc,EAAEP,KAC/D,QACA,6BAA6B,EAAEO,WAAW,QAAQ,EAAEP,KACpD,SACA,kBAAkB,EAAEA,KAAK,QAAQ,2BAA2B,CAAC;AACjE;AAEA,SAASQ,gBAAgBC,UAAkB;IACzC,MAAMC,eACJ;IACF,MAAMC,QAAQD,aAAaE,IAAI,CAACH;IAChC,IAAIE,SAAS,MAAM;QACjB,OAAO;IACT;IAEA,MAAME,SAASF,MAAMG,GAAG;IACxBC,QAAQC,IAAI,CACV,CAAC,EAAEb,OAAOH,KAAK,YAAY,oBAAoB,EAAEE,UAC/CW,QACA,yCAAyC,CAAC,GAC1C,CAAC,qDAAqD,CAAC,GACvD;IAEJ,OAAO;AACT;AAEA,MAAMI,0BAA0B,CAC9BC;IAEA,IAAIC,SAAcC;IAClB,MAAMP,SAAS,CAAC,GAAGQ;QACjB,IAAIF,WAAWC,WAAWD,SAASD;QACnC,IAAIC,OAAOG,OAAO,KAAK,MAAM;YAC3B,OAAOH,UAAUE;QACnB,OAAO,IAAIF,OAAOG,OAAO,EAAE;YACzB,OAAOH,OAAOG,OAAO;QACvB;QACA,OAAOH;IACT;IACAN,OAAOS,OAAO,GAAG;IACjB,OAAOT;AACT;AAEA,eAAeU,WACbC,GAAW,EACXjB,UAAkB,EAClBkB,OAAkC;IAElC,IAAIA,YAAY,SAASjB,gBAAgBD,aAAa;QACpD,OAAO;IACT;IAEA,IAAIkB,WAAW,MAAM;QACnBV,QAAQW,KAAK,CAACpB,oBAAoBC;QAClC,MAAM,IAAIoB,MAAMtB;IAClB;IAEA,MAAMI,aAAamB,QAAQC,OAAO,CAACtB,YAAY;QAAEuB,OAAO;YAACN;SAAI;IAAC;IAC9D,IAAIhB,gBAAgBC,aAAa;QAC/B,OAAO;IACT,OAAO,IAAIgB,YAAY,MAAM;QAC3B,OAAOR,wBAAwB,IAAMW,QAAQnB;IAC/C,OAAO;QACL,IAAI,OAAOgB,YAAY,YAAYM,OAAOC,IAAI,CAACP,SAASQ,MAAM,KAAK,GAAG;YACpE,OAAOhB,wBAAwB,IAAMW,QAAQnB;QAC/C;QACA,OAAOQ,wBAAwB,IAAMW,QAAQnB,YAAYgB;IAC3D;AACF;AAEA,SAASS,kBACPC,iBAAuC,EACvCC,uBAAgC;IAEhC,OAAO;QACLR,QAAQC,OAAO,CAAC;QAChBO,0BACI,QACA;YACER,QAAQC,OAAO,CAAC;YAChB;gBACEQ,UAAUF,qBAAqB;oBAAC;iBAAW;gBAC3CG,cAAc;oBACZ,iCAAiC;oBACjCC,SAAS;gBACX;gBACA,+CAA+C;gBAC/C,+CAA+C;gBAC/CC,OAAO;gBACPC,UAAU;oBACR,qBAAqB;gBACvB;YACF;SACD;KACN,CAACC,MAAM,CAACC;AACX;AAEA,OAAO,eAAeC,kBACpBpB,GAAW,EACXW,iBAAuC,EACvCC,0BAAmC,KAAK;IAExC,IAAIS,SAAS,MAAMzC,WACjBoB,KACA;IAGF,IAAIqB,UAAU,MAAM;QAClBA,SAAS;YACPC,SAASZ,kBAAkBC,mBAAmBC;QAChD;IACF;IAEA,IAAI,OAAOS,WAAW,YAAY;QAChC,MAAM,IAAIlB,MACR,CAAC,oGAAoG,CAAC,GACpG;IAEN;IAEA,6DAA6D;IAC7D,MAAMoB,aAAahB,OAAOC,IAAI,CAACa,QAAQG,IAAI,CAAC,CAACC,MAAQA,QAAQ;IAC7D,IAAIF,YAAY;QACdhC,QAAQC,IAAI,CACV,CAAC,EAAEb,OACDH,KAAK,YACL,uEAAuE,EAAE+C,WAAW,KAAK,CAAC,GAC1F,CAAC,uCAAuC,CAAC;IAE/C;IAEA,yEAAyE;IACzE,IAAID,UAAUD,OAAOC,OAAO;IAC5B,IAAIA,WAAW,QAAQ,OAAOA,YAAY,UAAU;QAClD,MAAM,IAAInB,MACR,CAAC,gEAAgE,CAAC;IAEtE;IAEA,IAAI,CAACuB,MAAMC,OAAO,CAACL,UAAU;QAC3B,0CAA0C;QAC1C,MAAMM,KAAKN;QAEXA,UAAUf,OAAOC,IAAI,CAACc,SAASO,MAAM,CAAC,CAACC,KAAKC;YAC1C,MAAMC,IAAIJ,EAAE,CAACG,KAAK;YAClB,IAAI,OAAOC,MAAM,aAAa;gBAC5BzC,QAAQW,KAAK,CAACpB,oBAAoBiD;gBAClC,MAAM,IAAI5B,MAAMtB;YAClB;YAEAiD,IAAIG,IAAI,CAAC;gBAACF;gBAAMC;aAAE;YAClB,OAAOF;QACT,GAAG,EAAE;IACP;IAEA,MAAMI,SAA2B,EAAE;IACnCZ,QAAQa,OAAO,CAAC,CAAC9C;QACf,IAAIA,UAAU,MAAM;YAClBE,QAAQC,IAAI,CACV,CAAC,EAAEb,OAAOH,KAAK,YAAY,IAAI,EAAEA,KAC/B,QACA,yDAAyD,CAAC;QAEhE,OAAO,IAAI,OAAOa,WAAW,UAAU;YACrC6C,OAAOD,IAAI,CAAC;gBAAC5C;gBAAQ;aAAK;QAC5B,OAAO,IAAIqC,MAAMC,OAAO,CAACtC,SAAS;YAChC,MAAMN,aAAaM,MAAM,CAAC,EAAE;YAC5B,MAAM+C,eAAe/C,MAAM,CAAC,EAAE;YAC9B,IACE,OAAON,eAAe,YACrB,CAAA,OAAOqD,iBAAiB,aACvB,OAAOA,iBAAiB,YACxB,OAAOA,iBAAiB,QAAO,GACjC;gBACAF,OAAOD,IAAI,CAAC;oBAAClD;oBAAYqD;iBAAa;YACxC,OAAO;gBACL,IAAI,OAAOrD,eAAe,UAAU;oBAClCQ,QAAQW,KAAK,CACX,CAAC,EAAEzB,IACDD,KAAK,UACL,yCAAyC,EAAEA,KAC3C,UACA,oBAAoB,EAAEO,WAAW,IAAI,CAAC,GACtC;gBAEN,OAAO;oBACLQ,QAAQW,KAAK,CACX,CAAC,EAAEzB,IACDD,KAAK,UACL,kFAAkF,EAAEO,WAAW,KAAK,CAAC,GACrG;gBAEN;gBACA,MAAM,IAAIoB,MAAMtB;YAClB;QACF,OAAO,IAAI,OAAOQ,WAAW,YAAY;YACvCE,QAAQW,KAAK,CACX,CAAC,EAAEzB,IACDD,KAAK,UACL,0FAA0F,EAAEA,KAC5F,UACA,4DAA4D,CAAC;YAEjE,MAAM,IAAI2B,MAAMtB;QAClB,OAAO;YACLU,QAAQW,KAAK,CACX,CAAC,EAAEzB,IACDD,KAAK,UACL,0CAA0C,EAAEa,OAAO,IAAI,CAAC,GACxD;YAEJ,MAAM,IAAIc,MAAMtB;QAClB;IACF;IAEA,MAAMwD,WAAW,MAAMC,QAAQC,GAAG,CAChCL,OAAOM,GAAG,CAAC,CAACR,IAAMjC,WAAWC,KAAKgC,CAAC,CAAC,EAAE,EAAEA,CAAC,CAAC,EAAE;IAE9C,MAAMS,WAA+CJ,SAASnB,MAAM,CAClEC;IAGF,OAAOsB;AACT"}