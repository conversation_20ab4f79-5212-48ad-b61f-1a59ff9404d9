import { ValidationOptions } from '../ValidationOptions';
export declare const IS_BOOLEAN_STRING = "isBooleanString";
/**
 * Checks if a string is a boolean.
 * If given value is not a string, then it returns false.
 */
export declare function isBooleanString(value: unknown): boolean;
/**
 * Checks if a string is a boolean.
 * If given value is not a string, then it returns false.
 */
export declare function IsBooleanString(validationOptions?: ValidationOptions): PropertyDecorator;
