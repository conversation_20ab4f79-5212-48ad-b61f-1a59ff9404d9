{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/fast-refresh-reducer.ts"], "names": ["fetchServerResponse", "createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "handleMutable", "applyFlightData", "createEmptyCacheNode", "handleSegmentMismatch", "hasInterceptionRouteInCurrentTree", "fastRefreshReducerImpl", "state", "action", "origin", "mutable", "href", "canonicalUrl", "preserveCustomHistoryState", "cache", "includeNextUrl", "tree", "lazyData", "URL", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "pushRef", "pendingPush", "currentTree", "currentCache", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "canonicalUrlOverrideHref", "undefined", "applied", "patchedTree", "fastRefreshReducerNoop", "_action", "fastRefreshReducer", "process", "env", "NODE_ENV"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,eAAe,QAAQ,uBAAsB;AAEtD,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,qBAAqB,QAAQ,6BAA4B;AAClE,SAASC,iCAAiC,QAAQ,2CAA0C;AAE5F,wFAAwF;AACxF,SAASC,uBACPC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/BF,QAAQG,0BAA0B,GAAG;IAErC,MAAMC,QAAmBX;IACzB,sFAAsF;IACtF,sHAAsH;IACtH,MAAMY,iBAAiBV,kCAAkCE,MAAMS,IAAI;IAEnE,uDAAuD;IACvD,wCAAwC;IACxCF,MAAMG,QAAQ,GAAGrB,oBACf,IAAIsB,IAAIP,MAAMF,SACd;QAACF,MAAMS,IAAI,CAAC,EAAE;QAAET,MAAMS,IAAI,CAAC,EAAE;QAAET,MAAMS,IAAI,CAAC,EAAE;QAAE;KAAU,EACxDD,iBAAiBR,MAAMY,OAAO,GAAG,MACjCZ,MAAMa,OAAO;IAGf,OAAON,MAAMG,QAAQ,CAACI,IAAI,CACxB;YAAC,CAACC,YAAYC,qBAAqB;QACjC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOtB,kBACLO,OACAG,SACAY,YACAf,MAAMiB,OAAO,CAACC,WAAW;QAE7B;QAEA,+DAA+D;QAC/DX,MAAMG,QAAQ,GAAG;QAEjB,IAAIS,cAAcnB,MAAMS,IAAI;QAC5B,IAAIW,eAAepB,MAAMO,KAAK;QAE9B,KAAK,MAAMc,kBAAkBN,WAAY;YACvC,oFAAoF;YACpF,IAAIM,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOxB;YACT;YAEA,mGAAmG;YACnG,MAAM,CAACyB,UAAU,GAAGJ;YACpB,MAAMK,UAAUnC,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJ4B,aACAM,WACAzB,MAAMK,YAAY;YAGpB,IAAIqB,YAAY,MAAM;gBACpB,OAAO7B,sBAAsBG,OAAOC,QAAQwB;YAC9C;YAEA,IAAIjC,4BAA4B2B,aAAaO,UAAU;gBACrD,OAAOjC,kBACLO,OACAG,SACAC,MACAJ,MAAMiB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMS,2BAA2BX,uBAC7B1B,kBAAkB0B,wBAClBY;YAEJ,IAAIZ,sBAAsB;gBACxBb,QAAQE,YAAY,GAAGsB;YACzB;YACA,MAAME,UAAUlC,gBAAgByB,cAAcb,OAAOc;YAErD,IAAIQ,SAAS;gBACX1B,QAAQI,KAAK,GAAGA;gBAChBa,eAAeb;YACjB;YAEAJ,QAAQ2B,WAAW,GAAGJ;YACtBvB,QAAQE,YAAY,GAAGD;YAEvBe,cAAcO;QAChB;QACA,OAAOhC,cAAcM,OAAOG;IAC9B,GACA,IAAMH;AAEV;AAEA,SAAS+B,uBACP/B,KAA2B,EAC3BgC,OAA0B;IAE1B,OAAOhC;AACT;AAEA,OAAO,MAAMiC,qBACXC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrBL,yBACAhC,uBAAsB"}