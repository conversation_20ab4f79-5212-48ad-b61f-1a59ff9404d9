{"version": 3, "sources": ["../../../../src/build/webpack/loaders/utils.ts"], "names": ["createHash", "RSC_MODULE_TYPES", "imageExtensions", "imageRegex", "RegExp", "join", "isActionServerLayerEntryModule", "mod", "rscInfo", "buildInfo", "rsc", "actions", "type", "server", "isActionClientLayerModule", "client", "isClientComponentEntryModule", "hasClientDirective", "isClientRef", "isActionLayerEntry", "test", "resource", "regexCSS", "isCSSMod", "loaders", "some", "loader", "includes", "getActionsFromBuildInfo", "generateActionId", "filePath", "exportName", "update", "digest", "encodeToBase64", "obj", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "decodeFromBase64", "str", "parse", "getLoaderModuleNamedExports", "resourcePath", "context", "Promise", "res", "rej", "loadModule", "err", "_source", "_sourceMap", "module", "exportNames", "dependencies", "filter", "dep", "constructor", "name", "map"], "mappings": "AACA,SAASA,UAAU,QAAQ,SAAQ;AACnC,SAASC,gBAAgB,QAAQ,gCAA+B;AAEhE,MAAMC,kBAAkB;IAAC;IAAO;IAAQ;IAAO;IAAQ;IAAQ;IAAO;CAAM;AAC5E,MAAMC,aAAa,IAAIC,OAAO,CAAC,IAAI,EAAEF,gBAAgBG,IAAI,CAAC,KAAK,EAAE,CAAC;AAElE,0FAA0F;AAC1F,OAAO,SAASC,+BAA+BC,GAG9C;IACC,MAAMC,UAAUD,IAAIE,SAAS,CAACC,GAAG;IACjC,OAAO,CAAC,CAAEF,CAAAA,CAAAA,2BAAAA,QAASG,OAAO,KAAIH,CAAAA,2BAAAA,QAASI,IAAI,MAAKX,iBAAiBY,MAAM,AAAD;AACxE;AAEA,sGAAsG;AACtG,SAASC,0BAA0BP,GAA0C;IAC3E,MAAMC,UAAUD,IAAIE,SAAS,CAACC,GAAG;IACjC,OAAO,CAAC,CAAEF,CAAAA,CAAAA,2BAAAA,QAASG,OAAO,KAAIH,CAAAA,2BAAAA,QAASI,IAAI,MAAKX,iBAAiBc,MAAM,AAAD;AACxE;AAEA,OAAO,SAASC,6BAA6BT,GAG5C;IACC,MAAMC,UAAUD,IAAIE,SAAS,CAACC,GAAG;IACjC,MAAMO,qBAAqBT,2BAAAA,QAASU,WAAW;IAC/C,MAAMC,qBAAqBL,0BAA0BP;IACrD,OACEU,sBAAsBE,sBAAsBhB,WAAWiB,IAAI,CAACb,IAAIc,QAAQ;AAE5E;AAEA,OAAO,MAAMC,WAAW,4BAA2B;AAEnD,6EAA6E;AAC7E,gDAAgD;AAChD,OAAO,SAASC,SAAShB,GAIxB;QAIGA;IAHF,OAAO,CAAC,CACNA,CAAAA,IAAIK,IAAI,KAAK,sBACZL,IAAIc,QAAQ,IAAIC,SAASF,IAAI,CAACb,IAAIc,QAAQ,OAC3Cd,eAAAA,IAAIiB,OAAO,qBAAXjB,aAAakB,IAAI,CACf,CAAC,EAAEC,MAAM,EAAE,GACTA,OAAOC,QAAQ,CAAC,iCAChBD,OAAOC,QAAQ,CAAC,wCAChBD,OAAOC,QAAQ,CAAC,4CACpB;AAEJ;AAEA,OAAO,SAASC,wBAAwBrB,GAGvC;QACQA,oBAAAA;IAAP,QAAOA,iBAAAA,IAAIE,SAAS,sBAAbF,qBAAAA,eAAeG,GAAG,qBAAlBH,mBAAoBI,OAAO;AACpC;AAEA,OAAO,SAASkB,iBAAiBC,QAAgB,EAAEC,UAAkB;IACnE,OAAO/B,WAAW,QACfgC,MAAM,CAACF,WAAW,MAAMC,YACxBE,MAAM,CAAC;AACZ;AAEA,OAAO,SAASC,eAA6BC,GAAM;IACjD,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,MAAMK,QAAQ,CAAC;AACnD;AAEA,OAAO,SAASC,iBAA+BC,GAAW;IACxD,OAAOJ,KAAKK,KAAK,CAACP,OAAOC,IAAI,CAACK,KAAK,UAAUF,QAAQ,CAAC;AACxD;AAEA,OAAO,eAAeI,4BACpBC,YAAoB,EACpBC,OAAmC;QAejCvC;IAbF,MAAMA,MAAM,MAAM,IAAIwC,QAA8B,CAACC,KAAKC;QACxDH,QAAQI,UAAU,CAChBL,cACA,CAACM,KAAmBC,SAAcC,YAAiBC;YACjD,IAAIH,KAAK;gBACP,OAAOF,IAAIE;YACb;YACAH,IAAIM;QACN;IAEJ;IAEA,MAAMC,cACJhD,EAAAA,oBAAAA,IAAIiD,YAAY,qBAAhBjD,kBACIkD,MAAM,CAAC,CAACC;QACR,OACE;YACE;YACA;SACD,CAAC/B,QAAQ,CAAC+B,IAAIC,WAAW,CAACC,IAAI,KAC/B,UAAUF,OACVA,IAAIE,IAAI,KAAK;IAEjB,GACCC,GAAG,CAAC,CAACH;QACJ,OAAOA,IAAIE,IAAI;IACjB,OAAM,EAAE;IACZ,OAAOL;AACT"}