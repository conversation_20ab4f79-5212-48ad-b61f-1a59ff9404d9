{"version": 3, "file": "IsJSON.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsJSON.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,eAAe,MAAM,sBAAsB,CAAC;AAEnD,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,iBAAqC;IAC1D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,MAAM,CAAC,KAAK,CAAC,EAAb,CAAa;YACjD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,iCAAiC,EAA9C,CAA8C,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isJSONValidator from 'validator/lib/isJSON';\n\nexport const IS_JSON = 'isJson';\n\n/**\n * Checks if the string is valid JSON (note: uses JSON.parse).\n * If given value is not a string, then it returns false.\n */\nexport function isJSON(value: unknown): boolean {\n  return typeof value === 'string' && isJSONValidator(value);\n}\n\n/**\n * Checks if the string is valid JSON (note: uses JSON.parse).\n * If given value is not a string, then it returns false.\n */\nexport function IsJSON(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_JSON,\n      validator: {\n        validate: (value, args): boolean => isJSON(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a json string', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}