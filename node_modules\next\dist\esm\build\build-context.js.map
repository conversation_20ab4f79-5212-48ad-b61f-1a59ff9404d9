{"version": 3, "sources": ["../../src/build/build-context.ts"], "names": ["pluginState", "resumePluginState", "resumedState", "Object", "assign", "getProxiedPluginState", "initialState", "Proxy", "get", "target", "key", "set", "value", "getPluginState", "NextBuildContext"], "mappings": "AASA,4EAA4E;AAC5E,4EAA4E;AAC5E,+DAA+D;AAC/D,+CAA+C;AAC/C,IAAIA,cAAmC,CAAC;AACxC,OAAO,SAASC,kBAAkBC,YAAkC;IAClEC,OAAOC,MAAM,CAACJ,aAAaE;AAC7B;AAEA,6EAA6E;AAC7E,+EAA+E;AAC/E,gBAAgB;AAChB,OAAO,SAASG,sBACdC,YAAmB;IAEnB,OAAO,IAAIC,MAAMP,aAAa;QAC5BQ,KAAIC,MAAM,EAAEC,GAAW;YACrB,IAAI,OAAOD,MAAM,CAACC,IAAI,KAAK,aAAa;gBACtC,OAAQD,MAAM,CAACC,IAAI,GAAGJ,YAAY,CAACI,IAAI;YACzC;YACA,OAAOD,MAAM,CAACC,IAAI;QACpB;QACAC,KAAIF,MAAM,EAAEC,GAAW,EAAEE,KAAK;YAC5BH,MAAM,CAACC,IAAI,GAAGE;YACd,OAAO;QACT;IACF;AACF;AAEA,OAAO,SAASC;IACd,OAAOb;AACT;AAMA,yDAAyD;AACzD,gFAAgF;AAChF,yCAAyC;AACzC,yEAAyE;AACzE,OAAO,MAAMc,mBA2CR,CAAC,EAAC"}