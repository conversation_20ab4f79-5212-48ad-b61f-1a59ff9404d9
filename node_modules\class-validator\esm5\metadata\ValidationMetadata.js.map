{"version": 3, "file": "ValidationMetadata.js", "sourceRoot": "", "sources": ["../../../src/metadata/ValidationMetadata.ts"], "names": [], "mappings": "AAGA;;GAEG;AACH;IAiEE,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,4BAAY,IAA4B;QA7BxC;;WAEG;QACH,WAAM,GAAa,EAAE,CAAC;QAOtB;;WAEG;QACH,SAAI,GAAY,KAAK,CAAC;QAEtB;;WAEG;QACH,YAAO,GAAS,SAAS,CAAC;QAYxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACxC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACxD,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;YACxC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;QAChD,CAAC;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AArFD,IAqFC", "sourcesContent": ["import { ValidationMetadataArgs } from './ValidationMetadataArgs';\nimport { ValidationArguments } from '../validation/ValidationArguments';\n\n/**\n * This metadata contains validation rules.\n */\nexport class ValidationMetadata {\n  // -------------------------------------------------------------------------\n  // Properties\n  // -------------------------------------------------------------------------\n\n  /**\n   * Validation type.\n   */\n  type: string;\n\n  /**\n   * Validator name.\n   */\n  name?: string;\n\n  /**\n   * Target class to which this validation is applied.\n   */\n  target: Function | string;\n\n  /**\n   * Property of the object to be validated.\n   */\n  propertyName: string;\n\n  /**\n   * Constraint class that performs validation. Used only for custom validations.\n   */\n  constraintCls: Function;\n\n  /**\n   * Array of constraints of this validation.\n   */\n  constraints: any[];\n\n  /**\n   * Validation message to be shown in the case of error.\n   */\n  message: string | ((args: ValidationArguments) => string);\n\n  /**\n   * Validation groups used for this validation.\n   */\n  groups: string[] = [];\n\n  /**\n   * Indicates if validation must be performed always, no matter of validation groups used.\n   */\n  always?: boolean;\n\n  /**\n   * Specifies if validated value is an array and each of its item must be validated.\n   */\n  each: boolean = false;\n\n  /*\n   * A transient set of data passed through to the validation result for response mapping\n   */\n  context?: any = undefined;\n\n  /**\n   * Extra options specific to validation type.\n   */\n  validationTypeOptions: any;\n\n  // -------------------------------------------------------------------------\n  // Constructor\n  // -------------------------------------------------------------------------\n\n  constructor(args: ValidationMetadataArgs) {\n    this.type = args.type;\n    this.name = args.name;\n    this.target = args.target;\n    this.propertyName = args.propertyName;\n    this.constraints = args?.constraints;\n    this.constraintCls = args.constraintCls;\n    this.validationTypeOptions = args.validationTypeOptions;\n    if (args.validationOptions) {\n      this.message = args.validationOptions.message;\n      this.groups = args.validationOptions.groups;\n      this.always = args.validationOptions.always;\n      this.each = args.validationOptions.each;\n      this.context = args.validationOptions.context;\n    }\n  }\n}\n"]}