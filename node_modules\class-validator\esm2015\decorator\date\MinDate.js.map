{"version": 3, "file": "MinDate.js", "sourceRoot": "", "sources": ["../../../../src/decorator/date/MinDate.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,QAAQ,GAAG,SAAS,CAAC;AAElC;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,IAAa,EAAE,OAA4B;IACjE,OAAO,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,YAAY,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;AAC7G,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,IAAyB,EAAE,iBAAqC;IACtF,OAAO,UAAU,CACf;QACE,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,CAAC,IAAI,CAAC;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACxE,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,2BAA2B,GAAG,UAAU,GAAG,2BAA2B,EACpF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const MIN_DATE = 'minDate';\n\n/**\n * Checks if the value is a date that's after the specified date.\n */\nexport function minDate(date: unknown, minDate: Date | (() => Date)): boolean {\n  return date instanceof Date && date.getTime() >= (minDate instanceof Date ? minDate : minDate()).getTime();\n}\n\n/**\n * Checks if the value is a date that's after the specified date.\n */\nexport function MinDate(date: Date | (() => Date), validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: MIN_DATE,\n      constraints: [date],\n      validator: {\n        validate: (value, args): boolean => minDate(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => 'minimal allowed date for ' + eachPrefix + '$property is $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}