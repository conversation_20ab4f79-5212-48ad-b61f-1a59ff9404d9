{"version": 3, "file": "searchNumbers.test.js", "names": ["_searchNumbers", "_interopRequireDefault", "require", "_metadataMin", "e", "__esModule", "_createForOfIteratorHelperLoose", "r", "t", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "o", "done", "value", "TypeError", "a", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "n", "describe", "it", "expectedNumbers", "country", "phone", "startsAt", "endsAt", "_iterator", "searchNumbers", "metadata", "_step", "number", "expect", "to", "deep", "equal", "shift"], "sources": ["../../source/legacy/searchNumbers.test.js"], "sourcesContent": ["import searchNumbers from './searchNumbers.js'\r\nimport metadata from '../../metadata.min.json' with { type: 'json' }\r\n\r\ndescribe('searchNumbers', () => {\r\n\tit('should iterate', () => {\r\n\t\tconst expectedNumbers =[{\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\t// number   : '+7 (800) 555-35-35',\r\n\t\t\tstartsAt : 14,\r\n\t\t\tendsAt   : 32\r\n\t\t}, {\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\t// number   : '(*************',\r\n\t\t\tstartsAt : 41,\r\n\t\t\tendsAt   : 55\r\n\t\t}]\r\n\r\n\t\tfor (const number of searchNumbers('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\texpect(number).to.deep.equal(expectedNumbers.shift())\r\n\t\t}\r\n\r\n\t\texpect(expectedNumbers.length).to.equal(0)\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAoE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,gCAAAC,CAAA,EAAAH,CAAA,QAAAI,CAAA,yBAAAC,MAAA,IAAAF,CAAA,CAAAE,MAAA,CAAAC,QAAA,KAAAH,CAAA,oBAAAC,CAAA,UAAAA,CAAA,GAAAA,CAAA,CAAAG,IAAA,CAAAJ,CAAA,GAAAK,IAAA,CAAAC,IAAA,CAAAL,CAAA,OAAAM,KAAA,CAAAC,OAAA,CAAAR,CAAA,MAAAC,CAAA,GAAAQ,2BAAA,CAAAT,CAAA,MAAAH,CAAA,IAAAG,CAAA,uBAAAA,CAAA,CAAAU,MAAA,IAAAT,CAAA,KAAAD,CAAA,GAAAC,CAAA,OAAAU,CAAA,kCAAAA,CAAA,IAAAX,CAAA,CAAAU,MAAA,KAAAE,IAAA,WAAAA,IAAA,MAAAC,KAAA,EAAAb,CAAA,CAAAW,CAAA,sBAAAG,SAAA;AAAA,SAAAL,4BAAAT,CAAA,EAAAe,CAAA,QAAAf,CAAA,2BAAAA,CAAA,SAAAgB,iBAAA,CAAAhB,CAAA,EAAAe,CAAA,OAAAd,CAAA,MAAAgB,QAAA,CAAAb,IAAA,CAAAJ,CAAA,EAAAkB,KAAA,6BAAAjB,CAAA,IAAAD,CAAA,CAAAmB,WAAA,KAAAlB,CAAA,GAAAD,CAAA,CAAAmB,WAAA,CAAAC,IAAA,aAAAnB,CAAA,cAAAA,CAAA,GAAAM,KAAA,CAAAc,IAAA,CAAArB,CAAA,oBAAAC,CAAA,+CAAAqB,IAAA,CAAArB,CAAA,IAAAe,iBAAA,CAAAhB,CAAA,EAAAe,CAAA;AAAA,SAAAC,kBAAAhB,CAAA,EAAAe,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAf,CAAA,CAAAU,MAAA,MAAAK,CAAA,GAAAf,CAAA,CAAAU,MAAA,YAAAb,CAAA,MAAA0B,CAAA,GAAAhB,KAAA,CAAAQ,CAAA,GAAAlB,CAAA,GAAAkB,CAAA,EAAAlB,CAAA,IAAA0B,CAAA,CAAA1B,CAAA,IAAAG,CAAA,CAAAH,CAAA,UAAA0B,CAAA;AAEpEC,QAAQ,CAAC,eAAe,EAAE,YAAM;EAC/BC,EAAE,CAAC,gBAAgB,EAAE,YAAM;IAC1B,IAAMC,eAAe,GAAE,CAAC;MACvBC,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtB;MACAC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,EAAE;MACFH,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtB;MACAC,QAAQ,EAAG,EAAE;MACbC,MAAM,EAAK;IACZ,CAAC,CAAC;IAEF,SAAAC,SAAA,GAAAhC,+BAAA,CAAqB,IAAAiC,yBAAa,EAAC,qFAAqF,EAAE,IAAI,EAAEC,uBAAQ,CAAC,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAnB,IAAA,GAAE;MAAA,IAAhIuB,MAAM,GAAAD,KAAA,CAAArB,KAAA;MAChBuB,MAAM,CAACD,MAAM,CAAC,CAACE,EAAE,CAACC,IAAI,CAACC,KAAK,CAACb,eAAe,CAACc,KAAK,CAAC,CAAC,CAAC;IACtD;IAEAJ,MAAM,CAACV,eAAe,CAAChB,MAAM,CAAC,CAAC2B,EAAE,CAACE,KAAK,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}