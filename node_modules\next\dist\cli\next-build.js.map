{"version": 3, "sources": ["../../src/cli/next-build.ts"], "names": ["nextBuild", "options", "directory", "process", "on", "exit", "debug", "experimentalDebugMemoryUsage", "profile", "lint", "mangling", "experimentalAppOnly", "experimentalTurbo", "experimentalBuildMode", "warn", "italic", "env", "EXPERIMENTAL_DEBUG_MEMORY_USAGE", "enableMemoryDebuggingMode", "dir", "getProjectDir", "existsSync", "printAndExit", "TURBOPACK", "build", "Boolean", "NEXT_DEBUG_BUILD", "catch", "err", "disableMemoryDebuggingMode", "console", "error", "isError", "code", "message", "finally"], "mappings": ";;;;;+BA4<PERSON><PERSON>;;;eAAAA;;;QA1GF;oBACoB;4BACJ;8DACL;qBACG;uBACQ;gEACT;+BACU;yBACY;0BACC;;;;;;AAa3C,MAAMA,YAAY,CAACC,SAA2BC;IAC5CC,QAAQC,EAAE,CAAC,WAAW,IAAMD,QAAQE,IAAI,CAAC;IACzCF,QAAQC,EAAE,CAAC,UAAU,IAAMD,QAAQE,IAAI,CAAC;IAExC,MAAM,EACJC,KAAK,EACLC,4BAA4B,EAC5BC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,mBAAmB,EACnBC,iBAAiB,EACjBC,qBAAqB,EACtB,GAAGZ;IAEJ,IAAI,CAACQ,MAAM;QACTK,IAAAA,SAAI,EAAC;IACP;IAEA,IAAI,CAACJ,UAAU;QACbI,IAAAA,SAAI,EACF;IAEJ;IAEA,IAAIN,SAAS;QACXM,IAAAA,SAAI,EACF,CAAC,sBAAsB,EAAEC,IAAAA,kBAAM,EAAC,sCAAsC,CAAC;IAE3E;IAEA,IAAIR,8BAA8B;QAChCJ,QAAQa,GAAG,CAACC,+BAA+B,GAAG;QAC9CC,IAAAA,kCAAyB;IAC3B;IAEA,MAAMC,MAAMC,IAAAA,4BAAa,EAAClB;IAE1B,yCAAyC;IACzC,IAAI,CAACmB,IAAAA,cAAU,EAACF,MAAM;QACpBG,IAAAA,mBAAY,EAAC,CAAC,gDAAgD,EAAEH,IAAI,CAAC;IACvE;IAEA,IAAIP,mBAAmB;QACrBT,QAAQa,GAAG,CAACO,SAAS,GAAG;IAC1B;IAEA,OAAOC,IAAAA,cAAK,EACVL,KACAX,SACAF,SAASmB,QAAQtB,QAAQa,GAAG,CAACU,gBAAgB,GAC7CjB,MACA,CAACC,UACDC,qBACA,CAAC,CAACR,QAAQa,GAAG,CAACO,SAAS,EACvBV,uBAECc,KAAK,CAAC,CAACC;QACN,IAAIrB,8BAA8B;YAChCsB,IAAAA,oCAA0B;QAC5B;QACAC,QAAQC,KAAK,CAAC;QACd,IACEC,IAAAA,gBAAO,EAACJ,QACPA,CAAAA,IAAIK,IAAI,KAAK,2BACZL,IAAIK,IAAI,KAAK,oBACbL,IAAIK,IAAI,KAAK,+BACbL,IAAIK,IAAI,KAAK,uBACbL,IAAIK,IAAI,KAAK,6BACbL,IAAIK,IAAI,KAAK,8BAA6B,GAC5C;YACAX,IAAAA,mBAAY,EAAC,CAAC,EAAE,EAAEM,IAAIM,OAAO,CAAC,CAAC;QACjC,OAAO;YACLJ,QAAQC,KAAK,CAAC;YACdT,IAAAA,mBAAY,EAACM;QACf;IACF,GACCO,OAAO,CAAC;QACP,IAAI5B,8BAA8B;YAChCsB,IAAAA,oCAA0B;QAC5B;IACF;AACJ"}