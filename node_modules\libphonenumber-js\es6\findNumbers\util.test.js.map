{"version": 3, "file": "util.test.js", "names": ["limit", "trimAfterFirstMatch", "startsWith", "endsWith", "describe", "it", "thrower", "expect", "to", "equal"], "sources": ["../../source/findNumbers/util.test.js"], "sourcesContent": ["import {\r\n\tlimit,\r\n\ttrimAfterFirstMatch,\r\n\tstartsWith,\r\n\tendsWith\r\n} from './util.js'\r\n\r\ndescribe('findNumbers/util', () => {\r\n\tit('should generate regexp limit', () => {\r\n\t\tlet thrower = () => limit(1, 0)\r\n\t\texpect(thrower).to.throw()\r\n\r\n\t\tthrower = () => limit(-1, 1)\r\n\t\texpect(thrower).to.throw()\r\n\r\n\t\tthrower = () => limit(0, 0)\r\n\t\texpect(thrower).to.throw()\r\n\t})\r\n\r\n\tit('should trimAfterFirstMatch', () => {\r\n\t\texpect(trimAfterFirstMatch(/\\d/, 'abc123')).to.equal('abc')\r\n\t\texpect(trimAfterFirstMatch(/\\d/, 'abc')).to.equal('abc')\r\n\t})\r\n\r\n\tit('should determine if a string starts with a substring', () => {\r\n\t\texpect(startsWith('𐍈123', '𐍈')).to.equal(true)\r\n\t\texpect(startsWith('1𐍈', '𐍈')).to.equal(false)\r\n\t})\r\n\r\n\tit('should determine if a string ends with a substring', () => {\r\n\t\texpect(endsWith('123𐍈', '𐍈')).to.equal(true)\r\n\t\texpect(endsWith('𐍈1', '𐍈')).to.equal(false)\r\n\t})\r\n})"], "mappings": "AAAA,SACCA,KAAK,EACLC,mBAAmB,EACnBC,UAAU,EACVC,QAAQ,QACF,WAAW;AAElBC,QAAQ,CAAC,kBAAkB,EAAE,YAAM;EAClCC,EAAE,CAAC,8BAA8B,EAAE,YAAM;IACxC,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAAA;IAC/BO,MAAM,CAACD,OAAO,CAAC,CAACE,EAAE,SAAM,CAAC,CAAC;IAE1BF,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASN,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAAA;IAC5BO,MAAM,CAACD,OAAO,CAAC,CAACE,EAAE,SAAM,CAAC,CAAC;IAE1BF,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASN,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAAA;IAC3BO,MAAM,CAACD,OAAO,CAAC,CAACE,EAAE,SAAM,CAAC,CAAC;EAC3B,CAAC,CAAC;EAEFH,EAAE,CAAC,4BAA4B,EAAE,YAAM;IACtCE,MAAM,CAACN,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAACO,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC3DF,MAAM,CAACN,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAACO,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EACzD,CAAC,CAAC;EAEFJ,EAAE,CAAC,sDAAsD,EAAE,YAAM;IAChEE,MAAM,CAACL,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAChDF,MAAM,CAACL,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAChD,CAAC,CAAC;EAEFJ,EAAE,CAAC,oDAAoD,EAAE,YAAM;IAC9DE,MAAM,CAACJ,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAACK,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC9CF,MAAM,CAACJ,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAACK,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC9C,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}