{"version": 3, "file": "ajax.js", "sourceRoot": "", "sources": ["../../../../src/internal/ajax/ajax.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACvC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,UAAU,CAAC;AAqIvD,SAAS,OAAO,CAAI,GAAW,EAAE,OAAgC;IAC/D,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,QAAQ,CAAI,GAAW,EAAE,IAAU,EAAE,OAAgC;IAC5E,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,UAAU,CAAI,GAAW,EAAE,OAAgC;IAClE,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,OAAO,CAAI,GAAW,EAAE,IAAU,EAAE,OAAgC;IAC3E,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,SAAS,CAAI,GAAW,EAAE,IAAU,EAAE,OAAgC;IAC7E,OAAO,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,KAAA,EAAE,IAAI,MAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;AACvD,CAAC;AAED,IAAM,WAAW,GAAG,GAAG,CAAC,UAAC,CAAoB,IAAK,OAAA,CAAC,CAAC,QAAQ,EAAV,CAAU,CAAC,CAAC;AAE9D,SAAS,WAAW,CAAI,GAAW,EAAE,OAAgC;IACnE,OAAO,WAAW,CAChB,IAAI,CAAI;QACN,MAAM,EAAE,KAAK;QACb,GAAG,KAAA;QACH,OAAO,SAAA;KACR,CAAC,CACH,CAAC;AACJ,CAAC;AAoGD,MAAM,CAAC,IAAM,IAAI,GAAuB,CAAC;IACvC,IAAM,MAAM,GAAG,UAAI,WAAgC;QACjD,IAAM,MAAM,GACV,OAAO,WAAW,KAAK,QAAQ;YAC7B,CAAC,CAAC;gBACE,GAAG,EAAE,WAAW;aACjB;YACH,CAAC,CAAC,WAAW,CAAC;QAClB,OAAO,QAAQ,CAAI,MAAM,CAAC,CAAC;IAC7B,CAAC,CAAC;IAEF,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC;IACrB,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC;IACvB,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;IAC3B,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC;IACrB,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;IACzB,MAAM,CAAC,OAAO,GAAG,WAAW,CAAC;IAE7B,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC,EAAE,CAAC;AAEL,IAAM,MAAM,GAAG,QAAQ,CAAC;AACxB,IAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,IAAM,SAAS,GAAG,WAAW,CAAC;AAC9B,IAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,IAAM,IAAI,GAAG,MAAM,CAAC;AAEpB,MAAM,UAAU,QAAQ,CAAI,IAAgB;IAC1C,OAAO,IAAI,UAAU,CAAC,UAAC,WAAW;;QAChC,IAAM,MAAM,cAEV,KAAK,EAAE,IAAI,EACX,WAAW,EAAE,KAAK,EAClB,eAAe,EAAE,KAAK,EACtB,MAAM,EAAE,KAAK,EACb,OAAO,EAAE,CAAC,EACV,YAAY,EAAE,MAAoC,IAE/C,IAAI,CACR,CAAC;QAEM,IAAA,WAAW,GAAuD,MAAM,YAA7D,EAAQ,cAAc,GAAiC,MAAM,KAAvC,EAAW,iBAAiB,GAAK,MAAM,QAAX,CAAY;QAEjF,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;QACrB,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,IAAI,SAAS,CAAC,iBAAiB,CAAC,CAAC;SACxC;QAED,IAAI,WAAW,EAAE;YACf,IAAI,cAA6B,CAAC;YAClC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAIrB,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE;oBACpB,MAAM,IAAI,SAAS,CAAC,aAAa,CAAC,CAAC;iBACpC;gBAED,cAAY,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAG7C,IAAI,eAAe,CAAC,WAAkB,CAAC,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG,IAAK,OAAA,cAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,EAA5B,CAA4B,CAAC,CAAC;gBAI9F,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,cAAY,CAAC;aACrC;iBAAM;gBAKL,cAAY,GAAG,IAAI,eAAe,CAAC,WAAkB,CAAC,CAAC;gBACvD,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,cAAY,CAAC;aAChC;SACF;QAKD,IAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,IAAI,iBAAiB,EAAE;YACrB,KAAK,IAAM,GAAG,IAAI,iBAAiB,EAAE;gBACnC,IAAI,iBAAiB,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;oBACzC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;iBACrD;aACF;SACF;QAED,IAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QASvC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,kBAAkB,IAAI,OAAO,CAAC,EAAE;YACpD,OAAO,CAAC,kBAAkB,CAAC,GAAG,gBAAgB,CAAC;SAChD;QAIO,IAAA,eAAe,GAAqC,MAAM,gBAA3C,EAAE,cAAc,GAAqB,MAAM,eAA3B,EAAE,cAAc,GAAK,MAAM,eAAX,CAAY;QACnE,IAAI,CAAC,eAAe,IAAI,CAAC,WAAW,CAAC,IAAI,cAAc,IAAI,cAAc,EAAE;YACzE,IAAM,UAAU,GAAG,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,eAAa,cAAc,cAAW,CAAC,CAAC,0CAAE,GAAG,EAAE,mCAAI,EAAE,CAAC;YAC3G,IAAI,UAAU,EAAE;gBACd,OAAO,CAAC,cAAc,CAAC,GAAG,UAAU,CAAC;aACtC;SACF;QAID,IAAM,IAAI,GAAG,uCAAuC,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;QAG9E,IAAM,QAAQ,yBACT,MAAM,KAGT,GAAG,KAAA;YACH,OAAO,SAAA;YACP,IAAI,MAAA,GACL,CAAC;QAEF,IAAI,GAAmB,CAAC;QAGxB,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;QAE/D;YAQU,IAAA,oBAAkB,GAAqE,IAAI,mBAAzE,EAAE,KAAmE,IAAI,wBAAxC,EAA/B,uBAAuB,mBAAG,KAAK,KAAA,EAAE,KAAkC,IAAI,sBAAT,EAA7B,qBAAqB,mBAAG,KAAK,KAAA,CAAU;YAQpG,IAAM,aAAa,GAAG,UAAC,IAAY,EAAE,YAAuB;gBAC1D,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE;;oBACzB,IAAM,KAAK,GAAG,YAAY,EAAE,CAAC;oBAC7B,MAAA,oBAAkB,aAAlB,oBAAkB,uBAAlB,oBAAkB,CAAE,KAAK,+CAAzB,oBAAkB,EAAU,KAAK,CAAC,CAAC;oBACnC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC3B,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAGF,aAAa,CAAC,SAAS,EAAE,cAAM,OAAA,IAAI,gBAAgB,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAnC,CAAmC,CAAC,CAAC;YAIpE,aAAa,CAAC,OAAO,EAAE,cAAM,OAAA,IAAI,SAAS,CAAC,SAAS,EAAE,GAAG,EAAE,QAAQ,CAAC,EAAvC,CAAuC,CAAC,CAAC;YAStE,IAAM,gBAAc,GAAG,UAAC,SAAwB,EAAE,KAAoB;gBACpE,OAAA,IAAI,YAAY,CAAI,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAK,SAAS,SAAI,KAAK,CAAC,IAAoC,CAAC;YAArG,CAAqG,CAAC;YAYxG,IAAM,kBAAgB,GAAG,UAAC,MAAW,EAAE,IAAY,EAAE,SAAwB;gBAC3E,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAC,KAAoB;oBACjD,WAAW,CAAC,IAAI,CAAC,gBAAc,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;gBACrD,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,IAAI,qBAAqB,EAAE;gBACzB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI,IAAK,OAAA,kBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,EAA1C,CAA0C,CAAC,CAAC;aAC3F;YAED,IAAI,oBAAkB,EAAE;gBACtB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI,IAAK,OAAA,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAC,CAAM,YAAK,OAAA,MAAA,oBAAkB,aAAlB,oBAAkB,uBAAlB,oBAAkB,CAAE,IAAI,+CAAxB,oBAAkB,EAAS,CAAC,CAAC,CAAA,EAAA,CAAC,EAA5E,CAA4E,CAAC,CAAC;aACvH;YAED,IAAI,uBAAuB,EAAE;gBAC3B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI,IAAK,OAAA,kBAAgB,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,EAArC,CAAqC,CAAC,CAAC;aAChF;YAED,IAAM,WAAS,GAAG,UAAC,MAAe;gBAChC,IAAM,GAAG,GAAG,YAAY,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACxD,WAAW,CAAC,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC;YAEF,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAC,CAAC;;gBAC9B,MAAA,oBAAkB,aAAlB,oBAAkB,uBAAlB,oBAAkB,CAAE,KAAK,+CAAzB,oBAAkB,EAAU,CAAC,CAAC,CAAC;gBAC/B,WAAS,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAC,KAAK;;gBACvB,IAAA,MAAM,GAAK,GAAG,OAAR,CAAS;gBAEvB,IAAI,MAAM,GAAG,GAAG,EAAE;oBAChB,MAAA,oBAAkB,aAAlB,oBAAkB,uBAAlB,oBAAkB,CAAE,QAAQ,+CAA5B,oBAAkB,CAAc,CAAC;oBAEjC,IAAI,QAAQ,SAAiB,CAAC;oBAC9B,IAAI;wBAIF,QAAQ,GAAG,gBAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;qBAC5C;oBAAC,OAAO,GAAG,EAAE;wBACZ,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACvB,OAAO;qBACR;oBAED,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC3B,WAAW,CAAC,QAAQ,EAAE,CAAC;iBACxB;qBAAM;oBACL,MAAA,oBAAkB,aAAlB,oBAAkB,uBAAlB,oBAAkB,CAAE,KAAK,+CAAzB,oBAAkB,EAAU,KAAK,CAAC,CAAC;oBACnC,WAAS,CAAC,MAAM,CAAC,CAAC;iBACnB;YACH,CAAC,CAAC,CAAC;SACJ;QAEO,IAAA,IAAI,GAAoB,QAAQ,KAA5B,EAAE,MAAM,GAAY,QAAQ,OAApB,EAAE,KAAK,GAAK,QAAQ,MAAb,CAAc;QAEzC,IAAI,IAAI,EAAE;YACR,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;SACvD;aAAM;YACL,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;SAC9B;QAGD,IAAI,KAAK,EAAE;YACT,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;YAC/B,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;SAC1C;QAED,IAAI,iBAAiB,IAAI,GAAG,EAAE;YAC5B,GAAG,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;SAChD;QAGD,KAAK,IAAM,GAAG,IAAI,OAAO,EAAE;YACzB,IAAI,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;gBAC/B,GAAG,CAAC,gBAAgB,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;aACzC;SACF;QAGD,IAAI,IAAI,EAAE;YACR,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAChB;aAAM;YACL,GAAG,CAAC,IAAI,EAAE,CAAC;SACZ;QAED,OAAO;YACL,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAe;gBAC5C,GAAG,CAAC,KAAK,EAAE,CAAC;aACb;QACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAWD,SAAS,uCAAuC,CAAC,IAAS,EAAE,OAA+B;;IACzF,IACE,CAAC,IAAI;QACL,OAAO,IAAI,KAAK,QAAQ;QACxB,UAAU,CAAC,IAAI,CAAC;QAChB,iBAAiB,CAAC,IAAI,CAAC;QACvB,aAAa,CAAC,IAAI,CAAC;QACnB,MAAM,CAAC,IAAI,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC;QACZ,gBAAgB,CAAC,IAAI,CAAC,EACtB;QAGA,OAAO,IAAI,CAAC;KACb;IAED,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAG3B,OAAO,IAAI,CAAC,MAAM,CAAC;KACpB;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAM5B,OAAO,CAAC,cAAc,CAAC,GAAG,MAAA,OAAO,CAAC,cAAc,CAAC,mCAAI,gCAAgC,CAAC;QACtF,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;KAC7B;IAID,MAAM,IAAI,SAAS,CAAC,mBAAmB,CAAC,CAAC;AAC3C,CAAC;AAED,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAE5C,SAAS,aAAa,CAAC,GAAQ,EAAE,IAAY;IAC3C,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,aAAW,IAAI,MAAG,CAAC;AACpD,CAAC;AAED,SAAS,aAAa,CAAC,IAAS;IAC9B,OAAO,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS,MAAM,CAAC,IAAS;IACvB,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,MAAM,CAAC,IAAS;IACvB,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAS;IAClC,OAAO,OAAO,WAAW,KAAK,WAAW,IAAI,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxE,CAAC;AAED,SAAS,UAAU,CAAC,IAAS;IAC3B,OAAO,OAAO,QAAQ,KAAK,WAAW,IAAI,IAAI,YAAY,QAAQ,CAAC;AACrE,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAS;IAClC,OAAO,OAAO,eAAe,KAAK,WAAW,IAAI,IAAI,YAAY,eAAe,CAAC;AACnF,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAS;IACjC,OAAO,OAAO,cAAc,KAAK,WAAW,IAAI,IAAI,YAAY,cAAc,CAAC;AACjF,CAAC"}