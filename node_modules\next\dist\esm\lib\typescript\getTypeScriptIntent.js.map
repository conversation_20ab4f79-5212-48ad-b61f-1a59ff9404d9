{"version": 3, "sources": ["../../../src/lib/typescript/getTypeScriptIntent.ts"], "names": ["existsSync", "promises", "fs", "path", "recursiveReadDir", "getTypeScriptIntent", "baseDir", "intentDirs", "tsconfigPath", "resolvedTsConfigPath", "join", "hasTypeScriptConfiguration", "content", "readFile", "encoding", "then", "txt", "trim", "firstTimeSetup", "tsFilesRegex", "excludedRegex", "dir", "typescriptFiles", "pathnameFilter", "name", "test", "ignoreFilter", "length"], "mappings": "AAAA,SAASA,UAAU,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AAC/C,OAAOC,UAAU,OAAM;AACvB,SAASC,gBAAgB,QAAQ,uBAAsB;AAIvD,OAAO,eAAeC,oBACpBC,OAAe,EACfC,UAAoB,EACpBC,YAAoB;IAEpB,MAAMC,uBAAuBN,KAAKO,IAAI,CAACJ,SAASE;IAEhD,sEAAsE;IACtE,WAAW;IACX,MAAMG,6BAA6BX,WAAWS;IAC9C,IAAIE,4BAA4B;QAC9B,MAAMC,UAAU,MAAMV,GACnBW,QAAQ,CAACJ,sBAAsB;YAAEK,UAAU;QAAO,GAClDC,IAAI,CACH,CAACC,MAAQA,IAAIC,IAAI,IACjB,IAAM;QAEV,OAAO;YAAEC,gBAAgBN,YAAY,MAAMA,YAAY;QAAK;IAC9D;IAEA,yEAAyE;IACzE,6EAA6E;IAC7E,gDAAgD;IAChD,mEAAmE;IACnE,MAAMO,eAAe;IACrB,MAAMC,gBAAgB;IACtB,KAAK,MAAMC,OAAOd,WAAY;QAC5B,MAAMe,kBAAkB,MAAMlB,iBAAiBiB,KAAK;YAClDE,gBAAgB,CAACC,OAASL,aAAaM,IAAI,CAACD;YAC5CE,cAAc,CAACF,OAASJ,cAAcK,IAAI,CAACD;QAC7C;QACA,IAAIF,gBAAgBK,MAAM,EAAE;YAC1B,OAAO;gBAAET,gBAAgB;YAAK;QAChC;IACF;IAEA,OAAO;AACT"}