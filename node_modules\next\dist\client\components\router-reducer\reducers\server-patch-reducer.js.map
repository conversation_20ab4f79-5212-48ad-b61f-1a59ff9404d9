{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["serverPatchReducer", "state", "action", "serverResponse", "flightData", "overrideCanonicalUrl", "mutable", "preserveCustomHistoryState", "handleExternalUrl", "pushRef", "pendingPush", "currentTree", "tree", "currentCache", "cache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "applyRouterStatePatchToTree", "canonicalUrl", "handleSegmentMismatch", "isNavigatingToNewRootLayout", "canonicalUrlOverrideHref", "createHrefFromUrl", "undefined", "createEmptyCacheNode", "applyFlightData", "patchedTree", "handleMutable"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;mCAhBkB;6CACU;6CACA;iCAOV;iCACF;+BACF;2BAEO;uCACC;AAE/B,SAASA,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,cAAc,EAAE,GAAGD;IAC3B,MAAM,CAACE,YAAYC,qBAAqB,GAAGF;IAE3C,MAAMG,UAAmB,CAAC;IAE1BA,QAAQC,0BAA0B,GAAG;IAErC,4DAA4D;IAC5D,IAAI,OAAOH,eAAe,UAAU;QAClC,OAAOI,IAAAA,kCAAiB,EACtBP,OACAK,SACAF,YACAH,MAAMQ,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAcV,MAAMW,IAAI;IAC5B,IAAIC,eAAeZ,MAAMa,KAAK;IAE9B,KAAK,MAAMC,kBAAkBX,WAAY;QACvC,mFAAmF;QACnF,MAAMY,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;QACtB;YAAC;eAAOJ;SAAkB,EAC1BL,aACAO,WACAjB,MAAMoB,YAAY;QAGpB,IAAIF,YAAY,MAAM;YACpB,OAAOG,IAAAA,4CAAqB,EAACrB,OAAOC,QAAQgB;QAC9C;QAEA,IAAIK,IAAAA,wDAA2B,EAACZ,aAAaQ,UAAU;YACrD,OAAOX,IAAAA,kCAAiB,EACtBP,OACAK,SACAL,MAAMoB,YAAY,EAClBpB,MAAMQ,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMc,2BAA2BnB,uBAC7BoB,IAAAA,oCAAiB,EAACpB,wBAClBqB;QAEJ,IAAIF,0BAA0B;YAC5BlB,QAAQe,YAAY,GAAGG;QACzB;QAEA,MAAMV,QAAmBa,IAAAA,+BAAoB;QAC7CC,IAAAA,gCAAe,EAACf,cAAcC,OAAOC;QAErCT,QAAQuB,WAAW,GAAGV;QACtBb,QAAQQ,KAAK,GAAGA;QAEhBD,eAAeC;QACfH,cAAcQ;IAChB;IAEA,OAAOW,IAAAA,4BAAa,EAAC7B,OAAOK;AAC9B"}