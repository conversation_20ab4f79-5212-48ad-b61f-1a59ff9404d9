{"version": 3, "file": "IsNumber.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsNumber.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,SAAS,GAAG,UAAU,CAAC;AAWpC;;GAEG;AACH,SAAgB,QAAQ,CAAC,KAAc,EAAE,UAA2B,EAAE;IACpE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC9C,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;IACjC,CAAC;IAED,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;QACxB,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC5B,CAAC;IAED,IAAI,OAAO,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;QAC3C,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACpB,aAAa,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACxD,CAAC;QACD,IAAI,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAxBD,4BAwBC;AAED;;GAEG;AACH,SAAgB,QAAQ,CAAC,UAA2B,EAAE,EAAE,iBAAqC;IAC3F,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,iBAAS;QACf,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACzE,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,oEAAoE,EAC/F,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,4BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_NUMBER = 'isNumber';\n\n/**\n * Options to be passed to IsNumber decorator.\n */\nexport interface IsNumberOptions {\n  allowNaN?: boolean;\n  allowInfinity?: boolean;\n  maxDecimalPlaces?: number;\n}\n\n/**\n * Checks if a given value is a number.\n */\nexport function isNumber(value: unknown, options: IsNumberOptions = {}): value is number {\n  if (typeof value !== 'number') {\n    return false;\n  }\n\n  if (value === Infinity || value === -Infinity) {\n    return !!options.allowInfinity;\n  }\n\n  if (Number.isNaN(value)) {\n    return !!options.allowNaN;\n  }\n\n  if (options.maxDecimalPlaces !== undefined) {\n    let decimalPlaces = 0;\n    if (value % 1 !== 0) {\n      decimalPlaces = value.toString().split('.')[1].length;\n    }\n    if (decimalPlaces > options.maxDecimalPlaces) {\n      return false;\n    }\n  }\n\n  return Number.isFinite(value);\n}\n\n/**\n * Checks if a value is a number.\n */\nexport function IsNumber(options: IsNumberOptions = {}, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_NUMBER,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isNumber(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a number conforming to the specified constraints',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}