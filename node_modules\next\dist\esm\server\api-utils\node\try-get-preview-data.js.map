{"version": 3, "sources": ["../../../../src/server/api-utils/node/try-get-preview-data.ts"], "names": ["checkIsOnDemandRevalidate", "clearPreviewData", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "SYMBOL_PREVIEW_DATA", "RequestCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tryGetPreviewData", "req", "res", "options", "multiZoneDraftMode", "cookies", "isOnDemandRevalidate", "headers", "from", "previewModeId", "get", "value", "tokenPreviewData", "data", "Object", "defineProperty", "enumerable", "encryptedPreviewData", "jsonwebtoken", "require", "verify", "previewModeSigningKey", "decryptWithSecret", "decryptedPreviewData", "<PERSON><PERSON><PERSON>", "previewModeEncryptionKey", "JSON", "parse"], "mappings": "AAEA,SAASA,yBAAyB,QAAQ,OAAM;AAKhD,SACEC,gBAAgB,EAChBC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,mBAAmB,QACd,WAAU;AACjB,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,cAAc,QAAQ,4CAA2C;AAE1E,OAAO,SAASC,kBACdC,GAAgD,EAChDC,GAAsC,EACtCC,OAA0B,EAC1BC,kBAA2B;QAiBLC,cACGA;IAhBzB,0DAA0D;IAC1D,cAAc;IACd,IAAIF,WAAWV,0BAA0BQ,KAAKE,SAASG,oBAAoB,EAAE;QAC3E,OAAO;IACT;IAEA,sCAAsC;IACtC,iDAAiD;IACjD,IAAIT,uBAAuBI,KAAK;QAC9B,OAAO,AAACA,GAAW,CAACJ,oBAAoB;IAC1C;IAEA,MAAMU,UAAUR,eAAeS,IAAI,CAACP,IAAIM,OAAO;IAC/C,MAAMF,UAAU,IAAIP,eAAeS;IAEnC,MAAME,iBAAgBJ,eAAAA,QAAQK,GAAG,CAACf,kDAAZU,aAA2CM,KAAK;IACtE,MAAMC,oBAAmBP,gBAAAA,QAAQK,GAAG,CAACd,gDAAZS,cAAyCM,KAAK;IAEvE,2DAA2D;IAC3D,IACEF,iBACA,CAACG,oBACDH,kBAAkBN,QAAQM,aAAa,EACvC;QACA,yCAAyC;QACzC,4CAA4C;QAC5C,4CAA4C;QAC5C,MAAMI,OAAO,CAAC;QACdC,OAAOC,cAAc,CAACd,KAAKJ,qBAAqB;YAC9Cc,OAAOE;YACPG,YAAY;QACd;QACA,OAAOH;IACT;IAEA,+BAA+B;IAC/B,IAAI,CAACJ,iBAAiB,CAACG,kBAAkB;QACvC,OAAO;IACT;IAEA,8CAA8C;IAC9C,IAAI,CAACH,iBAAiB,CAACG,kBAAkB;QACvC,IAAI,CAACR,oBAAoB;YACvBV,iBAAiBQ;QACnB;QACA,OAAO;IACT;IAEA,6CAA6C;IAC7C,IAAIO,kBAAkBN,QAAQM,aAAa,EAAE;QAC3C,IAAI,CAACL,oBAAoB;YACvBV,iBAAiBQ;QACnB;QACA,OAAO;IACT;IAEA,IAAIe;IAGJ,IAAI;QACF,MAAMC,eACJC,QAAQ;QACVF,uBAAuBC,aAAaE,MAAM,CACxCR,kBACAT,QAAQkB,qBAAqB;IAEjC,EAAE,OAAM;QACN,aAAa;QACb3B,iBAAiBQ;QACjB,OAAO;IACT;IAEA,MAAM,EAAEoB,iBAAiB,EAAE,GACzBH,QAAQ;IACV,MAAMI,uBAAuBD,kBAC3BE,OAAOhB,IAAI,CAACL,QAAQsB,wBAAwB,GAC5CR,qBAAqBJ,IAAI;IAG3B,IAAI;QACF,qCAAqC;QACrC,MAAMA,OAAOa,KAAKC,KAAK,CAACJ;QACxB,eAAe;QACfT,OAAOC,cAAc,CAACd,KAAKJ,qBAAqB;YAC9Cc,OAAOE;YACPG,YAAY;QACd;QACA,OAAOH;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF"}