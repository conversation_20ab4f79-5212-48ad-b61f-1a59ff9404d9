{"version": 3, "file": "searchPhoneNumbersInText.js", "names": ["_PhoneNumberMatcher", "_interopRequireDefault", "require", "_normalizeArguments2", "e", "__esModule", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "searchPhoneNumbersInText", "_normalizeArguments", "normalizeArguments", "text", "options", "metadata", "matcher", "PhoneNumberMatcher", "v2", "next", "hasNext", "done"], "sources": ["../source/searchPhoneNumbersInText.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function searchPhoneNumbersInText() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, { ...options, v2: true }, metadata)\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (matcher.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: matcher.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,mBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAwD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,QAAAR,CAAA,EAAAS,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAZ,CAAA,OAAAW,MAAA,CAAAE,qBAAA,QAAAV,CAAA,GAAAQ,MAAA,CAAAE,qBAAA,CAAAb,CAAA,GAAAS,CAAA,KAAAN,CAAA,GAAAA,CAAA,CAAAW,MAAA,WAAAL,CAAA,WAAAE,MAAA,CAAAI,wBAAA,CAAAf,CAAA,EAAAS,CAAA,EAAAO,UAAA,OAAAN,CAAA,CAAAO,IAAA,CAAAC,KAAA,CAAAR,CAAA,EAAAP,CAAA,YAAAO,CAAA;AAAA,SAAAS,cAAAnB,CAAA,aAAAS,CAAA,MAAAA,CAAA,GAAAW,SAAA,CAAAC,MAAA,EAAAZ,CAAA,UAAAC,CAAA,WAAAU,SAAA,CAAAX,CAAA,IAAAW,SAAA,CAAAX,CAAA,QAAAA,CAAA,OAAAD,OAAA,CAAAG,MAAA,CAAAD,CAAA,OAAAY,OAAA,WAAAb,CAAA,IAAAc,eAAA,CAAAvB,CAAA,EAAAS,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAAzB,CAAA,EAAAW,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAF,OAAA,CAAAG,MAAA,CAAAD,CAAA,GAAAY,OAAA,WAAAb,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAA1B,CAAA,EAAAS,CAAA,EAAAE,MAAA,CAAAI,wBAAA,CAAAL,CAAA,EAAAD,CAAA,iBAAAT,CAAA;AAAA,SAAAuB,gBAAAvB,CAAA,EAAAS,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAkB,cAAA,CAAAlB,CAAA,MAAAT,CAAA,GAAAW,MAAA,CAAAe,cAAA,CAAA1B,CAAA,EAAAS,CAAA,IAAAmB,KAAA,EAAAlB,CAAA,EAAAM,UAAA,MAAAa,YAAA,MAAAC,QAAA,UAAA9B,CAAA,CAAAS,CAAA,IAAAC,CAAA,EAAAV,CAAA;AAAA,SAAA2B,eAAAjB,CAAA,QAAAqB,CAAA,GAAAC,YAAA,CAAAtB,CAAA,gCAAAR,OAAA,CAAA6B,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAtB,CAAA,EAAAD,CAAA,oBAAAP,OAAA,CAAAQ,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAV,CAAA,GAAAU,CAAA,CAAAN,MAAA,CAAA6B,WAAA,kBAAAjC,CAAA,QAAA+B,CAAA,GAAA/B,CAAA,CAAAkC,IAAA,CAAAxB,CAAA,EAAAD,CAAA,gCAAAP,OAAA,CAAA6B,CAAA,UAAAA,CAAA,YAAAI,SAAA,yEAAA1B,CAAA,GAAA2B,MAAA,GAAAC,MAAA,EAAA3B,CAAA;AAEzC,SAAS4B,wBAAwBA,CAAA,EAAG;EAClD,IAAAC,mBAAA,GAAoC,IAAAC,+BAAkB,EAACpB,SAAS,CAAC;IAAzDqB,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC/B,IAAMC,OAAO,GAAG,IAAIC,8BAAkB,CAACJ,IAAI,EAAAtB,aAAA,CAAAA,aAAA,KAAOuB,OAAO;IAAEI,EAAE,EAAE;EAAI,IAAIH,QAAQ,CAAC;EAChF,OAAApB,eAAA,KACEnB,MAAM,CAACC,QAAQ,cAAI;IACnB,OAAO;MACH0C,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAQ;QACX,IAAIH,OAAO,CAACI,OAAO,CAAC,CAAC,EAAE;UACzB,OAAO;YACNC,IAAI,EAAE,KAAK;YACXrB,KAAK,EAAEgB,OAAO,CAACG,IAAI,CAAC;UACrB,CAAC;QACF;QACA,OAAO;UACNE,IAAI,EAAE;QACP,CAAC;MACC;IACJ,CAAC;EACF,CAAC;AAEH", "ignoreList": []}