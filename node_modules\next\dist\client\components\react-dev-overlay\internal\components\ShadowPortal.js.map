{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/components/ShadowPortal.tsx"], "names": ["ShadowPort<PERSON>", "children", "portalNode", "React", "useRef", "shadowNode", "forceUpdate", "useState", "useLayoutEffect", "ownerDocument", "document", "current", "createElement", "attachShadow", "mode", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "createPortal"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;;iEAHO;0BACM;AAEtB,SAASA,aAAa,KAA2C;IAA3C,IAAA,EAAEC,QAAQ,EAAiC,GAA3C;IAC3B,IAAIC,aAAaC,OAAMC,MAAM,CAAqB;IAClD,IAAIC,aAAaF,OAAMC,MAAM,CAAoB;IACjD,IAAI,GAAGE,YAAY,GAAGH,OAAMI,QAAQ;IAEpCJ,OAAMK,eAAe,CAAC;QACpB,MAAMC,gBAAgBC;QACtBR,WAAWS,OAAO,GAAGF,cAAcG,aAAa,CAAC;QACjDP,WAAWM,OAAO,GAAGT,WAAWS,OAAO,CAACE,YAAY,CAAC;YAAEC,MAAM;QAAO;QACpEL,cAAcM,IAAI,CAACC,WAAW,CAACd,WAAWS,OAAO;QACjDL,YAAY,CAAC;QACb,OAAO;YAC<PERSON>,IAAIJ,WAAWS,OAAO,IAAIT,WAAWS,OAAO,CAACF,aAAa,EAAE;gBAC1DP,WAAWS,OAAO,CAACF,aAAa,CAACM,IAAI,CAACE,WAAW,CAACf,WAAWS,OAAO;YACtE;QACF;IACF,GAAG,EAAE;IAEL,OAAON,WAAWM,OAAO,iBACrBO,IAAAA,sBAAY,EAACjB,UAAUI,WAAWM,OAAO,IACzC;AACN"}