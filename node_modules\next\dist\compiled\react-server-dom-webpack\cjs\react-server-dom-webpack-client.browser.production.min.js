/*
 React
 react-server-dom-webpack-client.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var r=require("react-dom"),t={stream:!0};function u(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var v=new Map;
function w(a){var b=__webpack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function x(){}
function y(a){for(var b=a[1],c=[],e=0;e<b.length;){var l=b[e++],k=b[e++],n=v.get(l);void 0===n?(z.set(l,k),k=__webpack_chunk_load__(l),c.push(k),n=v.set.bind(v,l,null),k.then(n,x),v.set(l,k)):null!==n&&c.push(n)}return 4===a.length?0===c.length?w(a[0]):Promise.all(c).then(function(){return w(a[0])}):0<c.length?Promise.all(c):null}var z=new Map,A=__webpack_require__.u;__webpack_require__.u=function(a){var b=z.get(a);return void 0!==b?b:A(a)};
var B=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,C=Symbol.for("react.element"),E=Symbol.for("react.lazy"),F=Symbol.iterator;function H(a){if(null===a||"object"!==typeof a)return null;a=F&&a[F]||a["@@iterator"];return"function"===typeof a?a:null}var I=Array.isArray,J=Object.getPrototypeOf,aa=Object.prototype,K=new WeakMap;function ba(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function ca(a,b,c,e){function l(m,d){if(null===d)return null;if("object"===typeof d){if("function"===typeof d.then){null===g&&(g=new FormData);n++;var h=k++;d.then(function(p){p=JSON.stringify(p,l);var q=g;q.append(b+h,p);n--;0===n&&c(q)},function(p){e(p)});return"$@"+h.toString(16)}if(I(d))return d;if(d instanceof FormData){null===g&&(g=new FormData);var f=g;m=k++;var D=b+m+"_";d.forEach(function(p,q){f.append(D+q,p)});return"$K"+m.toString(16)}if(d instanceof Map)return d=JSON.stringify(Array.from(d),
l),null===g&&(g=new FormData),m=k++,g.append(b+m,d),"$Q"+m.toString(16);if(d instanceof Set)return d=JSON.stringify(Array.from(d),l),null===g&&(g=new FormData),m=k++,g.append(b+m,d),"$W"+m.toString(16);if(H(d))return Array.from(d);m=J(d);if(m!==aa&&(null===m||null!==J(m)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return d}if("string"===typeof d){if("Z"===d[d.length-1]&&this[m]instanceof Date)return"$D"+d;
d="$"===d[0]?"$"+d:d;return d}if("boolean"===typeof d)return d;if("number"===typeof d)return ba(d);if("undefined"===typeof d)return"$undefined";if("function"===typeof d){d=K.get(d);if(void 0!==d)return d=JSON.stringify(d,l),null===g&&(g=new FormData),m=k++,g.set(b+m,d),"$F"+m.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof d){m=d.description;if(Symbol.for(m)!==d)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(d.description+") cannot be found among global symbols."));return"$S"+m}if("bigint"===typeof d)return"$n"+d.toString(10);throw Error("Type "+typeof d+" is not supported as an argument to a Server Function.");}var k=1,n=0,g=null;a=JSON.stringify(a,l);null===g?c(a):(g.set(b+"0",a),0===n&&c(g))}function da(a,b){K.set(a,b)}function L(a,b,c,e){this.status=a;this.value=b;this.reason=c;this._response=e}L.prototype=Object.create(Promise.prototype);
L.prototype.then=function(a,b){switch(this.status){case "resolved_model":M(this);break;case "resolved_module":N(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function ea(a){switch(a.status){case "resolved_model":M(a);break;case "resolved_module":N(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function O(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function Q(a,b,c){switch(a.status){case "fulfilled":O(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=c;break;case "rejected":c&&O(c,a.reason)}}
function R(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&O(c,b)}}function S(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,e=a.reason;a.status="resolved_module";a.value=b;null!==c&&(N(a),Q(a,c,e))}}var T=null,U=null;
function M(a){var b=T,c=U;T=a;U=null;var e=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var l=JSON.parse(e,a._response._fromJSON);if(null!==U&&0<U.deps)U.value=l,a.status="blocked",a.value=null,a.reason=null;else{var k=a.value;a.status="fulfilled";a.value=l;null!==k&&O(k,l)}}catch(n){a.status="rejected",a.reason=n}finally{T=b,U=c}}
function N(a){try{var b=a.value,c=__webpack_require__(b[0]);if(4===b.length&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var e="*"===b[2]?c:""===b[2]?c.__esModule?c.default:c:c[b[2]];a.status="fulfilled";a.value=e}catch(l){a.status="rejected",a.reason=l}}function V(a,b){a._chunks.forEach(function(c){"pending"===c.status&&R(c,b)})}function W(a,b){var c=a._chunks,e=c.get(b);e||(e=new L("pending",null,null,a),c.set(b,e));return e}
function fa(a,b,c,e){if(U){var l=U;e||l.deps++}else l=U={deps:e?0:1,value:null};return function(k){b[c]=k;l.deps--;0===l.deps&&"blocked"===a.status&&(k=a.value,a.status="fulfilled",a.value=l.value,null!==k&&O(k,l.value))}}function ha(a){return function(b){return R(a,b)}}
function ia(a,b){function c(){var l=Array.prototype.slice.call(arguments),k=b.bound;return k?"fulfilled"===k.status?e(b.id,k.value.concat(l)):Promise.resolve(k).then(function(n){return e(b.id,n.concat(l))}):e(b.id,l)}var e=a._callServer;K.set(c,b);return c}function X(a,b){a=W(a,b);switch(a.status){case "resolved_model":M(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ja(a,b,c,e){if("$"===e[0]){if("$"===e)return C;switch(e[1]){case "$":return e.slice(1);case "L":return b=parseInt(e.slice(2),16),a=W(a,b),{$$typeof:E,_payload:a,_init:ea};case "@":if(2===e.length)return new Promise(function(){});b=parseInt(e.slice(2),16);return W(a,b);case "S":return Symbol.for(e.slice(2));case "F":return b=parseInt(e.slice(2),16),b=X(a,b),ia(a,b);case "Q":return b=parseInt(e.slice(2),16),a=X(a,b),new Map(a);case "W":return b=parseInt(e.slice(2),16),a=X(a,b),new Set(a);case "I":return Infinity;
case "-":return"$-0"===e?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(e.slice(2)));case "n":return BigInt(e.slice(2));default:e=parseInt(e.slice(1),16);a=W(a,e);switch(a.status){case "resolved_model":M(a);break;case "resolved_module":N(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":return e=T,a.then(fa(e,b,c,"cyclic"===a.status),ha(e)),null;default:throw a.reason;}}}return e}
function ka(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function Y(a,b,c,e,l){var k=new Map;a={_bundlerConfig:a,_moduleLoading:b,_callServer:void 0!==c?c:ka,_encodeFormAction:e,_nonce:l,_chunks:k,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=la(a);return a}
function ma(a,b,c){var e=a._chunks,l=e.get(b);c=JSON.parse(c,a._fromJSON);var k=u(a._bundlerConfig,c);if(c=y(k)){if(l){var n=l;n.status="blocked"}else n=new L("blocked",null,null,a),e.set(b,n);c.then(function(){return S(n,k)},function(g){return R(n,g)})}else l?S(l,k):e.set(b,new L("resolved_module",k,null,a))}
function la(a){return function(b,c){return"string"===typeof c?ja(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===C?{$$typeof:C,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}
function Z(a,b){function c(k){var n=k.value;if(k.done)V(a,Error("Connection closed."));else{var g=0,m=a._rowState,d=a._rowID,h=a._rowTag,f=a._rowLength;k=a._buffer;for(var D=n.length;g<D;){var p=-1;switch(m){case 0:p=n[g++];58===p?m=1:d=d<<4|(96<p?p-87:p-48);continue;case 1:m=n[g];84===m?(h=m,m=2,g++):64<m&&91>m?(h=m,m=3,g++):(h=0,m=3);continue;case 2:p=n[g++];44===p?m=4:f=f<<4|(96<p?p-87:p-48);continue;case 3:p=n.indexOf(10,g);break;case 4:p=g+f,p>n.length&&(p=-1)}var q=n.byteOffset+g;if(-1<p){g=
new Uint8Array(n.buffer,q,p-g);f=a;q=h;var P=f._stringDecoder;h="";for(var G=0;G<k.length;G++)h+=P.decode(k[G],t);h+=P.decode(g);switch(q){case 73:ma(f,d,h);break;case 72:d=h[0];h=h.slice(1);f=JSON.parse(h,f._fromJSON);if(h=B.current)switch(d){case "D":h.prefetchDNS(f);break;case "C":"string"===typeof f?h.preconnect(f):h.preconnect(f[0],f[1]);break;case "L":d=f[0];g=f[1];3===f.length?h.preload(d,g,f[2]):h.preload(d,g);break;case "m":"string"===typeof f?h.preloadModule(f):h.preloadModule(f[0],f[1]);
break;case "S":"string"===typeof f?h.preinitStyle(f):h.preinitStyle(f[0],0===f[1]?void 0:f[1],3===f.length?f[2]:void 0);break;case "X":"string"===typeof f?h.preinitScript(f):h.preinitScript(f[0],f[1]);break;case "M":"string"===typeof f?h.preinitModuleScript(f):h.preinitModuleScript(f[0],f[1])}break;case 69:h=JSON.parse(h);g=h.digest;h=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
h.stack="Error: "+h.message;h.digest=g;g=f._chunks;(q=g.get(d))?R(q,h):g.set(d,new L("rejected",null,h,f));break;case 84:f._chunks.set(d,new L("fulfilled",h,null,f));break;case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");default:g=f._chunks,(q=g.get(d))?(f=q,d=h,"pending"===f.status&&(h=f.value,g=f.reason,f.status="resolved_model",
f.value=d,null!==h&&(M(f),Q(f,h,g)))):g.set(d,new L("resolved_model",h,null,f))}g=p;3===m&&g++;f=d=h=m=0;k.length=0}else{n=new Uint8Array(n.buffer,q,n.byteLength-g);k.push(n);f-=n.byteLength;break}}a._rowState=m;a._rowID=d;a._rowTag=h;a._rowLength=f;return l.read().then(c).catch(e)}}function e(k){V(a,k)}var l=b.getReader();l.read().then(c).catch(e)}
exports.createFromFetch=function(a,b){var c=Y(null,null,b&&b.callServer?b.callServer:void 0,void 0,void 0);a.then(function(e){Z(c,e.body)},function(e){V(c,e)});return W(c,0)};exports.createFromReadableStream=function(a,b){b=Y(null,null,b&&b.callServer?b.callServer:void 0,void 0,void 0);Z(b,a);return W(b,0)};exports.createServerReference=function(a,b){function c(){var e=Array.prototype.slice.call(arguments);return b(a,e)}da(c,{id:a,bound:null});return c};
exports.encodeReply=function(a){return new Promise(function(b,c){ca(a,"",b,c)})};

//# sourceMappingURL=react-server-dom-webpack-client.browser.production.min.js.map
