{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/hot-linked-text/index.tsx"], "names": ["React", "decodeMagicIdentifier", "MAGIC_IDENTIFIER_REGEX", "linkRegex", "splitRegexp", "RegExp", "source", "HotlinkedText", "props", "text", "matcher", "wordsAndWhitespaces", "split", "map", "word", "index", "test", "link", "exec", "href", "Fragment", "a", "target", "rel", "decodedWord", "i", "e"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SACEC,qBAAqB,EACrBC,sBAAsB,QACjB,gDAA+C;AAEtD,MAAMC,YAAY;AAElB,MAAMC,cAAc,IAAIC,OAAO,AAAC,MAAGH,uBAAuBI,MAAM,GAAC;AAEjE,OAAO,MAAMC,gBAGR,SAASA,cAAcC,KAAK;IAC/B,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE,GAAGF;IAE1B,MAAMG,sBAAsBF,KAAKG,KAAK,CAACR;IAEvC,qBACE;kBACGO,oBAAoBE,GAAG,CAAC,CAACC,MAAMC;YAC9B,IAAIZ,UAAUa,IAAI,CAACF,OAAO;gBACxB,MAAMG,OAAOd,UAAUe,IAAI,CAACJ;gBAC5B,MAAMK,OAAOF,IAAI,CAAC,EAAE;gBACpB,mFAAmF;gBACnF,IAAI,OAAOP,YAAY,cAAc,CAACA,QAAQS,OAAO;oBACnD,OAAOL;gBACT;gBACA,qBACE,KAACd,MAAMoB,QAAQ;8BACb,cAAA,KAACC;wBAAEF,MAAMA;wBAAMG,QAAO;wBAASC,KAAI;kCAChCT;;mBAFgB,AAAC,UAAOC;YAMjC;YACA,IAAI;gBACF,MAAMS,cAAcvB,sBAAsBa;gBAC1C,IAAIU,gBAAgBV,MAAM;oBACxB,qBACE,MAACW;;4BACE;4BACAD;4BACA;;uBAHK,AAAC,WAAQT;gBAMrB;YACF,EAAE,OAAOW,GAAG;gBACV,qBACE,MAACD;;wBACE;wBACAX;wBAAK;wBAAoB,KAAKY;wBAAE;wBAAE;;mBAF7B,AAAC,WAAQX;YAKrB;YACA,qBAAO,KAACf,MAAMoB,QAAQ;0BAAwBN;eAAlB,AAAC,UAAOC;QACtC;;AAGN,EAAC"}