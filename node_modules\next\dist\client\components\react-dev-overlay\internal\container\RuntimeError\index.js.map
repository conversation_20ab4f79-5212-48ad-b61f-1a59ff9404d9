{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/index.tsx"], "names": ["RuntimeError", "styles", "error", "firstFrame", "allLeadingFrames", "allCallStackFrames", "React", "useMemo", "filteredFrames", "frames", "filter", "f", "sourceStackFrame", "file", "includes", "methodName", "startsWith", "firstFirstPartyFrameIndex", "findIndex", "entry", "expanded", "Boolean", "originalCodeFrame", "originalStackFrame", "slice", "all", "setAll", "useState", "canShowMore", "leadingFramesGroupedByFramework", "stackFramesGroupedByFramework", "leading<PERSON>ram<PERSON>", "visibleCallStackFrames", "length", "groupStackFramesByFramework", "Fragment", "h2", "GroupedStackFrames", "groupedStackFrames", "show", "CodeFrame", "stackFrame", "codeFrame", "undefined", "button", "tabIndex", "data-nextjs-data-runtime-error-collapsed-action", "type", "onClick", "css"], "mappings": ";;;;;;;;;;;;;;;IASgBA,YAAY;eAAZA;;IA8FHC,MAAM;eAANA;;;;;;iEAvGU;2BACG;8BAEE;6CACgB;oCACT;;;;;;;;;;AAI5B,SAASD,aAAa,KAA4B;IAA5B,IAAA,EAAEE,KAAK,EAAqB,GAA5B;IAC3B,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,kBAAkB,EAAE,GACxDC,OAAMC,OAAO,CAAC;QACZ,MAAMC,iBAAiBN,MAAMO,MAAM,CAACC,MAAM,CACxC,CAACC;gBAIOA;mBAHN,CACEA,CAAAA,EAAEC,gBAAgB,CAACC,IAAI,KAAK,iBAC5B;gBAAC;gBAAa;aAAY,CAACC,QAAQ,CAACH,EAAEC,gBAAgB,CAACG,UAAU,CAAA,KAC9D,GAACJ,2BAAAA,EAAEC,gBAAgB,CAACC,IAAI,qBAAvBF,yBAAyBK,UAAU,CAAC;;QAG9C,MAAMC,4BAA4BT,eAAeU,SAAS,CACxD,CAACC,QACCA,MAAMC,QAAQ,IACdC,QAAQF,MAAMG,iBAAiB,KAC/BD,QAAQF,MAAMI,kBAAkB;YAItBf;QADd,OAAO;YACLL,YAAYK,CAAAA,4CAAAA,cAAc,CAACS,0BAA0B,YAAzCT,4CAA6C;YACzDJ,kBACEa,4BAA4B,IACxB,EAAE,GACFT,eAAegB,KAAK,CAAC,GAAGP;YAC9BZ,oBAAoBG,eAAegB,KAAK,CAACP,4BAA4B;QACvE;IACF,GAAG;QAACf,MAAMO,MAAM;KAAC;IAEnB,MAAM,CAACgB,KAAKC,OAAO,GAAGpB,OAAMqB,QAAQ,CAACxB,cAAc;IAEnD,MAAM,EACJyB,WAAW,EACXC,+BAA+B,EAC/BC,6BAA6B,EAC9B,GAAGxB,OAAMC,OAAO,CAAC;QAChB,MAAMwB,gBAAgB3B,iBAAiBM,MAAM,CAAC,CAACC,IAAMA,EAAES,QAAQ,IAAIK;QACnE,MAAMO,yBAAyB3B,mBAAmBK,MAAM,CACtD,CAACC,IAAMA,EAAES,QAAQ,IAAIK;QAGvB,OAAO;YACLG,aACEvB,mBAAmB4B,MAAM,KAAKD,uBAAuBC,MAAM,IAC1DR,OAAOtB,cAAc;YAExB2B,+BACEI,IAAAA,wDAA2B,EAAC7B;YAE9BwB,iCACEK,IAAAA,wDAA2B,EAACH;QAChC;IACF,GAAG;QAACN;QAAKpB;QAAoBD;QAAkBD;KAAW;IAE1D,qBACE,sBAACG,OAAM6B,QAAQ;;YACZhC,2BACC,sBAACG,OAAM6B,QAAQ;;kCACb,qBAACC;kCAAG;;kCACJ,qBAACC,sCAAkB;wBACjBC,oBAAoBT;wBACpBU,MAAMd;;kCAER,qBAACe,oBAAS;wBACRC,YAAYtC,WAAWoB,kBAAkB;wBACzCmB,WAAWvC,WAAWmB,iBAAiB;;;iBAGzCqB;YAEHb,8BAA8BG,MAAM,iBACnC,sBAAC3B,OAAM6B,QAAQ;;kCACb,qBAACC;kCAAG;;kCACJ,qBAACC,sCAAkB;wBACjBC,oBAAoBR;wBACpBS,MAAMd;;;iBAGRkB;YACHf,4BACC,qBAACtB,OAAM6B,QAAQ;0BACb,cAAA,sBAACS;oBACCC,UAAU;oBACVC,iDAA+C;oBAC/CC,MAAK;oBACLC,SAAS,IAAMtB,OAAO,CAACD;;wBAEtBA,MAAM,SAAS;wBAAO;;;iBAGzBkB;;;AAGV;AAEO,MAAM1C,aAASgD,kBAAG"}