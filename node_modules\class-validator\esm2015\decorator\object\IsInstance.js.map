{"version": 3, "file": "IsInstance.js", "sourceRoot": "", "sources": ["../../../../src/decorator/object/IsInstance.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,WAAW,GAAG,YAAY,CAAC;AAExC;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,MAAe,EAAE,qBAAkD;IAC5F,OAAO,CACL,qBAAqB,IAAI,OAAO,qBAAqB,KAAK,UAAU,IAAI,MAAM,YAAY,qBAAqB,CAChH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CACxB,UAAuC,EACvC,iBAAqC;IAErC,OAAO,UAAU,CACf;QACE,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,CAAC,UAAU,CAAC;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC3E,cAAc,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE;gBAChD,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;oBACzB,OAAO,UAAU,GAAG,oCAAoC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,EAAE,IAAc,EAAE,CAAC;gBAChG,CAAC;qBAAM,CAAC;oBACN,OAAO,UAAU,GAAG,GAAG,WAAW,8DAA8D,CAAC;gBACnG,CAAC;YACH,CAAC,EAAE,iBAAiB,CAAC;SACtB;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_INSTANCE = 'isInstance';\n\n/**\n * Checks if the value is an instance of the specified object.\n */\nexport function isInstance(object: unknown, targetTypeConstructor: new (...args: any[]) => any): boolean {\n  return (\n    targetTypeConstructor && typeof targetTypeConstructor === 'function' && object instanceof targetTypeConstructor\n  );\n}\n\n/**\n * Checks if the value is an instance of the specified object.\n */\nexport function IsInstance(\n  targetType: new (...args: any[]) => any,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_INSTANCE,\n      constraints: [targetType],\n      validator: {\n        validate: (value, args): boolean => isInstance(value, args?.constraints[0]),\n        defaultMessage: buildMessage((eachPrefix, args) => {\n          if (args?.constraints[0]) {\n            return eachPrefix + `$property must be an instance of ${args?.constraints[0].name as string}`;\n          } else {\n            return eachPrefix + `${IS_INSTANCE} decorator expects and object as value, but got falsy value.`;\n          }\n        }, validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}