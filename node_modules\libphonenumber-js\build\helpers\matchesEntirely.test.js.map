{"version": 3, "file": "matchesEntirely.test.js", "names": ["_matchesEntirely", "_interopRequireDefault", "require", "e", "__esModule", "describe", "it", "expect", "matchesEntirely", "undefined", "to", "equal"], "sources": ["../../source/helpers/matchesEntirely.test.js"], "sourcesContent": ["import matchesEntirely from './matchesEntirely.js'\r\n\r\ndescribe('matchesEntirely', () => {\r\n\tit('should work in edge cases', () => {\r\n\t\t// No text.\r\n\t\texpect(matchesEntirely(undefined, '')).to.equal(true)\r\n\r\n\t\t// \"OR\" in regexp.\r\n\t\texpect(matchesEntirely('911231231', '4\\d{8}|[1-9]\\d{7}')).to.equal(false)\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,gBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAkD,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAElDE,QAAQ,CAAC,iBAAiB,EAAE,YAAM;EACjCC,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC;IACAC,MAAM,CAAC,IAAAC,2BAAe,EAACC,SAAS,EAAE,EAAE,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAErD;IACAJ,MAAM,CAAC,IAAAC,2BAAe,EAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC,CAACE,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC1E,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}