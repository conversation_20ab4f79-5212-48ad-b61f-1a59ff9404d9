{"version": 3, "file": "formatIncompletePhoneNumber.test.js", "names": ["_formatIncompletePhoneNumber", "_interopRequireDefault", "require", "_metadataMin", "e", "__esModule", "describe", "it", "result", "expect", "formatIncompletePhoneNumber", "metadata", "to", "equal", "defaultCountry", "defaultCallingCode"], "sources": ["../source/formatIncompletePhoneNumber.test.js"], "sourcesContent": ["import formatIncompletePhoneNumber from './formatIncompletePhoneNumber.js'\r\n\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\ndescribe('formatIncompletePhoneNumber', () => {\r\n\tit('should format parsed input value', () => {\r\n\t\tlet result\r\n\r\n\t\t// National input.\r\n\t\texpect(formatIncompletePhoneNumber('880055535', 'RU', metadata)).to.equal('8 (800) 555-35')\r\n\r\n\t\t// International input, no country.\r\n\t\texpect(formatIncompletePhoneNumber('+780055535', null, metadata)).to.equal('****** 555 35')\r\n\r\n\t\t// International input, no country argument.\r\n\t\texpect(formatIncompletePhoneNumber('+780055535', metadata)).to.equal('****** 555 35')\r\n\r\n\t\t// International input, with country.\r\n\t\texpect(formatIncompletePhoneNumber('+780055535', 'RU', metadata)).to.equal('****** 555 35')\r\n\t})\r\n\r\n\tit('should support an object argument', () => {\r\n\t\texpect(\r\n            formatIncompletePhoneNumber('880055535', { defaultCountry: 'RU' }, metadata)\r\n        ).to.equal('8 (800) 555-35')\r\n\t\texpect(\r\n            formatIncompletePhoneNumber('880055535', { defaultCallingCode: '7' }, metadata)\r\n        ).to.equal('8 (800) 555-35')\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,4BAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEjEE,QAAQ,CAAC,6BAA6B,EAAE,YAAM;EAC7CC,EAAE,CAAC,kCAAkC,EAAE,YAAM;IAC5C,IAAIC,MAAM;;IAEV;IACAC,MAAM,CAAC,IAAAC,uCAA2B,EAAC,WAAW,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;;IAE3F;IACAJ,MAAM,CAAC,IAAAC,uCAA2B,EAAC,YAAY,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;;IAE3F;IACAJ,MAAM,CAAC,IAAAC,uCAA2B,EAAC,YAAY,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;;IAErF;IACAJ,MAAM,CAAC,IAAAC,uCAA2B,EAAC,YAAY,EAAE,IAAI,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC5F,CAAC,CAAC;EAEFN,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7CE,MAAM,CACI,IAAAC,uCAA2B,EAAC,WAAW,EAAE;MAAEI,cAAc,EAAE;IAAK,CAAC,EAAEH,uBAAQ,CAC/E,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAClCJ,MAAM,CACI,IAAAC,uCAA2B,EAAC,WAAW,EAAE;MAAEK,kBAAkB,EAAE;IAAI,CAAC,EAAEJ,uBAAQ,CAClF,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;EACnC,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}