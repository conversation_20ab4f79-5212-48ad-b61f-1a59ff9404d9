{"version": 3, "file": "takeLast.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/takeLast.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,6CAA4C;AAE5C,qCAAuC;AACvC,2DAAgE;AAyChE,SAAgB,QAAQ,CAAI,KAAa;IACvC,OAAO,KAAK,IAAI,CAAC;QACf,CAAC,CAAC,cAAM,OAAA,aAAK,EAAL,CAAK;QACb,CAAC,CAAC,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;YAKzB,IAAI,MAAM,GAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,SAAS,CACd,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAK;gBAEJ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAGnB,KAAK,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YAC1C,CAAC,EACD;;;oBAGE,KAAoB,IAAA,WAAA,SAAA,MAAM,CAAA,8BAAA,kDAAE;wBAAvB,IAAM,KAAK,mBAAA;wBACd,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;qBACxB;;;;;;;;;gBACD,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,CAAC,EAED,SAAS,EACT;gBAEE,MAAM,GAAG,IAAK,CAAC;YACjB,CAAC,CACF,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;AACT,CAAC;AApCD,4BAoCC"}