{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Overlay/body-locker.ts"], "names": ["previousBodyPaddingRight", "previousBodyOverflowSetting", "activeLocks", "lock", "setTimeout", "scrollBarGap", "window", "innerWidth", "document", "documentElement", "clientWidth", "body", "style", "paddingRight", "overflow", "unlock", "undefined"], "mappings": "AAAA,IAAIA;AACJ,IAAIC;AAEJ,IAAIC,cAAc;AAElB,OAAO,SAASC;IACdC,WAAW;QACT,IAAIF,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAMG,eACJC,OAAOC,UAAU,GAAGC,SAASC,eAAe,CAACC,WAAW;QAE1D,IAAIL,eAAe,GAAG;YACpBL,2BAA2BQ,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY;YAC3DL,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAG,AAAC,KAAER,eAAa;QACrD;QAEAJ,8BAA8BO,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ;QAC1DN,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG;IACjC;AACF;AAEA,OAAO,SAASC;IACdX,WAAW;QACT,IAAIF,gBAAgB,KAAK,EAAEA,gBAAgB,GAAG;YAC5C;QACF;QAEA,IAAIF,6BAA6BgB,WAAW;YAC1CR,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAGb;YACnCA,2BAA2BgB;QAC7B;QAEA,IAAIf,gCAAgCe,WAAW;YAC7CR,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAGb;YAC/BA,8BAA8Be;QAChC;IACF;AACF"}