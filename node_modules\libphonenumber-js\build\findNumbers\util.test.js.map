{"version": 3, "file": "util.test.js", "names": ["_util", "require", "describe", "it", "thrower", "limit", "expect", "to", "trimAfterFirstMatch", "equal", "startsWith", "endsWith"], "sources": ["../../source/findNumbers/util.test.js"], "sourcesContent": ["import {\r\n\tlimit,\r\n\ttrimAfterFirstMatch,\r\n\tstartsWith,\r\n\tendsWith\r\n} from './util.js'\r\n\r\ndescribe('findNumbers/util', () => {\r\n\tit('should generate regexp limit', () => {\r\n\t\tlet thrower = () => limit(1, 0)\r\n\t\texpect(thrower).to.throw()\r\n\r\n\t\tthrower = () => limit(-1, 1)\r\n\t\texpect(thrower).to.throw()\r\n\r\n\t\tthrower = () => limit(0, 0)\r\n\t\texpect(thrower).to.throw()\r\n\t})\r\n\r\n\tit('should trimAfterFirstMatch', () => {\r\n\t\texpect(trimAfterFirstMatch(/\\d/, 'abc123')).to.equal('abc')\r\n\t\texpect(trimAfterFirstMatch(/\\d/, 'abc')).to.equal('abc')\r\n\t})\r\n\r\n\tit('should determine if a string starts with a substring', () => {\r\n\t\texpect(startsWith('𐍈123', '𐍈')).to.equal(true)\r\n\t\texpect(startsWith('1𐍈', '𐍈')).to.equal(false)\r\n\t})\r\n\r\n\tit('should determine if a string ends with a substring', () => {\r\n\t\texpect(endsWith('123𐍈', '𐍈')).to.equal(true)\r\n\t\texpect(endsWith('𐍈1', '𐍈')).to.equal(false)\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAOAC,QAAQ,CAAC,kBAAkB,EAAE,YAAM;EAClCC,EAAE,CAAC,8BAA8B,EAAE,YAAM;IACxC,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAAC,WAAK,EAAC,CAAC,EAAE,CAAC,CAAC;IAAA;IAC/BC,MAAM,CAACF,OAAO,CAAC,CAACG,EAAE,SAAM,CAAC,CAAC;IAE1BH,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAAC,WAAK,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAAA;IAC5BC,MAAM,CAACF,OAAO,CAAC,CAACG,EAAE,SAAM,CAAC,CAAC;IAE1BH,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAAC,WAAK,EAAC,CAAC,EAAE,CAAC,CAAC;IAAA;IAC3BC,MAAM,CAACF,OAAO,CAAC,CAACG,EAAE,SAAM,CAAC,CAAC;EAC3B,CAAC,CAAC;EAEFJ,EAAE,CAAC,4BAA4B,EAAE,YAAM;IACtCG,MAAM,CAAC,IAAAE,yBAAmB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAACD,EAAE,CAACE,KAAK,CAAC,KAAK,CAAC;IAC3DH,MAAM,CAAC,IAAAE,yBAAmB,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAACD,EAAE,CAACE,KAAK,CAAC,KAAK,CAAC;EACzD,CAAC,CAAC;EAEFN,EAAE,CAAC,sDAAsD,EAAE,YAAM;IAChEG,MAAM,CAAC,IAAAI,gBAAU,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAACH,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC;IAChDH,MAAM,CAAC,IAAAI,gBAAU,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAACH,EAAE,CAACE,KAAK,CAAC,KAAK,CAAC;EAChD,CAAC,CAAC;EAEFN,EAAE,CAAC,oDAAoD,EAAE,YAAM;IAC9DG,MAAM,CAAC,IAAAK,cAAQ,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,IAAI,CAAC;IAC9CH,MAAM,CAAC,IAAAK,cAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAACJ,EAAE,CAACE,KAAK,CAAC,KAAK,CAAC;EAC9C,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}