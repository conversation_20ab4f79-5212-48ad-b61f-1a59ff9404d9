{"version": 3, "sources": ["../../src/build/type-check.ts"], "names": ["path", "Log", "Worker", "verifyAndLint", "createSpinner", "eventTypeCheckCompleted", "isError", "verifyTypeScriptSetup", "dir", "distDir", "intentDirs", "typeCheckPreflight", "tsconfigPath", "disableStaticImages", "cacheDir", "enableWorkerThreads", "hasAppDir", "hasPagesDir", "typeCheckWorker", "require", "resolve", "exposedMethods", "numWorkers", "maxRetries", "then", "result", "end", "catch", "process", "exit", "startTypeChecking", "config", "ignoreESLint", "nextBuildSpan", "pagesDir", "runLint", "shouldLint", "telemetry", "appDir", "ignoreTypeScriptErrors", "Boolean", "typescript", "ignoreBuildErrors", "eslintCacheDir", "join", "info", "typeCheckingAndLintingSpinnerPrefixText", "typeCheckingAndLintingSpinner", "typeCheckStart", "hrtime", "verifyResult", "typeCheckEnd", "Promise", "all", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "filter", "images", "experimental", "workerThreads", "resolved", "checkEnd", "eslint", "dirs", "stopAndPersist", "record", "durationInSeconds", "typescriptVersion", "version", "inputFilesCount", "totalFilesCount", "incremental", "err", "message", "flush"], "mappings": "AAIA,OAAOA,UAAU,OAAM;AACvB,YAAYC,SAAS,eAAc;AACnC,SAASC,MAAM,QAAQ,gBAAe;AACtC,SAASC,aAAa,QAAQ,uBAAsB;AACpD,OAAOC,mBAAmB,YAAW;AACrC,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,OAAOC,aAAa,kBAAiB;AAErC;;;;;;;CAOC,GACD,SAASC,sBACPC,GAAW,EACXC,OAAe,EACfC,UAAoB,EACpBC,kBAA2B,EAC3BC,YAAoB,EACpBC,mBAA4B,EAC5BC,QAA4B,EAC5BC,mBAAwC,EACxCC,SAAkB,EAClBC,WAAoB;IAEpB,MAAMC,kBAAkB,IAAIhB,OAC1BiB,QAAQC,OAAO,CAAC,mCAChB;QACEC,gBAAgB;YAAC;SAAwB;QACzCC,YAAY;QACZP;QACAQ,YAAY;IACd;IAKF,OAAOL,gBACJX,qBAAqB,CAAC;QACrBC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAE;QACAC;IACF,GACCO,IAAI,CAAC,CAACC;QACLP,gBAAgBQ,GAAG;QACnB,OAAOD;IACT,GACCE,KAAK,CAAC;QACL,2FAA2F;QAC3F,8FAA8F;QAC9FC,QAAQC,IAAI,CAAC;IACf;AACJ;AAEA,OAAO,eAAeC,kBAAkB,EACtChB,QAAQ,EACRiB,MAAM,EACNvB,GAAG,EACHwB,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,MAAM,EAYP;IACC,MAAMC,yBAAyBC,QAAQT,OAAOU,UAAU,CAACC,iBAAiB;IAE1E,MAAMC,iBAAiB3C,KAAK4C,IAAI,CAAC9B,UAAU;IAE3C,IAAIyB,wBAAwB;QAC1BtC,IAAI4C,IAAI,CAAC;IACX;IACA,IAAIV,WAAWH,cAAc;QAC3B,uEAAuE;QACvE/B,IAAI4C,IAAI,CAAC;IACX;IAEA,IAAIC;IACJ,IAAIC;IAIJ,IAAI,CAACR,0BAA0BH,YAAY;QACzCU,0CACE;IACJ,OAAO,IAAI,CAACP,wBAAwB;QAClCO,0CAA0C;IAC5C,OAAO,IAAIV,YAAY;QACrBU,0CAA0C;IAC5C;IAEA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAIA,yCAAyC;QAC3CC,gCAAgC3C,cAC9B0C;IAEJ;IAEA,MAAME,iBAAiBpB,QAAQqB,MAAM;IAErC,IAAI;QACF,MAAM,CAAC,CAACC,cAAcC,aAAa,CAAC,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACvDpB,cAAcqB,UAAU,CAAC,2BAA2BC,YAAY,CAAC,IAC/DhD,sBACEC,KACAuB,OAAOtB,OAAO,EACd;oBAACyB;oBAAUI;iBAAO,CAACkB,MAAM,CAAChB,UAC1B,CAACD,wBACDR,OAAOU,UAAU,CAAC7B,YAAY,EAC9BmB,OAAO0B,MAAM,CAAC5C,mBAAmB,EACjCC,UACAiB,OAAO2B,YAAY,CAACC,aAAa,EACjC,CAAC,CAACrB,QACF,CAAC,CAACJ,UACFV,IAAI,CAAC,CAACoC;oBACN,MAAMC,WAAWjC,QAAQqB,MAAM,CAACD;oBAChC,OAAO;wBAACY;wBAAUC;qBAAS;gBAC7B;YAEFzB,cACEH,cAAcqB,UAAU,CAAC,mBAAmBC,YAAY,CAAC;oBAIrDxB;gBAHF,MAAM5B,cACJK,KACAmC,iBACAZ,iBAAAA,OAAO+B,MAAM,qBAAb/B,eAAegC,IAAI,EACnBhC,OAAO2B,YAAY,CAACC,aAAa,EACjCtB;YAEJ;SACH;QACDU,iDAAAA,8BAA+BiB,cAAc;QAE7C,IAAI,CAACzB,0BAA0BW,cAAc;gBAKtBA,sBACAA,uBACJA;YANjBb,UAAU4B,MAAM,CACd5D,wBAAwB;gBACtB6D,mBAAmBf,YAAY,CAAC,EAAE;gBAClCgB,mBAAmBjB,aAAakB,OAAO;gBACvCC,eAAe,GAAEnB,uBAAAA,aAAazB,MAAM,qBAAnByB,qBAAqBmB,eAAe;gBACrDC,eAAe,GAAEpB,wBAAAA,aAAazB,MAAM,qBAAnByB,sBAAqBoB,eAAe;gBACrDC,WAAW,GAAErB,wBAAAA,aAAazB,MAAM,qBAAnByB,sBAAqBqB,WAAW;YAC/C;QAEJ;IACF,EAAE,OAAOC,KAAK;QACZ,mDAAmD;QACnD,8CAA8C;QAC9C,IAAIlE,QAAQkE,QAAQA,IAAIC,OAAO,KAAK,8BAA8B;YAChE,MAAMpC,UAAUqC,KAAK;YACrB9C,QAAQC,IAAI,CAAC;QACf;QACA,MAAM2C;IACR;AACF"}