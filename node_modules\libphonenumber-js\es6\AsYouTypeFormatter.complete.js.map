{"version": 3, "file": "AsYouTypeFormatter.complete.js", "names": ["checkNumberLength", "parseDigits", "formatNationalNumberUsingFormat", "formatCompleteNumber", "state", "format", "_ref", "metadata", "shouldTryNationalPrefixFormattingRule", "getSeparatorAfterNationalPrefix", "matcher", "RegExp", "concat", "pattern", "test", "nationalSignificantNumber", "formatNationalNumberWithAndWithoutNationalPrefixFormattingRule", "canFormatCompleteNumber", "_ref2", "international", "nationalPrefix", "carrierCode", "formattedNumber", "formatNationalNumber", "useNationalPrefixFormattingRule", "_ref3", "formattedNationalNumber", "useInternationalFormat", "withNationalPrefix", "complexPrefixBeforeNationalSignificantNumber", "isValidFormattedNationalNumber", "getNationalDigits"], "sources": ["../source/AsYouTypeFormatter.complete.js"], "sourcesContent": ["import checkNumberLength from './helpers/checkNumberLength.js'\r\nimport parseDigits from './helpers/parseDigits.js'\r\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js'\r\n\r\nexport default function formatCompleteNumber(state, format, {\r\n\tmetadata,\r\n\tshouldTryNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\tconst matcher = new RegExp(`^(?:${format.pattern()})$`)\r\n\tif (matcher.test(state.nationalSignificantNumber)) {\r\n\t\treturn formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(\r\n\t\t\tstate,\r\n\t\t\tformat,\r\n\t\t\t{\r\n\t\t\t\tmetadata,\r\n\t\t\t\tshouldTryNationalPrefixFormattingRule,\r\n\t\t\t\tgetSeparatorAfterNationalPrefix\r\n\t\t\t}\r\n\t\t)\r\n\t}\r\n}\r\n\r\nexport function canFormatCompleteNumber(nationalSignificantNumber, metadata) {\r\n\treturn checkNumberLength(nationalSignificantNumber, metadata) === 'IS_POSSIBLE'\r\n}\r\n\r\nfunction formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, {\r\n\tmetadata,\r\n\tshouldTryNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\t// `format` has already been checked for `nationalPrefix` requirement.\r\n\r\n\tconst {\r\n\t\tnationalSignificantNumber,\r\n\t\tinternational,\r\n\t\tnationalPrefix,\r\n\t\tcarrierCode\r\n\t} = state\r\n\r\n\t// Format the number with using `national_prefix_formatting_rule`.\r\n\t// If the resulting formatted number is a valid formatted number, then return it.\r\n\t//\r\n\t// Google's AsYouType formatter is different in a way that it doesn't try\r\n\t// to format using the \"national prefix formatting rule\", and instead it\r\n\t// simply prepends a national prefix followed by a \" \" character.\r\n\t// This code does that too, but as a fallback.\r\n\t// The reason is that \"national prefix formatting rule\" may use parentheses,\r\n\t// which wouldn't be included has it used the simpler Google's way.\r\n\t//\r\n\tif (shouldTryNationalPrefixFormattingRule(format)) {\r\n\t\tconst formattedNumber = formatNationalNumber(state, format, {\r\n\t\t\tuseNationalPrefixFormattingRule: true,\r\n\t\t\tgetSeparatorAfterNationalPrefix,\r\n\t\t\tmetadata\r\n\t\t})\r\n\t\tif (formattedNumber) {\r\n\t\t\treturn formattedNumber\r\n\t\t}\r\n\t}\r\n\r\n\t// Format the number without using `national_prefix_formatting_rule`.\r\n\treturn formatNationalNumber(state, format, {\r\n\t\tuseNationalPrefixFormattingRule: false,\r\n\t\tgetSeparatorAfterNationalPrefix,\r\n\t\tmetadata\r\n\t})\r\n}\r\n\r\nfunction formatNationalNumber(state, format, {\r\n\tmetadata,\r\n\tuseNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\tlet formattedNationalNumber = formatNationalNumberUsingFormat(\r\n\t\tstate.nationalSignificantNumber,\r\n\t\tformat,\r\n\t\t{\r\n\t\t\tcarrierCode: state.carrierCode,\r\n\t\t\tuseInternationalFormat: state.international,\r\n\t\t\twithNationalPrefix: useNationalPrefixFormattingRule,\r\n\t\t\tmetadata\r\n\t\t}\r\n\t)\r\n\tif (!useNationalPrefixFormattingRule) {\r\n\t\tif (state.nationalPrefix) {\r\n\t\t\t// If a national prefix was extracted, then just prepend it,\r\n\t\t\t// followed by a \" \" character.\r\n\t\t\tformattedNationalNumber = state.nationalPrefix +\r\n\t\t\t\tgetSeparatorAfterNationalPrefix(format) +\r\n\t\t\t\tformattedNationalNumber\r\n\t\t} else if (state.complexPrefixBeforeNationalSignificantNumber) {\r\n\t\t\tformattedNationalNumber = state.complexPrefixBeforeNationalSignificantNumber +\r\n\t\t\t\t' ' +\r\n\t\t\t\tformattedNationalNumber\r\n\t\t}\r\n\t}\r\n\tif (isValidFormattedNationalNumber(formattedNationalNumber, state)) {\r\n\t\treturn formattedNationalNumber\r\n\t}\r\n}\r\n\r\n// Check that the formatted phone number contains exactly\r\n// the same digits that have been input by the user.\r\n// For example, when \"0111523456789\" is input for `AR` country,\r\n// the extracted `this.nationalSignificantNumber` is \"91123456789\",\r\n// which means that the national part of `this.digits` isn't simply equal to\r\n// `this.nationalPrefix` + `this.nationalSignificantNumber`.\r\n//\r\n// Also, a `format` can add extra digits to the `this.nationalSignificantNumber`\r\n// being formatted via `metadata[country].national_prefix_transform_rule`.\r\n// For example, for `VI` country, it prepends `340` to the national number,\r\n// and if this check hasn't been implemented, then there would be a bug\r\n// when `340` \"area coude\" is \"duplicated\" during input for `VI` country:\r\n// https://github.com/catamphetamine/libphonenumber-js/issues/318\r\n//\r\n// So, all these \"gotchas\" are filtered out.\r\n//\r\n// In the original Google's code, the comments say:\r\n// \"Check that we didn't remove nor add any extra digits when we matched\r\n// this formatting pattern. This usually happens after we entered the last\r\n// digit during AYTF. Eg: In case of MX, we swallow mobile token (1) when\r\n// formatted but AYTF should retain all the number entered and not change\r\n// in order to match a format (of same leading digits and length) display\r\n// in that way.\"\r\n// \"If it's the same (i.e entered number and format is same), then it's\r\n// safe to return this in formatted number as nothing is lost / added.\"\r\n// Otherwise, don't use this format.\r\n// https://github.com/google/libphonenumber/commit/3e7c1f04f5e7200f87fb131e6f85c6e99d60f510#diff-9149457fa9f5d608a11bb975c6ef4bc5\r\n// https://github.com/google/libphonenumber/commit/3ac88c7106e7dcb553bcc794b15f19185928a1c6#diff-2dcb77e833422ee304da348b905cde0b\r\n//\r\nfunction isValidFormattedNationalNumber(formattedNationalNumber, state) {\r\n\treturn parseDigits(formattedNationalNumber) === state.getNationalDigits()\r\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,+BAA+B,MAAM,8CAA8C;AAE1F,eAAe,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,MAAM,EAAAC,IAAA,EAIvD;EAAA,IAHFC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IACRC,qCAAqC,GAAAF,IAAA,CAArCE,qCAAqC;IACrCC,+BAA+B,GAAAH,IAAA,CAA/BG,+BAA+B;EAE/B,IAAMC,OAAO,GAAG,IAAIC,MAAM,QAAAC,MAAA,CAAQP,MAAM,CAACQ,OAAO,CAAC,CAAC,OAAI,CAAC;EACvD,IAAIH,OAAO,CAACI,IAAI,CAACV,KAAK,CAACW,yBAAyB,CAAC,EAAE;IAClD,OAAOC,8DAA8D,CACpEZ,KAAK,EACLC,MAAM,EACN;MACCE,QAAQ,EAARA,QAAQ;MACRC,qCAAqC,EAArCA,qCAAqC;MACrCC,+BAA+B,EAA/BA;IACD,CACD,CAAC;EACF;AACD;AAEA,OAAO,SAASQ,uBAAuBA,CAACF,yBAAyB,EAAER,QAAQ,EAAE;EAC5E,OAAOP,iBAAiB,CAACe,yBAAyB,EAAER,QAAQ,CAAC,KAAK,aAAa;AAChF;AAEA,SAASS,8DAA8DA,CAACZ,KAAK,EAAEC,MAAM,EAAAa,KAAA,EAIlF;EAAA,IAHFX,QAAQ,GAAAW,KAAA,CAARX,QAAQ;IACRC,qCAAqC,GAAAU,KAAA,CAArCV,qCAAqC;IACrCC,+BAA+B,GAAAS,KAAA,CAA/BT,+BAA+B;EAE/B;;EAEA,IACCM,yBAAyB,GAItBX,KAAK,CAJRW,yBAAyB;IACzBI,aAAa,GAGVf,KAAK,CAHRe,aAAa;IACbC,cAAc,GAEXhB,KAAK,CAFRgB,cAAc;IACdC,WAAW,GACRjB,KAAK,CADRiB,WAAW;;EAGZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIb,qCAAqC,CAACH,MAAM,CAAC,EAAE;IAClD,IAAMiB,eAAe,GAAGC,oBAAoB,CAACnB,KAAK,EAAEC,MAAM,EAAE;MAC3DmB,+BAA+B,EAAE,IAAI;MACrCf,+BAA+B,EAA/BA,+BAA+B;MAC/BF,QAAQ,EAARA;IACD,CAAC,CAAC;IACF,IAAIe,eAAe,EAAE;MACpB,OAAOA,eAAe;IACvB;EACD;;EAEA;EACA,OAAOC,oBAAoB,CAACnB,KAAK,EAAEC,MAAM,EAAE;IAC1CmB,+BAA+B,EAAE,KAAK;IACtCf,+BAA+B,EAA/BA,+BAA+B;IAC/BF,QAAQ,EAARA;EACD,CAAC,CAAC;AACH;AAEA,SAASgB,oBAAoBA,CAACnB,KAAK,EAAEC,MAAM,EAAAoB,KAAA,EAIxC;EAAA,IAHFlB,QAAQ,GAAAkB,KAAA,CAARlB,QAAQ;IACRiB,+BAA+B,GAAAC,KAAA,CAA/BD,+BAA+B;IAC/Bf,+BAA+B,GAAAgB,KAAA,CAA/BhB,+BAA+B;EAE/B,IAAIiB,uBAAuB,GAAGxB,+BAA+B,CAC5DE,KAAK,CAACW,yBAAyB,EAC/BV,MAAM,EACN;IACCgB,WAAW,EAAEjB,KAAK,CAACiB,WAAW;IAC9BM,sBAAsB,EAAEvB,KAAK,CAACe,aAAa;IAC3CS,kBAAkB,EAAEJ,+BAA+B;IACnDjB,QAAQ,EAARA;EACD,CACD,CAAC;EACD,IAAI,CAACiB,+BAA+B,EAAE;IACrC,IAAIpB,KAAK,CAACgB,cAAc,EAAE;MACzB;MACA;MACAM,uBAAuB,GAAGtB,KAAK,CAACgB,cAAc,GAC7CX,+BAA+B,CAACJ,MAAM,CAAC,GACvCqB,uBAAuB;IACzB,CAAC,MAAM,IAAItB,KAAK,CAACyB,4CAA4C,EAAE;MAC9DH,uBAAuB,GAAGtB,KAAK,CAACyB,4CAA4C,GAC3E,GAAG,GACHH,uBAAuB;IACzB;EACD;EACA,IAAII,8BAA8B,CAACJ,uBAAuB,EAAEtB,KAAK,CAAC,EAAE;IACnE,OAAOsB,uBAAuB;EAC/B;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,8BAA8BA,CAACJ,uBAAuB,EAAEtB,KAAK,EAAE;EACvE,OAAOH,WAAW,CAACyB,uBAAuB,CAAC,KAAKtB,KAAK,CAAC2B,iBAAiB,CAAC,CAAC;AAC1E", "ignoreList": []}