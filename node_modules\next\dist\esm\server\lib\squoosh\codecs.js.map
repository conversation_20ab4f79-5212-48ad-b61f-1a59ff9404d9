{"version": 3, "sources": ["../../../../src/server/lib/squoosh/codecs.ts"], "names": ["promises", "fsp", "path", "instantiateEmscriptenWasm", "pathify", "mozEnc", "mozEncWasm", "resolve", "__dirname", "mozDec", "mozDecWasm", "webpEnc", "webpEncWasm", "webpDec", "webpDecWasm", "avifEnc", "avifEncWasm", "avifDec", "avifDecWasm", "pngEncDec", "pngEncDecWasm", "pngEncDecInit", "default", "readFile", "oxipng", "oxipngWasm", "oxipngInit", "resize", "resizeWasm", "resizeInit", "rotateWasm", "ImageData", "globalThis", "resizeNameToIndex", "name", "Error", "resizeWithAspect", "input_width", "input_height", "target_width", "target_height", "width", "height", "Math", "round", "preprocessors", "description", "instantiate", "buffer", "method", "premultiply", "linearRGB", "imageData", "cleanup", "defaultOptions", "fit<PERSON><PERSON><PERSON>", "rotate", "numRotations", "degrees", "sameDimensions", "size", "instance", "WebAssembly", "memory", "exports", "additionalPagesNeeded", "ceil", "byteLength", "grow", "view", "Uint8ClampedArray", "set", "slice", "codecs", "mozjpeg", "extension", "detectors", "dec", "enc", "defaultEncoderOptions", "quality", "baseline", "arithmetic", "progressive", "optimize_coding", "smoothing", "color_space", "quant_table", "trellis_multipass", "trellis_opt_zero", "trellis_opt_table", "trellis_loops", "auto_subsample", "chroma_subsample", "separate_chroma_quality", "chroma_quality", "autoOptimize", "option", "min", "max", "webp", "target_size", "target_PSNR", "sns_strength", "filter_strength", "filter_sharpness", "filter_type", "partitions", "segments", "pass", "show_compressed", "preprocessing", "autofilter", "partition_limit", "alpha_compression", "alpha_filtering", "alpha_quality", "lossless", "exact", "image_hint", "emulate_jpeg_size", "thread_level", "low_memory", "near_lossless", "use_delta_palette", "use_sharp_yuv", "avif", "cqLevel", "cqAlphaLevel", "denoiseLevel", "tileColsLog2", "tileRowsLog2", "speed", "subsample", "chromaDeltaQ", "sharpness", "tune", "decode", "encode", "opts", "simplePng", "Uint8Array", "optimise", "level"], "mappings": "AAAA,SAASA,YAAYC,GAAG,QAAQ,KAAI;AACpC,YAAYC,UAAU,OAAM;AAC5B,SAASC,yBAAyB,EAAEC,OAAO,QAAQ,wBAAuB;AAoC1E,aAAa;AACb,OAAOC,YAAY,gCAA+B;AAClD,MAAMC,aAAaJ,KAAKK,OAAO,CAACC,WAAW;AAC3C,aAAa;AACb,OAAOC,YAAY,gCAA+B;AAClD,MAAMC,aAAaR,KAAKK,OAAO,CAACC,WAAW;AAI3C,aAAa;AACb,OAAOG,aAAa,0BAAyB;AAC7C,MAAMC,cAAcV,KAAKK,OAAO,CAACC,WAAW;AAC5C,aAAa;AACb,OAAOK,aAAa,0BAAyB;AAC7C,MAAMC,cAAcZ,KAAKK,OAAO,CAACC,WAAW;AAI5C,aAAa;AACb,OAAOO,aAAa,0BAAyB;AAC7C,MAAMC,cAAcd,KAAKK,OAAO,CAACC,WAAW;AAC5C,aAAa;AACb,OAAOS,aAAa,0BAAyB;AAC7C,MAAMC,cAAchB,KAAKK,OAAO,CAACC,WAAW;AAE5C,MAAM;AACN,aAAa;AACb,YAAYW,eAAe,uBAAsB;AACjD,MAAMC,gBAAgBlB,KAAKK,OAAO,CAACC,WAAW;AAC9C,MAAMa,gBAAgB,IACpBF,UAAUG,OAAO,CAACrB,IAAIsB,QAAQ,CAACnB,QAAQgB;AAEzC,SAAS;AACT,aAAa;AACb,YAAYI,YAAY,0BAAyB;AACjD,MAAMC,aAAavB,KAAKK,OAAO,CAACC,WAAW;AAC3C,MAAMkB,aAAa,IAAMF,OAAOF,OAAO,CAACrB,IAAIsB,QAAQ,CAACnB,QAAQqB;AAE7D,SAAS;AACT,aAAa;AACb,YAAYE,YAAY,6BAA4B;AACpD,MAAMC,aAAa1B,KAAKK,OAAO,CAACC,WAAW;AAC3C,MAAMqB,aAAa,IAAMF,OAAOL,OAAO,CAACrB,IAAIsB,QAAQ,CAACnB,QAAQwB;AAE7D,SAAS;AACT,MAAME,aAAa5B,KAAKK,OAAO,CAACC,WAAW;AAE3C,uDAAuD;AACvD,OAAOuB,eAAe,eACrB;AAACC,WAAmBD,SAAS,GAAGA;AAEjC,SAASE,kBACPC,IAAqD;IAErD,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,MAAMC,MAAM,CAAC,0BAA0B,EAAED,KAAK,CAAC,CAAC;IACpD;AACF;AAEA,SAASE,iBAAiB,EACxBC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,aAAa,EACU;IACvB,IAAI,CAACD,gBAAgB,CAACC,eAAe;QACnC,MAAML,MAAM;IACd;IAEA,IAAII,gBAAgBC,eAAe;QACjC,OAAO;YAAEC,OAAOF;YAAcG,QAAQF;QAAc;IACtD;IAEA,IAAI,CAACD,cAAc;QACjB,OAAO;YACLE,OAAOE,KAAKC,KAAK,CAAC,AAACP,cAAcC,eAAgBE;YACjDE,QAAQF;QACV;IACF;IAEA,OAAO;QACLC,OAAOF;QACPG,QAAQC,KAAKC,KAAK,CAAC,AAACN,eAAeD,cAAeE;IACpD;AACF;AAEA,OAAO,MAAMM,gBAAgB;IAC3BlB,QAAQ;QACNO,MAAM;QACNY,aAAa;QACbC,aAAa;YACX,MAAMlB;YACN,OAAO,CACLmB,QACAX,aACAC,cACA,EAAEG,KAAK,EAAEC,MAAM,EAAEO,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAiB;gBAE9D,CAAA,EAAEV,KAAK,EAAEC,MAAM,EAAE,GAAGN,iBAAiB;oBACrCC;oBACAC;oBACAC,cAAcE;oBACdD,eAAeE;gBACjB,EAAC;gBACD,MAAMU,YAAY,IAAIrB,UACpBJ,OAAOA,MAAM,CACXqB,QACAX,aACAC,cACAG,OACAC,QACAT,kBAAkBgB,SAClBC,aACAC,YAEFV,OACAC;gBAEFf,OAAO0B,OAAO;gBACd,OAAOD;YACT;QACF;QACAE,gBAAgB;YACdL,QAAQ;YACRM,WAAW;YACXL,aAAa;YACbC,WAAW;QACb;IACF;IACAK,QAAQ;QACNtB,MAAM;QACNY,aAAa;QACbC,aAAa;YACX,OAAO,OACLC,QACAP,OACAC,QACA,EAAEe,YAAY,EAAiB;gBAE/B,MAAMC,UAAU,AAACD,eAAe,KAAM;gBACtC,MAAME,iBAAiBD,YAAY,KAAKA,YAAY;gBACpD,MAAME,OAAOnB,QAAQC,SAAS;gBAC9B,MAAMmB,WAAW,AACf,CAAA,MAAMC,YAAYf,WAAW,CAAC,MAAM9C,IAAIsB,QAAQ,CAACnB,QAAQ0B,aAAY,EACrE+B,QAAQ;gBACV,MAAM,EAAEE,MAAM,EAAE,GAAGF,SAASG,OAAO;gBACnC,MAAMC,wBAAwBtB,KAAKuB,IAAI,CACrC,AAACN,CAAAA,OAAO,IAAIG,OAAOf,MAAM,CAACmB,UAAU,GAAG,CAAA,IAAM,CAAA,KAAK,IAAG;gBAEvD,IAAIF,wBAAwB,GAAG;oBAC7BF,OAAOK,IAAI,CAACH;gBACd;gBACA,MAAMI,OAAO,IAAIC,kBAAkBP,OAAOf,MAAM;gBAChDqB,KAAKE,GAAG,CAACvB,QAAQ;gBACjBa,SAASG,OAAO,CAACR,MAAM,CAACf,OAAOC,QAAQgB;gBACvC,OAAO,IAAI3B,UACTsC,KAAKG,KAAK,CAACZ,OAAO,GAAGA,OAAO,IAAI,IAChCD,iBAAiBlB,QAAQC,QACzBiB,iBAAiBjB,SAASD;YAE9B;QACF;QACAa,gBAAgB;YACdG,cAAc;QAChB;IACF;AACF,EAAU;AAEV,OAAO,MAAMgB,SAAS;IACpBC,SAAS;QACPxC,MAAM;QACNyC,WAAW;QACXC,WAAW;YAAC;SAAgB;QAC5BC,KAAK,IACH1E,0BAA0BM,QAA+BC;QAC3DoE,KAAK,IACH3E,0BACEE,QACAC;QAEJyE,uBAAuB;YACrBC,SAAS;YACTC,UAAU;YACVC,YAAY;YACZC,aAAa;YACbC,iBAAiB;YACjBC,WAAW;YACXC,aAAa,EAAE,OAAO;YACtBC,aAAa;YACbC,mBAAmB;YACnBC,kBAAkB;YAClBC,mBAAmB;YACnBC,eAAe;YACfC,gBAAgB;YAChBC,kBAAkB;YAClBC,yBAAyB;YACzBC,gBAAgB;QAClB;QACAC,cAAc;YACZC,QAAQ;YACRC,KAAK;YACLC,KAAK;QACP;IACF;IACAC,MAAM;QACJlE,MAAM;QACNyC,WAAW;QACXC,WAAW;YAAC;SAAyB;QACrCC,KAAK,IACH1E,0BAA0BU,SAAgCC;QAC5DgE,KAAK,IACH3E,0BACEQ,SACAC;QAEJmE,uBAAuB;YACrBC,SAAS;YACTqB,aAAa;YACbC,aAAa;YACbrD,QAAQ;YACRsD,cAAc;YACdC,iBAAiB;YACjBC,kBAAkB;YAClBC,aAAa;YACbC,YAAY;YACZC,UAAU;YACVC,MAAM;YACNC,iBAAiB;YACjBC,eAAe;YACfC,YAAY;YACZC,iBAAiB;YACjBC,mBAAmB;YACnBC,iBAAiB;YACjBC,eAAe;YACfC,UAAU;YACVC,OAAO;YACPC,YAAY;YACZC,mBAAmB;YACnBC,cAAc;YACdC,YAAY;YACZC,eAAe;YACfC,mBAAmB;YACnBC,eAAe;QACjB;QACA7B,cAAc;YACZC,QAAQ;YACRC,KAAK;YACLC,KAAK;QACP;IACF;IACA2B,MAAM;QACJ5F,MAAM;QACNyC,WAAW;QACX,4CAA4C;QAC5CC,WAAW;YAAC;SAAyC;QACrDC,KAAK,IACH1E,0BAA0Bc,SAAgCC;QAC5D4D,KAAK;YACH,OAAO3E,0BACLY,SACAC;QAEJ;QACA+D,uBAAuB;YACrBgD,SAAS;YACTC,cAAc,CAAC;YACfC,cAAc;YACdC,cAAc;YACdC,cAAc;YACdC,OAAO;YACPC,WAAW;YACXC,cAAc;YACdC,WAAW;YACXC,MAAM,EAAE,iBAAiB;QAC3B;QACAxC,cAAc;YACZC,QAAQ;YACRC,KAAK;YACLC,KAAK;QACP;IACF;IACA3E,QAAQ;QACNU,MAAM;QACNyC,WAAW;QACX,4CAA4C;QAC5CC,WAAW;YAAC;SAA2B;QACvCC,KAAK;YACH,MAAMxD;YACN,OAAO;gBACLoH,QAAQ,CAACzF;oBACP,MAAMI,YAAYjC,UAAUsH,MAAM,CAACzF;oBACnC7B,UAAUkC,OAAO;oBACjB,OAAOD;gBACT;YACF;QACF;QACA0B,KAAK;YACH,MAAMzD;YACN,MAAMK;YACN,OAAO;gBACLgH,QAAQ,CACN1F,QACAP,OACAC,QACAiG;oBAEA,MAAMC,YAAYzH,UAAUuH,MAAM,CAChC,IAAIG,WAAW7F,SACfP,OACAC;oBAEF,MAAMU,YAAY5B,OAAOsH,QAAQ,CAACF,WAAWD,KAAKI,KAAK,EAAE;oBACzDvH,OAAO6B,OAAO;oBACd,OAAOD;gBACT;YACF;QACF;QACA2B,uBAAuB;YACrBgE,OAAO;QACT;QACA/C,cAAc;YACZC,QAAQ;YACRC,KAAK;YACLC,KAAK;QACP;IACF;AACF,EAAU"}