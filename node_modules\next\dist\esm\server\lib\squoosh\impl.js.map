{"version": 3, "sources": ["../../../../src/server/lib/squoosh/impl.ts"], "names": ["codecs", "supportedFormats", "preprocessors", "ImageData", "decodeBuffer", "_buffer", "Object", "buffer", "<PERSON><PERSON><PERSON>", "from", "firstChunk", "slice", "firstChunkString", "Array", "map", "v", "String", "fromCodePoint", "join", "key", "entries", "find", "detectors", "some", "detector", "exec", "Error", "encoder", "mod", "dec", "rgba", "decode", "Uint8Array", "rotate", "image", "numRotations", "m", "instantiate", "data", "width", "height", "resize", "p", "defaultOptions", "encodeJpeg", "quality", "e", "enc", "r", "encode", "defaultEncoderOptions", "encodeWebp", "encodeAvif", "val", "autoOptimize", "min", "cqLevel", "Math", "round", "encodePng"], "mappings": "AAAA,SAASA,UAAUC,gBAAgB,EAAEC,aAAa,QAAQ,WAAU;AACpE,OAAOC,eAAe,eAAc;AAIpC,OAAO,eAAeC,aACpBC,OAA4B;QAOhBC;IALZ,MAAMC,SAASC,OAAOC,IAAI,CAACJ;IAC3B,MAAMK,aAAaH,OAAOI,KAAK,CAAC,GAAG;IACnC,MAAMC,mBAAmBC,MAAMJ,IAAI,CAACC,YACjCI,GAAG,CAAC,CAACC,IAAMC,OAAOC,aAAa,CAACF,IAChCG,IAAI,CAAC;IACR,MAAMC,OAAMb,uBAAAA,OAAOc,OAAO,CAACnB,kBAAkBoB,IAAI,CAAC,CAAC,GAAG,EAAEC,SAAS,EAAE,CAAC,GAClEA,UAAUC,IAAI,CAAC,CAACC,WAAaA,SAASC,IAAI,CAACb,wCADjCN,oBAET,CAAC,EAAE;IACN,IAAI,CAACa,KAAK;QACR,MAAMO,MAAM,CAAC,gCAAgC,CAAC;IAChD;IACA,MAAMC,UAAU1B,gBAAgB,CAACkB,IAAI;IACrC,MAAMS,MAAM,MAAMD,QAAQE,GAAG;IAC7B,MAAMC,OAAOF,IAAIG,MAAM,CAAC,IAAIC,WAAWzB;IACvC,OAAOuB;AACT;AAEA,OAAO,eAAeG,OACpBC,KAAgB,EAChBC,YAAoB;IAEpBD,QAAQ/B,UAAUM,IAAI,CAACyB;IAEvB,MAAME,IAAI,MAAMlC,aAAa,CAAC,SAAS,CAACmC,WAAW;IACnD,OAAO,MAAMD,EAAEF,MAAMI,IAAI,EAAEJ,MAAMK,KAAK,EAAEL,MAAMM,MAAM,EAAE;QAAEL;IAAa;AACvE;AAQA,OAAO,eAAeM,OAAO,EAAEP,KAAK,EAAEK,KAAK,EAAEC,MAAM,EAAc;IAC/DN,QAAQ/B,UAAUM,IAAI,CAACyB;IAEvB,MAAMQ,IAAIxC,aAAa,CAAC,SAAS;IACjC,MAAMkC,IAAI,MAAMM,EAAEL,WAAW;IAC7B,OAAO,MAAMD,EAAEF,MAAMI,IAAI,EAAEJ,MAAMK,KAAK,EAAEL,MAAMM,MAAM,EAAE;QACpD,GAAGE,EAAEC,cAAc;QACnBJ;QACAC;IACF;AACF;AAEA,OAAO,eAAeI,WACpBV,KAAgB,EAChB,EAAEW,OAAO,EAAuB;IAEhCX,QAAQ/B,UAAUM,IAAI,CAACyB;IAEvB,MAAMY,IAAI7C,gBAAgB,CAAC,UAAU;IACrC,MAAMmC,IAAI,MAAMU,EAAEC,GAAG;IACrB,MAAMC,IAAI,MAAMZ,EAAEa,MAAM,CAACf,MAAMI,IAAI,EAAEJ,MAAMK,KAAK,EAAEL,MAAMM,MAAM,EAAE;QAC9D,GAAGM,EAAEI,qBAAqB;QAC1BL;IACF;IACA,OAAOrC,OAAOC,IAAI,CAACuC;AACrB;AAEA,OAAO,eAAeG,WACpBjB,KAAgB,EAChB,EAAEW,OAAO,EAAuB;IAEhCX,QAAQ/B,UAAUM,IAAI,CAACyB;IAEvB,MAAMY,IAAI7C,gBAAgB,CAAC,OAAO;IAClC,MAAMmC,IAAI,MAAMU,EAAEC,GAAG;IACrB,MAAMC,IAAI,MAAMZ,EAAEa,MAAM,CAACf,MAAMI,IAAI,EAAEJ,MAAMK,KAAK,EAAEL,MAAMM,MAAM,EAAE;QAC9D,GAAGM,EAAEI,qBAAqB;QAC1BL;IACF;IACA,OAAOrC,OAAOC,IAAI,CAACuC;AACrB;AAEA,OAAO,eAAeI,WACpBlB,KAAgB,EAChB,EAAEW,OAAO,EAAuB;IAEhCX,QAAQ/B,UAAUM,IAAI,CAACyB;IAEvB,MAAMY,IAAI7C,gBAAgB,CAAC,OAAO;IAClC,MAAMmC,IAAI,MAAMU,EAAEC,GAAG;IACrB,MAAMM,MAAMP,EAAEQ,YAAY,CAACC,GAAG,IAAI;IAClC,MAAMP,IAAI,MAAMZ,EAAEa,MAAM,CAACf,MAAMI,IAAI,EAAEJ,MAAMK,KAAK,EAAEL,MAAMM,MAAM,EAAE;QAC9D,GAAGM,EAAEI,qBAAqB;QAC1B,8DAA8D;QAC9D,qDAAqD;QACrDM,SAASC,KAAKC,KAAK,CAACL,MAAM,AAACR,UAAU,MAAOQ;IAC9C;IACA,OAAO7C,OAAOC,IAAI,CAACuC;AACrB;AAEA,OAAO,eAAeW,UACpBzB,KAAgB;IAEhBA,QAAQ/B,UAAUM,IAAI,CAACyB;IAEvB,MAAMY,IAAI7C,gBAAgB,CAAC,SAAS;IACpC,MAAMmC,IAAI,MAAMU,EAAEC,GAAG;IACrB,MAAMC,IAAI,MAAMZ,EAAEa,MAAM,CAACf,MAAMI,IAAI,EAAEJ,MAAMK,KAAK,EAAEL,MAAMM,MAAM,EAAE;QAC9D,GAAGM,EAAEI,qBAAqB;IAC5B;IACA,OAAO1C,OAAOC,IAAI,CAACuC;AACrB"}