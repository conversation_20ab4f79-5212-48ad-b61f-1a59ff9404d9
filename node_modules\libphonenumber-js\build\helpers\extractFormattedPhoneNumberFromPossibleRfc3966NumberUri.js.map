{"version": 3, "file": "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js", "names": ["_extractPhoneContext", "_interopRequireWildcard", "require", "_ParseError", "_interopRequireDefault", "e", "__esModule", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "_typeof", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri", "numberToParse", "_ref", "extractFormattedPhoneNumber", "phoneContext", "extractPhoneContext", "isPhoneContextValid", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phoneNumberString", "char<PERSON>t", "PLUS_SIGN", "indexOfRfc3966Prefix", "indexOf", "RFC3966_PREFIX_", "indexOfNationalNumber", "length", "indexOfPhoneContext", "RFC3966_PHONE_CONTEXT_", "substring", "indexOfIsdn", "RFC3966_ISDN_SUBADDRESS_"], "sources": ["../../source/helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js"], "sourcesContent": ["import extractPhoneContext, {\r\n\tisPhoneContextValid,\r\n\tPLUS_SIGN,\r\n\tRFC3966_PREFIX_,\r\n\tRFC3966_PHONE_CONTEXT_,\r\n\tRFC3966_ISDN_SUBADDRESS_\r\n} from './extractPhoneContext.js'\r\n\r\nimport ParseError from '../ParseError.js'\r\n\r\n/**\r\n * @param  {string} numberToParse\r\n * @param  {string} nationalNumber\r\n * @return {}\r\n */\r\nexport default function extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(numberToParse, {\r\n\textractFormattedPhoneNumber\r\n}) {\r\n\tconst phoneContext = extractPhoneContext(numberToParse)\r\n\tif (!isPhoneContextValid(phoneContext)) {\r\n\t\tthrow new ParseError('NOT_A_NUMBER')\r\n\t}\r\n\r\n\tlet phoneNumberString\r\n\r\n\tif (phoneContext === null) {\r\n\t\t// Extract a possible number from the string passed in.\r\n\t\t// (this strips leading characters that could not be the start of a phone number)\r\n\t\tphoneNumberString = extractFormattedPhoneNumber(numberToParse) || ''\r\n\t} else {\r\n\t\tphoneNumberString = ''\r\n\r\n\t\t// If the phone context contains a phone number prefix, we need to capture\r\n\t\t// it, whereas domains will be ignored.\r\n\t\tif (phoneContext.charAt(0) === PLUS_SIGN) {\r\n\t\t\tphoneNumberString += phoneContext\r\n\t\t}\r\n\r\n\t\t// Now append everything between the \"tel:\" prefix and the phone-context.\r\n\t\t// This should include the national number, an optional extension or\r\n\t\t// isdn-subaddress component. Note we also handle the case when \"tel:\" is\r\n\t\t// missing, as we have seen in some of the phone number inputs.\r\n\t\t// In that case, we append everything from the beginning.\r\n\t\tconst indexOfRfc3966Prefix = numberToParse.indexOf(RFC3966_PREFIX_)\r\n\t\tlet indexOfNationalNumber\r\n\t\t// RFC 3966 \"tel:\" prefix is preset at this stage because\r\n\t\t// `isPhoneContextValid()` requires it to be present.\r\n\t\t/* istanbul ignore else */\r\n\t\tif (indexOfRfc3966Prefix >= 0) {\r\n\t\t\tindexOfNationalNumber = indexOfRfc3966Prefix + RFC3966_PREFIX_.length\r\n\t\t} else {\r\n\t\t\tindexOfNationalNumber = 0\r\n\t\t}\r\n\t\tconst indexOfPhoneContext = numberToParse.indexOf(RFC3966_PHONE_CONTEXT_)\r\n\t\tphoneNumberString += numberToParse.substring(indexOfNationalNumber, indexOfPhoneContext)\r\n\t}\r\n\r\n\t// Delete the isdn-subaddress and everything after it if it is present.\r\n\t// Note extension won't appear at the same time with isdn-subaddress\r\n\t// according to paragraph 5.3 of the RFC3966 spec.\r\n\tconst indexOfIsdn = phoneNumberString.indexOf(RFC3966_ISDN_SUBADDRESS_)\r\n\tif (indexOfIsdn > 0) {\r\n\t\tphoneNumberString = phoneNumberString.substring(0, indexOfIsdn)\r\n\t}\r\n\t// If both phone context and isdn-subaddress are absent but other\r\n\t// parameters are present, the parameters are left in nationalNumber.\r\n\t// This is because we are concerned about deleting content from a potential\r\n\t// number string when there is no strong evidence that the number is\r\n\t// actually written in RFC3966.\r\n\r\n\tif (phoneNumberString !== '') {\r\n\t\treturn phoneNumberString\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA,IAAAA,oBAAA,GAAAC,uBAAA,CAAAC,OAAA;AAQA,IAAAC,WAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAyC,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,mBAAAT,CAAA,iBAAAA,CAAA,gBAAAU,OAAA,CAAAV,CAAA,0BAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAE,CAAA;AAEzC;AACA;AACA;AACA;AACA;AACe,SAASkB,uDAAuDA,CAACC,aAAa,EAAAC,IAAA,EAE1F;EAAA,IADFC,2BAA2B,GAAAD,IAAA,CAA3BC,2BAA2B;EAE3B,IAAMC,YAAY,GAAG,IAAAC,+BAAmB,EAACJ,aAAa,CAAC;EACvD,IAAI,CAAC,IAAAK,wCAAmB,EAACF,YAAY,CAAC,EAAE;IACvC,MAAM,IAAIG,sBAAU,CAAC,cAAc,CAAC;EACrC;EAEA,IAAIC,iBAAiB;EAErB,IAAIJ,YAAY,KAAK,IAAI,EAAE;IAC1B;IACA;IACAI,iBAAiB,GAAGL,2BAA2B,CAACF,aAAa,CAAC,IAAI,EAAE;EACrE,CAAC,MAAM;IACNO,iBAAiB,GAAG,EAAE;;IAEtB;IACA;IACA,IAAIJ,YAAY,CAACK,MAAM,CAAC,CAAC,CAAC,KAAKC,8BAAS,EAAE;MACzCF,iBAAiB,IAAIJ,YAAY;IAClC;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAMO,oBAAoB,GAAGV,aAAa,CAACW,OAAO,CAACC,oCAAe,CAAC;IACnE,IAAIC,qBAAqB;IACzB;IACA;IACA;IACA,IAAIH,oBAAoB,IAAI,CAAC,EAAE;MAC9BG,qBAAqB,GAAGH,oBAAoB,GAAGE,oCAAe,CAACE,MAAM;IACtE,CAAC,MAAM;MACND,qBAAqB,GAAG,CAAC;IAC1B;IACA,IAAME,mBAAmB,GAAGf,aAAa,CAACW,OAAO,CAACK,2CAAsB,CAAC;IACzET,iBAAiB,IAAIP,aAAa,CAACiB,SAAS,CAACJ,qBAAqB,EAAEE,mBAAmB,CAAC;EACzF;;EAEA;EACA;EACA;EACA,IAAMG,WAAW,GAAGX,iBAAiB,CAACI,OAAO,CAACQ,6CAAwB,CAAC;EACvE,IAAID,WAAW,GAAG,CAAC,EAAE;IACpBX,iBAAiB,GAAGA,iBAAiB,CAACU,SAAS,CAAC,CAAC,EAAEC,WAAW,CAAC;EAChE;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAIX,iBAAiB,KAAK,EAAE,EAAE;IAC7B,OAAOA,iBAAiB;EACzB;AACD", "ignoreList": []}