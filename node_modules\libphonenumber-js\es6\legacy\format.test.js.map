{"version": 3, "file": "format.test.js", "names": ["metadata", "type", "_formatNumber", "formatNumber", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "apply", "describe", "it", "expect", "to", "equal", "phone", "countryCallingCode", "options", "formatExtension", "number", "extension", "concat", "country", "ext", "nationalPrefix", "thrower", "be", "undefined", "fromCountry", "humanReadable"], "sources": ["../../source/legacy/format.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' with { type: 'json' }\r\nimport _formatNumber from './format.js'\r\n\r\nfunction formatNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _formatNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('format', () => {\r\n\tit('should work with the first argument being a E.164 number', () => {\r\n\t\texpect(formatNumber('+12133734253', 'NATIONAL')).to.equal('(*************')\r\n\t\texpect(formatNumber('+12133734253', 'INTERNATIONAL')).to.equal('****** 373 4253')\r\n\r\n\t\t// Invalid number.\r\n\t\texpect(formatNumber('+12111111111', 'NATIONAL')).to.equal('(*************')\r\n\r\n\t\t// Formatting invalid E.164 numbers.\r\n\t\texpect(formatNumber('+11111', 'INTERNATIONAL')).to.equal('+1 1111')\r\n\t\texpect(formatNumber('+11111', 'NATIONAL')).to.equal('1111')\r\n\t})\r\n\r\n\tit('should work with the first object argument expanded', () => {\r\n\t\texpect(formatNumber('2133734253', 'US', 'NATIONAL')).to.equal('(*************')\r\n\t\texpect(formatNumber('2133734253', 'US', 'INTERNATIONAL')).to.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should support legacy \"National\" / \"International\" formats', () => {\r\n\t\texpect(formatNumber('2133734253', 'US', 'National')).to.equal('(*************')\r\n\t\texpect(formatNumber('2133734253', 'US', 'International')).to.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should format using formats with no leading digits (`format.leadingDigitsPatterns().length === 0`)', () => {\r\n\t\texpect(\r\n            formatNumber({ phone: '12345678901', countryCallingCode: 888 }, 'INTERNATIONAL')\r\n        ).to.equal('+888 123 456 78901')\r\n\t})\r\n\r\n\tit('should sort out the arguments', () => {\r\n\t\tconst options = {\r\n\t\t\tformatExtension: (number, extension) => `${number} доб. ${extension}`\r\n\t\t}\r\n\r\n\t\texpect(formatNumber({\r\n\t\t\tphone   : '8005553535',\r\n\t\t\tcountry : 'RU',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'NATIONAL', options)).to.equal('8 (800) 555-35-35 доб. 123')\r\n\r\n\t\t// Parse number from string.\r\n\t\texpect(formatNumber('+78005553535', 'NATIONAL', options)).to.equal('8 (800) 555-35-35')\r\n\t\texpect(formatNumber('8005553535', 'RU', 'NATIONAL', options)).to.equal('8 (800) 555-35-35')\r\n\t})\r\n\r\n\tit('should format with national prefix when specifically instructed', () => {\r\n\t\t// With national prefix.\r\n\t\texpect(formatNumber('88005553535', 'RU', 'NATIONAL')).to.equal('8 (800) 555-35-35')\r\n\t\t// Without national prefix via an explicitly set option.\r\n\t\texpect(formatNumber('88005553535', 'RU', 'NATIONAL', { nationalPrefix: false })).to.equal('800 555-35-35')\r\n\t})\r\n\r\n\tit('should format valid phone numbers', () => {\r\n\t\t// Switzerland\r\n\t\texpect(formatNumber({ country: 'CH', phone: '446681800' }, 'INTERNATIONAL')).to.equal('+41 44 668 18 00')\r\n\t\texpect(formatNumber({ country: 'CH', phone: '446681800' }, 'E.164')).to.equal('+41446681800')\r\n\t\texpect(formatNumber({ country: 'CH', phone: '446681800' }, 'RFC3966')).to.equal('tel:+41446681800')\r\n\t\texpect(formatNumber({ country: 'CH', phone: '446681800' }, 'NATIONAL')).to.equal('044 668 18 00')\r\n\r\n\t\t// France\r\n\t\texpect(formatNumber({ country: 'FR', phone: '169454850' }, 'NATIONAL')).to.equal('01 69 45 48 50')\r\n\r\n\t\t// Kazakhstan\r\n\t\texpect(formatNumber('****** 211 1111', 'NATIONAL')).to.equal('8 (*************')\r\n\t})\r\n\r\n\tit('should format national numbers with national prefix even if it\\'s optional', () => {\r\n\t\t// Russia\r\n\t\texpect(formatNumber({ country: 'RU', phone: '9991234567' }, 'NATIONAL')).to.equal('8 (999) 123-45-67')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No phone number\r\n\t\texpect(formatNumber('', 'RU', 'INTERNATIONAL')).to.equal('')\r\n\t\texpect(formatNumber('', 'RU', 'NATIONAL')).to.equal('')\r\n\r\n\t\texpect(formatNumber({ country: 'RU', phone: '' }, 'INTERNATIONAL')).to.equal('+7')\r\n\t\texpect(formatNumber({ country: 'RU', phone: '' }, 'NATIONAL')).to.equal('')\r\n\r\n\t\t// No suitable format\r\n\t\texpect(formatNumber('+121337342530', 'US', 'NATIONAL')).to.equal('21337342530')\r\n\t\t// No suitable format (leading digits mismatch)\r\n\t\texpect(formatNumber('28199999', 'AD', 'NATIONAL')).to.equal('28199999')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => formatNumber(89150000000, 'RU', 'NATIONAL')\r\n\t\texpect(thrower).to.throw(\r\n            'A phone number must either be a string or an object of shape { phone, [country] }.'\r\n        )\r\n\r\n\t\t// No metadata for country\r\n\t\texpect(() => formatNumber('+121337342530', 'USA', 'NATIONAL')).to.throw('Unknown country')\r\n\t\texpect(() => formatNumber('21337342530', 'USA', 'NATIONAL')).to.throw('Unknown country')\r\n\r\n\t\t// No format type\r\n\t\tthrower = () => formatNumber('+123')\r\n\t\texpect(thrower).to.throw('`format` argument not passed')\r\n\r\n\t\t// Unknown format type\r\n\t\tthrower = () => formatNumber('123', 'US', 'Gay')\r\n\t\texpect(thrower).to.throw('Unknown \"format\" argument')\r\n\r\n\t\t// No metadata\r\n\t\tthrower = () => _formatNumber('123', 'US', 'E.164')\r\n\t\texpect(thrower).to.throw('`metadata`')\r\n\r\n\t\t// No formats\r\n\t\texpect(formatNumber('012345', 'AC', 'NATIONAL')).to.equal('012345')\r\n\r\n\t\t// No `fromCountry` for `IDD` format.\r\n\t\texpect(formatNumber('+78005553535', 'IDD')).to.be.undefined\r\n\r\n\t\t// `fromCountry` has no default IDD prefix.\r\n\t\texpect(formatNumber('+78005553535', 'IDD', { fromCountry: 'BO' })).to.be.undefined\r\n\r\n\t\t// No such country.\r\n\t\texpect(() => formatNumber({ phone: '123', country: 'USA' }, 'NATIONAL')).to.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should format phone number extensions', () => {\r\n\t\t// National\r\n\t\texpect(formatNumber({\r\n\t\t\tcountry: 'US',\r\n\t\t\tphone: '2133734253',\r\n\t\t\text: '123'\r\n\t\t},\r\n\t\t'NATIONAL')).to.equal('(************* ext. 123')\r\n\r\n\t\t// International\r\n\t\texpect(formatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL')).to.equal('****** 373 4253 ext. 123')\r\n\r\n\t\t// International\r\n\t\texpect(formatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL')).to.equal('****** 373 4253 ext. 123')\r\n\r\n\t\t// E.164\r\n\t\texpect(formatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'E.164')).to.equal('+12133734253')\r\n\r\n\t\t// RFC3966\r\n\t\texpect(formatNumber({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2133734253',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'RFC3966')).to.equal('tel:+12133734253;ext=123')\r\n\r\n\t\t// Custom ext prefix.\r\n\t\texpect(formatNumber({\r\n\t\t\tcountry : 'GB',\r\n\t\t\tphone   : '7912345678',\r\n\t\t\text     : '123'\r\n\t\t},\r\n\t\t'INTERNATIONAL')).to.equal('+44 7912 345678 x123')\r\n\t})\r\n\r\n\tit('should work with Argentina numbers', () => {\r\n\t\t// The same mobile number is written differently\r\n\t\t// in different formats in Argentina:\r\n\t\t// `9` gets prepended in international format.\r\n\t\texpect(formatNumber({ country: 'AR', phone: '3435551212' }, 'INTERNATIONAL')).to.equal('+54 3435 55 1212')\r\n\t\texpect(formatNumber({ country: 'AR', phone: '3435551212' }, 'NATIONAL')).to.equal('03435 55-1212')\r\n\t})\r\n\r\n\tit('should work with Mexico numbers', () => {\r\n\t\t// Fixed line.\r\n\t\texpect(formatNumber({ country: 'MX', phone: '4499780001' }, 'INTERNATIONAL')).to.equal('+52 ************')\r\n\t\texpect(formatNumber({ country: 'MX', phone: '4499780001' }, 'NATIONAL')).to.equal('************')\r\n\t\t\t// or '(449)978-0001'.\r\n\t\t// Mobile.\r\n\t\t// `1` is prepended before area code to mobile numbers in international format.\r\n\t\texpect(formatNumber({ country: 'MX', phone: '3312345678' }, 'INTERNATIONAL')).to.equal('+52 33 1234 5678')\r\n\t\texpect(formatNumber({ country: 'MX', phone: '3312345678' }, 'NATIONAL')).to.equal('33 1234 5678')\r\n\t\t\t// or '045 33 1234-5678'.\r\n\t})\r\n\r\n\tit('should format possible numbers', () => {\r\n\t\texpect(formatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'E.164')).to.equal('+71111111111')\r\n\r\n\t\texpect(formatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'NATIONAL')).to.equal('1111111111')\r\n\r\n\t\texpect(\r\n            formatNumber({ countryCallingCode: '7', phone: '1111111111' }, 'INTERNATIONAL')\r\n        ).to.equal('*************')\r\n\t})\r\n\r\n\tit('should format IDD-prefixed number', () => {\r\n\t\t// No `fromCountry`.\r\n\t\texpect(formatNumber('+78005553535', 'IDD')).to.be.undefined\r\n\r\n\t\t// No default IDD prefix.\r\n\t\texpect(formatNumber('+78005553535', 'IDD', { fromCountry: 'BO' })).to.be.undefined\r\n\r\n\t\t// Same country calling code.\r\n\t\texpect(\r\n            formatNumber('+12133734253', 'IDD', { fromCountry: 'CA', humanReadable: true })\r\n        ).to.equal('1 (*************')\r\n\t\texpect(\r\n            formatNumber('+78005553535', 'IDD', { fromCountry: 'KZ', humanReadable: true })\r\n        ).to.equal('8 (800) 555-35-35')\r\n\r\n\t\t// formatNumber('+78005553535', 'IDD', { fromCountry: 'US' }).should.equal('01178005553535')\r\n\t\texpect(\r\n            formatNumber('+78005553535', 'IDD', { fromCountry: 'US', humanReadable: true })\r\n        ).to.equal('011 7 800 555 35 35')\r\n\t})\r\n\r\n\tit('should format non-geographic numbering plan phone numbers', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/323\r\n\t\texpect(formatNumber('+870773111632', 'INTERNATIONAL')).to.equal('+870 773 111 632')\r\n\t\texpect(formatNumber('+870773111632', 'NATIONAL')).to.equal('773 111 632')\r\n\t})\r\n\r\n\tit('should use the default IDD prefix when formatting a phone number', () => {\r\n\t\t// Testing preferred international prefixes with ~ are supported.\r\n\t\t// (\"~\" designates waiting on a line until proceeding with the input).\r\n\t\texpect(formatNumber('+390236618300', 'IDD', { fromCountry: 'BY' })).to.equal('8~10 39 02 3661 8300')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,yBAAyB,QAAQC,IAAI,EAAE,MAAM;AAClE,OAAOC,aAAa,MAAM,aAAa;AAEvC,SAASC,YAAYA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAClCF,UAAU,CAACG,IAAI,CAACV,QAAQ,CAAC;EACzB,OAAOE,aAAa,CAACS,KAAK,CAAC,IAAI,EAAEJ,UAAU,CAAC;AAC7C;AAEAK,QAAQ,CAAC,QAAQ,EAAE,YAAM;EACxBC,EAAE,CAAC,0DAA0D,EAAE,YAAM;IACpEC,MAAM,CAACX,YAAY,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC3EF,MAAM,CAACX,YAAY,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;;IAEjF;IACAF,MAAM,CAACX,YAAY,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;;IAE3E;IACAF,MAAM,CAACX,YAAY,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IACnEF,MAAM,CAACX,YAAY,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;EAC5D,CAAC,CAAC;EAEFH,EAAE,CAAC,qDAAqD,EAAE,YAAM;IAC/DC,MAAM,CAACX,YAAY,CAAC,YAAY,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC/EF,MAAM,CAACX,YAAY,CAAC,YAAY,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EACtF,CAAC,CAAC;EAEFH,EAAE,CAAC,4DAA4D,EAAE,YAAM;IACtEC,MAAM,CAACX,YAAY,CAAC,YAAY,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC/EF,MAAM,CAACX,YAAY,CAAC,YAAY,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EACtF,CAAC,CAAC;EAEFH,EAAE,CAAC,oGAAoG,EAAE,YAAM;IAC9GC,MAAM,CACIX,YAAY,CAAC;MAAEc,KAAK,EAAE,aAAa;MAAEC,kBAAkB,EAAE;IAAI,CAAC,EAAE,eAAe,CACnF,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,oBAAoB,CAAC;EACvC,CAAC,CAAC;EAEFH,EAAE,CAAC,+BAA+B,EAAE,YAAM;IACzC,IAAMM,OAAO,GAAG;MACfC,eAAe,EAAE,SAAjBA,eAAeA,CAAGC,MAAM,EAAEC,SAAS;QAAA,UAAAC,MAAA,CAAQF,MAAM,2BAAAE,MAAA,CAASD,SAAS;MAAA;IACpE,CAAC;IAEDR,MAAM,CAACX,YAAY,CAAC;MACnBc,KAAK,EAAK,YAAY;MACtBO,OAAO,EAAG,IAAI;MACdC,GAAG,EAAO;IACX,CAAC,EACD,UAAU,EAAEN,OAAO,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,4BAA4B,CAAC;;IAE5D;IACAF,MAAM,CAACX,YAAY,CAAC,cAAc,EAAE,UAAU,EAAEgB,OAAO,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACvFF,MAAM,CAACX,YAAY,CAAC,YAAY,EAAE,IAAI,EAAE,UAAU,EAAEgB,OAAO,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;EAC5F,CAAC,CAAC;EAEFH,EAAE,CAAC,iEAAiE,EAAE,YAAM;IAC3E;IACAC,MAAM,CAACX,YAAY,CAAC,aAAa,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACnF;IACAF,MAAM,CAACX,YAAY,CAAC,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE;MAAEuB,cAAc,EAAE;IAAM,CAAC,CAAC,CAAC,CAACX,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC3G,CAAC,CAAC;EAEFH,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7C;IACAC,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAY,CAAC,EAAE,eAAe,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACzGF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAY,CAAC,EAAE,OAAO,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC7FF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAY,CAAC,EAAE,SAAS,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACnGF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAY,CAAC,EAAE,UAAU,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;;IAEjG;IACAF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAY,CAAC,EAAE,UAAU,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;;IAElG;IACAF,MAAM,CAACX,YAAY,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EACjF,CAAC,CAAC;EAEFH,EAAE,CAAC,4EAA4E,EAAE,YAAM;IACtF;IACAC,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAa,CAAC,EAAE,UAAU,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;EACvG,CAAC,CAAC;EAEFH,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC,IAAIc,OAAO;;IAEX;IACAb,MAAM,CAACX,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IAC5DF,MAAM,CAACX,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IAEvDF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAG,CAAC,EAAE,eAAe,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAClFF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAG,CAAC,EAAE,UAAU,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;;IAE3E;IACAF,MAAM,CAACX,YAAY,CAAC,eAAe,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IAC/E;IACAF,MAAM,CAACX,YAAY,CAAC,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;;IAEvE;IACAW,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASxB,YAAY,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC;IAAA;IAC3DW,MAAM,CAACa,OAAO,CAAC,CAACZ,EAAE,SAAM,CACd,oFACJ,CAAC;;IAEP;IACAD,MAAM,CAAC;MAAA,OAAMX,YAAY,CAAC,eAAe,EAAE,KAAK,EAAE,UAAU,CAAC;IAAA,EAAC,CAACY,EAAE,SAAM,CAAC,iBAAiB,CAAC;IAC1FD,MAAM,CAAC;MAAA,OAAMX,YAAY,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC;IAAA,EAAC,CAACY,EAAE,SAAM,CAAC,iBAAiB,CAAC;;IAExF;IACAY,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASxB,YAAY,CAAC,MAAM,CAAC;IAAA;IACpCW,MAAM,CAACa,OAAO,CAAC,CAACZ,EAAE,SAAM,CAAC,8BAA8B,CAAC;;IAExD;IACAY,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASxB,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;IAAA;IAChDW,MAAM,CAACa,OAAO,CAAC,CAACZ,EAAE,SAAM,CAAC,2BAA2B,CAAC;;IAErD;IACAY,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASzB,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;IAAA;IACnDY,MAAM,CAACa,OAAO,CAAC,CAACZ,EAAE,SAAM,CAAC,YAAY,CAAC;;IAEtC;IACAD,MAAM,CAACX,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;;IAEnE;IACAF,MAAM,CAACX,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAACY,EAAE,CAACa,EAAE,CAACC,SAAS;;IAE3D;IACAf,MAAM,CAACX,YAAY,CAAC,cAAc,EAAE,KAAK,EAAE;MAAE2B,WAAW,EAAE;IAAK,CAAC,CAAC,CAAC,CAACf,EAAE,CAACa,EAAE,CAACC,SAAS;;IAElF;IACAf,MAAM,CAAC;MAAA,OAAMX,YAAY,CAAC;QAAEc,KAAK,EAAE,KAAK;QAAEO,OAAO,EAAE;MAAM,CAAC,EAAE,UAAU,CAAC;IAAA,EAAC,CAACT,EAAE,SAAM,CAAC,iBAAiB,CAAC;EACrG,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjD;IACAC,MAAM,CAACX,YAAY,CAAC;MACnBqB,OAAO,EAAE,IAAI;MACbP,KAAK,EAAE,YAAY;MACnBQ,GAAG,EAAE;IACN,CAAC,EACD,UAAU,CAAC,CAAC,CAACV,EAAE,CAACC,KAAK,CAAC,yBAAyB,CAAC;;IAEhD;IACAF,MAAM,CAACX,YAAY,CAAC;MACnBqB,OAAO,EAAG,IAAI;MACdP,KAAK,EAAK,YAAY;MACtBQ,GAAG,EAAO;IACX,CAAC,EACD,eAAe,CAAC,CAAC,CAACV,EAAE,CAACC,KAAK,CAAC,0BAA0B,CAAC;;IAEtD;IACAF,MAAM,CAACX,YAAY,CAAC;MACnBqB,OAAO,EAAG,IAAI;MACdP,KAAK,EAAK,YAAY;MACtBQ,GAAG,EAAO;IACX,CAAC,EACD,eAAe,CAAC,CAAC,CAACV,EAAE,CAACC,KAAK,CAAC,0BAA0B,CAAC;;IAEtD;IACAF,MAAM,CAACX,YAAY,CAAC;MACnBqB,OAAO,EAAG,IAAI;MACdP,KAAK,EAAK,YAAY;MACtBQ,GAAG,EAAO;IACX,CAAC,EACD,OAAO,CAAC,CAAC,CAACV,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;;IAElC;IACAF,MAAM,CAACX,YAAY,CAAC;MACnBqB,OAAO,EAAG,IAAI;MACdP,KAAK,EAAK,YAAY;MACtBQ,GAAG,EAAO;IACX,CAAC,EACD,SAAS,CAAC,CAAC,CAACV,EAAE,CAACC,KAAK,CAAC,0BAA0B,CAAC;;IAEhD;IACAF,MAAM,CAACX,YAAY,CAAC;MACnBqB,OAAO,EAAG,IAAI;MACdP,KAAK,EAAK,YAAY;MACtBQ,GAAG,EAAO;IACX,CAAC,EACD,eAAe,CAAC,CAAC,CAACV,EAAE,CAACC,KAAK,CAAC,sBAAsB,CAAC;EACnD,CAAC,CAAC;EAEFH,EAAE,CAAC,oCAAoC,EAAE,YAAM;IAC9C;IACA;IACA;IACAC,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAa,CAAC,EAAE,eAAe,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IAC1GF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAa,CAAC,EAAE,UAAU,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EACnG,CAAC,CAAC;EAEFH,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3C;IACAC,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAa,CAAC,EAAE,eAAe,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IAC1GF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAa,CAAC,EAAE,UAAU,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAChG;IACD;IACA;IACAF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAa,CAAC,EAAE,eAAe,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IAC1GF,MAAM,CAACX,YAAY,CAAC;MAAEqB,OAAO,EAAE,IAAI;MAAEP,KAAK,EAAE;IAAa,CAAC,EAAE,UAAU,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAChG;EACF,CAAC,CAAC;EAEFH,EAAE,CAAC,gCAAgC,EAAE,YAAM;IAC1CC,MAAM,CAACX,YAAY,CAAC;MAAEe,kBAAkB,EAAE,GAAG;MAAED,KAAK,EAAE;IAAa,CAAC,EAAE,OAAO,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAExGF,MAAM,CAACX,YAAY,CAAC;MAAEe,kBAAkB,EAAE,GAAG;MAAED,KAAK,EAAE;IAAa,CAAC,EAAE,UAAU,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IAEzGF,MAAM,CACIX,YAAY,CAAC;MAAEe,kBAAkB,EAAE,GAAG;MAAED,KAAK,EAAE;IAAa,CAAC,EAAE,eAAe,CAClF,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAClC,CAAC,CAAC;EAEFH,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7C;IACAC,MAAM,CAACX,YAAY,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAACY,EAAE,CAACa,EAAE,CAACC,SAAS;;IAE3D;IACAf,MAAM,CAACX,YAAY,CAAC,cAAc,EAAE,KAAK,EAAE;MAAE2B,WAAW,EAAE;IAAK,CAAC,CAAC,CAAC,CAACf,EAAE,CAACa,EAAE,CAACC,SAAS;;IAElF;IACAf,MAAM,CACIX,YAAY,CAAC,cAAc,EAAE,KAAK,EAAE;MAAE2B,WAAW,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAK,CAAC,CAClF,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACpCF,MAAM,CACIX,YAAY,CAAC,cAAc,EAAE,KAAK,EAAE;MAAE2B,WAAW,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAK,CAAC,CAClF,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;;IAErC;IACAF,MAAM,CACIX,YAAY,CAAC,cAAc,EAAE,KAAK,EAAE;MAAE2B,WAAW,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAK,CAAC,CAClF,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,qBAAqB,CAAC;EACxC,CAAC,CAAC;EAEFH,EAAE,CAAC,2DAA2D,EAAE,YAAM;IACrE;IACAC,MAAM,CAACX,YAAY,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACnFF,MAAM,CAACX,YAAY,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;EAC1E,CAAC,CAAC;EAEFH,EAAE,CAAC,kEAAkE,EAAE,YAAM;IAC5E;IACA;IACAC,MAAM,CAACX,YAAY,CAAC,eAAe,EAAE,KAAK,EAAE;MAAE2B,WAAW,EAAE;IAAK,CAAC,CAAC,CAAC,CAACf,EAAE,CAACC,KAAK,CAAC,sBAAsB,CAAC;EACrG,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}