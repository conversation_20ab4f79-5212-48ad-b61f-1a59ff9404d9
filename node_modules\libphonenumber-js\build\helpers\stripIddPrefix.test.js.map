{"version": 3, "file": "stripIddPrefix.test.js", "names": ["_stripIddPrefix", "_interopRequireDefault", "require", "_metadataMin", "e", "__esModule", "describe", "it", "expect", "stripIddPrefix", "metadata", "to", "equal", "be", "undefined"], "sources": ["../../source/helpers/stripIddPrefix.test.js"], "sourcesContent": ["import stripIddPrefix from './stripIddPrefix.js'\r\n\r\nimport metadata from '../../metadata.min.json' with { type: 'json' }\r\n\r\ndescribe('stripIddPrefix', () => {\r\n\tit('should strip a valid IDD prefix', () => {\r\n\t\texpect(stripIddPrefix('01178005553535', 'US', '1', metadata)).to.equal('78005553535')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (no country calling code)', () => {\r\n\t\texpect(stripIddPrefix('011', 'US', '1', metadata)).to.equal('')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (valid country calling code)', () => {\r\n\t\texpect(stripIddPrefix('0117', 'US', '1', metadata)).to.equal('7')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (not a valid country calling code)', () => {\r\n\t\texpect(stripIddPrefix('0110', 'US', '1', metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAoE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEpEE,QAAQ,CAAC,gBAAgB,EAAE,YAAM;EAChCC,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3CC,MAAM,CAAC,IAAAC,0BAAc,EAAC,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;EACtF,CAAC,CAAC;EAEFL,EAAE,CAAC,2DAA2D,EAAE,YAAM;IACrEC,MAAM,CAAC,IAAAC,0BAAc,EAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;EAChE,CAAC,CAAC;EAEFL,EAAE,CAAC,8DAA8D,EAAE,YAAM;IACxEC,MAAM,CAAC,IAAAC,0BAAc,EAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EAClE,CAAC,CAAC;EAEFL,EAAE,CAAC,oEAAoE,EAAE,YAAM;IAC9EC,MAAM,CAAC,IAAAC,0BAAc,EAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAEC,uBAAQ,CAAC,CAAC,CAACC,EAAE,CAACE,EAAE,CAACC,SAAS;EACpE,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}