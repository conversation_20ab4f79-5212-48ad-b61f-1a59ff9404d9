{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-font-loader/index.ts"], "names": ["path", "bold", "cyan", "loaderUtils", "postcssNextFontPlugin", "promisify", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nextFontLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "callback", "async", "relativeFilePathFromRoot", "import", "functionName", "arguments", "data", "variableName", "JSON", "parse", "resourceQuery", "slice", "test", "err", "Error", "name", "isDev", "isServer", "assetPrefix", "fontLoaderPath", "postcss", "getPostcss", "getOptions", "emitFontFile", "content", "ext", "preload", "isUsingSizeAdjust", "opts", "context", "rootContext", "interpolatedName", "interpolateName", "outputPath", "emitFile", "fontLoader", "require", "default", "css", "fallbackFonts", "adjustFontFallback", "weight", "style", "variable", "resolve", "src", "dirname", "join", "startsWith", "loaderContext", "exports", "fontFamilyHash", "getHashDigest", "<PERSON><PERSON><PERSON>", "from", "result", "process", "undefined", "ast", "type", "version", "processor", "root"], "mappings": "AAEA,OAAOA,UAAU,OAAM;AACvB,SAASC,IAAI,EAAEC,IAAI,QAAQ,6BAA4B;AACvD,OAAOC,iBAAiB,mCAAkC;AAC1D,OAAOC,2BAA2B,sBAAqB;AACvD,SAASC,SAAS,QAAQ,OAAM;AAEhC,eAAe,eAAeC;IAC5B,MAAMC,qBACJ,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACnC,OAAOF,mBAAmBG,YAAY,CAAC;QACrC,MAAMC,WAAW,IAAI,CAACC,KAAK;QAE3B;;;;;;;;;KASC,GACD,MAAM,EACJZ,MAAMa,wBAAwB,EAC9BC,QAAQC,YAAY,EACpBC,WAAWC,IAAI,EACfC,YAAY,EACb,GAAGC,KAAKC,KAAK,CAAC,IAAI,CAACC,aAAa,CAACC,KAAK,CAAC;QAExC,oDAAoD;QACpD,IAAI,wBAAwBC,IAAI,CAACV,2BAA2B;YAC1D,MAAMW,MAAM,IAAIC,MACd,CAAC,EAAExB,KAAK,UAAU,gBAAgB,EAAEC,KAAK,sBAAsB,CAAC,CAAC;YAEnEsB,IAAIE,IAAI,GAAG;YACXf,SAASa;YACT;QACF;QAEA,MAAM,EACJG,KAAK,EACLC,QAAQ,EACRC,WAAW,EACXC,cAAc,EACdC,SAASC,UAAU,EACpB,GAAG,IAAI,CAACC,UAAU;QAEnB,IAAIJ,eAAe,CAAC,kBAAkBN,IAAI,CAACM,cAAc;YACvD,MAAML,MAAM,IAAIC,MACd;YAEFD,IAAIE,IAAI,GAAG;YACXf,SAASa;YACT;QACF;QAEA;;;;;;;;;KASC,GACD,MAAMU,eAAe,CACnBC,SACAC,KACAC,SACAC;YAEA,MAAMC,OAAO;gBAAEC,SAAS,IAAI,CAACC,WAAW;gBAAEN;YAAQ;YAClD,MAAMO,mBAAmBvC,YAAYwC,eAAe,CAClD,IAAI,EACJ,CAAC,mBAAmB,EAAEL,oBAAoB,OAAO,GAAG,EAClDD,UAAU,OAAO,GAClB,CAAC,EAAED,IAAI,CAAC,EACTG;YAEF,MAAMK,aAAa,CAAC,EAAEf,YAAY,OAAO,EAAEa,iBAAiB,CAAC;YAC7D,sCAAsC;YACtC,IAAI,CAACd,UAAU;gBACb,IAAI,CAACiB,QAAQ,CAACH,kBAAkBP,SAAS;YAC3C;YACA,6DAA6D;YAC7D,OAAOS;QACT;QAEA,IAAI;YACF,kFAAkF;YAClF,8FAA8F;YAC9F,MAAME,aAAyBC,QAAQjB,gBAAgBkB,OAAO;YAC9D,IAAI,EAAEC,GAAG,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GACrE,MAAM/C,mBAAmBE,UAAU,CAAC,eAAeC,YAAY,CAAC,IAC9DoC,WAAW;oBACT/B;oBACAG;oBACAD;oBACAiB;oBACAqB,SAAS,CAACC,MACRnD,UAAU,IAAI,CAACkD,OAAO,EACpBvD,KAAKyD,OAAO,CACVzD,KAAK0D,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE5B,4BAE9B2C,IAAIG,UAAU,CAAC,OAAOH,MAAM,CAAC,EAAE,EAAEA,IAAI,CAAC;oBAE1C7B;oBACAC;oBACAgC,eAAe,IAAI;gBACrB;YAGJ,MAAM,EAAE7B,OAAO,EAAE,GAAG,MAAMC;YAE1B,gFAAgF;YAChF,MAAM6B,UAAuC,EAAE;YAE/C,sFAAsF;YACtF,MAAMC,iBAAiB3D,YAAY4D,aAAa,CAC9CC,OAAOC,IAAI,CAAChB,MACZ,QACA,OACA;YAGF,6FAA6F;YAC7F,MAAMiB,SAAS,MAAM3D,mBAClBE,UAAU,CAAC,WACXC,YAAY,CAAC,IACZqB,QACE3B,sBAAsB;oBACpByD;oBACAC;oBACAZ;oBACAE;oBACAC;oBACAF;oBACAG;gBACF,IACAa,OAAO,CAAClB,KAAK;oBACbgB,MAAMG;gBACR;YAGJ,MAAMC,MAAM;gBACVC,MAAM;gBACNC,SAASL,OAAOM,SAAS,CAACD,OAAO;gBACjCE,MAAMP,OAAOO,IAAI;YACnB;YAEA,uHAAuH;YACvH9D,SAAS,MAAMuD,OAAOjB,GAAG,EAAE,MAAM;gBAC/BY;gBACAQ;gBACAP;YACF;QACF,EAAE,OAAOtC,KAAU;YACjBb,SAASa;QACX;IACF;AACF"}