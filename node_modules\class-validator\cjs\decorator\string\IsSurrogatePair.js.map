{"version": 3, "file": "IsSurrogatePair.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsSurrogatePair.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oFAAqE;AAExD,QAAA,iBAAiB,GAAG,iBAAiB,CAAC;AAEnD;;;GAGG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,yBAAwB,EAAC,KAAK,CAAC,CAAC;AACtE,CAAC;AAFD,0CAEC;AAED;;;GAGG;AACH,SAAgB,eAAe,CAAC,iBAAqC;IACnE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,yBAAiB;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;YAC1D,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,kDAAkD,EAC7E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,0CAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isSurrogatePairValidator from 'validator/lib/isSurrogatePair';\n\nexport const IS_SURROGATE_PAIR = 'isSurrogatePair';\n\n/**\n * Checks if the string contains any surrogate pairs chars.\n * If given value is not a string, then it returns false.\n */\nexport function isSurrogatePair(value: unknown): boolean {\n  return typeof value === 'string' && isSurrogatePairValidator(value);\n}\n\n/**\n * Checks if the string contains any surrogate pairs chars.\n * If given value is not a string, then it returns false.\n */\nexport function IsSurrogatePair(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_SURROGATE_PAIR,\n      validator: {\n        validate: (value, args): boolean => isSurrogatePair(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain any surrogate pairs chars',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}