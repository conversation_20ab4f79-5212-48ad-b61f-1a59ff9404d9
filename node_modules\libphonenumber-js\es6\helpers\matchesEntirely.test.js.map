{"version": 3, "file": "matchesEntirely.test.js", "names": ["matchesEntirely", "describe", "it", "expect", "undefined", "to", "equal"], "sources": ["../../source/helpers/matchesEntirely.test.js"], "sourcesContent": ["import matchesEntirely from './matchesEntirely.js'\r\n\r\ndescribe('matchesEntirely', () => {\r\n\tit('should work in edge cases', () => {\r\n\t\t// No text.\r\n\t\texpect(matchesEntirely(undefined, '')).to.equal(true)\r\n\r\n\t\t// \"OR\" in regexp.\r\n\t\texpect(matchesEntirely('911231231', '4\\d{8}|[1-9]\\d{7}')).to.equal(false)\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,eAAe,MAAM,sBAAsB;AAElDC,QAAQ,CAAC,iBAAiB,EAAE,YAAM;EACjCC,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC;IACAC,MAAM,CAACH,eAAe,CAACI,SAAS,EAAE,EAAE,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAErD;IACAH,MAAM,CAACH,eAAe,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC,CAACK,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC1E,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}