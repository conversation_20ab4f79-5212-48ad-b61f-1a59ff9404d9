{"version": 3, "sources": ["../../../src/lib/helpers/install.ts"], "names": ["yellow", "spawn", "install", "root", "dependencies", "packageManager", "isOnline", "devDependencies", "npmFlags", "yarnFlags", "Promise", "resolve", "reject", "args", "command", "<PERSON><PERSON><PERSON><PERSON>", "length", "push", "console", "log", "child", "stdio", "env", "process", "ADBLOCK", "NODE_ENV", "DISABLE_OPENCOLLECTIVE", "on", "code", "join"], "mappings": "AAAA,SAASA,MAAM,QAAQ,gBAAe;AACtC,OAAOC,WAAW,iCAAgC;AAmBlD;;;;CAIC,GACD,OAAO,SAASC,QACdC,IAAY,EACZC,YAA6B,EAC7B,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAe;IAE1D;;GAEC,GACD,MAAMC,WAAqB,EAAE;IAC7B;;GAEC,GACD,MAAMC,YAAsB,EAAE;IAC9B;;GAEC,GACD,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B,IAAIC;QACJ,IAAIC,UAAUT;QACd,MAAMU,UAAUV,mBAAmB;QAEnC,IAAID,gBAAgBA,aAAaY,MAAM,EAAE;YACvC;;OAEC,GACD,IAAID,SAAS;gBACX;;SAEC,GACDF,OAAO;oBAAC;oBAAO;iBAAU;gBACzB,IAAI,CAACP,UAAUO,KAAKI,IAAI,CAAC;gBACzBJ,KAAKI,IAAI,CAAC,SAASd;gBACnB,IAAII,iBAAiBM,KAAKI,IAAI,CAAC;gBAC/BJ,KAAKI,IAAI,IAAIb;YACf,OAAO;gBACL;;SAEC,GACDS,OAAO;oBAAC;oBAAW;iBAAe;gBAClCA,KAAKI,IAAI,CAACV,kBAAkB,eAAe;gBAC3CM,KAAKI,IAAI,IAAIb;YACf;QACF,OAAO;YACL;;;OAGC,GACDS,OAAO;gBAAC;aAAU;YAClB,IAAI,CAACP,UAAU;gBACbY,QAAQC,GAAG,CAACnB,OAAO;gBACnB,IAAIe,SAAS;oBACXG,QAAQC,GAAG,CAACnB,OAAO;oBACnBkB,QAAQC,GAAG;oBACXN,KAAKI,IAAI,CAAC;gBACZ,OAAO;oBACLC,QAAQC,GAAG;gBACb;YACF;QACF;QACA;;KAEC,GACD,IAAIJ,SAAS;YACXF,KAAKI,IAAI,IAAIR;QACf,OAAO;YACLI,KAAKI,IAAI,IAAIT;QACf;QACA;;KAEC,GACD,MAAMY,QAAQnB,MAAMa,SAASD,MAAM;YACjCQ,OAAO;YACPC,KAAK;gBACH,GAAGC,QAAQD,GAAG;gBACdE,SAAS;gBACT,mDAAmD;gBACnD,+BAA+B;gBAC/BC,UAAU;gBACVC,wBAAwB;YAC1B;QACF;QACAN,MAAMO,EAAE,CAAC,SAAS,CAACC;YACjB,IAAIA,SAAS,GAAG;gBACdhB,OAAO;oBAAEE,SAAS,CAAC,EAAEA,QAAQ,CAAC,EAAED,KAAKgB,IAAI,CAAC,KAAK,CAAC;gBAAC;gBACjD;YACF;YACAlB;QACF;IACF;AACF"}