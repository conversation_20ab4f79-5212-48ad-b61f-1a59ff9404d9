{"version": 3, "file": "IsBase64.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBase64.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAG1C,QAAA,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,SAAgB,QAAQ,CAAC,KAAc,EAAE,OAAqC;IAC5E,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,kBAAiB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACxE,CAAC;AAFD,4BAEC;AAED;;;GAGG;AACH,SAAgB,QAAQ,CACtB,OAAqC,EACrC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,iBAAS;QACf,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACzE,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,kCAAkC,EAAE,iBAAiB,CAAC;SAC/G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,4BAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBase64Validator from 'validator/lib/isBase64';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_BASE64 = 'isBase64';\n\n/**\n * Checks if a string is base64 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function isBase64(value: unknown, options?: ValidatorJS.IsBase64Options): boolean {\n  return typeof value === 'string' && isBase64Validator(value, options);\n}\n\n/**\n * Checks if a string is base64 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function IsBase64(\n  options?: ValidatorJS.IsBase64Options,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BASE64,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isBase64(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be base64 encoded', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}