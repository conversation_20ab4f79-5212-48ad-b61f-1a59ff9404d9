{"version": 3, "file": "AsYouTypeParser.js", "names": ["extractCountryCallingCode", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "extractNationalNumberFromPossiblyIncompleteNumber", "stripIddPrefix", "parseDigits", "VALID_DIGITS", "VALID_PUNCTUATION", "PLUS_CHARS", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN", "RegExp", "VALID_FORMATTED_PHONE_NUMBER_PART", "AFTER_PHONE_NUMBER_DIGITS_END_PATTERN", "COMPLEX_NATIONAL_PREFIX", "AsYouType<PERSON><PERSON><PERSON>", "_ref", "defaultCountry", "defaultCallingCode", "metadata", "onNationalSignificantNumberChange", "_classCallCheck", "_createClass", "key", "value", "input", "text", "state", "_extractFormattedDigi", "extractFormattedDigitsAndPlus", "_extractFormattedDigi2", "_slicedToArray", "formattedDigits", "hasPlus", "digits", "justLeadingPlus", "startInternationalNumber", "inputDigits", "nextDigits", "hasReceivedThreeLeadingDigits", "length", "appendDigits", "extractIddPrefix", "isWaitingForCountryCallingCode", "appendNationalSignificantNumberDigits", "international", "hasExtractedNationalSignificantNumber", "extractNationalSignificantNumber", "getNationalDigits", "stateUpdate", "update", "_ref2", "callingCode", "_extractCountryCallin", "getDigitsWithoutInternationalPrefix", "countryCallingCode", "number", "setCallingCode", "nationalSignificantNumber", "reset", "numberingPlan", "hasSelectedNumberingPlan", "nationalPrefixForParsing", "_nationalPrefixForParsing", "couldPossiblyExtractAnotherNationalSignificantNumber", "test", "undefined", "nationalDigits", "setState", "_extractNationalNumbe", "nationalPrefix", "nationalNumber", "carrierCode", "onExtractedNationalNumber", "extractAnotherNationalSignificantNumber", "prevNationalSignificantNumber", "_extractNationalNumbe2", "complexPrefixBeforeNationalSignificantNumber", "nationalSignificantNumberMatchesInput", "nationalSignificantNumberIndex", "lastIndexOf", "prefixBeforeNationalNumber", "slice", "reExtractNationalSignificantNumber", "extractCallingCodeAndNationalSignificantNumber", "fixMissingPlus", "IDDPrefix", "numberWithoutIDD", "country", "_extractCountryCallin2", "newCallingCode", "missingPlus", "_ref3", "resetNationalSignificantNumber", "default", "extractFormattedPhoneNumber", "startsAt", "search", "replace", "_extractFormattedDigitsAndPlus", "extractedNumber", "_extractFormattedDigi3", "_extractFormattedDigi4"], "sources": ["../source/AsYouTypeParser.js"], "sourcesContent": ["import extractCountryCallingCode from './helpers/extractCountryCallingCode.js'\r\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js'\r\nimport extractNationalNumberFromPossiblyIncompleteNumber from './helpers/extractNationalNumberFromPossiblyIncompleteNumber.js'\r\nimport stripIddPrefix from './helpers/stripIddPrefix.js'\r\nimport parseDigits from './helpers/parseDigits.js'\r\n\r\nimport {\r\n\tVALID_DIGITS,\r\n\tVALID_PUNCTUATION,\r\n\tPLUS_CHARS\r\n} from './constants.js'\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART =\r\n\t'[' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']+'\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN = new RegExp('^' + VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART + '$', 'i')\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_PART =\r\n\t'(?:' +\r\n\t\t'[' + PLUS_CHARS + ']' +\r\n\t\t'[' +\r\n\t\t\tVALID_PUNCTUATION +\r\n\t\t\tVALID_DIGITS +\r\n\t\t']*' +\r\n\t\t'|' +\r\n\t\t'[' +\r\n\t\t\tVALID_PUNCTUATION +\r\n\t\t\tVALID_DIGITS +\r\n\t\t']+' +\r\n\t')'\r\n\r\nconst AFTER_PHONE_NUMBER_DIGITS_END_PATTERN = new RegExp(\r\n\t'[^' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']+' +\r\n\t'.*' +\r\n\t'$'\r\n)\r\n\r\n// Tests whether `national_prefix_for_parsing` could match\r\n// different national prefixes.\r\n// Matches anything that's not a digit or a square bracket.\r\nconst COMPLEX_NATIONAL_PREFIX = /[^\\d\\[\\]]/\r\n\r\nexport default class AsYouTypeParser {\r\n\tconstructor({\r\n\t\tdefaultCountry,\r\n\t\tdefaultCallingCode,\r\n\t\tmetadata,\r\n\t\tonNationalSignificantNumberChange\r\n\t}) {\r\n\t\tthis.defaultCountry = defaultCountry\r\n\t\tthis.defaultCallingCode = defaultCallingCode\r\n\t\tthis.metadata = metadata\r\n\t\tthis.onNationalSignificantNumberChange = onNationalSignificantNumberChange\r\n\t}\r\n\r\n\tinput(text, state) {\r\n\t\tconst [formattedDigits, hasPlus] = extractFormattedDigitsAndPlus(text)\r\n\t\tconst digits = parseDigits(formattedDigits)\r\n\t\t// Checks for a special case: just a leading `+` has been entered.\r\n\t\tlet justLeadingPlus\r\n\t\tif (hasPlus) {\r\n\t\t\tif (!state.digits) {\r\n\t\t\t\tstate.startInternationalNumber()\r\n\t\t\t\tif (!digits) {\r\n\t\t\t\t\tjustLeadingPlus = true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (digits) {\r\n\t\t\tthis.inputDigits(digits, state)\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tdigits,\r\n\t\t\tjustLeadingPlus\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Inputs \"next\" phone number digits.\r\n\t * @param  {string} digits\r\n\t * @return {string} [formattedNumber] Formatted national phone number (if it can be formatted at this stage). Returning `undefined` means \"don't format the national phone number at this stage\".\r\n\t */\r\n\tinputDigits(nextDigits, state) {\r\n\t\tconst { digits } = state\r\n\t\tconst hasReceivedThreeLeadingDigits = digits.length < 3 && digits.length + nextDigits.length >= 3\r\n\r\n\t\t// Append phone number digits.\r\n\t\tstate.appendDigits(nextDigits)\r\n\r\n\t\t// Attempt to extract IDD prefix:\r\n\t\t// Some users input their phone number in international format,\r\n\t\t// but in an \"out-of-country\" dialing format instead of using the leading `+`.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/185\r\n\t\t// Detect such numbers as soon as there're at least 3 digits.\r\n\t\t// Google's library attempts to extract IDD prefix at 3 digits,\r\n\t\t// so this library just copies that behavior.\r\n\t\t// I guess that's because the most commot IDD prefixes are\r\n\t\t// `00` (Europe) and `011` (US).\r\n\t\t// There exist really long IDD prefixes too:\r\n\t\t// for example, in Australia the default IDD prefix is `0011`,\r\n\t\t// and it could even be as long as `14880011`.\r\n\t\t// An IDD prefix is extracted here, and then every time when\r\n\t\t// there's a new digit and the number couldn't be formatted.\r\n\t\tif (hasReceivedThreeLeadingDigits) {\r\n\t\t\tthis.extractIddPrefix(state)\r\n\t\t}\r\n\r\n\t\tif (this.isWaitingForCountryCallingCode(state)) {\r\n\t\t\tif (!this.extractCountryCallingCode(state)) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tstate.appendNationalSignificantNumberDigits(nextDigits)\r\n\t\t}\r\n\r\n\t\t// If a phone number is being input in international format,\r\n\t\t// then it's not valid for it to have a national prefix.\r\n\t\t// Still, some people incorrectly input such numbers with a national prefix.\r\n\t\t// In such cases, only attempt to strip a national prefix if the number becomes too long.\r\n\t\t// (but that is done later, not here)\r\n\t\tif (!state.international) {\r\n\t\t\tif (!this.hasExtractedNationalSignificantNumber) {\r\n\t\t\t\tthis.extractNationalSignificantNumber(\r\n\t\t\t\t\tstate.getNationalDigits(),\r\n\t\t\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tisWaitingForCountryCallingCode({ international, callingCode }) {\r\n\t\treturn international && !callingCode\r\n\t}\r\n\r\n\t// Extracts a country calling code from a number\r\n\t// being entered in internatonal format.\r\n\textractCountryCallingCode(state) {\r\n\t\tconst { countryCallingCode, number } = extractCountryCallingCode(\r\n\t\t\t'+' + state.getDigitsWithoutInternationalPrefix(),\r\n\t\t\tthis.defaultCountry,\r\n\t\t\tthis.defaultCallingCode,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (countryCallingCode) {\r\n\t\t\tstate.setCallingCode(countryCallingCode)\r\n\t\t\tstate.update({\r\n\t\t\t\tnationalSignificantNumber: number\r\n\t\t\t})\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\treset(numberingPlan) {\r\n\t\tif (numberingPlan) {\r\n\t\t\tthis.hasSelectedNumberingPlan = true\r\n\t\t\tconst nationalPrefixForParsing = numberingPlan._nationalPrefixForParsing()\r\n\t\t\tthis.couldPossiblyExtractAnotherNationalSignificantNumber = nationalPrefixForParsing && COMPLEX_NATIONAL_PREFIX.test(nationalPrefixForParsing)\r\n\t\t} else {\r\n\t\t\tthis.hasSelectedNumberingPlan = undefined\r\n\t\t\tthis.couldPossiblyExtractAnotherNationalSignificantNumber = undefined\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Extracts a national (significant) number from user input.\r\n\t * Google's library is different in that it only applies `national_prefix_for_parsing`\r\n\t * and doesn't apply `national_prefix_transform_rule` after that.\r\n\t * https://github.com/google/libphonenumber/blob/a3d70b0487875475e6ad659af404943211d26456/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L539\r\n\t * @return {boolean} [extracted]\r\n\t */\r\n\textractNationalSignificantNumber(nationalDigits, setState) {\r\n\t\tif (!this.hasSelectedNumberingPlan) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tconst {\r\n\t\t\tnationalPrefix,\r\n\t\t\tnationalNumber,\r\n\t\t\tcarrierCode\r\n\t\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\t\tnationalDigits,\r\n\t\t\tthis.metadata\r\n\t\t)\r\n\t\tif (nationalNumber === nationalDigits) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tthis.onExtractedNationalNumber(\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tnationalDigits,\r\n\t\t\tsetState\r\n\t\t)\r\n\t\treturn true\r\n\t}\r\n\r\n\t/**\r\n\t * In Google's code this function is called \"attempt to extract longer NDD\".\r\n\t * \"Some national prefixes are a substring of others\", they say.\r\n\t * @return {boolean} [result] — Returns `true` if extracting a national prefix produced different results from what they were.\r\n\t */\r\n\textractAnotherNationalSignificantNumber(nationalDigits, prevNationalSignificantNumber, setState) {\r\n\t\tif (!this.hasExtractedNationalSignificantNumber) {\r\n\t\t\treturn this.extractNationalSignificantNumber(nationalDigits, setState)\r\n\t\t}\r\n\t\tif (!this.couldPossiblyExtractAnotherNationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tconst {\r\n\t\t\tnationalPrefix,\r\n\t\t\tnationalNumber,\r\n\t\t\tcarrierCode\r\n\t\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\t\tnationalDigits,\r\n\t\t\tthis.metadata\r\n\t\t)\r\n\t\t// If a national prefix has been extracted previously,\r\n\t\t// then it's always extracted as additional digits are added.\r\n\t\t// That's assuming `extractNationalNumberFromPossiblyIncompleteNumber()`\r\n\t\t// doesn't do anything different from what it currently does.\r\n\t\t// So, just in case, here's this check, though it doesn't occur.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (nationalNumber === prevNationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tthis.onExtractedNationalNumber(\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tnationalDigits,\r\n\t\t\tsetState\r\n\t\t)\r\n\t\treturn true\r\n\t}\r\n\r\n\tonExtractedNationalNumber(\r\n\t\tnationalPrefix,\r\n\t\tcarrierCode,\r\n\t\tnationalSignificantNumber,\r\n\t\tnationalDigits,\r\n\t\tsetState\r\n\t) {\r\n\t\tlet complexPrefixBeforeNationalSignificantNumber\r\n\t\tlet nationalSignificantNumberMatchesInput\r\n\t\t// This check also works with empty `this.nationalSignificantNumber`.\r\n\t\tconst nationalSignificantNumberIndex = nationalDigits.lastIndexOf(nationalSignificantNumber)\r\n\t\t// If the extracted national (significant) number is the\r\n\t\t// last substring of the `digits`, then it means that it hasn't been altered:\r\n\t\t// no digits have been removed from the national (significant) number\r\n\t\t// while applying `national_prefix_transform_rule`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\r\n\t\tif (nationalSignificantNumberIndex >= 0 &&\r\n\t\t\tnationalSignificantNumberIndex === nationalDigits.length - nationalSignificantNumber.length) {\r\n\t\t\tnationalSignificantNumberMatchesInput = true\r\n\t\t\t// If a prefix of a national (significant) number is not as simple\r\n\t\t\t// as just a basic national prefix, then such prefix is stored in\r\n\t\t\t// `this.complexPrefixBeforeNationalSignificantNumber` property and will be\r\n\t\t\t// prepended \"as is\" to the national (significant) number to produce\r\n\t\t\t// a formatted result.\r\n\t\t\tconst prefixBeforeNationalNumber = nationalDigits.slice(0, nationalSignificantNumberIndex)\r\n\t\t\t// `prefixBeforeNationalNumber` is always non-empty,\r\n\t\t\t// because `onExtractedNationalNumber()` isn't called\r\n\t\t\t// when a national (significant) number hasn't been actually \"extracted\":\r\n\t\t\t// when a national (significant) number is equal to the national part of `digits`,\r\n\t\t\t// then `onExtractedNationalNumber()` doesn't get called.\r\n\t\t\tif (prefixBeforeNationalNumber !== nationalPrefix) {\r\n\t\t\t\tcomplexPrefixBeforeNationalSignificantNumber = prefixBeforeNationalNumber\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetState({\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tnationalSignificantNumberMatchesInput,\r\n\t\t\tcomplexPrefixBeforeNationalSignificantNumber\r\n\t\t})\r\n\t\t// `onExtractedNationalNumber()` is only called when\r\n\t\t// the national (significant) number actually did change.\r\n\t\tthis.hasExtractedNationalSignificantNumber = true\r\n\t\tthis.onNationalSignificantNumberChange()\r\n\t}\r\n\r\n\treExtractNationalSignificantNumber(state) {\r\n\t\t// Attempt to extract a national prefix.\r\n\t\t//\r\n\t\t// Some people incorrectly input national prefix\r\n\t\t// in an international phone number.\r\n\t\t// For example, some people write British phone numbers as `+44(0)...`.\r\n\t\t//\r\n\t\t// Also, in some rare cases, it is valid for a national prefix\r\n\t\t// to be a part of an international phone number.\r\n\t\t// For example, mobile phone numbers in Mexico are supposed to be\r\n\t\t// dialled internationally using a `1` national prefix,\r\n\t\t// so the national prefix will be part of an international number.\r\n\t\t//\r\n\t\t// Quote from:\r\n\t\t// https://www.mexperience.com/dialing-cell-phones-in-mexico/\r\n\t\t//\r\n\t\t// \"Dialing a Mexican cell phone from abroad\r\n\t\t// When you are calling a cell phone number in Mexico from outside Mexico,\r\n\t\t// it’s necessary to dial an additional “1” after Mexico’s country code\r\n\t\t// (which is “52”) and before the area code.\r\n\t\t// You also ignore the 045, and simply dial the area code and the\r\n\t\t// cell phone’s number.\r\n\t\t//\r\n\t\t// If you don’t add the “1”, you’ll receive a recorded announcement\r\n\t\t// asking you to redial using it.\r\n\t\t//\r\n\t\t// For example, if you are calling from the USA to a cell phone\r\n\t\t// in Mexico City, you would dial +52 – 1 – 55 – 1234 5678.\r\n\t\t// (Note that this is different to calling a land line in Mexico City\r\n\t\t// from abroad, where the number dialed would be +52 – 55 – 1234 5678)\".\r\n\t\t//\r\n\t\t// Google's demo output:\r\n\t\t// https://libphonenumber.appspot.com/phonenumberparser?number=%2b5215512345678&country=MX\r\n\t\t//\r\n\t\tif (this.extractAnotherNationalSignificantNumber(\r\n\t\t\tstate.getNationalDigits(),\r\n\t\t\tstate.nationalSignificantNumber,\r\n\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t)) {\r\n\t\t\treturn true\r\n\t\t}\r\n\t\t// If no format matches the phone number, then it could be\r\n\t\t// \"a really long IDD\" (quote from a comment in Google's library).\r\n\t\t// An IDD prefix is first extracted when the user has entered at least 3 digits,\r\n\t\t// and then here — every time when there's a new digit and the number\r\n\t\t// couldn't be formatted.\r\n\t\t// For example, in Australia the default IDD prefix is `0011`,\r\n\t\t// and it could even be as long as `14880011`.\r\n\t\t//\r\n\t\t// Could also check `!hasReceivedThreeLeadingDigits` here\r\n\t\t// to filter out the case when this check duplicates the one\r\n\t\t// already performed when there're 3 leading digits,\r\n\t\t// but it's not a big deal, and in most cases there\r\n\t\t// will be a suitable `format` when there're 3 leading digits.\r\n\t\t//\r\n\t\tif (this.extractIddPrefix(state)) {\r\n\t\t\tthis.extractCallingCodeAndNationalSignificantNumber(state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t\t// Google's AsYouType formatter supports sort of an \"autocorrection\" feature\r\n\t\t// when it \"autocorrects\" numbers that have been input for a country\r\n\t\t// with that country's calling code.\r\n\t\t// Such \"autocorrection\" feature looks weird, but different people have been requesting it:\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/376\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/375\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/316\r\n\t\tif (this.fixMissingPlus(state)) {\r\n\t\t\tthis.extractCallingCodeAndNationalSignificantNumber(state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\textractIddPrefix(state) {\r\n\t\t// An IDD prefix can't be present in a number written with a `+`.\r\n\t\t// Also, don't re-extract an IDD prefix if has already been extracted.\r\n\t\tconst {\r\n\t\t\tinternational,\r\n\t\t\tIDDPrefix,\r\n\t\t\tdigits,\r\n\t\t\tnationalSignificantNumber\r\n\t\t} = state\r\n\t\tif (international || IDDPrefix) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Some users input their phone number in \"out-of-country\"\r\n\t\t// dialing format instead of using the leading `+`.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/185\r\n\t\t// Detect such numbers.\r\n\t\tconst numberWithoutIDD = stripIddPrefix(\r\n\t\t\tdigits,\r\n\t\t\tthis.defaultCountry,\r\n\t\t\tthis.defaultCallingCode,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (numberWithoutIDD !== undefined && numberWithoutIDD !== digits) {\r\n\t\t\t// If an IDD prefix was stripped then convert the IDD-prefixed number\r\n\t\t\t// to international number for subsequent parsing.\r\n\t\t\tstate.update({\r\n\t\t\t\tIDDPrefix: digits.slice(0, digits.length - numberWithoutIDD.length)\r\n\t\t\t})\r\n\t\t\tthis.startInternationalNumber(state, {\r\n\t\t\t\tcountry: undefined,\r\n\t\t\t\tcallingCode: undefined\r\n\t\t\t})\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\tfixMissingPlus(state) {\r\n\t\tif (!state.international) {\r\n\t\t\tconst {\r\n\t\t\t\tcountryCallingCode: newCallingCode,\r\n\t\t\t\tnumber\r\n\t\t\t} = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(\r\n\t\t\t\tstate.digits,\r\n\t\t\t\tthis.defaultCountry,\r\n\t\t\t\tthis.defaultCallingCode,\r\n\t\t\t\tthis.metadata.metadata\r\n\t\t\t)\r\n\t\t\tif (newCallingCode) {\r\n\t\t\t\tstate.update({\r\n\t\t\t\t\tmissingPlus: true\r\n\t\t\t\t})\r\n\t\t\t\tthis.startInternationalNumber(state, {\r\n\t\t\t\t\tcountry: state.country,\r\n\t\t\t\t\tcallingCode: newCallingCode\r\n\t\t\t\t})\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tstartInternationalNumber(state, { country, callingCode }) {\r\n\t\tstate.startInternationalNumber(country, callingCode)\r\n\t\t// If a national (significant) number has been extracted before, reset it.\r\n\t\tif (state.nationalSignificantNumber) {\r\n\t\t\tstate.resetNationalSignificantNumber()\r\n\t\t\tthis.onNationalSignificantNumberChange()\r\n\t\t\tthis.hasExtractedNationalSignificantNumber = undefined\r\n\t\t}\r\n\t}\r\n\r\n\textractCallingCodeAndNationalSignificantNumber(state) {\r\n\t\tif (this.extractCountryCallingCode(state)) {\r\n\t\t\t// `this.extractCallingCode()` is currently called when the number\r\n\t\t\t// couldn't be formatted during the standard procedure.\r\n\t\t\t// Normally, the national prefix would be re-extracted\r\n\t\t\t// for an international number if such number couldn't be formatted,\r\n\t\t\t// but since it's already not able to be formatted,\r\n\t\t\t// there won't be yet another retry, so also extract national prefix here.\r\n\t\t\tthis.extractNationalSignificantNumber(\r\n\t\t\t\tstate.getNationalDigits(),\r\n\t\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t\t)\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number from text (if there's any).\r\n * @param  {string} text\r\n * @return {string} [formattedPhoneNumber]\r\n */\r\nfunction extractFormattedPhoneNumber(text) {\r\n\t// Attempt to extract a possible number from the string passed in.\r\n\tconst startsAt = text.search(VALID_FORMATTED_PHONE_NUMBER_PART)\r\n\tif (startsAt < 0) {\r\n\t\treturn\r\n\t}\r\n\t// Trim everything to the left of the phone number.\r\n\ttext = text.slice(startsAt)\r\n\t// Trim the `+`.\r\n\tlet hasPlus\r\n\tif (text[0] === '+') {\r\n\t\thasPlus = true\r\n\t\ttext = text.slice('+'.length)\r\n\t}\r\n\t// Trim everything to the right of the phone number.\r\n\ttext = text.replace(AFTER_PHONE_NUMBER_DIGITS_END_PATTERN, '')\r\n\t// Re-add the previously trimmed `+`.\r\n\tif (hasPlus) {\r\n\t\ttext = '+' + text\r\n\t}\r\n\treturn text\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\r\nfunction _extractFormattedDigitsAndPlus(text) {\r\n\t// Extract a formatted phone number part from text.\r\n\tconst extractedNumber = extractFormattedPhoneNumber(text) || ''\r\n\t// Trim a `+`.\r\n\tif (extractedNumber[0] === '+') {\r\n\t\treturn [extractedNumber.slice('+'.length), true]\r\n\t}\r\n\treturn [extractedNumber]\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\r\nexport function extractFormattedDigitsAndPlus(text) {\r\n\tlet [formattedDigits, hasPlus] = _extractFormattedDigitsAndPlus(text)\r\n\t// If the extracted phone number part\r\n\t// can possibly be a part of some valid phone number\r\n\t// then parse phone number characters from a formatted phone number.\r\n\tif (!VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN.test(formattedDigits)) {\r\n\t\tformattedDigits = ''\r\n\t}\r\n\treturn [formattedDigits, hasPlus]\r\n}"], "mappings": ";;;;;;;;;;;;AAAA,OAAOA,0BAAyB,MAAM,wCAAwC;AAC9E,OAAOC,+DAA+D,MAAM,8EAA8E;AAC1J,OAAOC,iDAAiD,MAAM,gEAAgE;AAC9H,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,WAAW,MAAM,0BAA0B;AAElD,SACCC,YAAY,EACZC,iBAAiB,EACjBC,UAAU,QACJ,gBAAgB;AAEvB,IAAMC,wCAAwC,GAC7C,GAAG,GACFF,iBAAiB,GACjBD,YAAY,GACb,IAAI;AAEL,IAAMI,gDAAgD,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGF,wCAAwC,GAAG,GAAG,EAAE,GAAG,CAAC;AAE9H,IAAMG,iCAAiC,GACtC,KAAK,GACJ,GAAG,GAAGJ,UAAU,GAAG,GAAG,GACtB,GAAG,GACFD,iBAAiB,GACjBD,YAAY,GACb,IAAI,GACJ,GAAG,GACH,GAAG,GACFC,iBAAiB,GACjBD,YAAY,GACb,IAAI,GACL,GAAG;AAEJ,IAAMO,qCAAqC,GAAG,IAAIF,MAAM,CACvD,IAAI,GACHJ,iBAAiB,GACjBD,YAAY,GACb,IAAI,GACJ,IAAI,GACJ,GACD,CAAC;;AAED;AACA;AACA;AACA,IAAMQ,uBAAuB,GAAG,WAAW;AAAA,IAEtBC,eAAe;EACnC,SAAAA,gBAAAC,IAAA,EAKG;IAAA,IAJFC,cAAc,GAAAD,IAAA,CAAdC,cAAc;MACdC,kBAAkB,GAAAF,IAAA,CAAlBE,kBAAkB;MAClBC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;MACRC,iCAAiC,GAAAJ,IAAA,CAAjCI,iCAAiC;IAAAC,eAAA,OAAAN,eAAA;IAEjC,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,iCAAiC,GAAGA,iCAAiC;EAC3E;EAAC,OAAAE,YAAA,CAAAP,eAAA;IAAAQ,GAAA;IAAAC,KAAA,EAED,SAAAC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAE;MAClB,IAAAC,qBAAA,GAAmCC,6BAA6B,CAACH,IAAI,CAAC;QAAAI,sBAAA,GAAAC,cAAA,CAAAH,qBAAA;QAA/DI,eAAe,GAAAF,sBAAA;QAAEG,OAAO,GAAAH,sBAAA;MAC/B,IAAMI,MAAM,GAAG7B,WAAW,CAAC2B,eAAe,CAAC;MAC3C;MACA,IAAIG,eAAe;MACnB,IAAIF,OAAO,EAAE;QACZ,IAAI,CAACN,KAAK,CAACO,MAAM,EAAE;UAClBP,KAAK,CAACS,wBAAwB,CAAC,CAAC;UAChC,IAAI,CAACF,MAAM,EAAE;YACZC,eAAe,GAAG,IAAI;UACvB;QACD;MACD;MACA,IAAID,MAAM,EAAE;QACX,IAAI,CAACG,WAAW,CAACH,MAAM,EAAEP,KAAK,CAAC;MAChC;MACA,OAAO;QACNO,MAAM,EAANA,MAAM;QACNC,eAAe,EAAfA;MACD,CAAC;IACF;;IAEA;AACD;AACA;AACA;AACA;EAJC;IAAAZ,GAAA;IAAAC,KAAA,EAKA,SAAAa,WAAWA,CAACC,UAAU,EAAEX,KAAK,EAAE;MAC9B,IAAQO,MAAM,GAAKP,KAAK,CAAhBO,MAAM;MACd,IAAMK,6BAA6B,GAAGL,MAAM,CAACM,MAAM,GAAG,CAAC,IAAIN,MAAM,CAACM,MAAM,GAAGF,UAAU,CAACE,MAAM,IAAI,CAAC;;MAEjG;MACAb,KAAK,CAACc,YAAY,CAACH,UAAU,CAAC;;MAE9B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIC,6BAA6B,EAAE;QAClC,IAAI,CAACG,gBAAgB,CAACf,KAAK,CAAC;MAC7B;MAEA,IAAI,IAAI,CAACgB,8BAA8B,CAAChB,KAAK,CAAC,EAAE;QAC/C,IAAI,CAAC,IAAI,CAAC1B,yBAAyB,CAAC0B,KAAK,CAAC,EAAE;UAC3C;QACD;MACD,CAAC,MAAM;QACNA,KAAK,CAACiB,qCAAqC,CAACN,UAAU,CAAC;MACxD;;MAEA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACX,KAAK,CAACkB,aAAa,EAAE;QACzB,IAAI,CAAC,IAAI,CAACC,qCAAqC,EAAE;UAChD,IAAI,CAACC,gCAAgC,CACpCpB,KAAK,CAACqB,iBAAiB,CAAC,CAAC,EACzB,UAACC,WAAW;YAAA,OAAKtB,KAAK,CAACuB,MAAM,CAACD,WAAW,CAAC;UAAA,CAC3C,CAAC;QACF;MACD;IACD;EAAC;IAAA1B,GAAA;IAAAC,KAAA,EAED,SAAAmB,8BAA8BA,CAAAQ,KAAA,EAAiC;MAAA,IAA9BN,aAAa,GAAAM,KAAA,CAAbN,aAAa;QAAEO,WAAW,GAAAD,KAAA,CAAXC,WAAW;MAC1D,OAAOP,aAAa,IAAI,CAACO,WAAW;IACrC;;IAEA;IACA;EAAA;IAAA7B,GAAA;IAAAC,KAAA,EACA,SAAAvB,yBAAyBA,CAAC0B,KAAK,EAAE;MAChC,IAAA0B,qBAAA,GAAuCpD,0BAAyB,CAC/D,GAAG,GAAG0B,KAAK,CAAC2B,mCAAmC,CAAC,CAAC,EACjD,IAAI,CAACrC,cAAc,EACnB,IAAI,CAACC,kBAAkB,EACvB,IAAI,CAACC,QAAQ,CAACA,QACf,CAAC;QALOoC,kBAAkB,GAAAF,qBAAA,CAAlBE,kBAAkB;QAAEC,MAAM,GAAAH,qBAAA,CAANG,MAAM;MAMlC,IAAID,kBAAkB,EAAE;QACvB5B,KAAK,CAAC8B,cAAc,CAACF,kBAAkB,CAAC;QACxC5B,KAAK,CAACuB,MAAM,CAAC;UACZQ,yBAAyB,EAAEF;QAC5B,CAAC,CAAC;QACF,OAAO,IAAI;MACZ;IACD;EAAC;IAAAjC,GAAA;IAAAC,KAAA,EAED,SAAAmC,KAAKA,CAACC,aAAa,EAAE;MACpB,IAAIA,aAAa,EAAE;QAClB,IAAI,CAACC,wBAAwB,GAAG,IAAI;QACpC,IAAMC,wBAAwB,GAAGF,aAAa,CAACG,yBAAyB,CAAC,CAAC;QAC1E,IAAI,CAACC,oDAAoD,GAAGF,wBAAwB,IAAIhD,uBAAuB,CAACmD,IAAI,CAACH,wBAAwB,CAAC;MAC/I,CAAC,MAAM;QACN,IAAI,CAACD,wBAAwB,GAAGK,SAAS;QACzC,IAAI,CAACF,oDAAoD,GAAGE,SAAS;MACtE;IACD;;IAEA;AACD;AACA;AACA;AACA;AACA;AACA;EANC;IAAA3C,GAAA;IAAAC,KAAA,EAOA,SAAAuB,gCAAgCA,CAACoB,cAAc,EAAEC,QAAQ,EAAE;MAC1D,IAAI,CAAC,IAAI,CAACP,wBAAwB,EAAE;QACnC;MACD;MACA,IAAAQ,qBAAA,GAIIlE,iDAAiD,CACpDgE,cAAc,EACd,IAAI,CAAChD,QACN,CAAC;QANAmD,cAAc,GAAAD,qBAAA,CAAdC,cAAc;QACdC,cAAc,GAAAF,qBAAA,CAAdE,cAAc;QACdC,WAAW,GAAAH,qBAAA,CAAXG,WAAW;MAKZ,IAAID,cAAc,KAAKJ,cAAc,EAAE;QACtC;MACD;MACA,IAAI,CAACM,yBAAyB,CAC7BH,cAAc,EACdE,WAAW,EACXD,cAAc,EACdJ,cAAc,EACdC,QACD,CAAC;MACD,OAAO,IAAI;IACZ;;IAEA;AACD;AACA;AACA;AACA;EAJC;IAAA7C,GAAA;IAAAC,KAAA,EAKA,SAAAkD,uCAAuCA,CAACP,cAAc,EAAEQ,6BAA6B,EAAEP,QAAQ,EAAE;MAChG,IAAI,CAAC,IAAI,CAACtB,qCAAqC,EAAE;QAChD,OAAO,IAAI,CAACC,gCAAgC,CAACoB,cAAc,EAAEC,QAAQ,CAAC;MACvE;MACA,IAAI,CAAC,IAAI,CAACJ,oDAAoD,EAAE;QAC/D;MACD;MACA,IAAAY,sBAAA,GAIIzE,iDAAiD,CACpDgE,cAAc,EACd,IAAI,CAAChD,QACN,CAAC;QANAmD,cAAc,GAAAM,sBAAA,CAAdN,cAAc;QACdC,cAAc,GAAAK,sBAAA,CAAdL,cAAc;QACdC,WAAW,GAAAI,sBAAA,CAAXJ,WAAW;MAKZ;MACA;MACA;MACA;MACA;MACA;MACA,IAAID,cAAc,KAAKI,6BAA6B,EAAE;QACrD;MACD;MACA,IAAI,CAACF,yBAAyB,CAC7BH,cAAc,EACdE,WAAW,EACXD,cAAc,EACdJ,cAAc,EACdC,QACD,CAAC;MACD,OAAO,IAAI;IACZ;EAAC;IAAA7C,GAAA;IAAAC,KAAA,EAED,SAAAiD,yBAAyBA,CACxBH,cAAc,EACdE,WAAW,EACXd,yBAAyB,EACzBS,cAAc,EACdC,QAAQ,EACP;MACD,IAAIS,4CAA4C;MAChD,IAAIC,qCAAqC;MACzC;MACA,IAAMC,8BAA8B,GAAGZ,cAAc,CAACa,WAAW,CAACtB,yBAAyB,CAAC;MAC5F;MACA;MACA;MACA;MACA;MACA,IAAIqB,8BAA8B,IAAI,CAAC,IACtCA,8BAA8B,KAAKZ,cAAc,CAAC3B,MAAM,GAAGkB,yBAAyB,CAAClB,MAAM,EAAE;QAC7FsC,qCAAqC,GAAG,IAAI;QAC5C;QACA;QACA;QACA;QACA;QACA,IAAMG,0BAA0B,GAAGd,cAAc,CAACe,KAAK,CAAC,CAAC,EAAEH,8BAA8B,CAAC;QAC1F;QACA;QACA;QACA;QACA;QACA,IAAIE,0BAA0B,KAAKX,cAAc,EAAE;UAClDO,4CAA4C,GAAGI,0BAA0B;QAC1E;MACD;MACAb,QAAQ,CAAC;QACRE,cAAc,EAAdA,cAAc;QACdE,WAAW,EAAXA,WAAW;QACXd,yBAAyB,EAAzBA,yBAAyB;QACzBoB,qCAAqC,EAArCA,qCAAqC;QACrCD,4CAA4C,EAA5CA;MACD,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC/B,qCAAqC,GAAG,IAAI;MACjD,IAAI,CAAC1B,iCAAiC,CAAC,CAAC;IACzC;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAA2D,kCAAkCA,CAACxD,KAAK,EAAE;MACzC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC+C,uCAAuC,CAC/C/C,KAAK,CAACqB,iBAAiB,CAAC,CAAC,EACzBrB,KAAK,CAAC+B,yBAAyB,EAC/B,UAACT,WAAW;QAAA,OAAKtB,KAAK,CAACuB,MAAM,CAACD,WAAW,CAAC;MAAA,CAC3C,CAAC,EAAE;QACF,OAAO,IAAI;MACZ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACP,gBAAgB,CAACf,KAAK,CAAC,EAAE;QACjC,IAAI,CAACyD,8CAA8C,CAACzD,KAAK,CAAC;QAC1D,OAAO,IAAI;MACZ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC0D,cAAc,CAAC1D,KAAK,CAAC,EAAE;QAC/B,IAAI,CAACyD,8CAA8C,CAACzD,KAAK,CAAC;QAC1D,OAAO,IAAI;MACZ;IACD;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAAkB,gBAAgBA,CAACf,KAAK,EAAE;MACvB;MACA;MACA,IACCkB,aAAa,GAIVlB,KAAK,CAJRkB,aAAa;QACbyC,SAAS,GAGN3D,KAAK,CAHR2D,SAAS;QACTpD,MAAM,GAEHP,KAAK,CAFRO,MAAM;QACNwB,yBAAyB,GACtB/B,KAAK,CADR+B,yBAAyB;MAE1B,IAAIb,aAAa,IAAIyC,SAAS,EAAE;QAC/B;MACD;MACA;MACA;MACA;MACA;MACA,IAAMC,gBAAgB,GAAGnF,cAAc,CACtC8B,MAAM,EACN,IAAI,CAACjB,cAAc,EACnB,IAAI,CAACC,kBAAkB,EACvB,IAAI,CAACC,QAAQ,CAACA,QACf,CAAC;MACD,IAAIoE,gBAAgB,KAAKrB,SAAS,IAAIqB,gBAAgB,KAAKrD,MAAM,EAAE;QAClE;QACA;QACAP,KAAK,CAACuB,MAAM,CAAC;UACZoC,SAAS,EAAEpD,MAAM,CAACgD,KAAK,CAAC,CAAC,EAAEhD,MAAM,CAACM,MAAM,GAAG+C,gBAAgB,CAAC/C,MAAM;QACnE,CAAC,CAAC;QACF,IAAI,CAACJ,wBAAwB,CAACT,KAAK,EAAE;UACpC6D,OAAO,EAAEtB,SAAS;UAClBd,WAAW,EAAEc;QACd,CAAC,CAAC;QACF,OAAO,IAAI;MACZ;IACD;EAAC;IAAA3C,GAAA;IAAAC,KAAA,EAED,SAAA6D,cAAcA,CAAC1D,KAAK,EAAE;MACrB,IAAI,CAACA,KAAK,CAACkB,aAAa,EAAE;QACzB,IAAA4C,sBAAA,GAGIvF,+DAA+D,CAClEyB,KAAK,CAACO,MAAM,EACZ,IAAI,CAACjB,cAAc,EACnB,IAAI,CAACC,kBAAkB,EACvB,IAAI,CAACC,QAAQ,CAACA,QACf,CAAC;UAPoBuE,cAAc,GAAAD,sBAAA,CAAlClC,kBAAkB;UAClBC,MAAM,GAAAiC,sBAAA,CAANjC,MAAM;QAOP,IAAIkC,cAAc,EAAE;UACnB/D,KAAK,CAACuB,MAAM,CAAC;YACZyC,WAAW,EAAE;UACd,CAAC,CAAC;UACF,IAAI,CAACvD,wBAAwB,CAACT,KAAK,EAAE;YACpC6D,OAAO,EAAE7D,KAAK,CAAC6D,OAAO;YACtBpC,WAAW,EAAEsC;UACd,CAAC,CAAC;UACF,OAAO,IAAI;QACZ;MACD;IACD;EAAC;IAAAnE,GAAA;IAAAC,KAAA,EAED,SAAAY,wBAAwBA,CAACT,KAAK,EAAAiE,KAAA,EAA4B;MAAA,IAAxBJ,OAAO,GAAAI,KAAA,CAAPJ,OAAO;QAAEpC,WAAW,GAAAwC,KAAA,CAAXxC,WAAW;MACrDzB,KAAK,CAACS,wBAAwB,CAACoD,OAAO,EAAEpC,WAAW,CAAC;MACpD;MACA,IAAIzB,KAAK,CAAC+B,yBAAyB,EAAE;QACpC/B,KAAK,CAACkE,8BAA8B,CAAC,CAAC;QACtC,IAAI,CAACzE,iCAAiC,CAAC,CAAC;QACxC,IAAI,CAAC0B,qCAAqC,GAAGoB,SAAS;MACvD;IACD;EAAC;IAAA3C,GAAA;IAAAC,KAAA,EAED,SAAA4D,8CAA8CA,CAACzD,KAAK,EAAE;MACrD,IAAI,IAAI,CAAC1B,yBAAyB,CAAC0B,KAAK,CAAC,EAAE;QAC1C;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,CAACoB,gCAAgC,CACpCpB,KAAK,CAACqB,iBAAiB,CAAC,CAAC,EACzB,UAACC,WAAW;UAAA,OAAKtB,KAAK,CAACuB,MAAM,CAACD,WAAW,CAAC;QAAA,CAC3C,CAAC;MACF;IACD;EAAC;AAAA;AAGF;AACA;AACA;AACA;AACA;AAJA,SA7YqBlC,eAAe,IAAA+E,OAAA;AAkZpC,SAASC,2BAA2BA,CAACrE,IAAI,EAAE;EAC1C;EACA,IAAMsE,QAAQ,GAAGtE,IAAI,CAACuE,MAAM,CAACrF,iCAAiC,CAAC;EAC/D,IAAIoF,QAAQ,GAAG,CAAC,EAAE;IACjB;EACD;EACA;EACAtE,IAAI,GAAGA,IAAI,CAACwD,KAAK,CAACc,QAAQ,CAAC;EAC3B;EACA,IAAI/D,OAAO;EACX,IAAIP,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACpBO,OAAO,GAAG,IAAI;IACdP,IAAI,GAAGA,IAAI,CAACwD,KAAK,CAAC,GAAG,CAAC1C,MAAM,CAAC;EAC9B;EACA;EACAd,IAAI,GAAGA,IAAI,CAACwE,OAAO,CAACrF,qCAAqC,EAAE,EAAE,CAAC;EAC9D;EACA,IAAIoB,OAAO,EAAE;IACZP,IAAI,GAAG,GAAG,GAAGA,IAAI;EAClB;EACA,OAAOA,IAAI;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASyE,8BAA8BA,CAACzE,IAAI,EAAE;EAC7C;EACA,IAAM0E,eAAe,GAAGL,2BAA2B,CAACrE,IAAI,CAAC,IAAI,EAAE;EAC/D;EACA,IAAI0E,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC/B,OAAO,CAACA,eAAe,CAAClB,KAAK,CAAC,GAAG,CAAC1C,MAAM,CAAC,EAAE,IAAI,CAAC;EACjD;EACA,OAAO,CAAC4D,eAAe,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASvE,6BAA6BA,CAACH,IAAI,EAAE;EACnD,IAAA2E,sBAAA,GAAiCF,8BAA8B,CAACzE,IAAI,CAAC;IAAA4E,sBAAA,GAAAvE,cAAA,CAAAsE,sBAAA;IAAhErE,eAAe,GAAAsE,sBAAA;IAAErE,OAAO,GAAAqE,sBAAA;EAC7B;EACA;EACA;EACA,IAAI,CAAC5F,gDAAgD,CAACuD,IAAI,CAACjC,eAAe,CAAC,EAAE;IAC5EA,eAAe,GAAG,EAAE;EACrB;EACA,OAAO,CAACA,eAAe,EAAEC,OAAO,CAAC;AAClC", "ignoreList": []}