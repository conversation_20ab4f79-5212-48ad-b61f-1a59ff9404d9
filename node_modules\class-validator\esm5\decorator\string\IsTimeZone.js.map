{"version": 3, "file": "IsTimeZone.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsTimeZone.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,WAAW,GAAG,YAAY,CAAC;AAExC;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,KAAc;IACvC,IAAI,CAAC;QACH,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACf,CAAC;QAED,oGAAoG;QACpG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;QAEpD,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,SAAS,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,iBAAqC;IAC9D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,UAAU,CAAC,KAAK,CAAC,EAAjB,CAAiB;YACrD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,0CAA0C,EAAvD,CAAuD,EACrE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_TIMEZONE = 'isTimeZone';\n\n/**\n * Checks if the string represents a valid IANA timezone\n * If the given value is not a valid IANA timezone, then it returns false.\n */\nexport function isTimeZone(value: unknown): boolean {\n  try {\n    if (typeof value !== 'string') {\n      return false;\n    }\n\n    /** Specifying an invalid time-zone will raise a `RangeError: Invalid time zone specified` error. */\n    Intl.DateTimeFormat(undefined, { timeZone: value });\n\n    return true;\n  } catch (exception) {\n    return false;\n  }\n}\n\n/**\n * Checks if the string represents a valid IANA timezone\n * If the given value is not a valid IANA timezone, then it returns false.\n */\nexport function IsTimeZone(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_TIMEZONE,\n      validator: {\n        validate: (value, args): boolean => isTimeZone(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid IANA time-zone',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}