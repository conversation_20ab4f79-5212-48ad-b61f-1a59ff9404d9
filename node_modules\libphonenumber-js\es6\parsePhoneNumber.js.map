{"version": 3, "file": "parsePhoneNumber.js", "names": ["normalizeArguments", "parsePhoneNumber_", "parsePhoneNumber", "_normalizeArguments", "arguments", "text", "options", "metadata"], "sources": ["../source/parsePhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumber_ from './parsePhoneNumber_.js'\r\n\r\nexport default function parsePhoneNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn parsePhoneNumber_(text, options, metadata)\r\n}\r\n"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,iBAAiB,MAAM,wBAAwB;AAEtD,eAAe,SAASC,gBAAgBA,CAAA,EAAG;EAC1C,IAAAC,mBAAA,GAAoCH,kBAAkB,CAACI,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC/B,OAAON,iBAAiB,CAACI,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAClD", "ignoreList": []}