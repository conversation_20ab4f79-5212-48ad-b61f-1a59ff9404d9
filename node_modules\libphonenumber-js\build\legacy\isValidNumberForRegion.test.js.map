{"version": 3, "file": "isValidNumberForRegion.test.js", "names": ["_metadataMin", "_interopRequireDefault", "require", "_isValidNumberForRegion2", "_isValidNumberForRegion_", "e", "__esModule", "isValidNumberForRegion", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "metadata", "isValidNumberForRegionCustom", "apply", "describe", "it", "expect", "to", "equal", "phone", "country", "_isValidNumberForRegion"], "sources": ["../../source/legacy/isValidNumberForRegion.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' with { type: 'json' }\r\nimport isValidNumberForRegionCustom from './isValidNumberForRegion.js'\r\nimport _isValidNumberForRegion from './isValidNumberForRegion_.js'\r\n\r\nfunction isValidNumberForRegion(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn isValidNumberForRegionCustom.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidNumberForRegion', () => {\r\n\tit('should detect if is valid number for region', () => {\r\n\t\texpect(isValidNumberForRegion('07624369230', 'GB')).to.equal(false)\r\n\t\texpect(isValidNumberForRegion('07624369230', 'IM')).to.equal(true)\r\n\t})\r\n\r\n\tit('should validate arguments', () => {\r\n\t\texpect(() => isValidNumberForRegion({ phone: '7624369230', country: 'GB' })).to.throw('number must be a string')\r\n\t\texpect(() => isValidNumberForRegion('7624369230')).to.throw('country must be a string')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// Not a \"viable\" phone number.\r\n\t\texpect(isValidNumberForRegion('7', 'GB')).to.equal(false)\r\n\r\n\t\t// `options` argument `if/else` coverage.\r\n\t\texpect(_isValidNumberForRegion('07624369230', 'GB', {}, metadata)).to.equal(false)\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,wBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,wBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAkE,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAElE,SAASE,sBAAsBA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC5CF,UAAU,CAACG,IAAI,CAACC,uBAAQ,CAAC;EACzB,OAAOC,mCAA4B,CAACC,KAAK,CAAC,IAAI,EAAEN,UAAU,CAAC;AAC5D;AAEAO,QAAQ,CAAC,wBAAwB,EAAE,YAAM;EACxCC,EAAE,CAAC,6CAA6C,EAAE,YAAM;IACvDC,MAAM,CAACb,sBAAsB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACnEF,MAAM,CAACb,sBAAsB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACnE,CAAC,CAAC;EAEFH,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrCC,MAAM,CAAC;MAAA,OAAMb,sBAAsB,CAAC;QAAEgB,KAAK,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IAAA,EAAC,CAACH,EAAE,SAAM,CAAC,yBAAyB,CAAC;IAChHD,MAAM,CAAC;MAAA,OAAMb,sBAAsB,CAAC,YAAY,CAAC;IAAA,EAAC,CAACc,EAAE,SAAM,CAAC,0BAA0B,CAAC;EACxF,CAAC,CAAC;EAEFF,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC;IACAC,MAAM,CAACb,sBAAsB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;;IAEzD;IACAF,MAAM,CAAC,IAAAK,mCAAuB,EAAC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEV,uBAAQ,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EACnF,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}