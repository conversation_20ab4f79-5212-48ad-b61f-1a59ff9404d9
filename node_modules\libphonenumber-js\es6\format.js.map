{"version": 3, "file": "format.js", "names": ["matchesEntirely", "formatNationalNumberUsingFormat", "<PERSON><PERSON><PERSON>", "getCountryCallingCode", "getIddPrefix", "formatRFC3966", "DEFAULT_OPTIONS", "formatExtension", "formattedNumber", "extension", "metadata", "concat", "ext", "formatNumber", "input", "format", "options", "_objectSpread", "country", "hasCountry", "Error", "countryCallingCode", "selectNumberingPlan", "phone", "nationalNumber", "v2", "number", "formatNationalNumber", "carrierCode", "addExtension", "fromCountry", "formatIDD", "formatAs", "chooseFormatForNumber", "formats", "useInternationalFormat", "withNationalPrefix", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefix", "availableFormats", "nationalNnumber", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "value", "leadingDigitsPatterns", "length", "lastLeadingDigitsPattern", "search", "pattern", "fromCountryCallingCode", "iddPrefix", "undefined"], "sources": ["../source/format.js"], "sourcesContent": ["// This is a port of Google Android `libphonenumber`'s\r\n// `phonenumberutil.js` of December 31th, 2018.\r\n//\r\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\r\n\r\nimport matchesEntirely from './helpers/matchesEntirely.js'\r\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js'\r\nimport Metadata, { getCountryCallingCode } from './metadata.js'\r\nimport getIddPrefix from './helpers/getIddPrefix.js'\r\nimport { formatRFC3966 } from './helpers/RFC3966.js'\r\n\r\nconst DEFAULT_OPTIONS = {\r\n\tformatExtension: (formattedNumber, extension, metadata) => `${formattedNumber}${metadata.ext()}${extension}`\r\n}\r\n\r\n/**\r\n * Formats a phone number.\r\n *\r\n * format(phoneNumberInstance, 'INTERNATIONAL', { ..., v2: true }, metadata)\r\n * format(phoneNumberInstance, 'NATIONAL', { ..., v2: true }, metadata)\r\n *\r\n * format({ phone: '8005553535', country: 'RU' }, 'INTERNATIONAL', { ... }, metadata)\r\n * format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {string} format\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function formatNumber(input, format, options, metadata) {\r\n\t// Apply default options.\r\n\tif (options) {\r\n\t\toptions = { ...DEFAULT_OPTIONS, ...options }\r\n\t} else {\r\n\t\toptions = DEFAULT_OPTIONS\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tif (input.country && input.country !== '001') {\r\n\t\t// Validate `input.country`.\r\n\t\tif (!metadata.hasCountry(input.country)) {\r\n\t\t\tthrow new Error(`Unknown country: ${input.country}`)\r\n\t\t}\r\n\t\tmetadata.country(input.country)\r\n\t}\r\n\telse if (input.countryCallingCode) {\r\n\t\tmetadata.selectNumberingPlan(input.countryCallingCode)\r\n\t}\r\n\telse return input.phone || ''\r\n\r\n\tconst countryCallingCode = metadata.countryCallingCode()\r\n\r\n\tconst nationalNumber = options.v2 ? input.nationalNumber : input.phone\r\n\r\n\t// This variable should have been declared inside `case`s\r\n\t// but Babel has a bug and it says \"duplicate variable declaration\".\r\n\tlet number\r\n\r\n\tswitch (format) {\r\n\t\tcase 'NATIONAL':\r\n\t\t\t// Legacy argument support.\r\n\t\t\t// (`{ country: ..., phone: '' }`)\r\n\t\t\tif (!nationalNumber) {\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t\tnumber = formatNationalNumber(nationalNumber, input.carrierCode, 'NATIONAL', metadata, options)\r\n\t\t\treturn addExtension(number, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tcase 'INTERNATIONAL':\r\n\t\t\t// Legacy argument support.\r\n\t\t\t// (`{ country: ..., phone: '' }`)\r\n\t\t\tif (!nationalNumber) {\r\n\t\t\t\treturn `+${countryCallingCode}`\r\n\t\t\t}\r\n\t\t\tnumber = formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata, options)\r\n\t\t\tnumber = `+${countryCallingCode} ${number}`\r\n\t\t\treturn addExtension(number, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tcase 'E.164':\r\n\t\t\t// `E.164` doesn't define \"phone number extensions\".\r\n\t\t\treturn `+${countryCallingCode}${nationalNumber}`\r\n\r\n\t\tcase 'RFC3966':\r\n\t\t\treturn formatRFC3966({\r\n\t\t\t\tnumber: `+${countryCallingCode}${nationalNumber}`,\r\n\t\t\t\text: input.ext\r\n\t\t\t})\r\n\r\n\t\t// For reference, here's Google's IDD formatter:\r\n\t\t// https://github.com/google/libphonenumber/blob/32719cf74e68796788d1ca45abc85dcdc63ba5b9/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L1546\r\n\t\t// Not saying that this IDD formatter replicates it 1:1, but it seems to work.\r\n\t\t// Who would even need to format phone numbers in IDD format anyway?\r\n\t\tcase 'IDD':\r\n\t\t\tif (!options.fromCountry) {\r\n\t\t\t\treturn\r\n\t\t\t\t// throw new Error('`fromCountry` option not passed for IDD-prefixed formatting.')\r\n\t\t\t}\r\n\t\t\tconst formattedNumber = formatIDD(\r\n\t\t\t\tnationalNumber,\r\n\t\t\t\tinput.carrierCode,\r\n\t\t\t\tcountryCallingCode,\r\n\t\t\t\toptions.fromCountry,\r\n\t\t\t\tmetadata\r\n\t\t\t)\r\n\t\t\treturn addExtension(formattedNumber, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tdefault:\r\n\t\t\tthrow new Error(`Unknown \"format\" argument passed to \"formatNumber()\": \"${format}\"`)\r\n\t}\r\n}\r\n\r\nfunction formatNationalNumber(number, carrierCode, formatAs, metadata, options) {\r\n\tconst format = chooseFormatForNumber(metadata.formats(), number)\r\n\tif (!format) {\r\n\t\treturn number\r\n\t}\r\n\treturn formatNationalNumberUsingFormat(\r\n\t\tnumber,\r\n\t\tformat,\r\n\t\t{\r\n\t\t\tuseInternationalFormat: formatAs === 'INTERNATIONAL',\r\n\t\t\twithNationalPrefix: format.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && (options && options.nationalPrefix === false) ? false : true,\r\n\t\t\tcarrierCode,\r\n\t\t\tmetadata\r\n\t\t}\r\n\t)\r\n}\r\n\r\nexport function chooseFormatForNumber(availableFormats, nationalNnumber) {\r\n\tfor (const format of availableFormats) {\r\n\t\t// Validate leading digits.\r\n\t\t// The test case for \"else path\" could be found by searching for\r\n\t\t// \"format.leadingDigitsPatterns().length === 0\".\r\n\t\tif (format.leadingDigitsPatterns().length > 0) {\r\n\t\t\t// The last leading_digits_pattern is used here, as it is the most detailed\r\n\t\t\tconst lastLeadingDigitsPattern = format.leadingDigitsPatterns()[format.leadingDigitsPatterns().length - 1]\r\n\t\t\t// If leading digits don't match then move on to the next phone number format\r\n\t\t\tif (nationalNnumber.search(lastLeadingDigitsPattern) !== 0) {\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Check that the national number matches the phone number format regular expression\r\n\t\tif (matchesEntirely(nationalNnumber, format.pattern())) {\r\n\t\t\treturn format\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction addExtension(formattedNumber, ext, metadata, formatExtension) {\r\n\treturn ext ? formatExtension(formattedNumber, ext, metadata) : formattedNumber\r\n}\r\n\r\nfunction formatIDD(\r\n\tnationalNumber,\r\n\tcarrierCode,\r\n\tcountryCallingCode,\r\n\tfromCountry,\r\n\tmetadata\r\n) {\r\n\tconst fromCountryCallingCode = getCountryCallingCode(fromCountry, metadata.metadata)\r\n\t// When calling within the same country calling code.\r\n\tif (fromCountryCallingCode === countryCallingCode) {\r\n\t\tconst formattedNumber = formatNationalNumber(nationalNumber, carrierCode, 'NATIONAL', metadata)\r\n\t\t// For NANPA regions, return the national format for these regions\r\n\t\t// but prefix it with the country calling code.\r\n\t\tif (countryCallingCode === '1') {\r\n\t\t\treturn countryCallingCode + ' ' + formattedNumber\r\n\t\t}\r\n\t\t// If regions share a country calling code, the country calling code need\r\n\t\t// not be dialled. This also applies when dialling within a region, so this\r\n\t\t// if clause covers both these cases. Technically this is the case for\r\n\t\t// dialling from La Reunion to other overseas departments of France (French\r\n\t\t// Guiana, Martinique, Guadeloupe), but not vice versa - so we don't cover\r\n\t\t// this edge case for now and for those cases return the version including\r\n\t\t// country calling code. Details here:\r\n\t\t// http://www.petitfute.com/voyage/225-info-pratiques-reunion\r\n\t\t//\r\n\t\treturn formattedNumber\r\n\t}\r\n\tconst iddPrefix = getIddPrefix(fromCountry, undefined, metadata.metadata)\r\n\tif (iddPrefix) {\r\n\t\treturn `${iddPrefix} ${countryCallingCode} ${formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata)}`\r\n\t}\r\n}"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,+BAA+B,MAAM,8CAA8C;AAC1F,OAAOC,QAAQ,IAAIC,qBAAqB,QAAQ,eAAe;AAC/D,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,IAAMC,eAAe,GAAG;EACvBC,eAAe,EAAE,SAAjBA,eAAeA,CAAGC,eAAe,EAAEC,SAAS,EAAEC,QAAQ;IAAA,UAAAC,MAAA,CAAQH,eAAe,EAAAG,MAAA,CAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC,EAAAD,MAAA,CAAGF,SAAS;EAAA;AAC3G,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASI,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEN,QAAQ,EAAE;EACtE;EACA,IAAIM,OAAO,EAAE;IACZA,OAAO,GAAAC,aAAA,CAAAA,aAAA,KAAQX,eAAe,GAAKU,OAAO,CAAE;EAC7C,CAAC,MAAM;IACNA,OAAO,GAAGV,eAAe;EAC1B;EAEAI,QAAQ,GAAG,IAAIR,QAAQ,CAACQ,QAAQ,CAAC;EAEjC,IAAII,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,KAAK,KAAK,EAAE;IAC7C;IACA,IAAI,CAACR,QAAQ,CAACS,UAAU,CAACL,KAAK,CAACI,OAAO,CAAC,EAAE;MACxC,MAAM,IAAIE,KAAK,qBAAAT,MAAA,CAAqBG,KAAK,CAACI,OAAO,CAAE,CAAC;IACrD;IACAR,QAAQ,CAACQ,OAAO,CAACJ,KAAK,CAACI,OAAO,CAAC;EAChC,CAAC,MACI,IAAIJ,KAAK,CAACO,kBAAkB,EAAE;IAClCX,QAAQ,CAACY,mBAAmB,CAACR,KAAK,CAACO,kBAAkB,CAAC;EACvD,CAAC,MACI,OAAOP,KAAK,CAACS,KAAK,IAAI,EAAE;EAE7B,IAAMF,kBAAkB,GAAGX,QAAQ,CAACW,kBAAkB,CAAC,CAAC;EAExD,IAAMG,cAAc,GAAGR,OAAO,CAACS,EAAE,GAAGX,KAAK,CAACU,cAAc,GAAGV,KAAK,CAACS,KAAK;;EAEtE;EACA;EACA,IAAIG,MAAM;EAEV,QAAQX,MAAM;IACb,KAAK,UAAU;MACd;MACA;MACA,IAAI,CAACS,cAAc,EAAE;QACpB,OAAO,EAAE;MACV;MACAE,MAAM,GAAGC,oBAAoB,CAACH,cAAc,EAAEV,KAAK,CAACc,WAAW,EAAE,UAAU,EAAElB,QAAQ,EAAEM,OAAO,CAAC;MAC/F,OAAOa,YAAY,CAACH,MAAM,EAAEZ,KAAK,CAACF,GAAG,EAAEF,QAAQ,EAAEM,OAAO,CAACT,eAAe,CAAC;IAE1E,KAAK,eAAe;MACnB;MACA;MACA,IAAI,CAACiB,cAAc,EAAE;QACpB,WAAAb,MAAA,CAAWU,kBAAkB;MAC9B;MACAK,MAAM,GAAGC,oBAAoB,CAACH,cAAc,EAAE,IAAI,EAAE,eAAe,EAAEd,QAAQ,EAAEM,OAAO,CAAC;MACvFU,MAAM,OAAAf,MAAA,CAAOU,kBAAkB,OAAAV,MAAA,CAAIe,MAAM,CAAE;MAC3C,OAAOG,YAAY,CAACH,MAAM,EAAEZ,KAAK,CAACF,GAAG,EAAEF,QAAQ,EAAEM,OAAO,CAACT,eAAe,CAAC;IAE1E,KAAK,OAAO;MACX;MACA,WAAAI,MAAA,CAAWU,kBAAkB,EAAAV,MAAA,CAAGa,cAAc;IAE/C,KAAK,SAAS;MACb,OAAOnB,aAAa,CAAC;QACpBqB,MAAM,MAAAf,MAAA,CAAMU,kBAAkB,EAAAV,MAAA,CAAGa,cAAc,CAAE;QACjDZ,GAAG,EAAEE,KAAK,CAACF;MACZ,CAAC,CAAC;;IAEH;IACA;IACA;IACA;IACA,KAAK,KAAK;MACT,IAAI,CAACI,OAAO,CAACc,WAAW,EAAE;QACzB;QACA;MACD;MACA,IAAMtB,eAAe,GAAGuB,SAAS,CAChCP,cAAc,EACdV,KAAK,CAACc,WAAW,EACjBP,kBAAkB,EAClBL,OAAO,CAACc,WAAW,EACnBpB,QACD,CAAC;MACD,OAAOmB,YAAY,CAACrB,eAAe,EAAEM,KAAK,CAACF,GAAG,EAAEF,QAAQ,EAAEM,OAAO,CAACT,eAAe,CAAC;IAEnF;MACC,MAAM,IAAIa,KAAK,gEAAAT,MAAA,CAA2DI,MAAM,OAAG,CAAC;EACtF;AACD;AAEA,SAASY,oBAAoBA,CAACD,MAAM,EAAEE,WAAW,EAAEI,QAAQ,EAAEtB,QAAQ,EAAEM,OAAO,EAAE;EAC/E,IAAMD,MAAM,GAAGkB,qBAAqB,CAACvB,QAAQ,CAACwB,OAAO,CAAC,CAAC,EAAER,MAAM,CAAC;EAChE,IAAI,CAACX,MAAM,EAAE;IACZ,OAAOW,MAAM;EACd;EACA,OAAOzB,+BAA+B,CACrCyB,MAAM,EACNX,MAAM,EACN;IACCoB,sBAAsB,EAAEH,QAAQ,KAAK,eAAe;IACpDI,kBAAkB,EAAErB,MAAM,CAACsB,sDAAsD,CAAC,CAAC,IAAKrB,OAAO,IAAIA,OAAO,CAACsB,cAAc,KAAK,KAAM,GAAG,KAAK,GAAG,IAAI;IACnJV,WAAW,EAAXA,WAAW;IACXlB,QAAQ,EAARA;EACD,CACD,CAAC;AACF;AAEA,OAAO,SAASuB,qBAAqBA,CAACM,gBAAgB,EAAEC,eAAe,EAAE;EACxE,SAAAC,SAAA,GAAAC,+BAAA,CAAqBH,gBAAgB,GAAAI,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAE;IAAA,IAA5B7B,MAAM,GAAA4B,KAAA,CAAAE,KAAA;IAChB;IACA;IACA;IACA,IAAI9B,MAAM,CAAC+B,qBAAqB,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAC9C;MACA,IAAMC,wBAAwB,GAAGjC,MAAM,CAAC+B,qBAAqB,CAAC,CAAC,CAAC/B,MAAM,CAAC+B,qBAAqB,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC;MAC1G;MACA,IAAIP,eAAe,CAACS,MAAM,CAACD,wBAAwB,CAAC,KAAK,CAAC,EAAE;QAC3D;MACD;IACD;IACA;IACA,IAAIhD,eAAe,CAACwC,eAAe,EAAEzB,MAAM,CAACmC,OAAO,CAAC,CAAC,CAAC,EAAE;MACvD,OAAOnC,MAAM;IACd;EACD;AACD;AAEA,SAASc,YAAYA,CAACrB,eAAe,EAAEI,GAAG,EAAEF,QAAQ,EAAEH,eAAe,EAAE;EACtE,OAAOK,GAAG,GAAGL,eAAe,CAACC,eAAe,EAAEI,GAAG,EAAEF,QAAQ,CAAC,GAAGF,eAAe;AAC/E;AAEA,SAASuB,SAASA,CACjBP,cAAc,EACdI,WAAW,EACXP,kBAAkB,EAClBS,WAAW,EACXpB,QAAQ,EACP;EACD,IAAMyC,sBAAsB,GAAGhD,qBAAqB,CAAC2B,WAAW,EAAEpB,QAAQ,CAACA,QAAQ,CAAC;EACpF;EACA,IAAIyC,sBAAsB,KAAK9B,kBAAkB,EAAE;IAClD,IAAMb,eAAe,GAAGmB,oBAAoB,CAACH,cAAc,EAAEI,WAAW,EAAE,UAAU,EAAElB,QAAQ,CAAC;IAC/F;IACA;IACA,IAAIW,kBAAkB,KAAK,GAAG,EAAE;MAC/B,OAAOA,kBAAkB,GAAG,GAAG,GAAGb,eAAe;IAClD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAOA,eAAe;EACvB;EACA,IAAM4C,SAAS,GAAGhD,YAAY,CAAC0B,WAAW,EAAEuB,SAAS,EAAE3C,QAAQ,CAACA,QAAQ,CAAC;EACzE,IAAI0C,SAAS,EAAE;IACd,UAAAzC,MAAA,CAAUyC,SAAS,OAAAzC,MAAA,CAAIU,kBAAkB,OAAAV,MAAA,CAAIgB,oBAAoB,CAACH,cAAc,EAAE,IAAI,EAAE,eAAe,EAAEd,QAAQ,CAAC;EACnH;AACD", "ignoreList": []}