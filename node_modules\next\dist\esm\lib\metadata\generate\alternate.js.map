{"version": 3, "sources": ["../../../../src/lib/metadata/generate/alternate.tsx"], "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AlternateLink", "descriptor", "props", "url", "link", "title", "href", "toString", "AlternatesMetadata", "alternates", "canonical", "languages", "media", "types", "rel", "Object", "entries", "flatMap", "locale", "descriptors", "map", "hrefLang", "mediaName", "type"], "mappings": ";AAGA,OAAOA,WAAW,QAAO;AACzB,SAASC,UAAU,QAAQ,SAAQ;AAEnC,SAASC,cAAc,EACrBC,UAAU,EACV,GAAGC,OAGwC;IAC3C,IAAI,CAACD,WAAWE,GAAG,EAAE,OAAO;IAC5B,qBACE,KAACC;QACE,GAAGF,KAAK;QACR,GAAID,WAAWI,KAAK,IAAI;YAAEA,OAAOJ,WAAWI,KAAK;QAAC,CAAC;QACpDC,MAAML,WAAWE,GAAG,CAACI,QAAQ;;AAGnC;AAEA,OAAO,SAASC,mBAAmB,EACjCC,UAAU,EAGX;IACC,IAAI,CAACA,YAAY,OAAO;IAExB,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAE,GAAGJ;IAE/C,OAAOV,WAAW;QAChBW,YACIV,cAAc;YAAEc,KAAK;YAAab,YAAYS;QAAU,KACxD;QACJC,YACII,OAAOC,OAAO,CAACL,WAAWM,OAAO,CAAC,CAAC,CAACC,QAAQC,YAAY,GACtDA,+BAAAA,YAAaC,GAAG,CAAC,CAACnB,aAChBD,cAAc;oBAAEc,KAAK;oBAAaO,UAAUH;oBAAQjB;gBAAW,OAGnE;QACJW,QACIG,OAAOC,OAAO,CAACJ,OAAOK,OAAO,CAAC,CAAC,CAACK,WAAWH,YAAY,GACrDA,+BAAAA,YAAaC,GAAG,CAAC,CAACnB,aAChBD,cAAc;oBAAEc,KAAK;oBAAaF,OAAOU;oBAAWrB;gBAAW,OAGnE;QACJY,QACIE,OAAOC,OAAO,CAACH,OAAOI,OAAO,CAAC,CAAC,CAACM,MAAMJ,YAAY,GAChDA,+BAAAA,YAAaC,GAAG,CAAC,CAACnB,aAChBD,cAAc;oBAAEc,KAAK;oBAAaS;oBAAMtB;gBAAW,OAGvD;KACL;AACH"}