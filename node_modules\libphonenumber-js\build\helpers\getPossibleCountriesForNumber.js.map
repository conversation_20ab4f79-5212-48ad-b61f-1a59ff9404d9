{"version": 3, "file": "getPossibleCountriesForNumber.js", "names": ["_metadata2", "_interopRequireDefault", "require", "e", "__esModule", "getPossibleCountriesForNumber", "callingCode", "nationalNumber", "metadata", "_metadata", "<PERSON><PERSON><PERSON>", "possibleCountries", "getCountryCodesForCallingCode", "filter", "country", "couldNationalNumberBelongToCountry", "selectNumberingPlan", "numberingPlan", "possibleLengths", "indexOf", "length"], "sources": ["../../source/helpers/getPossibleCountriesForNumber.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\n\r\n/**\r\n * Returns a list of countries that the phone number could potentially belong to.\r\n * @param  {string} callingCode — Calling code.\r\n * @param  {string} nationalNumber — National (significant) number.\r\n * @param  {object} metadata — Metadata.\r\n * @return {string[]} A list of possible countries.\r\n */\r\nexport default function getPossibleCountriesForNumber(callingCode, nationalNumber, metadata) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\tlet possibleCountries = _metadata.getCountryCodesForCallingCode(callingCode)\r\n\tif (!possibleCountries) {\r\n\t\treturn []\r\n\t}\r\n\treturn possibleCountries.filter((country) => {\r\n\t\treturn couldNationalNumberBelongToCountry(nationalNumber, country, metadata)\r\n\t})\r\n}\r\n\r\nfunction couldNationalNumberBelongToCountry(nationalNumber, country, metadata) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\t_metadata.selectNumberingPlan(country)\r\n\tif (_metadata.numberingPlan.possibleLengths().indexOf(nationalNumber.length) >= 0) {\r\n\t\treturn true\r\n\t}\r\n\treturn false\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqC,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASE,6BAA6BA,CAACC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,EAAE;EAC5F,IAAMC,SAAS,GAAG,IAAIC,qBAAQ,CAACF,QAAQ,CAAC;EACxC,IAAIG,iBAAiB,GAAGF,SAAS,CAACG,6BAA6B,CAACN,WAAW,CAAC;EAC5E,IAAI,CAACK,iBAAiB,EAAE;IACvB,OAAO,EAAE;EACV;EACA,OAAOA,iBAAiB,CAACE,MAAM,CAAC,UAACC,OAAO,EAAK;IAC5C,OAAOC,kCAAkC,CAACR,cAAc,EAAEO,OAAO,EAAEN,QAAQ,CAAC;EAC7E,CAAC,CAAC;AACH;AAEA,SAASO,kCAAkCA,CAACR,cAAc,EAAEO,OAAO,EAAEN,QAAQ,EAAE;EAC9E,IAAMC,SAAS,GAAG,IAAIC,qBAAQ,CAACF,QAAQ,CAAC;EACxCC,SAAS,CAACO,mBAAmB,CAACF,OAAO,CAAC;EACtC,IAAIL,SAAS,CAACQ,aAAa,CAACC,eAAe,CAAC,CAAC,CAACC,OAAO,CAACZ,cAAc,CAACa,MAAM,CAAC,IAAI,CAAC,EAAE;IAClF,OAAO,IAAI;EACZ;EACA,OAAO,KAAK;AACb", "ignoreList": []}