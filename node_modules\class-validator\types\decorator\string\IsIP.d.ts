import { ValidationOptions } from '../ValidationOptions';
export type IsIpVersion = '4' | '6' | 4 | 6;
export declare const IS_IP = "isIp";
/**
 * Checks if the string is an IP (version 4 or 6).
 * If given value is not a string, then it returns false.
 */
export declare function isIP(value: unknown, version?: IsIpVersion): boolean;
/**
 * Checks if the string is an IP (version 4 or 6).
 * If given value is not a string, then it returns false.
 */
export declare function IsIP(version?: IsIpVersion, validationOptions?: ValidationOptions): PropertyDecorator;
