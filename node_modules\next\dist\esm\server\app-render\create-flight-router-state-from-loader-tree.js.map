{"version": 3, "sources": ["../../../src/server/app-render/create-flight-router-state-from-loader-tree.ts"], "names": ["PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "segment", "searchParams", "isPageSegment", "stringified<PERSON><PERSON>y", "JSON", "stringify", "createFlightRouterStateFromLoaderTree", "parallelRoutes", "layout", "getDynamicParamFromSegment", "rootLayoutIncluded", "dynamicParam", "treeSegment", "segmentTree", "Object", "keys", "reduce", "existingValue", "currentValue"], "mappings": "AAGA,SAASA,gBAAgB,QAAQ,2BAA0B;AAE3D,OAAO,SAASC,6BACdC,OAAgB,EAChBC,YAAiB;IAEjB,MAAMC,gBAAgBF,YAAYF;IAElC,IAAII,eAAe;QACjB,MAAMC,mBAAmBC,KAAKC,SAAS,CAACJ;QACxC,OAAOE,qBAAqB,OACxBH,UAAU,MAAMG,mBAChBH;IACN;IAEA,OAAOA;AACT;AAEA,OAAO,SAASM,sCACd,CAACN,SAASO,gBAAgB,EAAEC,MAAM,EAAE,CAAa,EACjDC,0BAAsD,EACtDR,YAAiB,EACjBS,qBAAqB,KAAK;IAE1B,MAAMC,eAAeF,2BAA2BT;IAChD,MAAMY,cAAcD,eAAeA,aAAaC,WAAW,GAAGZ;IAE9D,MAAMa,cAAiC;QACrCd,6BAA6Ba,aAAaX;QAC1C,CAAC;KACF;IAED,IAAI,CAACS,sBAAsB,OAAOF,WAAW,aAAa;QACxDE,qBAAqB;QACrBG,WAAW,CAAC,EAAE,GAAG;IACnB;IAEAA,WAAW,CAAC,EAAE,GAAGC,OAAOC,IAAI,CAACR,gBAAgBS,MAAM,CACjD,CAACC,eAAeC;QACdD,aAAa,CAACC,aAAa,GAAGZ,sCAC5BC,cAAc,CAACW,aAAa,EAC5BT,4BACAR,cACAS;QAEF,OAAOO;IACT,GACA,CAAC;IAGH,OAAOJ;AACT"}