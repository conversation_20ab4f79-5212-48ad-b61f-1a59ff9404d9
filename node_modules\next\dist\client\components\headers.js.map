{"version": 3, "sources": ["../../../src/client/components/headers.ts"], "names": ["cookies", "draftMode", "headers", "callingExpression", "staticGenerationStore", "staticGenerationAsyncStorage", "getStore", "forceStatic", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seal", "Headers", "trackDynamicDataAccessed", "getExpectedRequestStore", "RequestCookiesAdapter", "RequestCookies", "requestStore", "asyncActionStore", "actionAsyncStorage", "isAction", "isAppRoute", "mutableCookies", "DraftMode"], "mappings": ";;;;;;;;;;;;;;;;IAsCgBA,OAAO;eAAPA;;IA0BAC,SAAS;eAATA;;IA3CAC,OAAO;eAAPA;;;gCAlBT;yBACwB;yBACA;4CACI;2BACT;kCACe;sDACI;6CACL;AAWjC,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,wBAAwBC,kEAA4B,CAACC,QAAQ;IAEnE,IAAIF,uBAAuB;QACzB,IAAIA,sBAAsBG,WAAW,EAAE;YACrC,wGAAwG;YACxG,OAAOC,uBAAc,CAACC,IAAI,CAAC,IAAIC,QAAQ,CAAC;QAC1C,OAAO;YACL,wGAAwG;YACxGC,IAAAA,0CAAwB,EAACP,uBAAuBD;QAClD;IACF;IAEA,OAAOS,IAAAA,oDAAuB,EAACT,mBAAmBD,OAAO;AAC3D;AAEO,SAASF;IACd,MAAMG,oBAAoB;IAC1B,MAAMC,wBAAwBC,kEAA4B,CAACC,QAAQ;IAEnE,IAAIF,uBAAuB;QACzB,IAAIA,sBAAsBG,WAAW,EAAE;YACrC,wGAAwG;YACxG,OAAOM,qCAAqB,CAACJ,IAAI,CAAC,IAAIK,uBAAc,CAAC,IAAIJ,QAAQ,CAAC;QACpE,OAAO;YACL,wGAAwG;YACxGC,IAAAA,0CAAwB,EAACP,uBAAuBD;QAClD;IACF;IAEA,MAAMY,eAAeH,IAAAA,oDAAuB,EAACT;IAE7C,MAAMa,mBAAmBC,8CAAkB,CAACX,QAAQ;IACpD,IAAIU,CAAAA,oCAAAA,iBAAkBE,QAAQ,MAAIF,oCAAAA,iBAAkBG,UAAU,GAAE;QAC9D,2EAA2E;QAC3E,+DAA+D;QAC/D,OAAOJ,aAAaK,cAAc;IACpC;IAEA,OAAOL,aAAaf,OAAO;AAC7B;AAEO,SAASC;IACd,MAAME,oBAAoB;IAC1B,MAAMY,eAAeH,IAAAA,oDAAuB,EAACT;IAE7C,OAAO,IAAIkB,oBAAS,CAACN,aAAad,SAAS;AAC7C"}