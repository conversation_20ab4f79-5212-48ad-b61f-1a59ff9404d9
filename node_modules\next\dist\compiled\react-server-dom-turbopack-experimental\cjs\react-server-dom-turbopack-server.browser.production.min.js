/*
 React
 react-server-dom-turbopack-server.browser.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var aa=require("react"),ba=require("react-dom"),l=null,p=0;function q(a,b){if(0!==b.byteLength)if(2048<b.byteLength)0<p&&(a.enqueue(new Uint8Array(l.buffer,0,p)),l=new Uint8Array(2048),p=0),a.enqueue(b);else{var c=l.length-p;c<b.byteLength&&(0===c?a.enqueue(l):(l.set(b.subarray(0,c),p),a.enqueue(l),b=b.subarray(c)),l=new Uint8Array(2048),p=0);l.set(b,p);p+=b.byteLength}return!0}var r=new TextEncoder;function ca(a,b){"function"===typeof a.error?a.error(b):a.close()}
var t=Symbol.for("react.client.reference"),da=Symbol.for("react.server.reference");function u(a,b,c){return Object.defineProperties(a,{$$typeof:{value:t},$$id:{value:b},$$async:{value:c}})}var ea=Function.prototype.bind,fa=Array.prototype.slice;function ia(){var a=ea.apply(this,arguments);if(this.$$typeof===da){var b=fa.call(arguments,1);return Object.defineProperties(a,{$$typeof:{value:da},$$id:{value:this.$$id},$$bound:{value:this.$$bound?this.$$bound.concat(b):b},bind:{value:ia}})}return a}
var ja=Promise.prototype,ka={get:function(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "displayName":return;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");
}throw Error("Cannot access "+(String(a.name)+"."+String(b))+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.");},set:function(){throw Error("Cannot assign to a client module from a server module.");}};
function la(a,b){switch(b){case "$$typeof":return a.$$typeof;case "$$id":return a.$$id;case "$$async":return a.$$async;case "name":return a.name;case "defaultProps":return;case "toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case "__esModule":var c=a.$$id;a.default=u(function(){throw Error("Attempted to call the default export of "+c+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#",a.$$async);return!0;case "then":if(a.then)return a.then;if(a.$$async)return;var e=u({},a.$$id,!0),d=new Proxy(e,ma);a.status="fulfilled";a.value=d;return a.then=u(function(g){return Promise.resolve(g(d))},a.$$id+"#then",!1)}if("symbol"===typeof b)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");e=a[b];e||(e=u(function(){throw Error("Attempted to call "+String(b)+"() from the server but "+String(b)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
},a.$$id+"#"+b,a.$$async),Object.defineProperty(e,"name",{value:b}),e=a[b]=new Proxy(e,ka));return e}
var ma={get:function(a,b){return la(a,b)},getOwnPropertyDescriptor:function(a,b){var c=Object.getOwnPropertyDescriptor(a,b);c||(c={value:la(a,b),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(a,b,c));return c},getPrototypeOf:function(){return ja},set:function(){throw Error("Cannot assign to a client module from a server module.");}},ua={prefetchDNS:na,preconnect:oa,preload:pa,preloadModule:qa,preinitStyle:ra,preinitScript:sa,preinitModuleScript:ta};
function na(a){if("string"===typeof a&&a){var b=v?v:null;if(b){var c=b.hints,e="D|"+a;c.has(e)||(c.add(e),w(b,"D",a))}}}function oa(a,b){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,d="C|"+(null==b?"null":b)+"|"+a;e.has(d)||(e.add(d),"string"===typeof b?w(c,"C",[a,b]):w(c,"C",a))}}}
function pa(a,b,c){if("string"===typeof a){var e=v?v:null;if(e){var d=e.hints,g="L";if("image"===b&&c){var f=c.imageSrcSet,k=c.imageSizes,h="";"string"===typeof f&&""!==f?(h+="["+f+"]","string"===typeof k&&(h+="["+k+"]")):h+="[][]"+a;g+="[image]"+h}else g+="["+b+"]"+a;d.has(g)||(d.add(g),(c=x(c))?w(e,"L",[a,b,c]):w(e,"L",[a,b]))}}}function qa(a,b){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,d="m|"+a;if(!e.has(d))return e.add(d),(b=x(b))?w(c,"m",[a,b]):w(c,"m",a)}}}
function ra(a,b,c){if("string"===typeof a){var e=v?v:null;if(e){var d=e.hints,g="S|"+a;if(!d.has(g))return d.add(g),(c=x(c))?w(e,"S",[a,"string"===typeof b?b:0,c]):"string"===typeof b?w(e,"S",[a,b]):w(e,"S",a)}}}function sa(a,b){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,d="X|"+a;if(!e.has(d))return e.add(d),(b=x(b))?w(c,"X",[a,b]):w(c,"X",a)}}}
function ta(a,b){if("string"===typeof a){var c=v?v:null;if(c){var e=c.hints,d="M|"+a;if(!e.has(d))return e.add(d),(b=x(b))?w(c,"M",[a,b]):w(c,"M",a)}}}function x(a){if(null==a)return null;var b=!1,c={},e;for(e in a)null!=a[e]&&(b=!0,c[e]=a[e]);return b?c:null}
var va=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,z=Symbol.for("react.element"),wa=Symbol.for("react.fragment"),xa=Symbol.for("react.context"),ya=Symbol.for("react.forward_ref"),za=Symbol.for("react.suspense"),Aa=Symbol.for("react.suspense_list"),Ba=Symbol.for("react.memo"),A=Symbol.for("react.lazy"),Ca=Symbol.for("react.memo_cache_sentinel"),B=Symbol.for("react.postpone"),Da=Symbol.iterator,Ea=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function Fa(){}function Ga(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(Fa,Fa),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(e){if("pending"===b.status){var d=b;d.status="fulfilled";d.value=e}},function(e){if("pending"===b.status){var d=b;d.status="rejected";d.reason=e}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}Ha=b;throw Ea;}}var Ha=null;
function Ia(){if(null===Ha)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=Ha;Ha=null;return a}var D=null,Ja=0,E=null;function Ka(){var a=E||[];E=null;return a}
var Pa={useMemo:function(a){return a()},useCallback:function(a){return a},useDebugValue:function(){},useDeferredValue:F,useTransition:F,readContext:La,useContext:La,useReducer:F,useRef:F,useState:F,useInsertionEffect:F,useLayoutEffect:F,useImperativeHandle:F,useEffect:F,useId:Ma,useSyncExternalStore:F,useCacheRefresh:function(){return Na},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=Ca;return b},use:Oa};
function F(){throw Error("This Hook is not supported in Server Components.");}function Na(){throw Error("Refreshing the cache is not supported in Server Components.");}function La(){throw Error("Cannot read a Client Context from a Server Component.");}function Ma(){if(null===D)throw Error("useId can only be used while React is rendering");var a=D.identifierCount++;return":"+D.identifierPrefix+"S"+a.toString(32)+":"}
function Oa(a){if(null!==a&&"object"===typeof a||"function"===typeof a){if("function"===typeof a.then){var b=Ja;Ja+=1;null===E&&(E=[]);return Ga(E,a,b)}a.$$typeof===xa&&La()}if(a.$$typeof===t){if(null!=a.value&&a.value.$$typeof===xa)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.");}throw Error("An unsupported type was passed to use(): "+String(a));}function Qa(){return(new AbortController).signal}
function Ra(){var a=v?v:null;return a?a.cache:new Map}var Sa={getCacheSignal:function(){var a=Ra(),b=a.get(Qa);void 0===b&&(b=Qa(),a.set(Qa,b));return b},getCacheForType:function(a){var b=Ra(),c=b.get(a);void 0===c&&(c=a(),b.set(a,c));return c}},Ta=Array.isArray,Ua=Object.getPrototypeOf;function Va(a){return Object.prototype.toString.call(a).replace(/^\[object (.*)\]$/,function(b,c){return c})}
function Wa(a){switch(typeof a){case "string":return JSON.stringify(10>=a.length?a:a.slice(0,10)+"...");case "object":if(Ta(a))return"[...]";if(null!==a&&a.$$typeof===Xa)return"client";a=Va(a);return"Object"===a?"{...}":a;case "function":return a.$$typeof===Xa?"client":(a=a.displayName||a.name)?"function "+a:"function";default:return String(a)}}
function Ya(a){if("string"===typeof a)return a;switch(a){case za:return"Suspense";case Aa:return"SuspenseList"}if("object"===typeof a)switch(a.$$typeof){case ya:return Ya(a.render);case Ba:return Ya(a.type);case A:var b=a._payload;a=a._init;try{return Ya(a(b))}catch(c){}}return""}var Xa=Symbol.for("react.client.reference");
function G(a,b){var c=Va(a);if("Object"!==c&&"Array"!==c)return c;c=-1;var e=0;if(Ta(a)){var d="[";for(var g=0;g<a.length;g++){0<g&&(d+=", ");var f=a[g];f="object"===typeof f&&null!==f?G(f):Wa(f);""+g===b?(c=d.length,e=f.length,d+=f):d=10>f.length&&40>d.length+f.length?d+f:d+"..."}d+="]"}else if(a.$$typeof===z)d="<"+Ya(a.type)+"/>";else{if(a.$$typeof===Xa)return"client";d="{";g=Object.keys(a);for(f=0;f<g.length;f++){0<f&&(d+=", ");var k=g[f],h=JSON.stringify(k);d+=('"'+k+'"'===h?k:h)+": ";h=a[k];
h="object"===typeof h&&null!==h?G(h):Wa(h);k===b?(c=d.length,e=h.length,d+=h):d=10>h.length&&40>d.length+h.length?d+h:d+"..."}d+="}"}return void 0===b?d:-1<c&&0<e?(a=" ".repeat(c)+"^".repeat(e),"\n  "+d+"\n  "+a):"\n  "+d}var Za=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,H=aa.__SECRET_SERVER_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
if(!H)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var $a=Object.prototype,I=JSON.stringify,ab=H.TaintRegistryObjects,J=H.TaintRegistryValues,bb=H.TaintRegistryByteLengths,cb=H.TaintRegistryPendingRequests,db=H.ReactCurrentCache,eb=Za.ReactCurrentDispatcher;function K(a){throw Error(a);}
function fb(a){a=a.taintCleanupQueue;cb.delete(a);for(var b=0;b<a.length;b++){var c=a[b],e=J.get(c);void 0!==e&&(1===e.count?J.delete(c):e.count--)}a.length=0}function gb(a){console.error(a)}function hb(){}
function ib(a,b,c,e,d){if(null!==db.current&&db.current!==Sa)throw Error("Currently React only supports one RSC renderer at a time.");va.current=ua;db.current=Sa;var g=new Set,f=[],k=[];cb.add(k);var h=new Set;b={status:0,flushScheduled:!1,fatalError:null,destination:null,bundlerConfig:b,cache:new Map,nextChunkId:0,pendingChunks:0,hints:h,abortableTasks:g,pingedTasks:f,completedImportChunks:[],completedHintChunks:[],completedRegularChunks:[],completedErrorChunks:[],writtenSymbols:new Map,writtenClientReferences:new Map,
writtenServerReferences:new Map,writtenObjects:new WeakMap,identifierPrefix:e||"",identifierCount:1,taintCleanupQueue:k,onError:void 0===c?gb:c,onPostpone:void 0===d?hb:d};a=jb(b,a,null,!1,g);f.push(a);return b}var v=null;
function kb(a,b,c){var e=jb(a,null,b.keyPath,b.implicitSlot,a.abortableTasks);switch(c.status){case "fulfilled":return e.model=c.value,lb(a,e),e.id;case "rejected":return b=c.reason,"object"===typeof b&&null!==b&&b.$$typeof===B?(L(a,b.message),M(a,e.id)):(b=N(a,b),O(a,e.id,b)),e.id;default:"string"!==typeof c.status&&(c.status="pending",c.then(function(d){"pending"===c.status&&(c.status="fulfilled",c.value=d)},function(d){"pending"===c.status&&(c.status="rejected",c.reason=d)}))}c.then(function(d){e.model=
d;lb(a,e)},function(d){"object"===typeof d&&null!==d&&d.$$typeof===B?(L(a,d.message),M(a,e.id)):(e.status=4,d=N(a,d),O(a,e.id,d));a.abortableTasks.delete(e);null!==a.destination&&P(a,a.destination)});return e.id}function w(a,b,c){c=I(c);var e=a.nextChunkId++;b="H"+b;b=e.toString(16)+":"+b;c=r.encode(b+c+"\n");a.completedHintChunks.push(c);!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination&&(c=a.destination,a.flushScheduled=!0,P(a,c))}
function mb(a){if("fulfilled"===a.status)return a.value;if("rejected"===a.status)throw a.reason;throw a;}function nb(a){switch(a.status){case "fulfilled":case "rejected":break;default:"string"!==typeof a.status&&(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)}))}return{$$typeof:A,_payload:a,_init:mb}}
function ob(a,b,c,e,d){var g=b.thenableState;b.thenableState=null;Ja=0;E=g;e=e(d,void 0);if("object"===typeof e&&null!==e&&"function"===typeof e.then){d=e;if("fulfilled"===d.status)return d.value;e=nb(e)}d=b.keyPath;g=b.implicitSlot;null!==c?b.keyPath=null===d?c:d+","+c:null===d&&(b.implicitSlot=!0);a=Q(a,b,pb,"",e);b.keyPath=d;b.implicitSlot=g;return a}function qb(a,b,c){return null!==b.keyPath?(a=[z,wa,b.keyPath,{children:c}],b.implicitSlot?[a]:a):c}
function rb(a,b,c,e){var d=a.keyPath;null===c?c=d:null!==d&&(c=d+","+c);b=[z,b,c,e];return a.implicitSlot&&null!==c?[b]:b}
function sb(a,b,c,e,d,g){if(null!==d&&void 0!==d)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"===typeof c)return c.$$typeof===t?rb(b,c,e,g):ob(a,b,e,c,g);if("string"===typeof c)return rb(b,c,e,g);if("symbol"===typeof c)return c===wa&&null===e?(e=b.implicitSlot,null===b.keyPath&&(b.implicitSlot=!0),a=Q(a,b,pb,"",g.children),b.implicitSlot=e,a):rb(b,c,e,g);if(null!=c&&"object"===typeof c){if(c.$$typeof===t)return rb(b,c,e,g);switch(c.$$typeof){case A:var f=
c._init;c=f(c._payload);return sb(a,b,c,e,d,g);case ya:return ob(a,b,e,c.render,g);case Ba:return sb(a,b,c.type,e,d,g)}}throw Error("Unsupported Server Component type: "+Wa(c));}function lb(a,b){var c=a.pingedTasks;c.push(b);1===c.length&&(a.flushScheduled=null!==a.destination,tb(a))}
function jb(a,b,c,e,d){a.pendingChunks++;var g=a.nextChunkId++;"object"!==typeof b||null===b||null!==c||e||a.writtenObjects.set(b,g);var f={id:g,status:0,model:b,keyPath:c,implicitSlot:e,ping:function(){return lb(a,f)},toJSON:function(k,h){a:{var m=f.keyPath,y=f.implicitSlot;try{var n=Q(a,f,this,k,h)}catch(ha){k=ha===Ea?Ia():ha;h=f.model;h="object"===typeof h&&null!==h&&(h.$$typeof===z||h.$$typeof===A);if("object"===typeof k&&null!==k){if("function"===typeof k.then){n=jb(a,f.model,f.keyPath,f.implicitSlot,
a.abortableTasks);var C=n.ping;k.then(C,C);n.thenableState=Ka();f.keyPath=m;f.implicitSlot=y;n=h?"$L"+n.id.toString(16):R(n.id);break a}if(k.$$typeof===B){a.pendingChunks++;n=a.nextChunkId++;L(a,k.message);M(a,n);f.keyPath=m;f.implicitSlot=y;n=h?"$L"+n.toString(16):R(n);break a}}f.keyPath=m;f.implicitSlot=y;if(h)a.pendingChunks++,m=a.nextChunkId++,y=N(a,k),O(a,m,y),n="$L"+m.toString(16);else throw k;}}return n},thenableState:null};d.add(f);return f}function R(a){return"$"+a.toString(16)}
function ub(a,b,c){a=I(c);b=b.toString(16)+":"+a+"\n";return r.encode(b)}
function vb(a,b,c,e){var d=e.$$async?e.$$id+"#async":e.$$id,g=a.writtenClientReferences,f=g.get(d);if(void 0!==f)return b[0]===z&&"1"===c?"$L"+f.toString(16):R(f);try{var k=a.bundlerConfig,h=e.$$id;f="";var m=k[h];if(m)f=m.name;else{var y=h.lastIndexOf("#");-1!==y&&(f=h.slice(y+1),m=k[h.slice(0,y)]);if(!m)throw Error('Could not find the module "'+h+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.');}var n=!0===e.$$async?[m.id,m.chunks,f,1]:[m.id,m.chunks,
f];a.pendingChunks++;var C=a.nextChunkId++,ha=I(n),Mb=C.toString(16)+":I"+ha+"\n",Nb=r.encode(Mb);a.completedImportChunks.push(Nb);g.set(d,C);return b[0]===z&&"1"===c?"$L"+C.toString(16):R(C)}catch(Ob){return a.pendingChunks++,b=a.nextChunkId++,c=N(a,Ob),O(a,b,c),R(b)}}function S(a,b){b=jb(a,b,null,!1,a.abortableTasks);wb(a,b);return b.id}
function T(a,b,c){if(bb.has(c.byteLength)){var e=J.get(String.fromCharCode.apply(String,new Uint8Array(c.buffer,c.byteOffset,c.byteLength)));void 0!==e&&K(e.message)}a.pendingChunks+=2;e=a.nextChunkId++;var d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength);c=2048<c.byteLength?d.slice():d;d=c.byteLength;b=e.toString(16)+":"+b+d.toString(16)+",";b=r.encode(b);a.completedRegularChunks.push(b,c);return R(e)}var U=!1;
function Q(a,b,c,e,d){b.model=d;if(d===z)return"$";if(null===d)return null;if("object"===typeof d){switch(d.$$typeof){case z:c=a.writtenObjects;e=c.get(d);if(void 0!==e){if(null===b.keyPath&&!b.implicitSlot)if(U===d)U=null;else return-1===e?(a=S(a,d),R(a)):R(e)}else c.set(d,-1);c=d.props;e=c.ref;return sb(a,b,d.type,d.key,void 0!==e?e:null,c);case A:return b.thenableState=null,c=d._init,d=c(d._payload),Q(a,b,pb,"",d)}if(d.$$typeof===t)return vb(a,c,e,d);c=ab.get(d);void 0!==c&&K(c);c=a.writtenObjects;
e=c.get(d);if("function"===typeof d.then){if(void 0!==e){if(null!==b.keyPath||b.implicitSlot)return"$@"+kb(a,b,d).toString(16);if(U===d)U=null;else return"$@"+e.toString(16)}a=kb(a,b,d);c.set(d,a);return"$@"+a.toString(16)}if(void 0!==e)if(U===d)U=null;else return-1===e?(a=S(a,d),R(a)):R(e);else c.set(d,-1);if(Ta(d))return qb(a,b,d);if(d instanceof Map){d=Array.from(d);for(b=0;b<d.length;b++)c=d[b][0],"object"===typeof c&&null!==c&&(e=a.writtenObjects,void 0===e.get(c)&&e.set(c,-1));return"$Q"+S(a,
d).toString(16)}if(d instanceof Set){d=Array.from(d);for(b=0;b<d.length;b++)c=d[b],"object"===typeof c&&null!==c&&(e=a.writtenObjects,void 0===e.get(c)&&e.set(c,-1));return"$W"+S(a,d).toString(16)}if(d instanceof ArrayBuffer)return T(a,"A",new Uint8Array(d));if(d instanceof Int8Array)return T(a,"C",d);if(d instanceof Uint8Array)return T(a,"c",d);if(d instanceof Uint8ClampedArray)return T(a,"U",d);if(d instanceof Int16Array)return T(a,"S",d);if(d instanceof Uint16Array)return T(a,"s",d);if(d instanceof
Int32Array)return T(a,"L",d);if(d instanceof Uint32Array)return T(a,"l",d);if(d instanceof Float32Array)return T(a,"F",d);if(d instanceof Float64Array)return T(a,"d",d);if(d instanceof BigInt64Array)return T(a,"N",d);if(d instanceof BigUint64Array)return T(a,"m",d);if(d instanceof DataView)return T(a,"V",d);null===d||"object"!==typeof d?c=null:(c=Da&&d[Da]||d["@@iterator"],c="function"===typeof c?c:null);if(c)return qb(a,b,Array.from(d));a=Ua(d);if(a!==$a&&(null===a||null!==Ua(a)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported.");
return d}if("string"===typeof d){b=J.get(d);void 0!==b&&K(b.message);if("Z"===d[d.length-1]&&c[e]instanceof Date)return"$D"+d;if(1024<=d.length)return a.pendingChunks+=2,b=a.nextChunkId++,d=r.encode(d),c=d.byteLength,c=b.toString(16)+":T"+c.toString(16)+",",c=r.encode(c),a.completedRegularChunks.push(c,d),R(b);a="$"===d[0]?"$"+d:d;return a}if("boolean"===typeof d)return d;if("number"===typeof d)return Number.isFinite(d)?0===d&&-Infinity===1/d?"$-0":d:Infinity===d?"$Infinity":-Infinity===d?"$-Infinity":
"$NaN";if("undefined"===typeof d)return"$undefined";if("function"===typeof d){if(d.$$typeof===t)return vb(a,c,e,d);if(d.$$typeof===da)return b=a.writtenServerReferences,c=b.get(d),void 0!==c?a="$F"+c.toString(16):(c=d.$$bound,c={id:d.$$id,bound:c?Promise.resolve(c):null},a=S(a,c),b.set(d,a),a="$F"+a.toString(16)),a;a=ab.get(d);void 0!==a&&K(a);if(/^on[A-Z]/.test(e))throw Error("Event handlers cannot be passed to Client Component props."+G(c,e)+"\nIf you need interactivity, consider converting part of this to a Client Component.");
throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+G(c,e));}if("symbol"===typeof d){b=a.writtenSymbols;var g=b.get(d);if(void 0!==g)return R(g);g=d.description;if(Symbol.for(g)!==d)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+(d.description+") cannot be found among global symbols.")+
G(c,e));a.pendingChunks++;c=a.nextChunkId++;e=ub(a,c,"$S"+g);a.completedImportChunks.push(e);b.set(d,c);return R(c)}if("bigint"===typeof d)return a=J.get(d),void 0!==a&&K(a.message),"$n"+d.toString(10);throw Error("Type "+typeof d+" is not supported in Client Component props."+G(c,e));}function L(a,b){var c=v;v=null;try{var e=a.onPostpone;e(b)}finally{v=c}}
function N(a,b){var c=v;v=null;try{var e=a.onError;var d=e(b)}finally{v=c}if(null!=d&&"string"!==typeof d)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof d+'" instead');return d||""}function xb(a,b){fb(a);null!==a.destination?(a.status=2,ca(a.destination,b)):(a.status=1,a.fatalError=b)}
function M(a,b){b=b.toString(16)+":P\n";b=r.encode(b);a.completedErrorChunks.push(b)}function O(a,b,c){c={digest:c};b=b.toString(16)+":E"+I(c)+"\n";b=r.encode(b);a.completedErrorChunks.push(b)}var pb={};
function wb(a,b){if(0===b.status)try{U=b.model;var c=Q(a,b,pb,"",b.model);U=c;b.keyPath=null;b.implicitSlot=!1;var e="object"===typeof c&&null!==c?I(c,b.toJSON):I(c),d=b.id.toString(16)+":"+e+"\n",g=r.encode(d);a.completedRegularChunks.push(g);a.abortableTasks.delete(b);b.status=1}catch(m){var f=m===Ea?Ia():m;if("object"===typeof f&&null!==f){if("function"===typeof f.then){var k=b.ping;f.then(k,k);b.thenableState=Ka();return}if(f.$$typeof===B){a.abortableTasks.delete(b);b.status=4;L(a,f.message);
M(a,b.id);return}}a.abortableTasks.delete(b);b.status=4;var h=N(a,f);O(a,b.id,h)}finally{}}function tb(a){var b=eb.current;eb.current=Pa;var c=v;D=v=a;try{var e=a.pingedTasks;a.pingedTasks=[];for(var d=0;d<e.length;d++)wb(a,e[d]);null!==a.destination&&P(a,a.destination)}catch(g){N(a,g),xb(a,g)}finally{eb.current=b,D=null,v=c}}
function P(a,b){l=new Uint8Array(2048);p=0;try{for(var c=a.completedImportChunks,e=0;e<c.length;e++)a.pendingChunks--,q(b,c[e]);c.splice(0,e);var d=a.completedHintChunks;for(e=0;e<d.length;e++)q(b,d[e]);d.splice(0,e);var g=a.completedRegularChunks;for(e=0;e<g.length;e++)a.pendingChunks--,q(b,g[e]);g.splice(0,e);var f=a.completedErrorChunks;for(e=0;e<f.length;e++)a.pendingChunks--,q(b,f[e]);f.splice(0,e)}finally{a.flushScheduled=!1,l&&0<p&&(b.enqueue(new Uint8Array(l.buffer,0,p)),l=null,p=0)}0===a.pendingChunks&&
(fb(a),b.close())}function yb(a,b){try{var c=a.abortableTasks;if(0<c.size){a.pendingChunks++;var e=a.nextChunkId++;if("object"===typeof b&&null!==b&&b.$$typeof===B)L(a,b.message),M(a,e,b);else{var d=void 0===b?Error("The render was aborted by the server without a reason."):b,g=N(a,d);O(a,e,g,d)}c.forEach(function(f){f.status=3;var k=R(e);f=ub(a,f.id,k);a.completedErrorChunks.push(f)});c.clear()}null!==a.destination&&P(a,a.destination)}catch(f){N(a,f),xb(a,f)}}
function zb(a,b){var c="",e=a[b];if(e)c=e.name;else{var d=b.lastIndexOf("#");-1!==d&&(c=b.slice(d+1),e=a[b.slice(0,d)]);if(!e)throw Error('Could not find the module "'+b+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.');}return[e.id,e.chunks,c]}var Ab=new Map;
function Bb(a){var b=__turbopack_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function Cb(){}
function Db(a){for(var b=a[1],c=[],e=0;e<b.length;e++){var d=b[e],g=Ab.get(d);if(void 0===g){g=__turbopack_load__(d);c.push(g);var f=Ab.set.bind(Ab,d,null);g.then(f,Cb);Ab.set(d,g)}else null!==g&&c.push(g)}return 4===a.length?0===c.length?Bb(a[0]):Promise.all(c).then(function(){return Bb(a[0])}):0<c.length?Promise.all(c):null}
function V(a){var b=__turbopack_require__(a[0]);if(4===a.length&&"function"===typeof b.then)if("fulfilled"===b.status)b=b.value;else throw b.reason;return"*"===a[2]?b:""===a[2]?b.__esModule?b.default:b:b[a[2]]}function W(a,b,c,e){this.status=a;this.value=b;this.reason=c;this._response=e}W.prototype=Object.create(Promise.prototype);
W.prototype.then=function(a,b){switch(this.status){case "resolved_model":Eb(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};function Fb(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}
function Gb(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&Fb(c,b)}}function Hb(a,b,c,e,d,g){var f=zb(a._bundlerConfig,b);a=Db(f);if(c)c=Promise.all([c,a]).then(function(k){k=k[0];var h=V(f);return h.bind.apply(h,[null].concat(k))});else if(a)c=Promise.resolve(a).then(function(){return V(f)});else return V(f);c.then(Ib(e,d,g),Jb(e));return null}var X=null,Y=null;
function Eb(a){var b=X,c=Y;X=a;Y=null;try{var e=JSON.parse(a.value,a._response._fromJSON);null!==Y&&0<Y.deps?(Y.value=e,a.status="blocked",a.value=null,a.reason=null):(a.status="fulfilled",a.value=e)}catch(d){a.status="rejected",a.reason=d}finally{X=b,Y=c}}function Kb(a,b){a._closed=!0;a._closedReason=b;a._chunks.forEach(function(c){"pending"===c.status&&Gb(c,b)})}
function Z(a,b){var c=a._chunks,e=c.get(b);e||(e=a._formData.get(a._prefix+b),e=null!=e?new W("resolved_model",e,null,a):a._closed?new W("rejected",null,a._closedReason,a):new W("pending",null,null,a),c.set(b,e));return e}function Ib(a,b,c){if(Y){var e=Y;e.deps++}else e=Y={deps:1,value:null};return function(d){b[c]=d;e.deps--;0===e.deps&&"blocked"===a.status&&(d=a.value,a.status="fulfilled",a.value=e.value,null!==d&&Fb(d,e.value))}}function Jb(a){return function(b){return Gb(a,b)}}
function Lb(a,b){a=Z(a,b);"resolved_model"===a.status&&Eb(a);if("fulfilled"!==a.status)throw a.reason;return a.value}
function Pb(a,b,c,e){if("$"===e[0])switch(e[1]){case "$":return e.slice(1);case "@":return b=parseInt(e.slice(2),16),Z(a,b);case "S":return Symbol.for(e.slice(2));case "F":return e=parseInt(e.slice(2),16),e=Lb(a,e),Hb(a,e.id,e.bound,X,b,c);case "Q":return b=parseInt(e.slice(2),16),a=Lb(a,b),new Map(a);case "W":return b=parseInt(e.slice(2),16),a=Lb(a,b),new Set(a);case "K":b=e.slice(2);var d=a._prefix+b+"_",g=new FormData;a._formData.forEach(function(f,k){k.startsWith(d)&&g.append(k.slice(d.length),
f)});return g;case "I":return Infinity;case "-":return"$-0"===e?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(e.slice(2)));case "n":return BigInt(e.slice(2));default:e=parseInt(e.slice(1),16);a=Z(a,e);switch(a.status){case "resolved_model":Eb(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":return e=X,a.then(Ib(e,b,c),Jb(e)),null;default:throw a.reason;}}return e}
function Qb(a,b){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:new FormData,e=new Map,d={_bundlerConfig:a,_prefix:b,_formData:c,_chunks:e,_fromJSON:function(g,f){return"string"===typeof f?Pb(d,this,g,f):f},_closed:!1,_closedReason:null};return d}function Rb(a){Kb(a,Error("Connection closed."))}
function Sb(a,b,c){var e=zb(a,b);a=Db(e);return c?Promise.all([c,a]).then(function(d){d=d[0];var g=V(e);return g.bind.apply(g,[null].concat(d))}):a?Promise.resolve(a).then(function(){return V(e)}):Promise.resolve(V(e))}function Tb(a,b,c){a=Qb(b,c,a);Rb(a);a=Z(a,0);a.then(function(){});if("fulfilled"!==a.status)throw a.reason;return a.value}exports.createClientModuleProxy=function(a){a=u({},a,!1);return new Proxy(a,ma)};
exports.decodeAction=function(a,b){var c=new FormData,e=null;a.forEach(function(d,g){g.startsWith("$ACTION_")?g.startsWith("$ACTION_REF_")?(d="$ACTION_"+g.slice(12)+":",d=Tb(a,b,d),e=Sb(b,d.id,d.bound)):g.startsWith("$ACTION_ID_")&&(d=g.slice(11),e=Sb(b,d,null)):c.append(g,d)});return null===e?null:e.then(function(d){return d.bind(null,c)})};exports.decodeReply=function(a,b){if("string"===typeof a){var c=new FormData;c.append("0",a);a=c}a=Qb(b,"",a);b=Z(a,0);Rb(a);return b};
exports.registerClientReference=function(a,b,c){return u(a,b+"#"+c,!1)};exports.registerServerReference=function(a,b,c){return Object.defineProperties(a,{$$typeof:{value:da},$$id:{value:null===c?b:b+"#"+c,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:ia,configurable:!0}})};
exports.renderToReadableStream=function(a,b,c){var e=ib(a,b,c?c.onError:void 0,c?c.identifierPrefix:void 0,c?c.onPostpone:void 0);if(c&&c.signal){var d=c.signal;if(d.aborted)yb(e,d.reason);else{var g=function(){yb(e,d.reason);d.removeEventListener("abort",g)};d.addEventListener("abort",g)}}return new ReadableStream({type:"bytes",start:function(){e.flushScheduled=null!==e.destination;tb(e)},pull:function(f){if(1===e.status)e.status=2,ca(f,e.fatalError);else if(2!==e.status&&null===e.destination){e.destination=
f;try{P(e,f)}catch(k){N(e,k),xb(e,k)}}},cancel:function(){}},{highWaterMark:0})};

//# sourceMappingURL=react-server-dom-turbopack-server.browser.production.min.js.map
