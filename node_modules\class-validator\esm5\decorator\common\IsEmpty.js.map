{"version": 3, "file": "IsEmpty.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsEmpty.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAElC;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,KAAc;IACpC,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,iBAAqC;IAC3D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,OAAO,CAAC,KAAK,CAAC,EAAd,CAAc;YAClD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,yBAAyB,EAAtC,CAAsC,EAAE,iBAAiB,CAAC;SACtG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_EMPTY = 'isEmpty';\n\n/**\n * Checks if given value is empty (=== '', === null, === undefined).\n */\nexport function isEmpty(value: unknown): boolean {\n  return value === '' || value === null || value === undefined;\n}\n\n/**\n * Checks if given value is empty (=== '', === null, === undefined).\n */\nexport function IsEmpty(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_EMPTY,\n      validator: {\n        validate: (value, args): boolean => isEmpty(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be empty', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}