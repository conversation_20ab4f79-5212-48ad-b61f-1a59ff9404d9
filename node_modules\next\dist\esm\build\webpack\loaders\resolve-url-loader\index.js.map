{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/resolve-url-loader/index.ts"], "names": ["SourceMapConsumer", "valueProcessor", "defaultJoin", "process", "resolveUrlLoader", "content", "sourceMap", "options", "Object", "assign", "silent", "absolute", "<PERSON><PERSON><PERSON><PERSON>", "root", "debug", "join", "getOptions", "sourceMapConsumer", "callback", "async", "postcss", "require", "resourcePath", "outputSourceMap", "Boolean", "transformDeclaration", "inputSourceMap", "catch", "onFailure", "then", "onSuccess", "error", "encodeError", "reworked", "map", "label", "exception", "Error", "concat", "message", "stack", "split", "trim", "filter"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,GAEA,SAASA,iBAAiB,QAAQ,gCAA+B;AACjE,OAAOC,oBAAoB,wBAAuB;AAClD,SAASC,WAAW,QAAQ,sBAAqB;AACjD,OAAOC,aAAa,gBAAe;AACnC;;;CAGC,GACD,eAAe,eAAeC,iBAE5B,gBAAgB,GAChBC,OAAe,EACf,mBAAmB,GACnBC,SAAc;IAEd,MAAMC,UAAUC,OAAOC,MAAM,CAC3B;QACEH,WAAW,IAAI,CAACA,SAAS;QACzBI,QAAQ;QACRC,UAAU;QACVC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,MAAMb;IACR,GACA,IAAI,CAACc,UAAU;IAGjB,IAAIC;IACJ,IAAIX,WAAW;QACbW,oBAAoB,IAAIjB,kBAAkBM;IAC5C;IAEA,MAAMY,WAAW,IAAI,CAACC,KAAK;IAC3B,MAAM,EAAEC,OAAO,EAAE,GAAGb,QAAQa,OAAO,GAC/B,MAAMb,QAAQa,OAAO,KACrB;QAAEA,SAASC,QAAQ;IAAW;IAClClB,QAAQiB,SAAS,IAAI,CAACE,YAAY,EAAEjB,SAAS;QAC3CkB,iBAAiBC,QAAQjB,QAAQD,SAAS;QAC1CmB,sBAAsBxB,eAAe,IAAI,CAACqB,YAAY,EAAEf;QACxDmB,gBAAgBpB;QAChBW,mBAAmBA;IACrB,EACE,mEAAmE;KAClEU,KAAK,CAACC,UACP,mEAAmE;KAClEC,IAAI,CAACC;IAER,SAASF,UAAUG,KAAY;QAC7B,mEAAmE;QACnEb,SAASc,YAAY,aAAaD;IACpC;IAEA,SAASD,UAAUG,QAAa;QAC9B,IAAIA,UAAU;YACZ,2BAA2B;YAC3B,+DAA+D;YAC/D,IAAI1B,QAAQD,SAAS,EAAE;gBACrBY,SAAS,MAAMe,SAAS5B,OAAO,EAAE4B,SAASC,GAAG;YAC/C,OAEK;gBACHhB,SAAS,MAAMe,SAAS5B,OAAO;YACjC;QACF;IACF;IAEA,SAAS2B,YAAYG,KAAU,EAAEC,SAAc;QAC7C,OAAO,IAAIC,MACT;YACE;YACA;YACA;gBAACF;aAAM,CACJG,MAAM,CACL,AAAC,OAAOF,cAAc,YAAYA,aAC/BA,qBAAqBC,SAAS;gBAC7BD,UAAUG,OAAO;gBAChBH,UAAkBI,KAAK,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAACC,IAAI;aAChD,IACD,EAAE,EAELC,MAAM,CAACnB,SACPT,IAAI,CAAC;SACT,CAACA,IAAI,CAAC;IAEX;AACF"}