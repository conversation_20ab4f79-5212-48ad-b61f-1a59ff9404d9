{"version": 3, "file": "getNumberType.js", "names": ["_metadata", "_interopRequireDefault", "require", "_matchesEntirely", "e", "__esModule", "_createForOfIteratorHelperLoose", "r", "t", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "o", "done", "value", "TypeError", "a", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "n", "NON_FIXED_LINE_PHONE_TYPES", "getNumberType", "input", "options", "metadata", "country", "countryCallingCode", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "nationalNumber", "v2", "phone", "matchesEntirely", "nationalNumberPattern", "isNumberTypeEqualTo", "type", "pattern", "_iterator", "_step", "possibleLengths", "indexOf"], "sources": ["../../source/helpers/getNumberType.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport matchesEntirely from './matchesEntirely.js'\r\n\r\nconst NON_FIXED_LINE_PHONE_TYPES = [\r\n\t'MOBILE',\r\n\t'PREMIUM_RATE',\r\n\t'TOLL_FREE',\r\n\t'SHARED_COST',\r\n\t'VOIP',\r\n\t'PERSONAL_NUMBER',\r\n\t'PAGER',\r\n\t'UAN',\r\n\t'VOICEMAIL'\r\n]\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function getNumberType(input, options, metadata)\r\n{\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\r\n\t// When `parse()` returns an empty object — `{}` —\r\n\t// that means that the phone number is malformed,\r\n\t// so it can't possibly be valid.\r\n\tif (!input.country && !input.countryCallingCode) {\r\n\t\treturn\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tmetadata.selectNumberingPlan(input.country, input.countryCallingCode)\r\n\r\n\tconst nationalNumber = options.v2 ? input.nationalNumber : input.phone\r\n\r\n\t// The following is copy-pasted from the original function:\r\n\t// https://github.com/googlei18n/libphonenumber/blob/3ea547d4fbaa2d0b67588904dfa5d3f2557c27ff/javascript/i18n/phonenumbers/phonenumberutil.js#L2835\r\n\r\n\t// Is this national number even valid for this country\r\n\tif (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern())) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// Is it fixed line number\r\n\tif (isNumberTypeEqualTo(nationalNumber, 'FIXED_LINE', metadata)) {\r\n\t\t// Because duplicate regular expressions are removed\r\n\t\t// to reduce metadata size, if \"mobile\" pattern is \"\"\r\n\t\t// then it means it was removed due to being a duplicate of the fixed-line pattern.\r\n\t\t//\r\n\t\tif (metadata.type('MOBILE') && metadata.type('MOBILE').pattern() === '') {\r\n\t\t\treturn 'FIXED_LINE_OR_MOBILE'\r\n\t\t}\r\n\r\n\t\t// `MOBILE` type pattern isn't included if it matched `FIXED_LINE` one.\r\n\t\t// For example, for \"US\" country.\r\n\t\t// Old metadata (< `1.0.18`) had a specific \"types\" data structure\r\n\t\t// that happened to be `undefined` for `MOBILE` in that case.\r\n\t\t// Newer metadata (>= `1.0.18`) has another data structure that is\r\n\t\t// not `undefined` for `MOBILE` in that case (it's just an empty array).\r\n\t\t// So this `if` is just for backwards compatibility with old metadata.\r\n\t\tif (!metadata.type('MOBILE')) {\r\n\t\t\treturn 'FIXED_LINE_OR_MOBILE'\r\n\t\t}\r\n\r\n\t\t// Check if the number happens to qualify as both fixed line and mobile.\r\n\t\t// (no such country in the minimal metadata set)\r\n\t\t/* istanbul ignore if */\r\n\t\tif (isNumberTypeEqualTo(nationalNumber, 'MOBILE', metadata)) {\r\n\t\t\treturn 'FIXED_LINE_OR_MOBILE'\r\n\t\t}\r\n\r\n\t\treturn 'FIXED_LINE'\r\n\t}\r\n\r\n\tfor (const type of NON_FIXED_LINE_PHONE_TYPES) {\r\n\t\tif (isNumberTypeEqualTo(nationalNumber, type, metadata)) {\r\n\t\t\treturn type\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport function isNumberTypeEqualTo(nationalNumber, type, metadata) {\r\n\ttype = metadata.type(type)\r\n\tif (!type || !type.pattern()) {\r\n\t\treturn false\r\n\t}\r\n\t// Check if any possible number lengths are present;\r\n\t// if so, we use them to avoid checking\r\n\t// the validation pattern if they don't match.\r\n\t// If they are absent, this means they match\r\n\t// the general description, which we have\r\n\t// already checked before a specific number type.\r\n\tif (type.possibleLengths() &&\r\n\t\ttype.possibleLengths().indexOf(nationalNumber.length) < 0) {\r\n\t\treturn false\r\n\t}\r\n\treturn matchesEntirely(nationalNumber, type.pattern())\r\n}"], "mappings": ";;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAkD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,gCAAAC,CAAA,EAAAH,CAAA,QAAAI,CAAA,yBAAAC,MAAA,IAAAF,CAAA,CAAAE,MAAA,CAAAC,QAAA,KAAAH,CAAA,oBAAAC,CAAA,UAAAA,CAAA,GAAAA,CAAA,CAAAG,IAAA,CAAAJ,CAAA,GAAAK,IAAA,CAAAC,IAAA,CAAAL,CAAA,OAAAM,KAAA,CAAAC,OAAA,CAAAR,CAAA,MAAAC,CAAA,GAAAQ,2BAAA,CAAAT,CAAA,MAAAH,CAAA,IAAAG,CAAA,uBAAAA,CAAA,CAAAU,MAAA,IAAAT,CAAA,KAAAD,CAAA,GAAAC,CAAA,OAAAU,CAAA,kCAAAA,CAAA,IAAAX,CAAA,CAAAU,MAAA,KAAAE,IAAA,WAAAA,IAAA,MAAAC,KAAA,EAAAb,CAAA,CAAAW,CAAA,sBAAAG,SAAA;AAAA,SAAAL,4BAAAT,CAAA,EAAAe,CAAA,QAAAf,CAAA,2BAAAA,CAAA,SAAAgB,iBAAA,CAAAhB,CAAA,EAAAe,CAAA,OAAAd,CAAA,MAAAgB,QAAA,CAAAb,IAAA,CAAAJ,CAAA,EAAAkB,KAAA,6BAAAjB,CAAA,IAAAD,CAAA,CAAAmB,WAAA,KAAAlB,CAAA,GAAAD,CAAA,CAAAmB,WAAA,CAAAC,IAAA,aAAAnB,CAAA,cAAAA,CAAA,GAAAM,KAAA,CAAAc,IAAA,CAAArB,CAAA,oBAAAC,CAAA,+CAAAqB,IAAA,CAAArB,CAAA,IAAAe,iBAAA,CAAAhB,CAAA,EAAAe,CAAA;AAAA,SAAAC,kBAAAhB,CAAA,EAAAe,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAf,CAAA,CAAAU,MAAA,MAAAK,CAAA,GAAAf,CAAA,CAAAU,MAAA,YAAAb,CAAA,MAAA0B,CAAA,GAAAhB,KAAA,CAAAQ,CAAA,GAAAlB,CAAA,GAAAkB,CAAA,EAAAlB,CAAA,IAAA0B,CAAA,CAAA1B,CAAA,IAAAG,CAAA,CAAAH,CAAA,UAAA0B,CAAA;AAElD,IAAMC,0BAA0B,GAAG,CAClC,QAAQ,EACR,cAAc,EACd,WAAW,EACX,aAAa,EACb,MAAM,EACN,iBAAiB,EACjB,OAAO,EACP,KAAK,EACL,WAAW,CACX;;AAED;AACe,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAC9D;EACC;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;;EAEvB;EACA;EACA;EACA,IAAI,CAACD,KAAK,CAACG,OAAO,IAAI,CAACH,KAAK,CAACI,kBAAkB,EAAE;IAChD;EACD;EAEAF,QAAQ,GAAG,IAAIG,oBAAQ,CAACH,QAAQ,CAAC;EAEjCA,QAAQ,CAACI,mBAAmB,CAACN,KAAK,CAACG,OAAO,EAAEH,KAAK,CAACI,kBAAkB,CAAC;EAErE,IAAMG,cAAc,GAAGN,OAAO,CAACO,EAAE,GAAGR,KAAK,CAACO,cAAc,GAAGP,KAAK,CAACS,KAAK;;EAEtE;EACA;;EAEA;EACA,IAAI,CAAC,IAAAC,2BAAe,EAACH,cAAc,EAAEL,QAAQ,CAACS,qBAAqB,CAAC,CAAC,CAAC,EAAE;IACvE;EACD;;EAEA;EACA,IAAIC,mBAAmB,CAACL,cAAc,EAAE,YAAY,EAAEL,QAAQ,CAAC,EAAE;IAChE;IACA;IACA;IACA;IACA,IAAIA,QAAQ,CAACW,IAAI,CAAC,QAAQ,CAAC,IAAIX,QAAQ,CAACW,IAAI,CAAC,QAAQ,CAAC,CAACC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;MACxE,OAAO,sBAAsB;IAC9B;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACZ,QAAQ,CAACW,IAAI,CAAC,QAAQ,CAAC,EAAE;MAC7B,OAAO,sBAAsB;IAC9B;;IAEA;IACA;IACA;IACA,IAAID,mBAAmB,CAACL,cAAc,EAAE,QAAQ,EAAEL,QAAQ,CAAC,EAAE;MAC5D,OAAO,sBAAsB;IAC9B;IAEA,OAAO,YAAY;EACpB;EAEA,SAAAa,SAAA,GAAA1C,+BAAA,CAAmByB,0BAA0B,GAAAkB,KAAA,IAAAA,KAAA,GAAAD,SAAA,IAAA7B,IAAA,GAAE;IAAA,IAApC2B,IAAI,GAAAG,KAAA,CAAA7B,KAAA;IACd,IAAIyB,mBAAmB,CAACL,cAAc,EAAEM,IAAI,EAAEX,QAAQ,CAAC,EAAE;MACxD,OAAOW,IAAI;IACZ;EACD;AACD;AAEO,SAASD,mBAAmBA,CAACL,cAAc,EAAEM,IAAI,EAAEX,QAAQ,EAAE;EACnEW,IAAI,GAAGX,QAAQ,CAACW,IAAI,CAACA,IAAI,CAAC;EAC1B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,CAAC,CAAC,EAAE;IAC7B,OAAO,KAAK;EACb;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAID,IAAI,CAACI,eAAe,CAAC,CAAC,IACzBJ,IAAI,CAACI,eAAe,CAAC,CAAC,CAACC,OAAO,CAACX,cAAc,CAACvB,MAAM,CAAC,GAAG,CAAC,EAAE;IAC3D,OAAO,KAAK;EACb;EACA,OAAO,IAAA0B,2BAAe,EAACH,cAAc,EAAEM,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;AACvD", "ignoreList": []}