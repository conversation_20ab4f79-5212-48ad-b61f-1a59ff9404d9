{"version": 3, "file": "AsYouTypeFormatter.PatternMatcher.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pattern", "_classCallCheck", "matchTree", "parse", "_createClass", "key", "value", "match", "string", "_ref", "arguments", "length", "undefined", "allowOverflow", "Error", "result", "split", "matched<PERSON><PERSON><PERSON>", "overflow", "default", "characters", "tree", "last", "characterString", "join", "indexOf", "partialMatch", "slice", "Array", "isArray", "restCharacters", "i", "subtree", "concat", "JSON", "stringify", "op", "_iterator", "_createForOfIteratorHelperLoose", "args", "_step", "done", "branch", "_iterator2", "_step2", "char"], "sources": ["../source/AsYouTypeFormatter.PatternMatcher.js"], "sourcesContent": ["import PatternParser from './AsYouTypeFormatter.PatternParser.js'\r\n\r\nexport default class PatternMatcher {\r\n\tconstructor(pattern) {\r\n\t\tthis.matchTree = new PatternParser().parse(pattern)\r\n\t}\r\n\r\n\tmatch(string, { allowOverflow } = {}) {\r\n\t\tif (!string) {\r\n\t\t\tthrow new Error('String is required')\r\n\t\t}\r\n\t\tconst result = match(string.split(''), this.matchTree, true)\r\n\t\tif (result && result.match) {\r\n\t\t\tdelete result.matchedChars\r\n\t\t}\r\n\t\tif (result && result.overflow) {\r\n\t\t\tif (!allowOverflow) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn result\r\n\t}\r\n}\r\n\r\n/**\r\n * Matches `characters` against a pattern compiled into a `tree`.\r\n * @param  {string[]} characters\r\n * @param  {Tree} tree — A pattern compiled into a `tree`. See the `*.d.ts` file for the description of the `tree` structure.\r\n * @param  {boolean} last — Whether it's the last (rightmost) subtree on its level of the match tree.\r\n * @return {object} See the `*.d.ts` file for the description of the result object.\r\n */\r\nfunction match(characters, tree, last) {\r\n\t// If `tree` is a string, then `tree` is a single character.\r\n\t// That's because when a pattern is parsed, multi-character-string parts\r\n\t// of a pattern are compiled into arrays of single characters.\r\n\t// I still wrote this piece of code for a \"general\" hypothetical case\r\n\t// when `tree` could be a string of several characters, even though\r\n\t// such case is not possible with the current implementation.\r\n\tif (typeof tree === 'string') {\r\n\t\tconst characterString = characters.join('')\r\n\t\tif (tree.indexOf(characterString) === 0) {\r\n\t\t\t// `tree` is always a single character.\r\n\t\t\t// If `tree.indexOf(characterString) === 0`\r\n\t\t\t// then `characters.length === tree.length`.\r\n\t\t\t/* istanbul ignore else */\r\n\t\t\tif (characters.length === tree.length) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tmatch: true,\r\n\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// `tree` is always a single character.\r\n\t\t\t// If `tree.indexOf(characterString) === 0`\r\n\t\t\t// then `characters.length === tree.length`.\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\treturn {\r\n\t\t\t\tpartialMatch: true,\r\n\t\t\t\t// matchedChars: characters\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (characterString.indexOf(tree) === 0) {\r\n\t\t\tif (last) {\r\n\t\t\t\t// The `else` path is not possible because `tree` is always a single character.\r\n\t\t\t\t// The `else` case for `characters.length > tree.length` would be\r\n\t\t\t\t// `characters.length <= tree.length` which means `characters.length <= 1`.\r\n\t\t\t\t// `characters` array can't be empty, so that means `characters === [tree]`,\r\n\t\t\t\t// which would also mean `tree.indexOf(characterString) === 0` and that'd mean\r\n\t\t\t\t// that the `if (tree.indexOf(characterString) === 0)` condition before this\r\n\t\t\t\t// `if` condition would be entered, and returned from there, not reaching this code.\r\n\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\tif (characters.length > tree.length) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\toverflow: true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tmatch: true,\r\n\t\t\t\tmatchedChars: characters.slice(0, tree.length)\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn\r\n\t}\r\n\r\n\tif (Array.isArray(tree)) {\r\n\t\tlet restCharacters = characters.slice()\r\n\t\tlet i = 0\r\n\t\twhile (i < tree.length) {\r\n\t\t\tconst subtree = tree[i]\r\n\t\t\tconst result = match(restCharacters, subtree, last && (i === tree.length - 1))\r\n\t\t\tif (!result) {\r\n\t\t\t\treturn\r\n\t\t\t} else if (result.overflow) {\r\n\t\t\t\treturn result\r\n\t\t\t} else if (result.match) {\r\n\t\t\t\t// Continue with the next subtree with the rest of the characters.\r\n\t\t\t\trestCharacters = restCharacters.slice(result.matchedChars.length)\r\n\t\t\t\tif (restCharacters.length === 0) {\r\n\t\t\t\t\tif (i === tree.length - 1) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t\t\t// matchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\tif (result.partialMatch) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t\t// matchedChars: characters\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error(`Unsupported match result:\\n${JSON.stringify(result, null, 2)}`)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ti++\r\n\t\t}\r\n\t\t// If `last` then overflow has already been checked\r\n\t\t// by the last element of the `tree` array.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (last) {\r\n\t\t\treturn {\r\n\t\t\t\toverflow: true\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tmatch: true,\r\n\t\t\tmatchedChars: characters.slice(0, characters.length - restCharacters.length)\r\n\t\t}\r\n\t}\r\n\r\n\tswitch (tree.op) {\r\n\t\tcase '|':\r\n\t\t\tlet partialMatch\r\n\t\t\tfor (const branch of tree.args) {\r\n\t\t\t\tconst result = match(characters, branch, last)\r\n\t\t\t\tif (result) {\r\n\t\t\t\t\tif (result.overflow) {\r\n\t\t\t\t\t\treturn result\r\n\t\t\t\t\t} else if (result.match) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: result.matchedChars\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\t\t\tif (result.partialMatch) {\r\n\t\t\t\t\t\t\tpartialMatch = true\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthrow new Error(`Unsupported match result:\\n${JSON.stringify(result, null, 2)}`)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (partialMatch) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t// matchedChars: ...\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// Not even a partial match.\r\n\t\t\treturn\r\n\r\n\t\tcase '[]':\r\n\t\t\tfor (const char of tree.args) {\r\n\t\t\t\tif (characters[0] === char) {\r\n\t\t\t\t\tif (characters.length === 1) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (last) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\toverflow: true\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\tmatchedChars: [char]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// No character matches.\r\n\t\t\treturn\r\n\r\n\t\t/* istanbul ignore next */\r\n\t\tdefault:\r\n\t\t\tthrow new Error(`Unsupported instruction tree: ${tree}`)\r\n\t}\r\n}"], "mappings": ";;;;;;;;;AAAA,OAAOA,aAAa,MAAM,uCAAuC;AAAA,IAE5CC,cAAc;EAClC,SAAAA,eAAYC,OAAO,EAAE;IAAAC,eAAA,OAAAF,cAAA;IACpB,IAAI,CAACG,SAAS,GAAG,IAAIJ,aAAa,CAAC,CAAC,CAACK,KAAK,CAACH,OAAO,CAAC;EACpD;EAAC,OAAAI,YAAA,CAAAL,cAAA;IAAAM,GAAA;IAAAC,KAAA,EAED,SAAAC,KAAKA,CAACC,MAAM,EAA0B;MAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,CAAC,CAAC;QAApBG,aAAa,GAAAJ,IAAA,CAAbI,aAAa;MAC5B,IAAI,CAACL,MAAM,EAAE;QACZ,MAAM,IAAIM,KAAK,CAAC,oBAAoB,CAAC;MACtC;MACA,IAAMC,MAAM,GAAGR,MAAK,CAACC,MAAM,CAACQ,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,CAACd,SAAS,EAAE,IAAI,CAAC;MAC5D,IAAIa,MAAM,IAAIA,MAAM,CAACR,KAAK,EAAE;QAC3B,OAAOQ,MAAM,CAACE,YAAY;MAC3B;MACA,IAAIF,MAAM,IAAIA,MAAM,CAACG,QAAQ,EAAE;QAC9B,IAAI,CAACL,aAAa,EAAE;UACnB;QACD;MACD;MACA,OAAOE,MAAM;IACd;EAAC;AAAA;AAGF;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAtBqBhB,cAAc,IAAAoB,OAAA;AA6BnC,SAASZ,MAAKA,CAACa,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACtC;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAC7B,IAAME,eAAe,GAAGH,UAAU,CAACI,IAAI,CAAC,EAAE,CAAC;IAC3C,IAAIH,IAAI,CAACI,OAAO,CAACF,eAAe,CAAC,KAAK,CAAC,EAAE;MACxC;MACA;MACA;MACA;MACA,IAAIH,UAAU,CAACT,MAAM,KAAKU,IAAI,CAACV,MAAM,EAAE;QACtC,OAAO;UACNJ,KAAK,EAAE,IAAI;UACXU,YAAY,EAAEG;QACf,CAAC;MACF;MACA;MACA;MACA;MACA;MACA,OAAO;QACNM,YAAY,EAAE;QACd;MACD,CAAC;IACF;IACA,IAAIH,eAAe,CAACE,OAAO,CAACJ,IAAI,CAAC,KAAK,CAAC,EAAE;MACxC,IAAIC,IAAI,EAAE;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIF,UAAU,CAACT,MAAM,GAAGU,IAAI,CAACV,MAAM,EAAE;UACpC,OAAO;YACNO,QAAQ,EAAE;UACX,CAAC;QACF;MACD;MACA,OAAO;QACNX,KAAK,EAAE,IAAI;QACXU,YAAY,EAAEG,UAAU,CAACO,KAAK,CAAC,CAAC,EAAEN,IAAI,CAACV,MAAM;MAC9C,CAAC;IACF;IACA;EACD;EAEA,IAAIiB,KAAK,CAACC,OAAO,CAACR,IAAI,CAAC,EAAE;IACxB,IAAIS,cAAc,GAAGV,UAAU,CAACO,KAAK,CAAC,CAAC;IACvC,IAAII,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGV,IAAI,CAACV,MAAM,EAAE;MACvB,IAAMqB,OAAO,GAAGX,IAAI,CAACU,CAAC,CAAC;MACvB,IAAMhB,MAAM,GAAGR,MAAK,CAACuB,cAAc,EAAEE,OAAO,EAAEV,IAAI,IAAKS,CAAC,KAAKV,IAAI,CAACV,MAAM,GAAG,CAAE,CAAC;MAC9E,IAAI,CAACI,MAAM,EAAE;QACZ;MACD,CAAC,MAAM,IAAIA,MAAM,CAACG,QAAQ,EAAE;QAC3B,OAAOH,MAAM;MACd,CAAC,MAAM,IAAIA,MAAM,CAACR,KAAK,EAAE;QACxB;QACAuB,cAAc,GAAGA,cAAc,CAACH,KAAK,CAACZ,MAAM,CAACE,YAAY,CAACN,MAAM,CAAC;QACjE,IAAImB,cAAc,CAACnB,MAAM,KAAK,CAAC,EAAE;UAChC,IAAIoB,CAAC,KAAKV,IAAI,CAACV,MAAM,GAAG,CAAC,EAAE;YAC1B,OAAO;cACNJ,KAAK,EAAE,IAAI;cACXU,YAAY,EAAEG;YACf,CAAC;UACF,CAAC,MAAM;YACN,OAAO;cACNM,YAAY,EAAE;cACd;YACD,CAAC;UACF;QACD;MACD,CAAC,MAAM;QACN;QACA,IAAIX,MAAM,CAACW,YAAY,EAAE;UACxB,OAAO;YACNA,YAAY,EAAE;YACd;UACD,CAAC;QACF,CAAC,MAAM;UACN,MAAM,IAAIZ,KAAK,+BAAAmB,MAAA,CAA+BC,IAAI,CAACC,SAAS,CAACpB,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAE,CAAC;QACjF;MACD;MACAgB,CAAC,EAAE;IACJ;IACA;IACA;IACA;IACA,IAAIT,IAAI,EAAE;MACT,OAAO;QACNJ,QAAQ,EAAE;MACX,CAAC;IACF;IACA,OAAO;MACNX,KAAK,EAAE,IAAI;MACXU,YAAY,EAAEG,UAAU,CAACO,KAAK,CAAC,CAAC,EAAEP,UAAU,CAACT,MAAM,GAAGmB,cAAc,CAACnB,MAAM;IAC5E,CAAC;EACF;EAEA,QAAQU,IAAI,CAACe,EAAE;IACd,KAAK,GAAG;MACP,IAAIV,YAAY;MAChB,SAAAW,SAAA,GAAAC,+BAAA,CAAqBjB,IAAI,CAACkB,IAAI,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAI,IAAA,GAAE;QAAA,IAArBC,MAAM,GAAAF,KAAA,CAAAlC,KAAA;QAChB,IAAMS,OAAM,GAAGR,MAAK,CAACa,UAAU,EAAEsB,MAAM,EAAEpB,IAAI,CAAC;QAC9C,IAAIP,OAAM,EAAE;UACX,IAAIA,OAAM,CAACG,QAAQ,EAAE;YACpB,OAAOH,OAAM;UACd,CAAC,MAAM,IAAIA,OAAM,CAACR,KAAK,EAAE;YACxB,OAAO;cACNA,KAAK,EAAE,IAAI;cACXU,YAAY,EAAEF,OAAM,CAACE;YACtB,CAAC;UACF,CAAC,MAAM;YACN;YACA,IAAIF,OAAM,CAACW,YAAY,EAAE;cACxBA,YAAY,GAAG,IAAI;YACpB,CAAC,MAAM;cACN,MAAM,IAAIZ,KAAK,+BAAAmB,MAAA,CAA+BC,IAAI,CAACC,SAAS,CAACpB,OAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAE,CAAC;YACjF;UACD;QACD;MACD;MACA,IAAIW,YAAY,EAAE;QACjB,OAAO;UACNA,YAAY,EAAE;UACd;QACD,CAAC;MACF;MACA;MACA;IAED,KAAK,IAAI;MACR,SAAAiB,UAAA,GAAAL,+BAAA,CAAmBjB,IAAI,CAACkB,IAAI,GAAAK,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAAF,IAAA,GAAE;QAAA,IAAnBI,KAAI,GAAAD,MAAA,CAAAtC,KAAA;QACd,IAAIc,UAAU,CAAC,CAAC,CAAC,KAAKyB,KAAI,EAAE;UAC3B,IAAIzB,UAAU,CAACT,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAO;cACNJ,KAAK,EAAE,IAAI;cACXU,YAAY,EAAEG;YACf,CAAC;UACF;UACA,IAAIE,IAAI,EAAE;YACT,OAAO;cACNJ,QAAQ,EAAE;YACX,CAAC;UACF;UACA,OAAO;YACNX,KAAK,EAAE,IAAI;YACXU,YAAY,EAAE,CAAC4B,KAAI;UACpB,CAAC;QACF;MACD;MACA;MACA;;IAED;IACA;MACC,MAAM,IAAI/B,KAAK,kCAAAmB,MAAA,CAAkCZ,IAAI,CAAE,CAAC;EAC1D;AACD", "ignoreList": []}