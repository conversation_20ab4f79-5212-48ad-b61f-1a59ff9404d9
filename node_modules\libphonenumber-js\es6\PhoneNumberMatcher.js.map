{"version": 3, "file": "PhoneNumberMatcher.js", "names": ["MAX_LENGTH_FOR_NSN", "MAX_LENGTH_COUNTRY_CODE", "VALID_PUNCTUATION", "createExtensionPattern", "RegExpCache", "limit", "trimAfterFirstMatch", "_pL", "_pN", "pZ", "PZ", "pNd", "<PERSON><PERSON><PERSON>", "parsePreCandidate", "isValidPreCandidate", "isValidCandidate", "LEAD_CLASS", "isSupportedCountry", "parsePhoneNumber", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "EXTN_PATTERNS_FOR_MATCHING", "INNER_MATCHES", "concat", "leadLimit", "punctuationLimit", "digitBlockLimit", "blockLimit", "punctuation", "digitSequence", "PATTERN", "UNWANTED_END_CHAR_PATTERN", "RegExp", "MAX_SAFE_INTEGER", "Number", "Math", "pow", "PhoneNumberMatcher", "text", "arguments", "length", "undefined", "options", "metadata", "_classCallCheck", "v2", "defaultCallingCode", "defaultCountry", "leniency", "extended", "max<PERSON>ries", "TypeError", "state", "searchIndex", "regExpCache", "_createClass", "key", "value", "find", "matches", "exec", "candidate", "offset", "index", "match", "parseAndVerify", "extractInnerMatch", "startsAt", "endsAt", "number", "phoneNumber", "result", "phone", "nationalNumber", "country", "countryCallingCode", "ext", "substring", "_i", "_INNER_MATCHES", "innerMatchPattern", "isFirstMatch", "<PERSON><PERSON><PERSON>", "innerMatchRegExp", "slice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "isPossible", "hasNext", "lastMatch", "next", "Error", "default"], "sources": ["../source/PhoneNumberMatcher.js"], "sourcesContent": ["/**\r\n * A port of Google's `PhoneNumberMatcher.java`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberMatcher.java\r\n * Date: 08.03.2018.\r\n */\r\n\r\nimport {\r\n  MAX_LENGTH_FOR_NSN,\r\n  MAX_LENGTH_COUNTRY_CODE,\r\n  VALID_PUNCTUATION\r\n} from './constants.js'\r\n\r\nimport createExtensionPattern from './helpers/extension/createExtensionPattern.js'\r\n\r\nimport RegExpCache from './findNumbers/RegExpCache.js'\r\n\r\nimport {\r\n\tlimit,\r\n\ttrimAfterFirstMatch\r\n} from './findNumbers/util.js'\r\n\r\nimport {\r\n\t_pL,\r\n\t_pN,\r\n\tpZ,\r\n\tPZ,\r\n\tpNd\r\n} from './findNumbers/utf-8.js'\r\n\r\nimport Leniency from './findNumbers/Leniency.js'\r\nimport parsePreCandidate from './findNumbers/parsePreCandidate.js'\r\nimport isValidPreCandidate from './findNumbers/isValidPreCandidate.js'\r\nimport isValidCandidate, { LEAD_CLASS } from './findNumbers/isValidCandidate.js'\r\n\r\nimport { isSupportedCountry } from './metadata.js'\r\n\r\nimport parsePhoneNumber from './parsePhoneNumber.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nconst EXTN_PATTERNS_FOR_MATCHING = createExtensionPattern('matching')\r\n\r\n/**\r\n * Patterns used to extract phone numbers from a larger phone-number-like pattern. These are\r\n * ordered according to specificity. For example, white-space is last since that is frequently\r\n * used in numbers, not just to separate two numbers. We have separate patterns since we don't\r\n * want to break up the phone-number-like text on more than one different kind of symbol at one\r\n * time, although symbols of the same type (e.g. space) can be safely grouped together.\r\n *\r\n * Note that if there is a match, we will always check any text found up to the first match as\r\n * well.\r\n */\r\nconst INNER_MATCHES =\r\n[\r\n\t// Breaks on the slash - e.g. \"************/************\"\r\n\t'\\\\/+(.*)/',\r\n\r\n\t// Note that the bracket here is inside the capturing group, since we consider it part of the\r\n\t// phone number. Will match a pattern like \"(************* (*************\".\r\n\t'(\\\\([^(]*)',\r\n\r\n\t// Breaks on a hyphen - e.g. \"12345 - ************ is my number.\"\r\n\t// We require a space on either side of the hyphen for it to be considered a separator.\r\n\t`(?:${pZ}-|-${pZ})${pZ}*(.+)`,\r\n\r\n\t// Various types of wide hyphens. Note we have decided not to enforce a space here, since it's\r\n\t// possible that it's supposed to be used to break two numbers without spaces, and we haven't\r\n\t// seen many instances of it used within a number.\r\n\t`[\\u2012-\\u2015\\uFF0D]${pZ}*(.+)`,\r\n\r\n\t// Breaks on a full stop - e.g. \"12345. ************ is my number.\"\r\n\t`\\\\.+${pZ}*([^.]+)`,\r\n\r\n\t// Breaks on space - e.g. \"3324451234 8002341234\"\r\n\t`${pZ}+(${PZ}+)`\r\n]\r\n\r\n// Limit on the number of leading (plus) characters.\r\nconst leadLimit = limit(0, 2)\r\n\r\n// Limit on the number of consecutive punctuation characters.\r\nconst punctuationLimit = limit(0, 4)\r\n\r\n/* The maximum number of digits allowed in a digit-separated block. As we allow all digits in a\r\n * single block, set high enough to accommodate the entire national number and the international\r\n * country code. */\r\nconst digitBlockLimit = MAX_LENGTH_FOR_NSN + MAX_LENGTH_COUNTRY_CODE\r\n\r\n// Limit on the number of blocks separated by punctuation.\r\n// Uses digitBlockLimit since some formats use spaces to separate each digit.\r\nconst blockLimit = limit(0, digitBlockLimit)\r\n\r\n/* A punctuation sequence allowing white space. */\r\nconst punctuation = `[${VALID_PUNCTUATION}]` + punctuationLimit\r\n\r\n// A digits block without punctuation.\r\nconst digitSequence = pNd + limit(1, digitBlockLimit)\r\n\r\n/**\r\n * Phone number pattern allowing optional punctuation.\r\n * The phone number pattern used by `find()`, similar to\r\n * VALID_PHONE_NUMBER, but with the following differences:\r\n * <ul>\r\n *   <li>All captures are limited in order to place an upper bound to the text matched by the\r\n *       pattern.\r\n * <ul>\r\n *   <li>Leading punctuation / plus signs are limited.\r\n *   <li>Consecutive occurrences of punctuation are limited.\r\n *   <li>Number of digits is limited.\r\n * </ul>\r\n *   <li>No whitespace is allowed at the start or end.\r\n *   <li>No alpha digits (vanity numbers such as 1-800-SIX-FLAGS) are currently supported.\r\n * </ul>\r\n */\r\nconst PATTERN = '(?:' + LEAD_CLASS + punctuation + ')' + leadLimit\r\n\t+ digitSequence + '(?:' + punctuation + digitSequence + ')' + blockLimit\r\n\t+ '(?:' + EXTN_PATTERNS_FOR_MATCHING + ')?'\r\n\r\n// Regular expression of trailing characters that we want to remove.\r\n// We remove all characters that are not alpha or numerical characters.\r\n// The hash character is retained here, as it may signify\r\n// the previous block was an extension.\r\n//\r\n// // Don't know what does '&&' mean here.\r\n// const UNWANTED_END_CHAR_PATTERN = new RegExp(`[[\\\\P{N}&&\\\\P{L}]&&[^#]]+$`)\r\n//\r\nconst UNWANTED_END_CHAR_PATTERN = new RegExp(`[^${_pN}${_pL}#]+$`)\r\n\r\n// const NON_DIGITS_PATTERN = /(\\D+)/\r\n\r\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER || Math.pow(2, 53) - 1\r\n\r\n/**\r\n * A stateful class that finds and extracts telephone numbers from {@linkplain CharSequence text}.\r\n * Instances can be created using the {@linkplain PhoneNumberUtil#findNumbers factory methods} in\r\n * {@link PhoneNumberUtil}.\r\n *\r\n * <p>Vanity numbers (phone numbers using alphabetic digits such as <tt>1-800-SIX-FLAGS</tt> are\r\n * not found.\r\n *\r\n * <p>This class is not thread-safe.\r\n */\r\nexport default class PhoneNumberMatcher\r\n{\r\n  /**\r\n   * @param {string} text — the character sequence that we will search, null for no text.\r\n   * @param {'POSSIBLE'|'VALID'|'STRICT_GROUPING'|'EXACT_GROUPING'} [options.leniency] — The leniency to use when evaluating candidate phone numbers. See `source/findNumbers/Leniency.js` for more details.\r\n   * @param {number} [options.maxTries] — The maximum number of invalid numbers to try before giving up on the text. This is to cover degenerate cases where the text has a lot of false positives in it. Must be >= 0.\r\n   */\r\n  constructor(text = '', options = {}, metadata)\r\n  {\r\n    options = {\r\n      v2: options.v2,\r\n      defaultCallingCode: options.defaultCallingCode,\r\n      defaultCountry: options.defaultCountry && isSupportedCountry(options.defaultCountry, metadata) ? options.defaultCountry : undefined,\r\n      // Here it should've assigned a default value only if `options.leniency === undefined`.\r\n      leniency: options.leniency || (options.extended ? 'POSSIBLE' : 'VALID'),\r\n      // Here it should've assigned a default value only if `options.maxTries === undefined`.\r\n      maxTries: options.maxTries || MAX_SAFE_INTEGER\r\n    }\r\n\r\n    // Validate `leniency`.\r\n\t\t// if (!options.leniency) {\r\n\t\t// \tthrow new TypeError('`leniency` is required')\r\n\t\t// }\r\n\t\tif (!Leniency[options.leniency]) {\r\n\t\t\tthrow new TypeError(`Unknown leniency: \"${options.leniency}\"`)\r\n\t\t}\r\n    if (options.leniency !== 'POSSIBLE' && options.leniency !== 'VALID') {\r\n      throw new TypeError(`Invalid \\`leniency\\`: \"${options.leniency}\". Supported values: \"POSSIBLE\", \"VALID\".`)\r\n    }\r\n\r\n    // Validate `maxTries`.\r\n\t\tif (options.maxTries < 0) {\r\n\t\t\tthrow new TypeError('`maxTries` must be `>= 0`')\r\n\t\t}\r\n\r\n\t\tthis.text = text\r\n\t\tthis.options = options\r\n    this.metadata = metadata\r\n\r\n\t\t// The degree of phone number validation.\r\n\t\tthis.leniency = Leniency[options.leniency]\r\n\r\n\t\t/** The maximum number of retries after matching an invalid number. */\r\n\t\tthis.maxTries = options.maxTries\r\n\r\n\t\tthis.PATTERN = new RegExp(PATTERN, 'ig')\r\n\r\n    /** The iteration tristate. */\r\n    this.state = 'NOT_READY'\r\n\r\n    /** The next index to start searching at. Undefined in {@link State#DONE}. */\r\n    this.searchIndex = 0\r\n\r\n    // A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\r\n    // countries being used for the same doc with ~10 patterns for each country. Some pages will have\r\n    // a lot more countries in use, but typically fewer numbers for each so expanding the cache for\r\n    // that use-case won't have a lot of benefit.\r\n    this.regExpCache = new RegExpCache(32)\r\n  }\r\n\r\n  /**\r\n   * Attempts to find the next subsequence in the searched sequence on or after {@code searchIndex}\r\n   * that represents a phone number. Returns the next match, null if none was found.\r\n   *\r\n   * @param index  the search index to start searching at\r\n   * @return  the phone number match found, null if none can be found\r\n   */\r\n\tfind() {\r\n\t\t// // Reset the regular expression.\r\n\t\t// this.PATTERN.lastIndex = index\r\n\r\n\t\tlet matches\r\n\t\twhile ((this.maxTries > 0) && (matches = this.PATTERN.exec(this.text)) !== null) {\r\n\t\t\tlet candidate = matches[0]\r\n\t\t\tconst offset = matches.index\r\n\r\n\t\t\tcandidate = parsePreCandidate(candidate)\r\n\r\n\t\t\tif (isValidPreCandidate(candidate, offset, this.text)) {\r\n\t\t\t\tconst match =\r\n\t\t\t\t\t// Try to come up with a valid match given the entire candidate.\r\n\t\t\t\t\tthis.parseAndVerify(candidate, offset, this.text)\r\n\t\t\t\t\t// If that failed, try to find an \"inner match\" -\r\n\t\t\t\t\t// there might be a phone number within this candidate.\r\n\t\t\t\t\t|| this.extractInnerMatch(candidate, offset, this.text)\r\n\r\n\t\t\t\tif (match) {\r\n\t\t\t\t\tif (this.options.v2) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tstartsAt: match.startsAt,\r\n\t\t\t\t\t\t\tendsAt: match.endsAt,\r\n\t\t\t\t\t\t\tnumber: match.phoneNumber\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n            const { phoneNumber } = match\r\n\r\n            const result = {\r\n              startsAt: match.startsAt,\r\n              endsAt: match.endsAt,\r\n              phone: phoneNumber.nationalNumber\r\n            }\r\n\r\n            if (phoneNumber.country) {\r\n              /* istanbul ignore if */\r\n              if (USE_NON_GEOGRAPHIC_COUNTRY_CODE && country === '001') {\r\n                result.countryCallingCode = phoneNumber.countryCallingCode\r\n              } else {\r\n                result.country = phoneNumber.country\r\n              }\r\n            } else {\r\n              result.countryCallingCode = phoneNumber.countryCallingCode\r\n            }\r\n\r\n            if (phoneNumber.ext) {\r\n              result.ext = phoneNumber.ext\r\n            }\r\n\r\n            return result\r\n          }\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tthis.maxTries--\r\n\t\t}\r\n\t}\r\n\r\n  /**\r\n   * Attempts to extract a match from `substring`\r\n   * if the substring itself does not qualify as a match.\r\n   */\r\n  extractInnerMatch(substring, offset, text) {\r\n    for (const innerMatchPattern of INNER_MATCHES) {\r\n      let isFirstMatch = true\r\n      let candidateMatch\r\n      const innerMatchRegExp = new RegExp(innerMatchPattern, 'g')\r\n      while (this.maxTries > 0 && (candidateMatch = innerMatchRegExp.exec(substring)) !== null) {\r\n        if (isFirstMatch) {\r\n          // We should handle any group before this one too.\r\n          const candidate = trimAfterFirstMatch(\r\n            UNWANTED_END_CHAR_PATTERN,\r\n            substring.slice(0, candidateMatch.index)\r\n          )\r\n\r\n          const match = this.parseAndVerify(candidate, offset, text)\r\n\r\n          if (match) {\r\n            return match\r\n          }\r\n\r\n          this.maxTries--\r\n          isFirstMatch = false\r\n        }\r\n\r\n        const candidate = trimAfterFirstMatch(UNWANTED_END_CHAR_PATTERN, candidateMatch[1])\r\n\r\n        // Java code does `groupMatcher.start(1)` here,\r\n        // but there's no way in javascript to get a `candidate` start index,\r\n        // therefore resort to using this kind of an approximation.\r\n        // (`groupMatcher` is called `candidateInSubstringMatch` in this javascript port)\r\n        // https://stackoverflow.com/questions/15934353/get-index-of-each-capture-in-a-javascript-regex\r\n        const candidateIndexGuess = substring.indexOf(candidate, candidateMatch.index)\r\n\r\n        const match = this.parseAndVerify(candidate, offset + candidateIndexGuess, text)\r\n        if (match) {\r\n          return match\r\n        }\r\n\r\n        this.maxTries--\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Parses a phone number from the `candidate` using `parse` and\r\n   * verifies it matches the requested `leniency`. If parsing and verification succeed,\r\n   * a corresponding `PhoneNumberMatch` is returned, otherwise this method returns `null`.\r\n   *\r\n   * @param candidate  the candidate match\r\n   * @param offset  the offset of {@code candidate} within {@link #text}\r\n   * @return  the parsed and validated phone number match, or null\r\n   */\r\n  parseAndVerify(candidate, offset, text) {\r\n    if (!isValidCandidate(candidate, offset, text, this.options.leniency)) {\r\n      return\r\n  \t}\r\n\r\n    const phoneNumber = parsePhoneNumber(\r\n      candidate,\r\n      {\r\n        extended: true,\r\n        defaultCountry: this.options.defaultCountry,\r\n        defaultCallingCode: this.options.defaultCallingCode\r\n      },\r\n      this.metadata\r\n    )\r\n\r\n    if (!phoneNumber) {\r\n      return\r\n    }\r\n\r\n    if (!phoneNumber.isPossible()) {\r\n      return\r\n    }\r\n\r\n    if (this.leniency(phoneNumber, {\r\n      candidate,\r\n      defaultCountry: this.options.defaultCountry,\r\n      metadata: this.metadata,\r\n      regExpCache: this.regExpCache\r\n    })) {\r\n      return {\r\n        startsAt: offset,\r\n        endsAt: offset + candidate.length,\r\n        phoneNumber\r\n      }\r\n    }\r\n  }\r\n\r\n  hasNext()\r\n  {\r\n    if (this.state === 'NOT_READY')\r\n    {\r\n      this.lastMatch = this.find() // (this.searchIndex)\r\n\r\n      if (this.lastMatch)\r\n      {\r\n        // this.searchIndex = this.lastMatch.endsAt\r\n        this.state = 'READY'\r\n      }\r\n      else\r\n      {\r\n        this.state = 'DONE'\r\n      }\r\n    }\r\n\r\n    return this.state === 'READY'\r\n  }\r\n\r\n  next()\r\n  {\r\n    // Check the state and find the next match as a side-effect if necessary.\r\n    if (!this.hasNext())\r\n    {\r\n      throw new Error('No next element')\r\n    }\r\n\r\n    // Don't retain that memory any longer than necessary.\r\n    const result = this.lastMatch\r\n    this.lastMatch = null\r\n    this.state = 'NOT_READY'\r\n    return result\r\n  }\r\n}"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;AAEA,SACEA,kBAAkB,EAClBC,uBAAuB,EACvBC,iBAAiB,QACZ,gBAAgB;AAEvB,OAAOC,sBAAsB,MAAM,+CAA+C;AAElF,OAAOC,WAAW,MAAM,8BAA8B;AAEtD,SACCC,KAAK,EACLC,mBAAmB,QACb,uBAAuB;AAE9B,SACCC,GAAG,EACHC,GAAG,EACHC,EAAE,EACFC,EAAE,EACFC,GAAG,QACG,wBAAwB;AAE/B,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,gBAAgB,IAAIC,UAAU,QAAQ,mCAAmC;AAEhF,SAASC,kBAAkB,QAAQ,eAAe;AAElD,OAAOC,gBAAgB,MAAM,uBAAuB;AAEpD,IAAMC,+BAA+B,GAAG,KAAK;AAE7C,IAAMC,0BAA0B,GAAGjB,sBAAsB,CAAC,UAAU,CAAC;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMkB,aAAa,GACnB;AACC;AACA,WAAW;AAEX;AACA;AACA,YAAY,EAEZ;AACA;AAAA,MAAAC,MAAA,CACMb,EAAE,SAAAa,MAAA,CAAMb,EAAE,OAAAa,MAAA,CAAIb,EAAE,YAEtB;AACA;AACA;AAAA,wBAAAa,MAAA,CACwBb,EAAE,YAE1B;AAAA,OAAAa,MAAA,CACOb,EAAE,eAET;AAAA,GAAAa,MAAA,CACGb,EAAE,QAAAa,MAAA,CAAKZ,EAAE,QACZ;;AAED;AACA,IAAMa,SAAS,GAAGlB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;AAE7B;AACA,IAAMmB,gBAAgB,GAAGnB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;AAEpC;AACA;AACA;AACA,IAAMoB,eAAe,GAAGzB,kBAAkB,GAAGC,uBAAuB;;AAEpE;AACA;AACA,IAAMyB,UAAU,GAAGrB,KAAK,CAAC,CAAC,EAAEoB,eAAe,CAAC;;AAE5C;AACA,IAAME,WAAW,GAAG,IAAAL,MAAA,CAAIpB,iBAAiB,SAAMsB,gBAAgB;;AAE/D;AACA,IAAMI,aAAa,GAAGjB,GAAG,GAAGN,KAAK,CAAC,CAAC,EAAEoB,eAAe,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMI,OAAO,GAAG,KAAK,GAAGb,UAAU,GAAGW,WAAW,GAAG,GAAG,GAAGJ,SAAS,GAC/DK,aAAa,GAAG,KAAK,GAAGD,WAAW,GAAGC,aAAa,GAAG,GAAG,GAAGF,UAAU,GACtE,KAAK,GAAGN,0BAA0B,GAAG,IAAI;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMU,yBAAyB,GAAG,IAAIC,MAAM,MAAAT,MAAA,CAAMd,GAAG,EAAAc,MAAA,CAAGf,GAAG,SAAM,CAAC;;AAElE;;AAEA,IAAMyB,gBAAgB,GAAGC,MAAM,CAACD,gBAAgB,IAAIE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,IAUqBC,kBAAkB;EAErC;AACF;AACA;AACA;AACA;EACE,SAAAA,mBAAA,EACA;IAAA,IADYC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAAA,IAAEG,OAAO,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,IAAEI,QAAQ,GAAAJ,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAAAG,eAAA,OAAAP,kBAAA;IAE3CK,OAAO,GAAG;MACRG,EAAE,EAAEH,OAAO,CAACG,EAAE;MACdC,kBAAkB,EAAEJ,OAAO,CAACI,kBAAkB;MAC9CC,cAAc,EAAEL,OAAO,CAACK,cAAc,IAAI7B,kBAAkB,CAACwB,OAAO,CAACK,cAAc,EAAEJ,QAAQ,CAAC,GAAGD,OAAO,CAACK,cAAc,GAAGN,SAAS;MACnI;MACAO,QAAQ,EAAEN,OAAO,CAACM,QAAQ,KAAKN,OAAO,CAACO,QAAQ,GAAG,UAAU,GAAG,OAAO,CAAC;MACvE;MACAC,QAAQ,EAAER,OAAO,CAACQ,QAAQ,IAAIjB;IAChC,CAAC;;IAED;IACF;IACA;IACA;IACA,IAAI,CAACpB,QAAQ,CAAC6B,OAAO,CAACM,QAAQ,CAAC,EAAE;MAChC,MAAM,IAAIG,SAAS,wBAAA5B,MAAA,CAAuBmB,OAAO,CAACM,QAAQ,OAAG,CAAC;IAC/D;IACE,IAAIN,OAAO,CAACM,QAAQ,KAAK,UAAU,IAAIN,OAAO,CAACM,QAAQ,KAAK,OAAO,EAAE;MACnE,MAAM,IAAIG,SAAS,0BAAA5B,MAAA,CAA2BmB,OAAO,CAACM,QAAQ,mDAA2C,CAAC;IAC5G;;IAEA;IACF,IAAIN,OAAO,CAACQ,QAAQ,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIC,SAAS,CAAC,2BAA2B,CAAC;IACjD;IAEA,IAAI,CAACb,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACI,OAAO,GAAGA,OAAO;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;;IAE1B;IACA,IAAI,CAACK,QAAQ,GAAGnC,QAAQ,CAAC6B,OAAO,CAACM,QAAQ,CAAC;;IAE1C;IACA,IAAI,CAACE,QAAQ,GAAGR,OAAO,CAACQ,QAAQ;IAEhC,IAAI,CAACpB,OAAO,GAAG,IAAIE,MAAM,CAACF,OAAO,EAAE,IAAI,CAAC;;IAEtC;IACA,IAAI,CAACsB,KAAK,GAAG,WAAW;;IAExB;IACA,IAAI,CAACC,WAAW,GAAG,CAAC;;IAEpB;IACA;IACA;IACA;IACA,IAAI,CAACC,WAAW,GAAG,IAAIjD,WAAW,CAAC,EAAE,CAAC;EACxC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE,OAAAkD,YAAA,CAAAlB,kBAAA;IAAAmB,GAAA;IAAAC,KAAA,EAOD,SAAAC,IAAIA,CAAA,EAAG;MACN;MACA;;MAEA,IAAIC,OAAO;MACX,OAAQ,IAAI,CAACT,QAAQ,GAAG,CAAC,IAAK,CAACS,OAAO,GAAG,IAAI,CAAC7B,OAAO,CAAC8B,IAAI,CAAC,IAAI,CAACtB,IAAI,CAAC,MAAM,IAAI,EAAE;QAChF,IAAIuB,SAAS,GAAGF,OAAO,CAAC,CAAC,CAAC;QAC1B,IAAMG,MAAM,GAAGH,OAAO,CAACI,KAAK;QAE5BF,SAAS,GAAG/C,iBAAiB,CAAC+C,SAAS,CAAC;QAExC,IAAI9C,mBAAmB,CAAC8C,SAAS,EAAEC,MAAM,EAAE,IAAI,CAACxB,IAAI,CAAC,EAAE;UACtD,IAAM0B,KAAK;UACV;UACA,IAAI,CAACC,cAAc,CAACJ,SAAS,EAAEC,MAAM,EAAE,IAAI,CAACxB,IAAI;UAChD;UACA;UAAA,GACG,IAAI,CAAC4B,iBAAiB,CAACL,SAAS,EAAEC,MAAM,EAAE,IAAI,CAACxB,IAAI,CAAC;UAExD,IAAI0B,KAAK,EAAE;YACV,IAAI,IAAI,CAACtB,OAAO,CAACG,EAAE,EAAE;cACpB,OAAO;gBACNsB,QAAQ,EAAEH,KAAK,CAACG,QAAQ;gBACxBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;gBACpBC,MAAM,EAAEL,KAAK,CAACM;cACf,CAAC;YACF,CAAC,MAAM;cACA,IAAQA,WAAW,GAAKN,KAAK,CAArBM,WAAW;cAEnB,IAAMC,MAAM,GAAG;gBACbJ,QAAQ,EAAEH,KAAK,CAACG,QAAQ;gBACxBC,MAAM,EAAEJ,KAAK,CAACI,MAAM;gBACpBI,KAAK,EAAEF,WAAW,CAACG;cACrB,CAAC;cAED,IAAIH,WAAW,CAACI,OAAO,EAAE;gBACvB;gBACA,IAAItD,+BAA+B,IAAIsD,OAAO,KAAK,KAAK,EAAE;kBACxDH,MAAM,CAACI,kBAAkB,GAAGL,WAAW,CAACK,kBAAkB;gBAC5D,CAAC,MAAM;kBACLJ,MAAM,CAACG,OAAO,GAAGJ,WAAW,CAACI,OAAO;gBACtC;cACF,CAAC,MAAM;gBACLH,MAAM,CAACI,kBAAkB,GAAGL,WAAW,CAACK,kBAAkB;cAC5D;cAEA,IAAIL,WAAW,CAACM,GAAG,EAAE;gBACnBL,MAAM,CAACK,GAAG,GAAGN,WAAW,CAACM,GAAG;cAC9B;cAEA,OAAOL,MAAM;YACf;UACN;QACD;QAEA,IAAI,CAACrB,QAAQ,EAAE;MAChB;IACD;;IAEC;AACF;AACA;AACA;EAHE;IAAAM,GAAA;IAAAC,KAAA,EAIA,SAAAS,iBAAiBA,CAACW,SAAS,EAAEf,MAAM,EAAExB,IAAI,EAAE;MACzC,SAAAwC,EAAA,MAAAC,cAAA,GAAgCzD,aAAa,EAAAwD,EAAA,GAAAC,cAAA,CAAAvC,MAAA,EAAAsC,EAAA,IAAE;QAA1C,IAAME,iBAAiB,GAAAD,cAAA,CAAAD,EAAA;QAC1B,IAAIG,YAAY,GAAG,IAAI;QACvB,IAAIC,cAAc;QAClB,IAAMC,gBAAgB,GAAG,IAAInD,MAAM,CAACgD,iBAAiB,EAAE,GAAG,CAAC;QAC3D,OAAO,IAAI,CAAC9B,QAAQ,GAAG,CAAC,IAAI,CAACgC,cAAc,GAAGC,gBAAgB,CAACvB,IAAI,CAACiB,SAAS,CAAC,MAAM,IAAI,EAAE;UACxF,IAAII,YAAY,EAAE;YAChB;YACA,IAAMpB,UAAS,GAAGtD,mBAAmB,CACnCwB,yBAAyB,EACzB8C,SAAS,CAACO,KAAK,CAAC,CAAC,EAAEF,cAAc,CAACnB,KAAK,CACzC,CAAC;YAED,IAAMC,MAAK,GAAG,IAAI,CAACC,cAAc,CAACJ,UAAS,EAAEC,MAAM,EAAExB,IAAI,CAAC;YAE1D,IAAI0B,MAAK,EAAE;cACT,OAAOA,MAAK;YACd;YAEA,IAAI,CAACd,QAAQ,EAAE;YACf+B,YAAY,GAAG,KAAK;UACtB;UAEA,IAAMpB,SAAS,GAAGtD,mBAAmB,CAACwB,yBAAyB,EAAEmD,cAAc,CAAC,CAAC,CAAC,CAAC;;UAEnF;UACA;UACA;UACA;UACA;UACA,IAAMG,mBAAmB,GAAGR,SAAS,CAACS,OAAO,CAACzB,SAAS,EAAEqB,cAAc,CAACnB,KAAK,CAAC;UAE9E,IAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAACJ,SAAS,EAAEC,MAAM,GAAGuB,mBAAmB,EAAE/C,IAAI,CAAC;UAChF,IAAI0B,KAAK,EAAE;YACT,OAAOA,KAAK;UACd;UAEA,IAAI,CAACd,QAAQ,EAAE;QACjB;MACF;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EARE;IAAAM,GAAA;IAAAC,KAAA,EASA,SAAAQ,cAAcA,CAACJ,SAAS,EAAEC,MAAM,EAAExB,IAAI,EAAE;MACtC,IAAI,CAACtB,gBAAgB,CAAC6C,SAAS,EAAEC,MAAM,EAAExB,IAAI,EAAE,IAAI,CAACI,OAAO,CAACM,QAAQ,CAAC,EAAE;QACrE;MACH;MAEC,IAAMsB,WAAW,GAAGnD,gBAAgB,CAClC0C,SAAS,EACT;QACEZ,QAAQ,EAAE,IAAI;QACdF,cAAc,EAAE,IAAI,CAACL,OAAO,CAACK,cAAc;QAC3CD,kBAAkB,EAAE,IAAI,CAACJ,OAAO,CAACI;MACnC,CAAC,EACD,IAAI,CAACH,QACP,CAAC;MAED,IAAI,CAAC2B,WAAW,EAAE;QAChB;MACF;MAEA,IAAI,CAACA,WAAW,CAACiB,UAAU,CAAC,CAAC,EAAE;QAC7B;MACF;MAEA,IAAI,IAAI,CAACvC,QAAQ,CAACsB,WAAW,EAAE;QAC7BT,SAAS,EAATA,SAAS;QACTd,cAAc,EAAE,IAAI,CAACL,OAAO,CAACK,cAAc;QAC3CJ,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBW,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC,CAAC,EAAE;QACF,OAAO;UACLa,QAAQ,EAAEL,MAAM;UAChBM,MAAM,EAAEN,MAAM,GAAGD,SAAS,CAACrB,MAAM;UACjC8B,WAAW,EAAXA;QACF,CAAC;MACH;IACF;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAED,SAAA+B,OAAOA,CAAA,EACP;MACE,IAAI,IAAI,CAACpC,KAAK,KAAK,WAAW,EAC9B;QACE,IAAI,CAACqC,SAAS,GAAG,IAAI,CAAC/B,IAAI,CAAC,CAAC,EAAC;;QAE7B,IAAI,IAAI,CAAC+B,SAAS,EAClB;UACE;UACA,IAAI,CAACrC,KAAK,GAAG,OAAO;QACtB,CAAC,MAED;UACE,IAAI,CAACA,KAAK,GAAG,MAAM;QACrB;MACF;MAEA,OAAO,IAAI,CAACA,KAAK,KAAK,OAAO;IAC/B;EAAC;IAAAI,GAAA;IAAAC,KAAA,EAED,SAAAiC,IAAIA,CAAA,EACJ;MACE;MACA,IAAI,CAAC,IAAI,CAACF,OAAO,CAAC,CAAC,EACnB;QACE,MAAM,IAAIG,KAAK,CAAC,iBAAiB,CAAC;MACpC;;MAEA;MACA,IAAMpB,MAAM,GAAG,IAAI,CAACkB,SAAS;MAC7B,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACrC,KAAK,GAAG,WAAW;MACxB,OAAOmB,MAAM;IACf;EAAC;AAAA;AAAA,SA3PkBlC,kBAAkB,IAAAuD,OAAA", "ignoreList": []}