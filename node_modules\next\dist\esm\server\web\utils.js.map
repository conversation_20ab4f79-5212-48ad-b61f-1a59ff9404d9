{"version": 3, "sources": ["../../../src/server/web/utils.ts"], "names": ["NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "fromNodeOutgoingHttpHeaders", "nodeHeaders", "headers", "Headers", "key", "value", "Object", "entries", "values", "Array", "isArray", "v", "toString", "append", "splitCookiesString", "cookiesString", "cookiesStrings", "pos", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "skipWhitespace", "length", "test", "char<PERSON>t", "notSpecialChar", "push", "substring", "toNodeOutgoingHttpHeaders", "cookies", "toLowerCase", "validateURL", "url", "String", "URL", "error", "Error", "cause", "normalizeNextQueryParam", "onKeyNormalized", "prefixes", "prefix", "startsWith", "normalizedKey"], "mappings": "AACA,SACEA,+BAA+B,EAC/BC,uBAAuB,QAClB,sBAAqB;AAE5B;;;;;;;;CAQC,GACD,OAAO,SAASC,4BACdC,WAAgC;IAEhC,MAAMC,UAAU,IAAIC;IACpB,KAAK,IAAI,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACN,aAAc;QACpD,MAAMO,SAASC,MAAMC,OAAO,CAACL,SAASA,QAAQ;YAACA;SAAM;QACrD,KAAK,IAAIM,KAAKH,OAAQ;YACpB,IAAI,OAAOG,MAAM,aAAa;YAC9B,IAAI,OAAOA,MAAM,UAAU;gBACzBA,IAAIA,EAAEC,QAAQ;YAChB;YAEAV,QAAQW,MAAM,CAACT,KAAKO;QACtB;IACF;IACA,OAAOT;AACT;AAEA;;;;;;;;;AASA,GACA,OAAO,SAASY,mBAAmBC,aAAqB;IACtD,IAAIC,iBAAiB,EAAE;IACvB,IAAIC,MAAM;IACV,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAIC;IAEJ,SAASC;QACP,MAAON,MAAMF,cAAcS,MAAM,IAAI,KAAKC,IAAI,CAACV,cAAcW,MAAM,CAACT,MAAO;YACzEA,OAAO;QACT;QACA,OAAOA,MAAMF,cAAcS,MAAM;IACnC;IAEA,SAASG;QACPR,KAAKJ,cAAcW,MAAM,CAACT;QAE1B,OAAOE,OAAO,OAAOA,OAAO,OAAOA,OAAO;IAC5C;IAEA,MAAOF,MAAMF,cAAcS,MAAM,CAAE;QACjCN,QAAQD;QACRK,wBAAwB;QAExB,MAAOC,iBAAkB;YACvBJ,KAAKJ,cAAcW,MAAM,CAACT;YAC1B,IAAIE,OAAO,KAAK;gBACd,uEAAuE;gBACvEC,YAAYH;gBACZA,OAAO;gBAEPM;gBACAF,YAAYJ;gBAEZ,MAAOA,MAAMF,cAAcS,MAAM,IAAIG,iBAAkB;oBACrDV,OAAO;gBACT;gBAEA,8BAA8B;gBAC9B,IAAIA,MAAMF,cAAcS,MAAM,IAAIT,cAAcW,MAAM,CAACT,SAAS,KAAK;oBACnE,6BAA6B;oBAC7BK,wBAAwB;oBACxB,2DAA2D;oBAC3DL,MAAMI;oBACNL,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOE;oBACnDF,QAAQD;gBACV,OAAO;oBACL,uCAAuC;oBACvC,8BAA8B;oBAC9BA,MAAMG,YAAY;gBACpB;YACF,OAAO;gBACLH,OAAO;YACT;QACF;QAEA,IAAI,CAACK,yBAAyBL,OAAOF,cAAcS,MAAM,EAAE;YACzDR,eAAeY,IAAI,CAACb,cAAcc,SAAS,CAACX,OAAOH,cAAcS,MAAM;QACzE;IACF;IAEA,OAAOR;AACT;AAEA;;;;;;CAMC,GACD,OAAO,SAASc,0BACd5B,OAAgB;IAEhB,MAAMD,cAAmC,CAAC;IAC1C,MAAM8B,UAAoB,EAAE;IAC5B,IAAI7B,SAAS;QACX,KAAK,MAAM,CAACE,KAAKC,MAAM,IAAIH,QAAQK,OAAO,GAAI;YAC5C,IAAIH,IAAI4B,WAAW,OAAO,cAAc;gBACtC,mEAAmE;gBACnE,kEAAkE;gBAClE,gCAAgC;gBAChCD,QAAQH,IAAI,IAAId,mBAAmBT;gBACnCJ,WAAW,CAACG,IAAI,GAAG2B,QAAQP,MAAM,KAAK,IAAIO,OAAO,CAAC,EAAE,GAAGA;YACzD,OAAO;gBACL9B,WAAW,CAACG,IAAI,GAAGC;YACrB;QACF;IACF;IACA,OAAOJ;AACT;AAEA;;CAEC,GACD,OAAO,SAASgC,YAAYC,GAAiB;IAC3C,IAAI;QACF,OAAOC,OAAO,IAAIC,IAAID,OAAOD;IAC/B,EAAE,OAAOG,OAAY;QACnB,MAAM,IAAIC,MACR,CAAC,kBAAkB,EAAEH,OACnBD,KACA,4FAA4F,CAAC,EAC/F;YAAEK,OAAOF;QAAM;IAEnB;AACF;AAEA;;;;CAIC,GACD,OAAO,SAASG,wBACdpC,GAAW,EACXqC,eAAgD;IAEhD,MAAMC,WAAW;QAAC3C;QAAyBD;KAAgC;IAC3E,KAAK,MAAM6C,UAAUD,SAAU;QAC7B,IAAItC,QAAQuC,UAAUvC,IAAIwC,UAAU,CAACD,SAAS;YAC5C,MAAME,gBAAgBzC,IAAIyB,SAAS,CAACc,OAAOnB,MAAM;YACjDiB,gBAAgBI;QAClB;IACF;AACF"}