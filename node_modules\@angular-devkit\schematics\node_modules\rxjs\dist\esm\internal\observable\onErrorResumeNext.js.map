{"version": 3, "file": "onErrorResumeNext.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/onErrorResumeNext.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAsExC,MAAM,UAAU,iBAAiB,CAC/B,GAAG,OAAsE;IAEzE,MAAM,WAAW,GAA4B,cAAc,CAAC,OAAO,CAAQ,CAAC;IAE5E,OAAO,IAAI,UAAU,CAAC,CAAC,UAAU,EAAE,EAAE;QACnC,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,MAAM,aAAa,GAAG,GAAG,EAAE;YACzB,IAAI,WAAW,GAAG,WAAW,CAAC,MAAM,EAAE;gBACpC,IAAI,UAAiC,CAAC;gBACtC,IAAI;oBACF,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;iBACpD;gBAAC,OAAO,GAAG,EAAE;oBACZ,aAAa,EAAE,CAAC;oBAChB,OAAO;iBACR;gBACD,MAAM,eAAe,GAAG,IAAI,kBAAkB,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;gBAClF,UAAU,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;gBACtC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;aACpC;iBAAM;gBACL,UAAU,CAAC,QAAQ,EAAE,CAAC;aACvB;QACH,CAAC,CAAC;QACF,aAAa,EAAE,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC"}