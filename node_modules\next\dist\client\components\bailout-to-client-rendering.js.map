{"version": 3, "sources": ["../../../src/client/components/bailout-to-client-rendering.ts"], "names": ["bailoutToClientRendering", "reason", "staticGenerationStore", "staticGenerationAsyncStorage", "getStore", "forceStatic", "isStaticGeneration", "BailoutToCSRError"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;8BAHkB;sDACW;AAEtC,SAASA,yBAAyBC,MAAc;IACrD,MAAMC,wBAAwBC,kEAA4B,CAACC,QAAQ;IAEnE,IAAIF,yCAAAA,sBAAuBG,WAAW,EAAE;IAExC,IAAIH,yCAAAA,sBAAuBI,kBAAkB,EAC3C,MAAM,IAAIC,+BAAiB,CAACN;AAChC"}