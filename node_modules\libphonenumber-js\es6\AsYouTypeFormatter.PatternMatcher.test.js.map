{"version": 3, "file": "AsYouTypeFormatter.PatternMatcher.test.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "describe", "it", "expect", "to", "matcher", "match", "be", "undefined", "deep", "equal", "allowOverflow", "overflow", "partialMatch"], "sources": ["../source/AsYouTypeFormatter.PatternMatcher.test.js"], "sourcesContent": ["import PatternMatcher from './AsYouTypeFormatter.PatternMatcher.js'\r\n\r\ndescribe('AsYouTypeFormatter.PatternMatcher', function() {\r\n\tit('should throw when no pattern is passed', function() {\r\n\t\texpect(() => new PatternMatcher()).to.throw('Pattern is required')\r\n\t})\r\n\r\n\tit('should throw when no string is passed', function() {\r\n\t\tconst matcher = new PatternMatcher('1')\r\n\t\texpect(() => matcher.match()).to.throw('String is required')\r\n\t})\r\n\r\n\tit('should throw on illegal characters', function() {\r\n\t\texpect(() => new PatternMatcher('4(5|6)7')).to.throw('Illegal characters')\r\n\t})\r\n\r\n\tit('should throw on an illegal ] operator', function() {\r\n\t\texpect(() => new PatternMatcher('4]7')).to.throw('\"]\" operator must be preceded by \"[\" operator')\r\n\t})\r\n\r\n\tit('should throw on an illegal - operator in a one-of set', function() {\r\n\t\texpect(() => new PatternMatcher('[-5]')).to.throw('Couldn\\'t parse a one-of set pattern: -5')\r\n\t})\r\n\r\n\tit('should throw on a non-finalized context', function() {\r\n\t\texpect(() => new PatternMatcher('4(?:5|7')).to.throw('Non-finalized contexts left when pattern parse ended')\r\n\t})\r\n\r\n\tit('should throw on an illegal (|) operator', function() {\r\n\t\texpect(() => new PatternMatcher('4(?:5|)7')).to.throw('No instructions found after \"|\" operator in an \"or\" group')\r\n\t})\r\n\r\n\tit('should throw on an illegal ) operator', function() {\r\n\t\texpect(() => new PatternMatcher('4[56)]7')).to.throw('\")\" operator must be preceded by \"(?:\" operator')\r\n\t})\r\n\r\n\tit('should throw on an illegal | operator', function() {\r\n\t\texpect(() => new PatternMatcher('4[5|6]7')).to.throw('operator can only be used inside \"or\" groups')\r\n\t})\r\n\r\n\tit('should match a one-digit pattern', function() {\r\n\t\tconst matcher = new PatternMatcher('4')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('44')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('44', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match a two-digit pattern', function() {\r\n\t\tconst matcher = new PatternMatcher('44')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('44')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('444')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('444', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('55')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match a one-digit one-of set (single digit)', function() {\r\n\t\tconst matcher = new PatternMatcher('[4]')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('44')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('44', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match a one-digit one-of set (multiple digits)', function() {\r\n\t\tconst matcher = new PatternMatcher('[479]')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('44')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('44', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match a one-digit one-of set using a dash notation (not inclusive)', function() {\r\n\t\tconst matcher = new PatternMatcher('[2-5]')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('44')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('44', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match a one-digit one-of set using a dash notation (inclusive)', function() {\r\n\t\tconst matcher = new PatternMatcher('[3-4]')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('44')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('44', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match a one-digit one-of set including a dash notation', function() {\r\n\t\tconst matcher = new PatternMatcher('[124-68]')\r\n\r\n\t\texpect(matcher.match('0')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('1')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('2')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('3')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('5')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('6')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('7')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('8')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('9')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('88')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('88', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match a two-digit one-of set', function() {\r\n\t\tconst matcher = new PatternMatcher('[479][45]')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('5')).to.be.undefined\r\n\t\texpect(matcher.match('55')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('44')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('444')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('444', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match a two-digit one-of set (regular digit and a one-of set)', function() {\r\n\t\tconst matcher = new PatternMatcher('1[45]')\r\n\r\n\t\texpect(matcher.match('0')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('1')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('15')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('16')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match a pattern with an or group', function() {\r\n\t\tconst matcher = new PatternMatcher('7(?:1[0-68]|2[1-9])')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('7')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('71')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('73')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('711')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('717')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('720')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('722')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('7222')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('7222', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match an or pattern containing or groups', function() {\r\n\t\tconst matcher = new PatternMatcher('2(?:2[024-9]|3[0-59]|47|6[245]|9[02-8])|3(?:3[28]|4[03-9]|5[2-46-8]|7[1-578]|8[2-9])')\r\n\r\n\t\texpect(matcher.match('1')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('2')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('3')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('4')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('21')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('22')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('221')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('222')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('2222')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('2222', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('3')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('33')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('332')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('333')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match an or pattern', function() {\r\n\t\tconst matcher = new PatternMatcher('6|8')\r\n\r\n\t\texpect(matcher.match('5')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('6')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('7')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('8')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match an or pattern (one-of sets)', function() {\r\n\t\tconst matcher = new PatternMatcher('[123]|[5-8]')\r\n\r\n\t\texpect(matcher.match('0')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('1')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('2')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('3')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('4')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('5')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('6')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('7')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('8')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('9')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('18')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('18', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should match an or pattern (different lengths)', function() {\r\n\t\tconst matcher = new PatternMatcher('60|8')\r\n\r\n\t\texpect(matcher.match('5')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('6')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('60')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('61')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('7')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('8')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('68')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match an or pattern (one-of sets) (different lengths)', function() {\r\n\t\tconst matcher = new PatternMatcher('[123]|[5-8][2-8]')\r\n\r\n\t\texpect(matcher.match('0')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match an or pattern (one-of sets and regular digits) (different lengths)', function() {\r\n\t\tconst matcher = new PatternMatcher('[2358][2-5]|4')\r\n\r\n\t\texpect(matcher.match('0')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('2')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('21')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('22')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('25')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('26')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('222')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('222', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('3')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('6')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match an or pattern (one-of sets and regular digits mixed) (different lengths)', function() {\r\n\t\tconst matcher = new PatternMatcher('[2358]2|4')\r\n\r\n\t\texpect(matcher.match('0')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('2')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('21')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('22')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('222')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('222', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('3')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('6')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match an or pattern (one-of sets groups and regular digits mixed) (different lengths)', function() {\r\n\t\tconst matcher = new PatternMatcher('1(?:11|[2-9])')\r\n\r\n\t\texpect(matcher.match('1')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('10')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('11')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('111')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('1111')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('1111', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('12')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('122')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('19')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('5')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match nested or groups', function() {\r\n\t\tconst matcher = new PatternMatcher('1(?:2(?:3(?:4|5)|6)|7(?:8|9))0')\r\n\r\n\t\texpect(matcher.match('1')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('2')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('11')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('12')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('121')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('123')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('1231')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('1234')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('12340')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('123401')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('123401', { allowOverflow: true })).to.deep.equal({\r\n\t\t\toverflow: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('12350')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('12360')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('1260')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('1270')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('1770')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('1780')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('1790')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('18')).to.be.undefined\r\n\t})\r\n\r\n\tit('should match complex patterns', function() {\r\n\t\tconst matcher = new PatternMatcher('(?:31|4)6|51|6(?:5[0-3579]|[6-9])|7(?:20|32|8)|[89]')\r\n\r\n\t\texpect(matcher.match('0')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('3')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('31')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('32')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('316')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('315')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('4')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('46')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('47')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('5')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('50')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('51')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('6')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('64')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('65')).to.deep.equal({\r\n\t\t\tpartialMatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('650')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('654')).to.be.undefined\r\n\r\n\t\texpect(matcher.match('69')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('8')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\r\n\t\texpect(matcher.match('9')).to.deep.equal({\r\n\t\t\tmatch: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('shouldn\\'t match things that shouldn\\'t match', function() {\r\n\t\t// There was a bug: \"leading digits\" `\"2\"` matched \"leading digits pattern\" `\"90\"`.\r\n\t\t// The incorrect `.match()` function result was `{ oveflow: true }`\r\n\t\t// while it should've been `undefined`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/66\r\n\t\texpect(new PatternMatcher('2').match('90', { allowOverflow: true })).to.be.undefined\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,cAAc,MAAM,wCAAwC;AAEnEC,QAAQ,CAAC,mCAAmC,EAAE,YAAW;EACxDC,EAAE,CAAC,wCAAwC,EAAE,YAAW;IACvDC,MAAM,CAAC;MAAA,OAAM,IAAIH,cAAc,CAAC,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,qBAAqB,CAAC;EACnE,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,EAAE,YAAW;IACtD,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,GAAG,CAAC;IACvCG,MAAM,CAAC;MAAA,OAAME,OAAO,CAACC,KAAK,CAAC,CAAC;IAAA,EAAC,CAACF,EAAE,SAAM,CAAC,oBAAoB,CAAC;EAC7D,CAAC,CAAC;EAEFF,EAAE,CAAC,oCAAoC,EAAE,YAAW;IACnDC,MAAM,CAAC;MAAA,OAAM,IAAIH,cAAc,CAAC,SAAS,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,oBAAoB,CAAC;EAC3E,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,EAAE,YAAW;IACtDC,MAAM,CAAC;MAAA,OAAM,IAAIH,cAAc,CAAC,KAAK,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,+CAA+C,CAAC;EAClG,CAAC,CAAC;EAEFF,EAAE,CAAC,uDAAuD,EAAE,YAAW;IACtEC,MAAM,CAAC;MAAA,OAAM,IAAIH,cAAc,CAAC,MAAM,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,0CAA0C,CAAC;EAC9F,CAAC,CAAC;EAEFF,EAAE,CAAC,yCAAyC,EAAE,YAAW;IACxDC,MAAM,CAAC;MAAA,OAAM,IAAIH,cAAc,CAAC,SAAS,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,sDAAsD,CAAC;EAC7G,CAAC,CAAC;EAEFF,EAAE,CAAC,yCAAyC,EAAE,YAAW;IACxDC,MAAM,CAAC;MAAA,OAAM,IAAIH,cAAc,CAAC,UAAU,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,2DAA2D,CAAC;EACnH,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,EAAE,YAAW;IACtDC,MAAM,CAAC;MAAA,OAAM,IAAIH,cAAc,CAAC,SAAS,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,iDAAiD,CAAC;EACxG,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,EAAE,YAAW;IACtDC,MAAM,CAAC;MAAA,OAAM,IAAIH,cAAc,CAAC,SAAS,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,8CAA8C,CAAC;EACrG,CAAC,CAAC;EAEFF,EAAE,CAAC,kCAAkC,EAAE,YAAW;IACjD,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,GAAG,CAAC;IAEvCG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAClEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,kCAAkC,EAAE,YAAW;IACjD,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,IAAI,CAAC;IAExCG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACnEE,QAAQ,EAAE;IACX,CAAC,CAAC;IAEFT,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC5C,CAAC,CAAC;EAEFN,EAAE,CAAC,oDAAoD,EAAE,YAAW;IACnE,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,KAAK,CAAC;IAEzCG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAClEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,uDAAuD,EAAE,YAAW;IACtE,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,OAAO,CAAC;IAE3CG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAClEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,2EAA2E,EAAE,YAAW;IAC1F,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,OAAO,CAAC;IAE3CG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAClEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,uEAAuE,EAAE,YAAW;IACtF,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,OAAO,CAAC;IAE3CG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAClEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,+DAA+D,EAAE,YAAW;IAC9E,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,UAAU,CAAC;IAE9CG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAClEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,qCAAqC,EAAE,YAAW;IACpD,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,WAAW,CAAC;IAE/CG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAC1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACnEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,sEAAsE,EAAE,YAAW;IACrF,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,OAAO,CAAC;IAE3CG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC5C,CAAC,CAAC;EAEFN,EAAE,CAAC,yCAAyC,EAAE,YAAW;IACxD,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,qBAAqB,CAAC;IAEzDG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC1CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC1CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE7CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACpEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,iDAAiD,EAAE,YAAW;IAChE,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,sFAAsF,CAAC;IAE1HG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC1CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE7CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACpEE,QAAQ,EAAE;IACX,CAAC,CAAC;IAEFT,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC1CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC7C,CAAC,CAAC;EAEFN,EAAE,CAAC,4BAA4B,EAAE,YAAW;IAC3C,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,KAAK,CAAC;IAEzCG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC,CAAC;EAEFJ,EAAE,CAAC,0CAA0C,EAAE,YAAW;IACzD,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,aAAa,CAAC;IAEjDG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAClEE,QAAQ,EAAE;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFV,EAAE,CAAC,gDAAgD,EAAE,YAAW;IAC/D,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,MAAM,CAAC;IAE1CG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC5C,CAAC,CAAC;EAEFN,EAAE,CAAC,8DAA8D,EAAE,YAAW;IAC7E,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,kBAAkB,CAAC;IAEtDG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC3C,CAAC,CAAC;EAEFN,EAAE,CAAC,iFAAiF,EAAE,YAAW;IAChG,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,eAAe,CAAC;IAEnDG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACnEE,QAAQ,EAAE;IACX,CAAC,CAAC;IAEFT,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC3C,CAAC,CAAC;EAEFN,EAAE,CAAC,uFAAuF,EAAE,YAAW;IACtG,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,WAAW,CAAC;IAE/CG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACnEE,QAAQ,EAAE;IACX,CAAC,CAAC;IAEFT,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC3C,CAAC,CAAC;EAEFN,EAAE,CAAC,8FAA8F,EAAE,YAAW;IAC7G,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,eAAe,CAAC;IAEnDG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC1CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE7CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACpEE,QAAQ,EAAE;IACX,CAAC,CAAC;IAEFT,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC3C,CAAC,CAAC;EAEFN,EAAE,CAAC,+BAA+B,EAAE,YAAW;IAC9C,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,gCAAgC,CAAC;IAEpEG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC1CG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE7CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC3CG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC5CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE/CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,QAAQ,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACtEE,QAAQ,EAAE;IACX,CAAC,CAAC;IAEFT,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC5CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE9CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC3CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE7CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE7CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC3CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC3CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;EAC5C,CAAC,CAAC;EAEFN,EAAE,CAAC,+BAA+B,EAAE,YAAW;IAC9C,IAAMG,OAAO,GAAG,IAAIL,cAAc,CAAC,qDAAqD,CAAC;IAEzFG,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE1CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC1CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE3CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCG,YAAY,EAAE;IACf,CAAC,CAAC;IAEFV,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MAC1CJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACF,EAAE,CAACG,EAAE,CAACC,SAAS;IAE5CL,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACzCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;IAEFH,MAAM,CAACE,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,CAACK,IAAI,CAACC,KAAK,CAAC;MACxCJ,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC,CAAC;EAEFJ,EAAE,CAAC,+CAA+C,EAAE,YAAW;IAC9D;IACA;IACA;IACA;IACAC,MAAM,CAAC,IAAIH,cAAc,CAAC,GAAG,CAAC,CAACM,KAAK,CAAC,IAAI,EAAE;MAAEK,aAAa,EAAE;IAAK,CAAC,CAAC,CAAC,CAACP,EAAE,CAACG,EAAE,CAACC,SAAS;EACrF,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}