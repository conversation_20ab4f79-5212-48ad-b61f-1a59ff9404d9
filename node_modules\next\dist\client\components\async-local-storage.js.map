{"version": 3, "sources": ["../../../src/client/components/async-local-storage.ts"], "names": ["createAsyncLocalStorage", "sharedAsyncLocalStorageNotAvailableError", "Error", "FakeAsyncLocalStorage", "disable", "getStore", "undefined", "run", "exit", "enterWith", "maybeGlobalAsyncLocalStorage", "globalThis", "AsyncLocalStorage"], "mappings": ";;;;+BAiCgBA;;;eAAAA;;;AA/BhB,MAAMC,2CAA2C,IAAIC,MACnD;AAGF,MAAMC;IAGJC,UAAgB;QACd,MAAMH;IACR;IAEAI,WAA8B;QAC5B,4EAA4E;QAC5E,OAAOC;IACT;IAEAC,MAAY;QACV,MAAMN;IACR;IAEAO,OAAa;QACX,MAAMP;IACR;IAEAQ,YAAkB;QAChB,MAAMR;IACR;AACF;AAEA,MAAMS,+BAA+B,AAACC,WAAmBC,iBAAiB;AAEnE,SAASZ;IAGd,IAAIU,8BAA8B;QAChC,OAAO,IAAIA;IACb;IACA,OAAO,IAAIP;AACb"}