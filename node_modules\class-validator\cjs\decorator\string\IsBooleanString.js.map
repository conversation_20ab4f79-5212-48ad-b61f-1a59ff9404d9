{"version": 3, "file": "IsBooleanString.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBooleanString.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,wEAAyD;AAE5C,QAAA,iBAAiB,GAAG,iBAAiB,CAAC;AAEnD;;;GAGG;AACH,SAAgB,eAAe,CAAC,KAAc;IAC5C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,mBAAkB,EAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAFD,0CAEC;AAED;;;GAGG;AACH,SAAgB,eAAe,CAAC,iBAAqC;IACnE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,yBAAiB;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;YAC1D,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,oCAAoC,EAC/D,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,0CAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBooleanValidator from 'validator/lib/isBoolean';\n\nexport const IS_BOOLEAN_STRING = 'isBooleanString';\n\n/**\n * Checks if a string is a boolean.\n * If given value is not a string, then it returns false.\n */\nexport function isBooleanString(value: unknown): boolean {\n  return typeof value === 'string' && isBooleanValidator(value);\n}\n\n/**\n * Checks if a string is a boolean.\n * If given value is not a string, then it returns false.\n */\nexport function IsBooleanString(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BOOLEAN_STRING,\n      validator: {\n        validate: (value, args): boolean => isBooleanString(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a boolean string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}