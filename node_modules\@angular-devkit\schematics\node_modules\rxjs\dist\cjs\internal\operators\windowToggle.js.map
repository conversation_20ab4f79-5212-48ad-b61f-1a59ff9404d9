{"version": 3, "file": "windowToggle.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/windowToggle.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AACA,sCAAqC;AACrC,gDAA+C;AAE/C,qCAAuC;AACvC,qDAAoD;AACpD,2DAAgE;AAChE,qCAAoC;AACpC,+CAA8C;AAiD9C,SAAgB,YAAY,CAC1B,QAA4B,EAC5B,eAAuD;IAEvD,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,IAAM,WAAW,GAAG,UAAC,GAAQ;YAC3B,OAAO,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC7B;YACD,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC;QAEF,qBAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAC3B,6CAAwB,CACtB,UAAU,EACV,UAAC,SAAS;YACR,IAAM,MAAM,GAAG,IAAI,iBAAO,EAAK,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,IAAM,mBAAmB,GAAG,IAAI,2BAAY,EAAE,CAAC;YAC/C,IAAM,WAAW,GAAG;gBAClB,qBAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC3B,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAClB,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACpC,CAAC,CAAC;YAEF,IAAI,eAAgC,CAAC;YACrC,IAAI;gBACF,eAAe,GAAG,qBAAS,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;aACzD;YAAC,OAAO,GAAG,EAAE;gBACZ,WAAW,CAAC,GAAG,CAAC,CAAC;gBACjB,OAAO;aACR;YAED,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAEvC,mBAAmB,CAAC,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,6CAAwB,CAAC,UAAU,EAAE,WAAW,EAAE,WAAI,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QAC3H,CAAC,EACD,WAAI,CACL,CACF,CAAC;QAGF,MAAM,CAAC,SAAS,CACd,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAQ;;YAGP,IAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;;gBACpC,KAAqB,IAAA,gBAAA,SAAA,WAAW,CAAA,wCAAA,iEAAE;oBAA7B,IAAM,QAAM,wBAAA;oBACf,QAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACpB;;;;;;;;;QACH,CAAC,EACD;YAEE,OAAO,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,QAAQ,EAAE,CAAC;aAC7B;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,EACD,WAAW,EACX;YAME,OAAO,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,WAAW,EAAE,CAAC;aAChC;QACH,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AA5ED,oCA4EC"}