{"version": 3, "file": "mergeArrays.test.js", "names": ["mergeArrays", "describe", "it", "expect", "to", "deep", "equal"], "sources": ["../../source/helpers/mergeArrays.test.js"], "sourcesContent": ["import mergeArrays from './mergeArrays.js'\r\n\r\ndescribe('mergeArrays', () => {\r\n\tit('should merge arrays', () => {\r\n\t\texpect(mergeArrays([1, 2], [2, 3])).to.deep.equal([1, 2, 3])\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAE1CC,QAAQ,CAAC,aAAa,EAAE,YAAM;EAC7BC,EAAE,CAAC,qBAAqB,EAAE,YAAM;IAC/BC,MAAM,CAACH,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACI,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}