{"version": 3, "file": "AsYouTypeFormatter.PatternParser.test.js", "names": ["_AsYouTypeFormatterPatternParser", "_interopRequireDefault", "require", "e", "__esModule", "describe", "it", "expect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "to", "equal", "deep", "op", "args"], "sources": ["../source/AsYouTypeFormatter.PatternParser.test.js"], "sourcesContent": ["import PatternParser from './AsYouTypeFormatter.PatternParser.js'\r\n\r\ndescribe('PatternParser', function() {\r\n\tit('should parse single-character patterns', function() {\r\n\t\texpect(new PatternParser().parse('2')).to.equal('2')\r\n\t})\r\n\r\n\tit('should parse string patterns', function() {\r\n\t\texpect(new PatternParser().parse('123')).to.deep.equal(['1', '2', '3'])\r\n\t})\r\n\r\n\tit('should parse \"one of\" patterns', function() {\r\n\t\texpect(new PatternParser().parse('[5-9]')).to.deep.equal({\r\n\t\t\top: '[]',\r\n\t\t\targs: ['5', '6', '7', '8', '9']\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse \"or\" patterns', function() {\r\n\t\texpect(new PatternParser().parse('123|[5-9]')).to.deep.equal({\r\n\t\t\top: '|',\r\n\t\t\targs: [\r\n\t\t\t\t['1', '2', '3'],\r\n\t\t\t\t{\r\n\t\t\t\t\top: '[]',\r\n\t\t\t\t\targs: ['5', '6', '7', '8', '9']\r\n\t\t\t\t}\r\n\t\t\t]\r\n\t\t})\r\n\r\n\t\texpect(new <PERSON><PERSON><PERSON>ars<PERSON>().parse('123|[5-9]0')).to.deep.equal({\r\n\t\t\top: '|',\r\n\t\t\targs: [\r\n\t\t\t\t['1', '2', '3'],\r\n\t\t\t\t[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\top: '[]',\r\n\t\t\t\t\t\targs: ['5', '6', '7', '8', '9']\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'0'\r\n\t\t\t\t]\r\n\t\t\t]\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse nested \"or\" patterns', function() {\r\n\t\texpect(new PatternParser().parse('123|(?:2|34)[5-9]')).to.deep.equal({\r\n\t\t\top: '|',\r\n\t\t\targs: [\r\n\t\t\t\t['1', '2', '3'],\r\n\t\t\t\t[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\top: '|',\r\n\t\t\t\t\t\targs: [\r\n\t\t\t\t\t\t\t'2',\r\n\t\t\t\t\t\t\t['3', '4']\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\top: '[]',\r\n\t\t\t\t\t\targs: ['5', '6', '7', '8', '9']\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t]\r\n\t\t})\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,gCAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAiE,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEjEE,QAAQ,CAAC,eAAe,EAAE,YAAW;EACpCC,EAAE,CAAC,wCAAwC,EAAE,YAAW;IACvDC,MAAM,CAAC,IAAIC,2CAAa,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EACrD,CAAC,CAAC;EAEFL,EAAE,CAAC,8BAA8B,EAAE,YAAW;IAC7CC,MAAM,CAAC,IAAIC,2CAAa,CAAC,CAAC,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACE,IAAI,CAACD,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EACxE,CAAC,CAAC;EAEFL,EAAE,CAAC,gCAAgC,EAAE,YAAW;IAC/CC,MAAM,CAAC,IAAIC,2CAAa,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAAC,CAACC,EAAE,CAACE,IAAI,CAACD,KAAK,CAAC;MACxDE,EAAE,EAAE,IAAI;MACRC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,CAAC,CAAC;EACH,CAAC,CAAC;EAEFR,EAAE,CAAC,4BAA4B,EAAE,YAAW;IAC3CC,MAAM,CAAC,IAAIC,2CAAa,CAAC,CAAC,CAACC,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,EAAE,CAACE,IAAI,CAACD,KAAK,CAAC;MAC5DE,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,CACL,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACf;QACCD,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;MAC/B,CAAC;IAEH,CAAC,CAAC;IAEFP,MAAM,CAAC,IAAIC,2CAAa,CAAC,CAAC,CAACC,KAAK,CAAC,YAAY,CAAC,CAAC,CAACC,EAAE,CAACE,IAAI,CAACD,KAAK,CAAC;MAC7DE,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,CACL,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACf,CACC;QACCD,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;MAC/B,CAAC,EACD,GAAG,CACH;IAEH,CAAC,CAAC;EACH,CAAC,CAAC;EAEFR,EAAE,CAAC,mCAAmC,EAAE,YAAW;IAClDC,MAAM,CAAC,IAAIC,2CAAa,CAAC,CAAC,CAACC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAACC,EAAE,CAACE,IAAI,CAACD,KAAK,CAAC;MACpEE,EAAE,EAAE,GAAG;MACPC,IAAI,EAAE,CACL,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACf,CACC;QACCD,EAAE,EAAE,GAAG;QACPC,IAAI,EAAE,CACL,GAAG,EACH,CAAC,GAAG,EAAE,GAAG,CAAC;MAEZ,CAAC,EACD;QACCD,EAAE,EAAE,IAAI;QACRC,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;MAC/B,CAAC,CACD;IAEH,CAAC,CAAC;EACH,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}