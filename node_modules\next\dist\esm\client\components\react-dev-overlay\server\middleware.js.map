{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware.ts"], "names": ["constants", "FS", "promises", "fs", "path", "SourceMapConsumer", "getRawSourceMap", "launchEditor", "badRequest", "findSourcePackage", "getOriginalCodeFrame", "internalServerError", "json", "noContent", "getServerError", "parseStack", "getModuleId", "compilation", "module", "chunkGraph", "getModuleById", "id", "modules", "find", "searchModule", "findModuleNotFoundFromError", "errorMessage", "match", "getModuleSource", "codeGenerationResults", "get", "sources", "getSourcePath", "source", "replace", "findOriginalSourcePositionAndContent", "webpackSource", "position", "consumer", "map", "sourcePosition", "originalPositionFor", "line", "column", "sourceContent", "sourceContentFor", "destroy", "findOriginalSourcePositionAndContentFromCompilation", "moduleId", "importedModule", "buildInfo", "importLocByPath", "createOriginalStackFrame", "modulePath", "rootDirectory", "frame", "lineNumber", "moduleNotFound", "result", "filePath", "resolve", "includes", "traced", "file", "relative", "methodName", "name", "arguments", "originalStackFrame", "originalCodeFrame", "sourcePackage", "getSourceById", "isFile", "fileContent", "readFile", "catch", "moduleSource", "err", "console", "error", "getOverlayMiddleware", "options", "req", "res", "next", "pathname", "searchParams", "URL", "url", "parseInt", "getAll", "filter", "Boolean", "isServer", "isEdgeServer", "isAppDirectory", "isClient", "test", "startsWith", "stats", "serverStats", "edgeServerStats", "log", "originalStackFrameResponse", "fileExists", "access", "F_OK", "then"], "mappings": "AAAA,SAASA,aAAaC,EAAE,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACpD,OAAOC,UAAU,OAAM;AACvB,SAASC,iBAAiB,QAAQ,kCAAiC;AAEnE,SAASC,eAAe,QAAQ,sCAAqC;AACrE,SAASC,YAAY,QAAQ,mCAAkC;AAC/D,SACEC,UAAU,EACVC,iBAAiB,EACjBC,oBAAoB,EACpBC,mBAAmB,EACnBC,IAAI,EACJC,SAAS,QAEJ,WAAU;AACjB,SAASC,cAAc,QAAQ,sCAAqC;AACpE,SAASC,UAAU,QAAQ,iCAAgC;AAO3D,SAASC,YAAYC,WAAgB,EAAEC,MAAW;IAChD,OAAOD,YAAYE,UAAU,CAACH,WAAW,CAACE;AAC5C;AAEA,SAASE,cACPC,EAAsB,EACtBJ,WAAgC;IAEhC,OAAO;WAAIA,YAAYK,OAAO;KAAC,CAACC,IAAI,CAClC,CAACC,eAAiBR,YAAYC,aAAaO,kBAAkBH;AAEjE;AAEA,SAASI,4BAA4BC,YAAgC;QAC5DA;IAAP,OAAOA,iCAAAA,sBAAAA,aAAcC,KAAK,CAAC,wCAApBD,mBAAyC,CAAC,EAAE;AACrD;AAEA,SAASE,gBAAgBX,WAAgB,EAAEC,MAAW;QAGlDD;IAFF,IAAI,CAACC,QAAQ,OAAO;QAElBD;IADF,OACEA,CAAAA,sDAAAA,yCAAAA,YAAYY,qBAAqB,CAACC,GAAG,CAACZ,4BAAtCD,uCAA+Cc,OAAO,CAACD,GAAG,CAAC,yBAA3Db,qDACA;AAEJ;AAEA,SAASe,cAAcC,MAAc;IACnC,OAAOA,OAAOC,OAAO,CAAC,qDAAqD;AAC7E;AAEA,eAAeC,qCACbC,aAAkB,EAClBC,QAAiD;IAEjD,MAAMC,WAAW,MAAM,IAAIjC,kBAAkB+B,cAAcG,GAAG;IAC9D,IAAI;YAGQF;QAFV,MAAMG,iBAAiBF,SAASG,mBAAmB,CAAC;YAClDC,MAAML,SAASK,IAAI;YACnBC,QAAQN,CAAAA,mBAAAA,SAASM,MAAM,YAAfN,mBAAmB;QAC7B;QAEA,IAAI,CAACG,eAAeP,MAAM,EAAE;YAC1B,OAAO;QACT;YAGEK;QADF,MAAMM,gBACJN,CAAAA,6BAAAA,SAASO,gBAAgB,CACvBL,eAAeP,MAAM,EACrB,uBAAuB,GAAG,iBAF5BK,6BAGK;QAEP,OAAO;YACLE;YACAI;QACF;IACF,SAAU;QACRN,SAASQ,OAAO;IAClB;AACF;AAEA,SAASC,oDACPC,QAA4B,EAC5BC,cAAsB,EACtBhC,WAAgC;QAGzBC,mCAAAA;IADP,MAAMA,SAASE,cAAc4B,UAAU/B;QAChCC;IAAP,OAAOA,CAAAA,wCAAAA,2BAAAA,oBAAAA,OAAQgC,SAAS,sBAAjBhC,oCAAAA,kBAAmBiC,eAAe,qBAAlCjC,kCAAoCY,GAAG,CAACmB,2BAAxC/B,wCAA2D;AACpE;AAEA,OAAO,eAAekC,yBAAyB,KAgB9C;IAhB8C,IAAA,EAC7CnB,MAAM,EACNe,QAAQ,EACRK,UAAU,EACVC,aAAa,EACbC,KAAK,EACL7B,YAAY,EACZT,WAAW,EASZ,GAhB8C;QA0DzC,gHAAgH;IAChH,kGAAkG;IAClGsC,2BAAAA;IA3CJ,MAAM,EAAEC,UAAU,EAAEb,MAAM,EAAE,GAAGY;IAC/B,MAAME,iBAAiBhC,4BAA4BC;IACnD,MAAMgC,SAAS,MAAM,AAAC,CAAA;QACpB,IAAID,gBAAgB;YAClB,IAAI,CAACxC,aAAa,OAAO;YAEzB,OAAO8B,oDACLC,UACAS,gBACAxC;QAEJ;QACA,iDAAiD;QACjD,OAAO,MAAMkB,qCAAqCF,QAAQ;YACxDS,MAAMc,qBAAAA,aAAc;YACpBb;QACF;IACF,CAAA;IAEA,IAAI,EAACe,0BAAAA,OAAQlB,cAAc,CAACP,MAAM,GAAE,OAAO;IAE3C,MAAM,EAAEO,cAAc,EAAEI,aAAa,EAAE,GAAGc;IAE1C,MAAMC,WAAWvD,KAAKwD,OAAO,CAC3BN,eACAtB,cAEE,AADA,oFAAoF;IACnFQ,CAAAA,eAAeP,MAAM,CAAC4B,QAAQ,CAAC,OAC5BR,aACAb,eAAeP,MAAM,AAAD,KAAMoB;QASvBb;IALX,MAAMsB,SAAS;QACbC,MAAMnB,gBACFxC,KAAK4D,QAAQ,CAACV,eAAeK,YAC7BnB,eAAeP,MAAM;QACzBuB,YAAYhB,eAAeE,IAAI;QAC/BC,QAAQ,AAACH,CAAAA,CAAAA,yBAAAA,eAAeG,MAAM,YAArBH,yBAAyB,CAAA,IAAK;QACvCyB,YACEzB,eAAe0B,IAAI,MAGnBX,oBAAAA,MAAMU,UAAU,sBAAhBV,4BAAAA,kBACIrB,OAAO,CAAC,8BAA8B,+BAD1CqB,0BAEIrB,OAAO,CAAC,wBAAwB;QACtCiC,WAAW,EAAE;IACf;IAEA,OAAO;QACLC,oBAAoBN;QACpBO,mBAAmB3D,qBAAqBoD,QAAQlB;QAChD0B,eAAe7D,kBAAkBqD;IACnC;AACF;AAEA,OAAO,eAAeS,cACpBC,MAAe,EACfnD,EAAU,EACVJ,WAAiC;IAEjC,IAAIuD,QAAQ;QACV,MAAMC,cAA6B,MAAMtE,GACtCuE,QAAQ,CAACrD,IAAI,SACbsD,KAAK,CAAC,IAAM;QAEf,IAAIF,eAAe,MAAM;YACvB,OAAO;QACT;QAEA,MAAMlC,MAAMjC,gBAAgBmE;QAC5B,IAAIlC,OAAO,MAAM;YACf,OAAO;QACT;QAEA,OAAO;YACLA;gBACE,OAAOA;YACT;QACF;IACF;IAEA,IAAI;QACF,IAAI,CAACtB,aAAa;YAChB,OAAO;QACT;QAEA,MAAMC,SAASE,cAAcC,IAAIJ;QACjC,MAAM2D,eAAehD,gBAAgBX,aAAaC;QAClD,OAAO0D;IACT,EAAE,OAAOC,KAAK;QACZC,QAAQC,KAAK,CAAC,AAAC,qCAAkC1D,KAAG,OAAMwD;QAC1D,OAAO;IACT;AACF;AAEA,OAAO,SAASG,qBAAqBC,OAKpC;IACC,OAAO,eACLC,GAAoB,EACpBC,GAAmB,EACnBC,IAAc;QAEd,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUL,IAAIM,GAAG;YAKtCF,mBACJA;QAJnB,MAAM/B,QAAQ;YACZQ,MAAMuB,aAAaxD,GAAG,CAAC;YACvBmC,YAAYqB,aAAaxD,GAAG,CAAC;YAC7B0B,YAAYiC,SAASH,CAAAA,oBAAAA,aAAaxD,GAAG,CAAC,yBAAjBwD,oBAAkC,KAAK,OAAO;YACnE3C,QAAQ8C,SAASH,CAAAA,qBAAAA,aAAaxD,GAAG,CAAC,qBAAjBwD,qBAA8B,KAAK,OAAO;YAC3DnB,WAAWmB,aAAaI,MAAM,CAAC,aAAaC,MAAM,CAACC;QACrD;QAEA,MAAMC,WAAWP,aAAaxD,GAAG,CAAC,gBAAgB;QAClD,MAAMgE,eAAeR,aAAaxD,GAAG,CAAC,oBAAoB;QAC1D,MAAMiE,iBAAiBT,aAAaxD,GAAG,CAAC,sBAAsB;QAE9D,IAAIuD,aAAa,kCAAkC;YACjD,MAAMW,WAAW,CAACH,YAAY,CAACC;YAE/B,IAAIxB,gBAAgB7D,kBAAkB8C;YAEtC,IACE,CACE,CAAA,iDAAiD0C,IAAI,CAAC1C,MAAMQ,IAAI,KAChER,MAAMC,UAAU,AAAD,GAEjB;gBACA,IAAIc,eAAe,OAAO1D,KAAKuE,KAAK;oBAAEb;gBAAc;gBACpD,OAAO9D,WAAW2E;YACpB;YAEA,MAAMnC,WAAmBO,MAAMQ,IAAI,CAAC7B,OAAO,CACzC,8DACA;YAEF,MAAMmB,aAAaE,MAAMQ,IAAI,CAAC7B,OAAO,CACnC,yEACA;YAGF,IAAID,SAAiB;YAErB,IAAIhB;YAEJ,MAAMuD,SAASjB,MAAMQ,IAAI,CAACmC,UAAU,CAAC;YAErC,IAAI;gBACF,IAAIF,YAAYD,gBAAgB;wBAChBd;oBAAdhE,eAAcgE,iBAAAA,QAAQkB,KAAK,uBAAblB,eAAiBhE,WAAW;oBAC1C,+BAA+B;oBAC/B,kDAAkD;oBAClD,oJAAoJ;oBACpJgB,SAAS,MAAMsC,cAAcC,QAAQxB,UAAU/B;gBACjD;gBACA,yBAAyB;gBACzB,yHAAyH;gBACzH,oKAAoK;gBACpK,IAAI,AAAC4E,CAAAA,YAAYE,cAAa,KAAM9D,WAAW,MAAM;wBACrCgD;oBAAdhE,eAAcgE,uBAAAA,QAAQmB,WAAW,uBAAnBnB,qBAAuBhE,WAAW;oBAChDgB,SAAS,MAAMsC,cAAcC,QAAQxB,UAAU/B;gBACjD;gBACA,8BAA8B;gBAC9B,uHAAuH;gBACvH,IAAI,AAAC6E,CAAAA,gBAAgBC,cAAa,KAAM9D,WAAW,MAAM;wBACzCgD;oBAAdhE,eAAcgE,2BAAAA,QAAQoB,eAAe,uBAAvBpB,yBAA2BhE,WAAW;oBACpDgB,SAAS,MAAMsC,cAAcC,QAAQxB,UAAU/B;gBACjD;YACF,EAAE,OAAO4D,KAAK;gBACZC,QAAQwB,GAAG,CAAC,6BAA6BzB;gBACzC,OAAOlE,oBAAoBwE;YAC7B;YAEA,IAAI,CAAClD,QAAQ;gBACX,IAAIqC,eAAe,OAAO1D,KAAKuE,KAAK;oBAAEb;gBAAc;gBACpD,OAAOzD,UAAUsE;YACnB;YAEA,IAAI;gBACF,MAAMoB,6BAA6B,MAAMnD,yBAAyB;oBAChEG;oBACAtB;oBACAe;oBACAK;oBACAC,eAAe2B,QAAQ3B,aAAa;oBACpCrC;gBACF;gBAEA,IAAIsF,+BAA+B,MAAM;oBACvC,IAAIjC,eAAe,OAAO1D,KAAKuE,KAAK;wBAAEb;oBAAc;oBACpD,OAAOzD,UAAUsE;gBACnB;gBAEA,OAAOvE,KAAKuE,KAAKoB;YACnB,EAAE,OAAO1B,KAAK;gBACZC,QAAQwB,GAAG,CAAC,+BAA+BzB;gBAC3C,OAAOlE,oBAAoBwE;YAC7B;QACF,OAAO,IAAIE,aAAa,2BAA2B;YACjD,IAAI,CAAC9B,MAAMQ,IAAI,EAAE,OAAOvD,WAAW2E;YAEnC,kFAAkF;YAClF,MAAMxB,WAAWvD,KAAKwD,OAAO,CAC3BqB,QAAQ3B,aAAa,EACrBC,MAAMQ,IAAI,CAAC7B,OAAO,CAAC,gBAAgB;YAErC,MAAMsE,aAAa,MAAMrG,GAAGsG,MAAM,CAAC9C,UAAU1D,GAAGyG,IAAI,EAAEC,IAAI,CACxD,IAAM,MACN,IAAM;YAER,IAAI,CAACH,YAAY,OAAO3F,UAAUsE;YAElC,IAAI;oBAC6C5B;gBAA/C,MAAMhD,aAAaoD,UAAUJ,MAAMC,UAAU,EAAED,CAAAA,gBAAAA,MAAMZ,MAAM,YAAZY,gBAAgB;YACjE,EAAE,OAAOsB,KAAK;gBACZC,QAAQwB,GAAG,CAAC,4BAA4BzB;gBACxC,OAAOlE,oBAAoBwE;YAC7B;YAEA,OAAOtE,UAAUsE;QACnB;QACA,OAAOC;IACT;AACF"}