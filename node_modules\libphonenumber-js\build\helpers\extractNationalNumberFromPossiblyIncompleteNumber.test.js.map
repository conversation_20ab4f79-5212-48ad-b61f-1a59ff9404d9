{"version": 3, "file": "extractNationalNumberFromPossiblyIncompleteNumber.test.js", "names": ["_metadata", "_interopRequireDefault", "require", "_metadataMin", "_extractNationalNumberFromPossiblyIncompleteNumber", "e", "__esModule", "describe", "it", "meta", "<PERSON><PERSON><PERSON>", "metadata", "country", "expect", "extractNationalNumberFromPossiblyIncompleteNumber", "to", "deep", "equal", "nationalPrefix", "undefined", "carrierCode", "nationalNumber"], "sources": ["../../source/helpers/extractNationalNumberFromPossiblyIncompleteNumber.test.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport metadata from '../../metadata.min.json' with { type: 'json' }\r\nimport extractNationalNumberFromPossiblyIncompleteNumber from './extractNationalNumberFromPossiblyIncompleteNumber.js'\r\n\r\ndescribe('extractNationalNumberFromPossiblyIncompleteNumber', () => {\r\n\tit('should parse a carrier code when there is no national prefix transform rule', () => {\r\n\t\tconst meta = new Metadata(metadata)\r\n\t\tmeta.country('AU')\r\n\t\texpect(extractNationalNumberFromPossiblyIncompleteNumber('18311800123', meta)).to.deep.equal({\r\n\t\t\tnationalPrefix: undefined,\r\n\t\t\tcarrierCode: '1831',\r\n\t\t\tnationalNumber: '1800123'\r\n\t\t})\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,kDAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAsH,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEtHE,QAAQ,CAAC,mDAAmD,EAAE,YAAM;EACnEC,EAAE,CAAC,6EAA6E,EAAE,YAAM;IACvF,IAAMC,IAAI,GAAG,IAAIC,oBAAQ,CAACC,uBAAQ,CAAC;IACnCF,IAAI,CAACG,OAAO,CAAC,IAAI,CAAC;IAClBC,MAAM,CAAC,IAAAC,6DAAiD,EAAC,aAAa,EAAEL,IAAI,CAAC,CAAC,CAACM,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC5FC,cAAc,EAAEC,SAAS;MACzBC,WAAW,EAAE,MAAM;MACnBC,cAAc,EAAE;IACjB,CAAC,CAAC;EACH,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}