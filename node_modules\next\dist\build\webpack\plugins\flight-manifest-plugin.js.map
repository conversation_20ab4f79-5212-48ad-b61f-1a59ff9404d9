{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-manifest-plugin.ts"], "names": ["ClientReferenceManifestPlugin", "pluginState", "getProxiedPluginState", "serverModuleIds", "edgeServerModuleIds", "ASYNC_CLIENT_MODULES", "getAppPathRequiredChunks", "chunkGroup", "excludedFiles", "deploymentIdChunkQuery", "getDeploymentIdQueryOrEmptyString", "chunks", "for<PERSON>ach", "chunk", "SYSTEM_ENTRYPOINTS", "has", "name", "id", "chunkId", "files", "file", "endsWith", "push", "encodeURI", "entryNameToGroupName", "entryName", "groupName", "slice", "lastIndexOf", "replace", "test", "mergeManifest", "manifest", "manifestToMerge", "Object", "assign", "clientModules", "ssrModuleMapping", "edgeSSRModuleMapping", "entryCSSFiles", "PLUGIN_NAME", "constructor", "options", "dev", "appDir", "appDirBase", "path", "dirname", "sep", "apply", "compiler", "hooks", "compilation", "tap", "normalModuleFactory", "dependencyFactories", "set", "webpack", "dependencies", "ModuleDependency", "dependencyTemplates", "NullDependency", "Template", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "assets", "createAsset", "context", "manifestsPerGroup", "Map", "manifestEntryFiles", "configuredCrossOriginLoading", "outputOptions", "crossOriginLoading", "crossOriginMode", "publicPath", "Error", "prefix", "rootMainFiles", "Set", "entrypoints", "get", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "getFiles", "add", "entrypoint", "APP_CLIENT_INTERNALS", "moduleLoading", "crossOrigin", "chunkEntryName", "filter", "f", "startsWith", "requiredChunks", "recordModule", "modId", "mod", "resource", "type", "_identifier", "moduleReferences", "moduleIdMapping", "edgeModuleIdMapping", "ssrNamedModuleId", "relative", "resourceResolveData", "isAsyncModule", "esmResource", "matchResource", "BARREL_OPTIMIZATION_PREFIX", "formatBarrelOptimizedResource", "addClientReference", "exportName", "async", "edgeExportName", "addSSRIdMapping", "checkedChunkGroups", "checkedChunks", "recordChunkGroup", "entryMods", "chunkGraph", "getChunkEntryModulesIterable", "layer", "WEBPACK_LAYERS", "appPagesBrowser", "request", "includes", "connections", "getModuleReferencesInOrder", "moduleGraph", "connection", "dependency", "clientEntryMod", "getResolvedModule", "getModuleId", "module", "concatenatedMod", "concatenatedModId", "child", "childrenIterable", "pageName", "mergedManifest", "segments", "split", "group", "segment", "json", "JSON", "stringify", "pagePath", "pageBundlePath", "normalizePagePath", "length", "CLIENT_REFERENCE_MANIFEST", "sources", "RawSource"], "mappings": "AAAA;;;;;CAKC;;;;+BA4KYA;;;eAAAA;;;8DA1KI;yBACgB;2BAM1B;8BAE+B;4BAEP;mCACG;8BAEgB;uBAI3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBP,MAAMC,cAAcC,IAAAA,mCAAqB,EAAC;IACxCC,iBAAiB,CAAC;IAClBC,qBAAqB,CAAC;IACtB,uCAAuC;IACvCC,sBAAsB,CAAC;AACzB;AA4CA,SAASC,yBACPC,UAA8B,EAC9BC,aAA0B;IAE1B,MAAMC,yBAAyBC,IAAAA,+CAAiC;IAEhE,MAAMC,SAAwB,EAAE;IAChCJ,WAAWI,MAAM,CAACC,OAAO,CAAC,CAACC;QACzB,IAAIC,6BAAkB,CAACC,GAAG,CAACF,MAAMG,IAAI,IAAI,KAAK;YAC5C,OAAO;QACT;QAEA,4DAA4D;QAC5D,+DAA+D;QAC/D,+DAA+D;QAC/D,mCAAmC;QACnC,IAAIH,MAAMI,EAAE,IAAI,MAAM;YACpB,MAAMC,UAAU,KAAKL,MAAMI,EAAE;YAC7BJ,MAAMM,KAAK,CAACP,OAAO,CAAC,CAACQ;gBACnB,6DAA6D;gBAC7D,0BAA0B;gBAC1B,IAAI,CAACA,KAAKC,QAAQ,CAAC,QAAQ,OAAO;gBAClC,IAAID,KAAKC,QAAQ,CAAC,mBAAmB,OAAO;gBAC5C,IAAIb,cAAcO,GAAG,CAACK,OAAO,OAAO;gBAEpC,sFAAsF;gBACtF,iFAAiF;gBACjF,iFAAiF;gBACjF,iFAAiF;gBACjF,2DAA2D;gBAC3D,OAAOT,OAAOW,IAAI,CAACJ,SAASK,UAAUH,OAAOX;YAC/C;QACF;IACF;IACA,OAAOE;AACT;AAEA,8EAA8E;AAC9E,6EAA6E;AAC7E,YAAY;AACZ,+BAA+B;AAC/B,4BAA4B;AAC5B,2CAA2C;AAC3C,0CAA0C;AAC1C,kCAAkC;AAClC,gDAAgD;AAChD,SAASa,qBAAqBC,SAAiB;IAC7C,IAAIC,YAAYD,UACbE,KAAK,CAAC,GAAGF,UAAUG,WAAW,CAAC,KAChC,eAAe;KACdC,OAAO,CAAC,aAAa,GACtB,2EAA2E;KAC1EA,OAAO,CAAC,0BAA0B,GACnC,2GAA2G;IAC3G,gHAAgH;IAChH,mHAAmH;KAClHA,OAAO,CAAC,6BAA6B;IAExC,sBAAsB;IACtBH,YAAYA,UACTG,OAAO,CAAC,oBAAoB,QAC5BA,OAAO,CAAC,aAAa;IAExB,kCAAkC;IAClC,MAAO,oBAAoBC,IAAI,CAACJ,WAAY;QAC1CA,YAAYA,UAAUG,OAAO,CAAC,sBAAsB;IACtD;IAEA,OAAOH;AACT;AAEA,SAASK,cACPC,QAAiC,EACjCC,eAAwC;IAExCC,OAAOC,MAAM,CAACH,SAASI,aAAa,EAAEH,gBAAgBG,aAAa;IACnEF,OAAOC,MAAM,CAACH,SAASK,gBAAgB,EAAEJ,gBAAgBI,gBAAgB;IACzEH,OAAOC,MAAM,CACXH,SAASM,oBAAoB,EAC7BL,gBAAgBK,oBAAoB;IAEtCJ,OAAOC,MAAM,CAACH,SAASO,aAAa,EAAEN,gBAAgBM,aAAa;AACrE;AAEA,MAAMC,cAAc;AAEb,MAAMxC;IAKXyC,YAAYC,OAAgB,CAAE;aAJ9BC,MAAsB;QAKpB,IAAI,CAACA,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,UAAU,GAAGC,aAAI,CAACC,OAAO,CAAC,IAAI,CAACH,MAAM,IAAIE,aAAI,CAACE,GAAG;IACxD;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAC5Bb,aACA,CAACY,aAAa,EAAEE,mBAAmB,EAAE;YACnCF,YAAYG,mBAAmB,CAACC,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrCL;YAEFF,YAAYQ,mBAAmB,CAACJ,GAAG,CACjCC,gBAAO,CAACC,YAAY,CAACC,gBAAgB,EACrC,IAAIF,gBAAO,CAACC,YAAY,CAACG,cAAc,CAACC,QAAQ;YAElDV,YAAYD,KAAK,CAACY,aAAa,CAACV,GAAG,CACjC;gBACErC,MAAMwB;gBACN,iEAAiE;gBACjE,0CAA0C;gBAC1CwB,OAAOP,gBAAO,CAACQ,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACC,SAAW,IAAI,CAACC,WAAW,CAACD,QAAQf,aAAaF,SAASmB,OAAO;QAEtE;IAEJ;IAEAD,YACED,MAAqC,EACrCf,WAAgC,EAChCiB,OAAe,EACf;YAuBAjB;QAtBA,MAAMkB,oBAAoB,IAAIC;QAC9B,MAAMC,qBAA+B,EAAE;QAEvC,MAAMC,+BACJrB,YAAYsB,aAAa,CAACC,kBAAkB;QAC9C,MAAMC,kBACJ,OAAOH,iCAAiC,WACpCA,iCAAiC,oBAC/BA,+BACA,cACF;QAEN,IAAI,OAAOrB,YAAYsB,aAAa,CAACG,UAAU,KAAK,UAAU;YAC5D,MAAM,IAAIC,MACR;QAEJ;QACA,MAAMC,SAAS3B,YAAYsB,aAAa,CAACG,UAAU,IAAI;QAEvD,8EAA8E;QAC9E,8DAA8D;QAC9D,MAAMG,gBAA6B,IAAIC;SACvC7B,+BAAAA,YAAY8B,WAAW,CACpBC,GAAG,CAACC,+CAAoC,sBAD3ChC,6BAEIiC,QAAQ,GACTzE,OAAO,CAAC,CAACQ;YACR,IAAI,oCAAoCU,IAAI,CAACV,OAAO;gBAClD4D,cAAcM,GAAG,CAAClE,KAAKS,OAAO,CAAC,OAAO;YACxC;QACF;QAEF,KAAK,IAAI,CAACJ,WAAW8D,WAAW,IAAInC,YAAY8B,WAAW,CAAE;YAC3D,IACEzD,cAAc2D,+CAAoC,IAClD3D,cAAc+D,+BAAoB,EAClC;gBACA/D,YAAY;YACd,OAAO,IAAI,CAAC,YAAYK,IAAI,CAACL,YAAY;gBACvC;YACF;YAEA,MAAMO,WAAoC;gBACxCyD,eAAe;oBACbV;oBACAW,aAAad;gBACf;gBACAvC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,sCAAsC;YACtC,MAAMoD,iBAAiB,AAAC,CAAA,IAAI,CAAC9C,UAAU,GAAGpB,SAAQ,EAAGI,OAAO,CAC1D,UACAiB,aAAI,CAACE,GAAG;YAEVhB,SAASO,aAAa,CAACoD,eAAe,GAAGJ,WACtCF,QAAQ,GACRO,MAAM,CAAC,CAACC,IAAM,CAACA,EAAEC,UAAU,CAAC,wBAAwBD,EAAExE,QAAQ,CAAC;YAElE,MAAM0E,iBAAiBzF,yBAAyBiF,YAAYP;YAC5D,MAAMgB,eAAe,CAACC,OAAiBC;oBAoBnCA,0BAqBEA;gBAxCJ,IAAIC,WACFD,IAAIE,IAAI,KAAK,qBAETF,IAAIG,WAAW,CAAC1E,KAAK,CAACuE,IAAIG,WAAW,CAACzE,WAAW,CAAC,OAAO,KACzDsE,IAAIC,QAAQ;gBAElB,IAAI,CAACA,UAAU;oBACb;gBACF;gBAEA,MAAMG,mBAAmBtE,SAASI,aAAa;gBAC/C,MAAMmE,kBAAkBvE,SAASK,gBAAgB;gBACjD,MAAMmE,sBAAsBxE,SAASM,oBAAoB;gBAEzD,4EAA4E;gBAC5E,6EAA6E;gBAC7E,sBAAsB;gBACtB,IAAImE,mBAAmBC,IAAAA,cAAQ,EAC7BrC,SACA6B,EAAAA,2BAAAA,IAAIS,mBAAmB,qBAAvBT,yBAAyBpD,IAAI,KAAIqD;gBAGnC,IAAI,CAACM,iBAAiBX,UAAU,CAAC,MAC/BW,mBAAmB,CAAC,EAAE,EAAEA,iBAAiB5E,OAAO,CAAC,OAAO,KAAK,CAAC;gBAEhE,MAAM+E,gBAAgB,CAAC,CAAC3G,YAAYI,oBAAoB,CAAC6F,IAAIC,QAAQ,CAAC;gBAEtE,wEAAwE;gBACxE,oEAAoE;gBACpE,MAAMU,cAAc,0BAA0B/E,IAAI,CAACqE,YAC/CA,SAAStE,OAAO,CACd,2BACA,kBAAkBA,OAAO,CAAC,OAAOiB,aAAI,CAACE,GAAG,KAE3C;gBAEJ,wEAAwE;gBACxE,2EAA2E;gBAC3E,8DAA8D;gBAC9D,yDAAyD;gBACzD,KAAIkD,qBAAAA,IAAIY,aAAa,qBAAjBZ,mBAAmBJ,UAAU,CAACiB,qCAA0B,GAAG;oBAC7DN,mBAAmBO,IAAAA,oCAA6B,EAC9CP,kBACAP,IAAIY,aAAa;oBAEnBX,WAAWa,IAAAA,oCAA6B,EAACb,UAAUD,IAAIY,aAAa;gBACtE;gBAEA,SAASG;oBACP,MAAMC,aAAaf;oBACnBnE,SAASI,aAAa,CAAC8E,WAAW,GAAG;wBACnCjG,IAAIgF;wBACJjF,MAAM;wBACNL,QAAQoF;wBACRoB,OAAOP;oBACT;oBACA,IAAIC,aAAa;wBACf,MAAMO,iBAAiBP;wBACvB7E,SAASI,aAAa,CAACgF,eAAe,GACpCpF,SAASI,aAAa,CAAC8E,WAAW;oBACtC;gBACF;gBAEA,SAASG;oBACP,MAAMH,aAAaf;oBACnB,IACE,OAAOlG,YAAYE,eAAe,CAACsG,iBAAiB,KAAK,aACzD;wBACAF,eAAe,CAACN,MAAM,GAAGM,eAAe,CAACN,MAAM,IAAI,CAAC;wBACpDM,eAAe,CAACN,MAAM,CAAC,IAAI,GAAG;4BAC5B,GAAGjE,SAASI,aAAa,CAAC8E,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvCvG,QAAQ,EAAE;4BACVM,IAAIhB,YAAYE,eAAe,CAACsG,iBAAiB;wBACnD;oBACF;oBAEA,IACE,OAAOxG,YAAYG,mBAAmB,CAACqG,iBAAiB,KACxD,aACA;wBACAD,mBAAmB,CAACP,MAAM,GAAGO,mBAAmB,CAACP,MAAM,IAAI,CAAC;wBAC5DO,mBAAmB,CAACP,MAAM,CAAC,IAAI,GAAG;4BAChC,GAAGjE,SAASI,aAAa,CAAC8E,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvCvG,QAAQ,EAAE;4BACVM,IAAIhB,YAAYG,mBAAmB,CAACqG,iBAAiB;wBACvD;oBACF;gBACF;gBAEAQ;gBACAI;gBAEArF,SAASI,aAAa,GAAGkE;gBACzBtE,SAASK,gBAAgB,GAAGkE;gBAC5BvE,SAASM,oBAAoB,GAAGkE;YAClC;YAEA,MAAMc,qBAAqB,IAAIrC;YAC/B,MAAMsC,gBAAgB,IAAItC;YAE1B,SAASuC,iBAAiBjH,UAAsB;gBAC9C,yEAAyE;gBACzE,IAAI+G,mBAAmBvG,GAAG,CAACR,aAAa;gBACxC+G,mBAAmBhC,GAAG,CAAC/E;gBACvB,0EAA0E;gBAC1E,oEAAoE;gBACpE,oEAAoE;gBACpE,qDAAqD;gBACrD,6CAA6C;gBAC7CA,WAAWI,MAAM,CAACC,OAAO,CAAC,CAACC;oBACzB,mEAAmE;oBACnE,IAAI0G,cAAcxG,GAAG,CAACF,QAAQ;oBAC9B0G,cAAcjC,GAAG,CAACzE;oBAClB,MAAM4G,YACJrE,YAAYsE,UAAU,CAACC,4BAA4B,CAAC9G;oBACtD,KAAK,MAAMqF,OAAOuB,UAAW;wBAC3B,IAAIvB,IAAI0B,KAAK,KAAKC,0BAAc,CAACC,eAAe,EAAE;wBAElD,MAAMC,UAAU,AAAC7B,IAA6B6B,OAAO;wBAErD,IACE,CAACA,WACD,CAACA,QAAQC,QAAQ,CAAC,wCAClB;4BACA;wBACF;wBAEA,MAAMC,cAAcC,IAAAA,iCAA0B,EAC5ChC,KACA9C,YAAY+E,WAAW;wBAGzB,KAAK,MAAMC,cAAcH,YAAa;4BACpC,MAAMI,aAAaD,WAAWC,UAAU;4BACxC,IAAI,CAACA,YAAY;4BAEjB,MAAMC,iBAAiBlF,YAAY+E,WAAW,CAACI,iBAAiB,CAC9DF;4BAEF,MAAMpC,QAAQ7C,YAAYsE,UAAU,CAACc,WAAW,CAC9CF;4BAGF,IAAIrC,UAAU,MAAM;gCAClBD,aAAaC,OAAOqC;4BACtB,OAAO;oCAGHF;gCAFF,oEAAoE;gCACpE,IACEA,EAAAA,qBAAAA,WAAWK,MAAM,qBAAjBL,mBAAmB3F,WAAW,CAACzB,IAAI,MAAK,sBACxC;oCACA,MAAM0H,kBAAkBN,WAAWK,MAAM;oCACzC,MAAME,oBACJvF,YAAYsE,UAAU,CAACc,WAAW,CAACE;oCACrC1C,aAAa2C,mBAAmBL;gCAClC;4BACF;wBACF;oBACF;gBACF;gBAEA,8CAA8C;gBAC9C,KAAK,MAAMM,SAASrI,WAAWsI,gBAAgB,CAAE;oBAC/CrB,iBAAiBoB;gBACnB;YACF;YAEApB,iBAAiBjC;YAEjB,8EAA8E;YAC9E,iBAAiB;YACjB,sBAAsB;YACtB,IAAI,oBAAoBzD,IAAI,CAACL,YAAY;gBACvC+C,mBAAmBlD,IAAI,CAACG,UAAUI,OAAO,CAAC,qBAAqB;YACjE;YAEA,MAAMH,YAAYF,qBAAqBC;YACvC,IAAI,CAAC6C,kBAAkBvD,GAAG,CAACW,YAAY;gBACrC4C,kBAAkBd,GAAG,CAAC9B,WAAW,EAAE;YACrC;YACA4C,kBAAkBa,GAAG,CAACzD,WAAYJ,IAAI,CAACU;QACzC;QAEA,+BAA+B;QAC/B,KAAK,MAAM8G,YAAYtE,mBAAoB;YACzC,MAAMuE,iBAA0C;gBAC9CtD,eAAe;oBACbV;oBACAW,aAAad;gBACf;gBACAvC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;YAClB;YAEA,MAAMyG,WAAW;mBAAIxH,qBAAqBsH,UAAUG,KAAK,CAAC;gBAAM;aAAO;YACvE,IAAIC,QAAQ;YACZ,KAAK,MAAMC,WAAWH,SAAU;gBAC9B,KAAK,MAAMhH,YAAYsC,kBAAkBa,GAAG,CAAC+D,UAAU,EAAE,CAAE;oBACzDnH,cAAcgH,gBAAgB/G;gBAChC;gBACAkH,SAAS,AAACA,CAAAA,QAAQ,MAAM,EAAC,IAAKC;YAChC;YAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACP;YAE5B,MAAMQ,WAAWT,SAASjH,OAAO,CAAC,QAAQ;YAC1C,MAAM2H,iBAAiBC,IAAAA,oCAAiB,EAACF,SAAS5H,KAAK,CAAC,MAAM+H,MAAM;YACpEvF,MAAM,CACJ,eAAeqF,iBAAiB,MAAMG,oCAAyB,GAAG,MACnE,GAAG,IAAIC,gBAAO,CAACC,SAAS,CACvB,CAAC,oFAAoF,EAAER,KAAKC,SAAS,CACnGC,SAAS5H,KAAK,CAAC,MAAM+H,MAAM,GAC3B,EAAE,EAAEN,KAAK,CAAC;QAEhB;QAEAnJ,YAAYI,oBAAoB,GAAG,CAAC;IACtC;AACF"}