{"version": 3, "sources": ["../../../../src/server/typescript/rules/server.ts"], "names": ["DISALLOWED_SERVER_REACT_APIS", "DISALLOWED_SERVER_REACT_DOM_APIS", "NEXT_TS_ERRORS", "getTs", "serverLayer", "filterCompletionsAtPosition", "entries", "filter", "e", "includes", "name", "source", "hasDisallowedReactAPIDefinition", "definitions", "some", "d", "containerName", "getSemanticDiagnosticsForImportDeclaration", "node", "ts", "diagnostics", "importPath", "moduleSpecifier", "getText", "importClause", "<PERSON><PERSON><PERSON><PERSON>", "test", "isNamedImports", "elements", "element", "push", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_SERVER_API", "messageText", "start", "getStart", "length", "getWidth"], "mappings": "AAAA,SACEA,4BAA4B,EAC5BC,gCAAgC,EAChCC,cAAc,QACT,cAAa;AACpB,SAASC,KAAK,QAAQ,WAAU;AAGhC,MAAMC,cAAc;IAClB,6EAA6E;IAC7EC,6BAA4BC,OAAmC;QAC7D,OAAOA,QAAQC,MAAM,CAAC,CAACC;YACrB,gCAAgC;YAChC,IACER,6BAA6BS,QAAQ,CAACD,EAAEE,IAAI,KAC5CF,EAAEG,MAAM,KAAK,SACb;gBACA,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7CC,iCACEC,WAA+C;QAE/C,OAAOA,+BAAAA,YAAaC,IAAI,CACtB,CAACC,IACCf,6BAA6BS,QAAQ,CAACM,EAAEL,IAAI,KAC5CK,EAAEC,aAAa,KAAK;IAE1B;IAEA,2DAA2D;IAC3DC,4CACEN,MAA2B,EAC3BO,IAAgC;QAEhC,MAAMC,KAAKhB;QAEX,MAAMiB,cAAqC,EAAE;QAE7C,MAAMC,aAAaH,KAAKI,eAAe,CAACC,OAAO,CAACZ;QAChD,MAAMa,eAAeN,KAAKM,YAAY;QACtC,MAAMC,gBAAgBD,gCAAAA,aAAcC,aAAa;QAEjD,IAAID,cAAc;YAChB,IAAI,kBAAkBE,IAAI,CAACL,aAAa;gBACtC,iCAAiC;gBACjC,IAAII,iBAAiBN,GAAGQ,cAAc,CAACF,gBAAgB;oBACrD,MAAMG,WAAWH,cAAcG,QAAQ;oBACvC,KAAK,MAAMC,WAAWD,SAAU;wBAC9B,MAAMlB,OAAOmB,QAAQnB,IAAI,CAACa,OAAO,CAACZ;wBAClC,IAAIX,6BAA6BS,QAAQ,CAACC,OAAO;4BAC/CU,YAAYU,IAAI,CAAC;gCACfC,MAAMpB;gCACNqB,UAAUb,GAAGc,kBAAkB,CAACC,KAAK;gCACrCC,MAAMjC,eAAekC,kBAAkB;gCACvCC,aAAa,CAAC,CAAC,EAAE3B,KAAK,sCAAsC,CAAC;gCAC7D4B,OAAOT,QAAQnB,IAAI,CAAC6B,QAAQ;gCAC5BC,QAAQX,QAAQnB,IAAI,CAAC+B,QAAQ;4BAC/B;wBACF;oBACF;gBACF;YACF,OAAO,IAAI,sBAAsBf,IAAI,CAACL,aAAa;gBACjD,qCAAqC;gBACrC,IAAII,iBAAiBN,GAAGQ,cAAc,CAACF,gBAAgB;oBACrD,MAAMG,WAAWH,cAAcG,QAAQ;oBACvC,KAAK,MAAMC,WAAWD,SAAU;wBAC9B,MAAMlB,OAAOmB,QAAQnB,IAAI,CAACa,OAAO,CAACZ;wBAClC,IAAIV,iCAAiCQ,QAAQ,CAACC,OAAO;4BACnDU,YAAYU,IAAI,CAAC;gCACfC,MAAMpB;gCACNqB,UAAUb,GAAGc,kBAAkB,CAACC,KAAK;gCACrCC,MAAMjC,eAAekC,kBAAkB;gCACvCC,aAAa,CAAC,CAAC,EAAE3B,KAAK,sCAAsC,CAAC;gCAC7D4B,OAAOT,QAAQnB,IAAI,CAAC6B,QAAQ;gCAC5BC,QAAQX,QAAQnB,IAAI,CAAC+B,QAAQ;4BAC/B;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOrB;IACT;AACF;AAEA,eAAehB,YAAW"}