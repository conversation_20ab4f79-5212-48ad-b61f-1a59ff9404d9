{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/getErrorByType.ts"], "names": ["getErrorByType", "ev", "isAppDir", "id", "event", "type", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "readyRuntimeError", "runtime", "error", "reason", "frames", "getOriginalStackFrames", "getErrorSource", "toString", "componentStackFrames", "_", "Error"], "mappings": ";;;;+BAkBsBA;;;eAAAA;;;wBAff;4BAEgC;6BAGR;AAUxB,eAAeA,eACpBC,EAAuB,EACvBC,QAAiB;IAEjB,MAAM,EAAEC,EAAE,EAAEC,KAAK,EAAE,GAAGH;IACtB,OAAQG,MAAMC,IAAI;QAChB,KAAKC,8BAAsB;QAC3B,KAAKC,kCAA0B;YAAE;gBAC/B,MAAMC,oBAAuC;oBAC3CL;oBACAM,SAAS;oBACTC,OAAON,MAAMO,MAAM;oBACnBC,QAAQ,MAAMC,IAAAA,kCAAsB,EAClCT,MAAMQ,MAAM,EACZE,IAAAA,2BAAc,EAACV,MAAMO,MAAM,GAC3BT,UACAE,MAAMO,MAAM,CAACI,QAAQ;gBAEzB;gBACA,IAAIX,MAAMC,IAAI,KAAKC,8BAAsB,EAAE;oBACzCE,kBAAkBQ,oBAAoB,GAAGZ,MAAMY,oBAAoB;gBACrE;gBACA,OAAOR;YACT;QACA;YAAS;gBACP;YACF;IACF;IACA,6DAA6D;IAC7D,MAAMS,IAAWb;IACjB,MAAM,IAAIc,MAAM;AAClB"}