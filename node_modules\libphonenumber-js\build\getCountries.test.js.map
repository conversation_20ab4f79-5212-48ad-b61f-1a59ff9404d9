{"version": 3, "file": "getCountries.test.js", "names": ["_metadataMin", "_interopRequireDefault", "require", "_getCountries", "e", "__esModule", "describe", "it", "expect", "getCountries", "metadata", "indexOf", "to", "equal"], "sources": ["../source/getCountries.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\nimport getCountries from './getCountries.js'\r\n\r\ndescribe('getCountries', () => {\r\n\tit('should get countries list', () => {\r\n\t\texpect(getCountries(metadata).indexOf('RU') > 0).to.equal(true);\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,aAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA4C,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE5CE,QAAQ,CAAC,cAAc,EAAE,YAAM;EAC9BC,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrCC,MAAM,CAAC,IAAAC,wBAAY,EAACC,uBAAQ,CAAC,CAACC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAChE,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}