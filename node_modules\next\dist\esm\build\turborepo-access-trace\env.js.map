{"version": 3, "sources": ["../../../src/build/turborepo-access-trace/env.ts"], "names": ["envProxy", "envVars", "newEnv", "Proxy", "process", "env", "get", "target", "key", "receiver", "add", "Reflect", "set", "value", "oldEnv"], "mappings": "AAEA;;;;;;CAMC,GACD,OAAO,SAASA,SAASC,OAAgB;IACvC,MAAMC,SAAS,IAAIC,MAAMC,QAAQC,GAAG,EAAE;QACpCC,KAAK,CAACC,QAAQC,KAAKC;YACjBR,QAAQS,GAAG,CAACF;YACZ,OAAOG,QAAQL,GAAG,CAACC,QAAQC,KAAKC;QAClC;QACAG,KAAK,CAACL,QAAQC,KAAKK;YACjB,OAAOF,QAAQC,GAAG,CAACL,QAAQC,KAAKK;QAClC;IACF;IAEA,MAAMC,SAASV,QAAQC,GAAG;IAC1BD,QAAQC,GAAG,GAAGH;IAEd,4DAA4D;IAC5D,OAAO;QACLE,QAAQC,GAAG,GAAGS;IAChB;AACF"}