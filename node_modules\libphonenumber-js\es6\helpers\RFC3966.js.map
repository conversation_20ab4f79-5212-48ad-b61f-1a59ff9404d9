{"version": 3, "file": "RFC3966.js", "names": ["isViablePhoneNumber", "parseRFC3966", "text", "number", "ext", "replace", "_iterator", "_createForOfIteratorHelperLoose", "split", "_step", "done", "part", "value", "_part$split", "_part$split2", "_slicedToArray", "name", "result", "formatRFC3966", "_ref", "Error", "concat"], "sources": ["../../source/helpers/RFC3966.js"], "sourcesContent": ["import isViablePhoneNumber from './isViablePhoneNumber.js'\r\n\r\n// https://www.ietf.org/rfc/rfc3966.txt\r\n\r\n/**\r\n * @param  {string} text - Phone URI (RFC 3966).\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\r\nexport function parseRFC3966(text) {\r\n\tlet number\r\n\tlet ext\r\n\r\n\t// Replace \"tel:\" with \"tel=\" for parsing convenience.\r\n\ttext = text.replace(/^tel:/, 'tel=')\r\n\r\n\tfor (const part of text.split(';')) {\r\n\t\tconst [name, value] = part.split('=')\r\n\t\tswitch (name) {\r\n\t\t\tcase 'tel':\r\n\t\t\t\tnumber = value\r\n\t\t\t\tbreak\r\n\t\t\tcase 'ext':\r\n\t\t\t\text = value\r\n\t\t\t\tbreak\r\n\t\t\tcase 'phone-context':\r\n\t\t\t\t// Only \"country contexts\" are supported.\r\n\t\t\t\t// \"Domain contexts\" are ignored.\r\n\t\t\t\tif (value[0] === '+') {\r\n\t\t\t\t\tnumber = value + number\r\n\t\t\t\t}\r\n\t\t\t\tbreak\r\n\t\t}\r\n\t}\r\n\r\n\t// If the phone number is not viable, then abort.\r\n\tif (!isViablePhoneNumber(number)) {\r\n\t\treturn {}\r\n\t}\r\n\r\n\tconst result = { number }\r\n\tif (ext) {\r\n\t\tresult.ext = ext\r\n\t}\r\n\treturn result\r\n}\r\n\r\n/**\r\n * @param  {object} - `{ ?number, ?extension }`.\r\n * @return {string} Phone URI (RFC 3966).\r\n */\r\nexport function formatRFC3966({ number, ext }) {\r\n\tif (!number) {\r\n\t\treturn ''\r\n\t}\r\n\tif (number[0] !== '+') {\r\n\t\tthrow new Error(`\"formatRFC3966()\" expects \"number\" to be in E.164 format.`)\r\n\t}\r\n\treturn `tel:${number}${ext ? ';ext=' + ext : ''}`\r\n}"], "mappings": ";;;;;;;AAAA,OAAOA,mBAAmB,MAAM,0BAA0B;;AAE1D;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAE;EAClC,IAAIC,MAAM;EACV,IAAIC,GAAG;;EAEP;EACAF,IAAI,GAAGA,IAAI,CAACG,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;EAEpC,SAAAC,SAAA,GAAAC,+BAAA,CAAmBL,IAAI,CAACM,KAAK,CAAC,GAAG,CAAC,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAI,IAAA,GAAE;IAAA,IAAzBC,IAAI,GAAAF,KAAA,CAAAG,KAAA;IACd,IAAAC,WAAA,GAAsBF,IAAI,CAACH,KAAK,CAAC,GAAG,CAAC;MAAAM,YAAA,GAAAC,cAAA,CAAAF,WAAA;MAA9BG,IAAI,GAAAF,YAAA;MAAEF,KAAK,GAAAE,YAAA;IAClB,QAAQE,IAAI;MACX,KAAK,KAAK;QACTb,MAAM,GAAGS,KAAK;QACd;MACD,KAAK,KAAK;QACTR,GAAG,GAAGQ,KAAK;QACX;MACD,KAAK,eAAe;QACnB;QACA;QACA,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACrBT,MAAM,GAAGS,KAAK,GAAGT,MAAM;QACxB;QACA;IACF;EACD;;EAEA;EACA,IAAI,CAACH,mBAAmB,CAACG,MAAM,CAAC,EAAE;IACjC,OAAO,CAAC,CAAC;EACV;EAEA,IAAMc,MAAM,GAAG;IAAEd,MAAM,EAANA;EAAO,CAAC;EACzB,IAAIC,GAAG,EAAE;IACRa,MAAM,CAACb,GAAG,GAAGA,GAAG;EACjB;EACA,OAAOa,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAAAC,IAAA,EAAkB;EAAA,IAAfhB,MAAM,GAAAgB,IAAA,CAANhB,MAAM;IAAEC,GAAG,GAAAe,IAAA,CAAHf,GAAG;EAC1C,IAAI,CAACD,MAAM,EAAE;IACZ,OAAO,EAAE;EACV;EACA,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACtB,MAAM,IAAIiB,KAAK,gEAA4D,CAAC;EAC7E;EACA,cAAAC,MAAA,CAAclB,MAAM,EAAAkB,MAAA,CAAGjB,GAAG,GAAG,OAAO,GAAGA,GAAG,GAAG,EAAE;AAChD", "ignoreList": []}