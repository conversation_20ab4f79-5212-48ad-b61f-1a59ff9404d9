{"version": 3, "file": "normalizeArguments.js", "names": ["_isObject", "_interopRequireDefault", "require", "e", "__esModule", "ownKeys", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "_typeof", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "a", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "Array", "from", "test", "n", "l", "iterator", "u", "f", "next", "done", "isArray", "normalizeArguments", "args", "_Array$prototype$slic", "prototype", "_Array$prototype$slic2", "arg_1", "arg_2", "arg_3", "arg_4", "text", "options", "metadata", "undefined", "defaultCountry", "isObject", "Error", "concat"], "sources": ["../source/normalizeArguments.js"], "sourcesContent": ["import isObject from './helpers/isObject.js'\r\n\r\n// Extracts the following properties from function arguments:\r\n// * input `text`\r\n// * `options` object\r\n// * `metadata` JSON\r\nexport default function normalizeArguments(args) {\r\n\tconst [arg_1, arg_2, arg_3, arg_4] = Array.prototype.slice.call(args)\r\n\r\n\tlet text\r\n\tlet options\r\n\tlet metadata\r\n\r\n\t// If the phone number is passed as a string.\r\n\t// `parsePhoneNumber('88005553535', ...)`.\r\n\tif (typeof arg_1 === 'string') {\r\n\t\ttext = arg_1\r\n\t}\r\n\telse throw new TypeError('A text for parsing must be a string.')\r\n\r\n\t// If \"default country\" argument is being passed then move it to `options`.\r\n\t// `parsePhoneNumber('88005553535', 'RU', [options], metadata)`.\r\n\tif (!arg_2 || typeof arg_2 === 'string')\r\n\t{\r\n\t\tif (arg_4) {\r\n\t\t\toptions = arg_3\r\n\t\t\tmetadata = arg_4\r\n\t\t} else {\r\n\t\t\toptions = undefined\r\n\t\t\tmetadata = arg_3\r\n\t\t}\r\n\r\n\t\tif (arg_2) {\r\n\t\t\toptions = { defaultCountry: arg_2, ...options }\r\n\t\t}\r\n\t}\r\n\t// `defaultCountry` is not passed.\r\n\t// Example: `parsePhoneNumber('+78005553535', [options], metadata)`.\r\n\telse if (isObject(arg_2))\r\n\t{\r\n\t\tif (arg_3) {\r\n\t\t\toptions  = arg_2\r\n\t\t\tmetadata = arg_3\r\n\t\t} else {\r\n\t\t\tmetadata = arg_2\r\n\t\t}\r\n\t}\r\n\telse throw new Error(`Invalid second argument: ${arg_2}`)\r\n\r\n\treturn {\r\n\t\ttext,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA4C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,QAAAF,CAAA,EAAAG,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAN,CAAA,OAAAK,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAP,CAAA,GAAAG,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAV,CAAA,EAAAG,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAd,CAAA,aAAAG,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAD,OAAA,CAAAG,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,IAAAe,eAAA,CAAAlB,CAAA,EAAAG,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAc,yBAAA,GAAAd,MAAA,CAAAe,gBAAA,CAAApB,CAAA,EAAAK,MAAA,CAAAc,yBAAA,CAAAf,CAAA,KAAAF,OAAA,CAAAG,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAgB,cAAA,CAAArB,CAAA,EAAAG,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAH,CAAA;AAAA,SAAAkB,gBAAAlB,CAAA,EAAAG,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAmB,cAAA,CAAAnB,CAAA,MAAAH,CAAA,GAAAK,MAAA,CAAAgB,cAAA,CAAArB,CAAA,EAAAG,CAAA,IAAAoB,KAAA,EAAAnB,CAAA,EAAAO,UAAA,MAAAa,YAAA,MAAAC,QAAA,UAAAzB,CAAA,CAAAG,CAAA,IAAAC,CAAA,EAAAJ,CAAA;AAAA,SAAAsB,eAAAlB,CAAA,QAAAsB,CAAA,GAAAC,YAAA,CAAAvB,CAAA,gCAAAwB,OAAA,CAAAF,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAvB,CAAA,EAAAD,CAAA,oBAAAyB,OAAA,CAAAxB,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAJ,CAAA,GAAAI,CAAA,CAAAyB,MAAA,CAAAC,WAAA,kBAAA9B,CAAA,QAAA0B,CAAA,GAAA1B,CAAA,CAAA+B,IAAA,CAAA3B,CAAA,EAAAD,CAAA,gCAAAyB,OAAA,CAAAF,CAAA,UAAAA,CAAA,YAAAM,SAAA,yEAAA7B,CAAA,GAAA8B,MAAA,GAAAC,MAAA,EAAA9B,CAAA;AAAA,SAAA+B,eAAAhC,CAAA,EAAAH,CAAA,WAAAoC,eAAA,CAAAjC,CAAA,KAAAkC,qBAAA,CAAAlC,CAAA,EAAAH,CAAA,KAAAsC,2BAAA,CAAAnC,CAAA,EAAAH,CAAA,KAAAuC,gBAAA;AAAA,SAAAA,iBAAA,cAAAP,SAAA;AAAA,SAAAM,4BAAAnC,CAAA,EAAAqC,CAAA,QAAArC,CAAA,2BAAAA,CAAA,SAAAsC,iBAAA,CAAAtC,CAAA,EAAAqC,CAAA,OAAApC,CAAA,MAAAsC,QAAA,CAAAX,IAAA,CAAA5B,CAAA,EAAAwC,KAAA,6BAAAvC,CAAA,IAAAD,CAAA,CAAAyC,WAAA,KAAAxC,CAAA,GAAAD,CAAA,CAAAyC,WAAA,CAAAC,IAAA,aAAAzC,CAAA,cAAAA,CAAA,GAAA0C,KAAA,CAAAC,IAAA,CAAA5C,CAAA,oBAAAC,CAAA,+CAAA4C,IAAA,CAAA5C,CAAA,IAAAqC,iBAAA,CAAAtC,CAAA,EAAAqC,CAAA;AAAA,SAAAC,kBAAAtC,CAAA,EAAAqC,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAArC,CAAA,CAAAa,MAAA,MAAAwB,CAAA,GAAArC,CAAA,CAAAa,MAAA,YAAAhB,CAAA,MAAAiD,CAAA,GAAAH,KAAA,CAAAN,CAAA,GAAAxC,CAAA,GAAAwC,CAAA,EAAAxC,CAAA,IAAAiD,CAAA,CAAAjD,CAAA,IAAAG,CAAA,CAAAH,CAAA,UAAAiD,CAAA;AAAA,SAAAZ,sBAAAlC,CAAA,EAAA+C,CAAA,QAAA9C,CAAA,WAAAD,CAAA,gCAAA0B,MAAA,IAAA1B,CAAA,CAAA0B,MAAA,CAAAsB,QAAA,KAAAhD,CAAA,4BAAAC,CAAA,QAAAJ,CAAA,EAAAiD,CAAA,EAAAvB,CAAA,EAAA0B,CAAA,EAAAZ,CAAA,OAAAa,CAAA,OAAA7C,CAAA,iBAAAkB,CAAA,IAAAtB,CAAA,GAAAA,CAAA,CAAA2B,IAAA,CAAA5B,CAAA,GAAAmD,IAAA,QAAAJ,CAAA,QAAA7C,MAAA,CAAAD,CAAA,MAAAA,CAAA,UAAAiD,CAAA,uBAAAA,CAAA,IAAArD,CAAA,GAAA0B,CAAA,CAAAK,IAAA,CAAA3B,CAAA,GAAAmD,IAAA,MAAAf,CAAA,CAAA5B,IAAA,CAAAZ,CAAA,CAAAuB,KAAA,GAAAiB,CAAA,CAAAxB,MAAA,KAAAkC,CAAA,GAAAG,CAAA,iBAAAlD,CAAA,IAAAK,CAAA,OAAAyC,CAAA,GAAA9C,CAAA,yBAAAkD,CAAA,YAAAjD,CAAA,eAAAgD,CAAA,GAAAhD,CAAA,cAAAC,MAAA,CAAA+C,CAAA,MAAAA,CAAA,2BAAA5C,CAAA,QAAAyC,CAAA,aAAAT,CAAA;AAAA,SAAAJ,gBAAAjC,CAAA,QAAA2C,KAAA,CAAAU,OAAA,CAAArD,CAAA,UAAAA,CAAA;AAE5C;AACA;AACA;AACA;AACe,SAASsD,kBAAkBA,CAACC,IAAI,EAAE;EAChD,IAAAC,qBAAA,GAAqCb,KAAK,CAACc,SAAS,CAACjB,KAAK,CAACZ,IAAI,CAAC2B,IAAI,CAAC;IAAAG,sBAAA,GAAA1B,cAAA,CAAAwB,qBAAA;IAA9DG,KAAK,GAAAD,sBAAA;IAAEE,KAAK,GAAAF,sBAAA;IAAEG,KAAK,GAAAH,sBAAA;IAAEI,KAAK,GAAAJ,sBAAA;EAEjC,IAAIK,IAAI;EACR,IAAIC,OAAO;EACX,IAAIC,QAAQ;;EAEZ;EACA;EACA,IAAI,OAAON,KAAK,KAAK,QAAQ,EAAE;IAC9BI,IAAI,GAAGJ,KAAK;EACb,CAAC,MACI,MAAM,IAAI9B,SAAS,CAAC,sCAAsC,CAAC;;EAEhE;EACA;EACA,IAAI,CAAC+B,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACvC;IACC,IAAIE,KAAK,EAAE;MACVE,OAAO,GAAGH,KAAK;MACfI,QAAQ,GAAGH,KAAK;IACjB,CAAC,MAAM;MACNE,OAAO,GAAGE,SAAS;MACnBD,QAAQ,GAAGJ,KAAK;IACjB;IAEA,IAAID,KAAK,EAAE;MACVI,OAAO,GAAArD,aAAA;QAAKwD,cAAc,EAAEP;MAAK,GAAKI,OAAO,CAAE;IAChD;EACD;EACA;EACA;EAAA,KACK,IAAI,IAAAI,oBAAQ,EAACR,KAAK,CAAC,EACxB;IACC,IAAIC,KAAK,EAAE;MACVG,OAAO,GAAIJ,KAAK;MAChBK,QAAQ,GAAGJ,KAAK;IACjB,CAAC,MAAM;MACNI,QAAQ,GAAGL,KAAK;IACjB;EACD,CAAC,MACI,MAAM,IAAIS,KAAK,6BAAAC,MAAA,CAA6BV,KAAK,CAAE,CAAC;EAEzD,OAAO;IACNG,IAAI,EAAJA,IAAI;IACJC,OAAO,EAAPA,OAAO;IACPC,QAAQ,EAARA;EACD,CAAC;AACF", "ignoreList": []}