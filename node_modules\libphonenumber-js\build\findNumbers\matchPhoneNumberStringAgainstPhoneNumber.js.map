{"version": 3, "file": "matchPhoneNumberStringAgainstPhoneNumber.js", "names": ["_parsePhoneNumber", "_interopRequireDefault", "require", "e", "__esModule", "matchPhoneNumberStringAgainstPhoneNumber", "phoneNumberString", "phoneNumber", "metadata", "phoneNumberStringContainsCallingCode", "parsedPhoneNumber", "parsePhoneNumber", "defaultCallingCode", "countryCallingCode", "ext", "number", "nationalNumber", "indexOf"], "sources": ["../../source/findNumbers/matchPhoneNumberStringAgainstPhoneNumber.js"], "sourcesContent": ["import parsePhoneNumber from '../parsePhoneNumber.js'\r\n\r\n/**\r\n * Matches a phone number object against a phone number string.\r\n * @param  {string} phoneNumberString\r\n * @param  {PhoneNumber} phoneNumber\r\n * @param  {object} metadata — Metadata JSON\r\n * @return {'INVALID_NUMBER'|'NO_MATCH'|'SHORT_NSN_MATCH'|'NSN_MATCH'|'EXACT_MATCH'}\r\n */\r\nexport default function matchPhoneNumberStringAgainstPhoneNumber(phoneNumberString, phoneNumber, metadata) {\r\n\t// Parse `phoneNumberString`.\r\n\tlet phoneNumberStringContainsCallingCode = true\r\n\tlet parsedPhoneNumber = parsePhoneNumber(phoneNumberString, metadata)\r\n\tif (!parsedPhoneNumber) {\r\n\t\t// If `phoneNumberString` didn't contain a country calling code\r\n\t\t// then substitute it with the `phoneNumber`'s country calling code.\r\n\t\tphoneNumberStringContainsCallingCode = false\r\n\t\tparsedPhoneNumber = parsePhoneNumber(phoneNumberString, { defaultCallingCode: phoneNumber.countryCallingCode }, metadata)\r\n\t}\r\n\tif (!parsedPhoneNumber) {\r\n\t\treturn 'INVALID_NUMBER'\r\n\t}\r\n\r\n\t// Check that the extensions match.\r\n\tif (phoneNumber.ext) {\r\n\t\tif (parsedPhoneNumber.ext !== phoneNumber.ext) {\r\n\t\t\treturn 'NO_MATCH'\r\n\t\t}\r\n\t} else {\r\n\t\tif (parsedPhoneNumber.ext) {\r\n\t\t\treturn 'NO_MATCH'\r\n\t\t}\r\n\t}\r\n\r\n\t// Check that country calling codes match.\r\n\tif (phoneNumberStringContainsCallingCode) {\r\n\t\tif (phoneNumber.countryCallingCode !== parsedPhoneNumber.countryCallingCode) {\r\n\t\t\treturn 'NO_MATCH'\r\n\t\t}\r\n\t}\r\n\r\n\t// Check if the whole numbers match.\r\n\tif (phoneNumber.number === parsedPhoneNumber.number) {\r\n\t\tif (phoneNumberStringContainsCallingCode) {\r\n\t\t\treturn 'EXACT_MATCH'\r\n\t\t} else {\r\n\t\t\treturn 'NSN_MATCH'\r\n\t\t}\r\n\t}\r\n\r\n\t// Check if one national number is a \"suffix\" of the other.\r\n\tif (\r\n\t\tphoneNumber.nationalNumber.indexOf(parsedPhoneNumber.nationalNumber) === 0 ||\r\n\t\tparsedPhoneNumber.nationalNumber.indexOf(phoneNumber.nationalNumber) === 0\r\n\t) {\r\n\t\t// \"A SHORT_NSN_MATCH occurs if there is a difference because of the\r\n\t\t//  presence or absence of an 'Italian leading zero', the presence or\r\n\t\t//  absence of an extension, or one NSN being a shorter variant of the\r\n\t\t//  other.\"\r\n\t\treturn 'SHORT_NSN_MATCH'\r\n\t}\r\n\r\n\treturn 'NO_MATCH'\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,iBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAqD,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASE,wCAAwCA,CAACC,iBAAiB,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EAC1G;EACA,IAAIC,oCAAoC,GAAG,IAAI;EAC/C,IAAIC,iBAAiB,GAAG,IAAAC,4BAAgB,EAACL,iBAAiB,EAAEE,QAAQ,CAAC;EACrE,IAAI,CAACE,iBAAiB,EAAE;IACvB;IACA;IACAD,oCAAoC,GAAG,KAAK;IAC5CC,iBAAiB,GAAG,IAAAC,4BAAgB,EAACL,iBAAiB,EAAE;MAAEM,kBAAkB,EAAEL,WAAW,CAACM;IAAmB,CAAC,EAAEL,QAAQ,CAAC;EAC1H;EACA,IAAI,CAACE,iBAAiB,EAAE;IACvB,OAAO,gBAAgB;EACxB;;EAEA;EACA,IAAIH,WAAW,CAACO,GAAG,EAAE;IACpB,IAAIJ,iBAAiB,CAACI,GAAG,KAAKP,WAAW,CAACO,GAAG,EAAE;MAC9C,OAAO,UAAU;IAClB;EACD,CAAC,MAAM;IACN,IAAIJ,iBAAiB,CAACI,GAAG,EAAE;MAC1B,OAAO,UAAU;IAClB;EACD;;EAEA;EACA,IAAIL,oCAAoC,EAAE;IACzC,IAAIF,WAAW,CAACM,kBAAkB,KAAKH,iBAAiB,CAACG,kBAAkB,EAAE;MAC5E,OAAO,UAAU;IAClB;EACD;;EAEA;EACA,IAAIN,WAAW,CAACQ,MAAM,KAAKL,iBAAiB,CAACK,MAAM,EAAE;IACpD,IAAIN,oCAAoC,EAAE;MACzC,OAAO,aAAa;IACrB,CAAC,MAAM;MACN,OAAO,WAAW;IACnB;EACD;;EAEA;EACA,IACCF,WAAW,CAACS,cAAc,CAACC,OAAO,CAACP,iBAAiB,CAACM,cAAc,CAAC,KAAK,CAAC,IAC1EN,iBAAiB,CAACM,cAAc,CAACC,OAAO,CAACV,WAAW,CAACS,cAAc,CAAC,KAAK,CAAC,EACzE;IACD;IACA;IACA;IACA;IACA,OAAO,iBAAiB;EACzB;EAEA,OAAO,UAAU;AAClB", "ignoreList": []}