{"version": 3, "file": "RFC3966.js", "names": ["_isViablePhoneNumber", "_interopRequireDefault", "require", "e", "__esModule", "_slicedToArray", "r", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "TypeError", "l", "t", "Symbol", "iterator", "n", "i", "u", "a", "f", "o", "call", "next", "Object", "done", "push", "value", "length", "Array", "isArray", "_createForOfIteratorHelperLoose", "bind", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "parseRFC3966", "text", "number", "ext", "replace", "_iterator", "split", "_step", "part", "_part$split", "_part$split2", "isViablePhoneNumber", "result", "formatRFC3966", "_ref", "Error", "concat"], "sources": ["../../source/helpers/RFC3966.js"], "sourcesContent": ["import isViablePhoneNumber from './isViablePhoneNumber.js'\r\n\r\n// https://www.ietf.org/rfc/rfc3966.txt\r\n\r\n/**\r\n * @param  {string} text - Phone URI (RFC 3966).\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\r\nexport function parseRFC3966(text) {\r\n\tlet number\r\n\tlet ext\r\n\r\n\t// Replace \"tel:\" with \"tel=\" for parsing convenience.\r\n\ttext = text.replace(/^tel:/, 'tel=')\r\n\r\n\tfor (const part of text.split(';')) {\r\n\t\tconst [name, value] = part.split('=')\r\n\t\tswitch (name) {\r\n\t\t\tcase 'tel':\r\n\t\t\t\tnumber = value\r\n\t\t\t\tbreak\r\n\t\t\tcase 'ext':\r\n\t\t\t\text = value\r\n\t\t\t\tbreak\r\n\t\t\tcase 'phone-context':\r\n\t\t\t\t// Only \"country contexts\" are supported.\r\n\t\t\t\t// \"Domain contexts\" are ignored.\r\n\t\t\t\tif (value[0] === '+') {\r\n\t\t\t\t\tnumber = value + number\r\n\t\t\t\t}\r\n\t\t\t\tbreak\r\n\t\t}\r\n\t}\r\n\r\n\t// If the phone number is not viable, then abort.\r\n\tif (!isViablePhoneNumber(number)) {\r\n\t\treturn {}\r\n\t}\r\n\r\n\tconst result = { number }\r\n\tif (ext) {\r\n\t\tresult.ext = ext\r\n\t}\r\n\treturn result\r\n}\r\n\r\n/**\r\n * @param  {object} - `{ ?number, ?extension }`.\r\n * @return {string} Phone URI (RFC 3966).\r\n */\r\nexport function formatRFC3966({ number, ext }) {\r\n\tif (!number) {\r\n\t\treturn ''\r\n\t}\r\n\tif (number[0] !== '+') {\r\n\t\tthrow new Error(`\"formatRFC3966()\" expects \"number\" to be in E.164 format.`)\r\n\t}\r\n\treturn `tel:${number}${ext ? ';ext=' + ext : ''}`\r\n}"], "mappings": ";;;;;;;AAAA,IAAAA,oBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0D,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,eAAAC,CAAA,EAAAH,CAAA,WAAAI,eAAA,CAAAD,CAAA,KAAAE,qBAAA,CAAAF,CAAA,EAAAH,CAAA,KAAAM,2BAAA,CAAAH,CAAA,EAAAH,CAAA,KAAAO,gBAAA;AAAA,SAAAA,iBAAA,cAAAC,SAAA;AAAA,SAAAH,sBAAAF,CAAA,EAAAM,CAAA,QAAAC,CAAA,WAAAP,CAAA,gCAAAQ,MAAA,IAAAR,CAAA,CAAAQ,MAAA,CAAAC,QAAA,KAAAT,CAAA,4BAAAO,CAAA,QAAAV,CAAA,EAAAa,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,OAAAC,CAAA,OAAAC,CAAA,iBAAAJ,CAAA,IAAAJ,CAAA,GAAAA,CAAA,CAAAS,IAAA,CAAAhB,CAAA,GAAAiB,IAAA,QAAAX,CAAA,QAAAY,MAAA,CAAAX,CAAA,MAAAA,CAAA,UAAAO,CAAA,uBAAAA,CAAA,IAAAjB,CAAA,GAAAc,CAAA,CAAAK,IAAA,CAAAT,CAAA,GAAAY,IAAA,MAAAN,CAAA,CAAAO,IAAA,CAAAvB,CAAA,CAAAwB,KAAA,GAAAR,CAAA,CAAAS,MAAA,KAAAhB,CAAA,GAAAQ,CAAA,iBAAAd,CAAA,IAAAe,CAAA,OAAAL,CAAA,GAAAV,CAAA,yBAAAc,CAAA,YAAAP,CAAA,eAAAK,CAAA,GAAAL,CAAA,cAAAW,MAAA,CAAAN,CAAA,MAAAA,CAAA,2BAAAG,CAAA,QAAAL,CAAA,aAAAG,CAAA;AAAA,SAAAZ,gBAAAD,CAAA,QAAAuB,KAAA,CAAAC,OAAA,CAAAxB,CAAA,UAAAA,CAAA;AAAA,SAAAyB,gCAAAzB,CAAA,EAAAH,CAAA,QAAAU,CAAA,yBAAAC,MAAA,IAAAR,CAAA,CAAAQ,MAAA,CAAAC,QAAA,KAAAT,CAAA,oBAAAO,CAAA,UAAAA,CAAA,GAAAA,CAAA,CAAAS,IAAA,CAAAhB,CAAA,GAAAiB,IAAA,CAAAS,IAAA,CAAAnB,CAAA,OAAAgB,KAAA,CAAAC,OAAA,CAAAxB,CAAA,MAAAO,CAAA,GAAAJ,2BAAA,CAAAH,CAAA,MAAAH,CAAA,IAAAG,CAAA,uBAAAA,CAAA,CAAAsB,MAAA,IAAAf,CAAA,KAAAP,CAAA,GAAAO,CAAA,OAAAQ,CAAA,kCAAAA,CAAA,IAAAf,CAAA,CAAAsB,MAAA,KAAAH,IAAA,WAAAA,IAAA,MAAAE,KAAA,EAAArB,CAAA,CAAAe,CAAA,sBAAAV,SAAA;AAAA,SAAAF,4BAAAH,CAAA,EAAAa,CAAA,QAAAb,CAAA,2BAAAA,CAAA,SAAA2B,iBAAA,CAAA3B,CAAA,EAAAa,CAAA,OAAAN,CAAA,MAAAqB,QAAA,CAAAZ,IAAA,CAAAhB,CAAA,EAAA6B,KAAA,6BAAAtB,CAAA,IAAAP,CAAA,CAAA8B,WAAA,KAAAvB,CAAA,GAAAP,CAAA,CAAA8B,WAAA,CAAAC,IAAA,aAAAxB,CAAA,cAAAA,CAAA,GAAAgB,KAAA,CAAAS,IAAA,CAAAhC,CAAA,oBAAAO,CAAA,+CAAA0B,IAAA,CAAA1B,CAAA,IAAAoB,iBAAA,CAAA3B,CAAA,EAAAa,CAAA;AAAA,SAAAc,kBAAA3B,CAAA,EAAAa,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAb,CAAA,CAAAsB,MAAA,MAAAT,CAAA,GAAAb,CAAA,CAAAsB,MAAA,YAAAzB,CAAA,MAAAa,CAAA,GAAAa,KAAA,CAAAV,CAAA,GAAAhB,CAAA,GAAAgB,CAAA,EAAAhB,CAAA,IAAAa,CAAA,CAAAb,CAAA,IAAAG,CAAA,CAAAH,CAAA,UAAAa,CAAA;AAE1D;;AAEA;AACA;AACA;AACA;AACO,SAASwB,YAAYA,CAACC,IAAI,EAAE;EAClC,IAAIC,MAAM;EACV,IAAIC,GAAG;;EAEP;EACAF,IAAI,GAAGA,IAAI,CAACG,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC;EAEpC,SAAAC,SAAA,GAAAd,+BAAA,CAAmBU,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC,GAAAC,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAApB,IAAA,GAAE;IAAA,IAAzBuB,IAAI,GAAAD,KAAA,CAAApB,KAAA;IACd,IAAAsB,WAAA,GAAsBD,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC;MAAAI,YAAA,GAAA7C,cAAA,CAAA4C,WAAA;MAA9BZ,IAAI,GAAAa,YAAA;MAAEvB,KAAK,GAAAuB,YAAA;IAClB,QAAQb,IAAI;MACX,KAAK,KAAK;QACTK,MAAM,GAAGf,KAAK;QACd;MACD,KAAK,KAAK;QACTgB,GAAG,GAAGhB,KAAK;QACX;MACD,KAAK,eAAe;QACnB;QACA;QACA,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACrBe,MAAM,GAAGf,KAAK,GAAGe,MAAM;QACxB;QACA;IACF;EACD;;EAEA;EACA,IAAI,CAAC,IAAAS,+BAAmB,EAACT,MAAM,CAAC,EAAE;IACjC,OAAO,CAAC,CAAC;EACV;EAEA,IAAMU,MAAM,GAAG;IAAEV,MAAM,EAANA;EAAO,CAAC;EACzB,IAAIC,GAAG,EAAE;IACRS,MAAM,CAACT,GAAG,GAAGA,GAAG;EACjB;EACA,OAAOS,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAAAC,IAAA,EAAkB;EAAA,IAAfZ,MAAM,GAAAY,IAAA,CAANZ,MAAM;IAAEC,GAAG,GAAAW,IAAA,CAAHX,GAAG;EAC1C,IAAI,CAACD,MAAM,EAAE;IACZ,OAAO,EAAE;EACV;EACA,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACtB,MAAM,IAAIa,KAAK,gEAA4D,CAAC;EAC7E;EACA,cAAAC,MAAA,CAAcd,MAAM,EAAAc,MAAA,CAAGb,GAAG,GAAG,OAAO,GAAGA,GAAG,GAAG,EAAE;AAChD", "ignoreList": []}