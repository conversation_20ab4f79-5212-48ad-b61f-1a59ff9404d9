{"version": 3, "file": "parse.js", "names": ["_parseNumber", "normalizeArguments", "parseNumber", "_normalizeArguments", "arguments", "text", "options", "metadata"], "sources": ["../../source/legacy/parse.js"], "sourcesContent": ["import _parseNumber from '../parse.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function parseNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _parseNumber(text, options, metadata)\r\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,aAAa;AACtC,OAAOC,kBAAkB,MAAM,0BAA0B;AAEzD,eAAe,SAASC,WAAWA,CAAA,EAAG;EACrC,IAAAC,mBAAA,GAAoCF,kBAAkB,CAACG,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC/B,OAAOP,YAAY,CAACK,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAC7C", "ignoreList": []}