{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-ssr-loader/index.ts"], "names": ["getModuleBuildInfo", "WEBPACK_RESOURCE_QUERIES", "RouteKind", "normalizePagePath", "loadEntrypoint", "swapDistFolderWithEsmDistFolder", "path", "replace", "getRouteModuleOptions", "page", "options", "definition", "kind", "PAGES", "pathname", "bundlePath", "filename", "edgeSSRLoader", "dev", "absolutePagePath", "absoluteAppPath", "absoluteDocumentPath", "absolute500Path", "absoluteErrorPath", "isServerComponent", "stringifiedConfig", "stringifiedConfigBase64", "appDirLoader", "appDirLoaderBase64", "pagesType", "sriEnabled", "cache<PERSON><PERSON><PERSON>", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "serverActions", "getOptions", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "isAppDir", "buildInfo", "_module", "nextEdgeSSR", "route", "pagePath", "utils", "contextify", "context", "rootContext", "appPath", "errorPath", "documentPath", "userland500Path", "stringifiedPagePath", "stringify", "pageModPath", "substring", "length", "edgeSSREntry", "VAR_USERLAND", "VAR_PAGE", "nextConfig", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "VAR_MODULE_DOCUMENT", "VAR_MODULE_APP", "VAR_MODULE_GLOBAL_ERROR", "pageRouteModuleOptions", "errorRouteModuleOptions", "user500RouteModuleOptions", "userland500Page"], "mappings": "AAKA,SAASA,kBAAkB,QAAQ,2BAA0B;AAC7D,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SAASC,SAAS,QAAQ,uCAAsC;AAChE,SAASC,iBAAiB,QAAQ,uDAAsD;AACxF,SAASC,cAAc,QAAQ,2BAA0B;AAyBzD;;;;;;;AAOA,GACA,SAASC,gCAAgCC,IAAY;IACnD,OAAOA,KAAKC,OAAO,CAAC,mBAAmB;AACzC;AAEA,SAASC,sBAAsBC,IAAY;IACzC,MAAMC,UAAoE;QACxEC,YAAY;YACVC,MAAMV,UAAUW,KAAK;YACrBJ,MAAMN,kBAAkBM;YACxBK,UAAUL;YACV,2CAA2C;YAC3CM,YAAY;YACZC,UAAU;QACZ;IACF;IAEA,OAAON;AACT;AAEA,MAAMO,gBACJ,eAAeA;IACb,MAAM,EACJC,GAAG,EACHT,IAAI,EACJU,gBAAgB,EAChBC,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,iBAAiB,EACjBC,iBAAiB,EACjBC,mBAAmBC,uBAAuB,EAC1CC,cAAcC,kBAAkB,EAChCC,SAAS,EACTC,UAAU,EACVC,YAAY,EACZC,eAAe,EACfC,kBAAkBC,sBAAsB,EACxCC,aAAa,EACd,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMH,mBAAqCI,KAAKC,KAAK,CACnDC,OAAOC,IAAI,CAACN,wBAAwB,UAAUO,QAAQ;IAGxD,MAAMhB,oBAAoBc,OAAOC,IAAI,CACnCd,2BAA2B,IAC3B,UACAe,QAAQ;IACV,MAAMd,eAAeY,OAAOC,IAAI,CAC9BZ,sBAAsB,IACtB,UACAa,QAAQ;IACV,MAAMC,WAAWb,cAAc;IAE/B,MAAMc,YAAY3C,mBAAmB,IAAI,CAAC4C,OAAO;IACjDD,UAAUE,WAAW,GAAG;QACtBrB;QACAf,MAAMA;QACNiC;IACF;IACAC,UAAUG,KAAK,GAAG;QAChBrC;QACAU;QACAa;QACAC;IACF;IAEA,MAAMc,WAAW,IAAI,CAACC,KAAK,CAACC,UAAU,CACpC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChChC;IAEF,MAAMiC,UAAU,IAAI,CAACJ,KAAK,CAACC,UAAU,CACnC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChC9C,gCAAgCe;IAElC,MAAMiC,YAAY,IAAI,CAACL,KAAK,CAACC,UAAU,CACrC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChC9C,gCAAgCkB;IAElC,MAAM+B,eAAe,IAAI,CAACN,KAAK,CAACC,UAAU,CACxC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChC9C,gCAAgCgB;IAElC,MAAMkC,kBAAkBjC,kBACpB,IAAI,CAAC0B,KAAK,CAACC,UAAU,CACnB,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,WAAW,EAChC9C,gCAAgCiB,oBAElC;IAEJ,MAAMkC,sBAAsBnB,KAAKoB,SAAS,CAACV;IAE3C,MAAMW,cAAc,CAAC,EAAE/B,aAAa,EAAE6B,oBAAoBG,SAAS,CACjE,GACAH,oBAAoBI,MAAM,GAAG,GAC7B,EAAElB,WAAW,CAAC,CAAC,EAAEzC,yBAAyB4D,YAAY,CAAC,CAAC,GAAG,GAAG,CAAC;IAEjE,IAAInB,UAAU;QACZ,OAAO,MAAMtC,eACX,gBACA;YACE0D,cAAcJ;YACdK,UAAUtD;QACZ,GACA;YACEqB,YAAYO,KAAKoB,SAAS,CAAC3B;YAC3BkC,YAAYvC;YACZD,mBAAmBa,KAAKoB,SAAS,CAACjC;YAClCN,KAAKmB,KAAKoB,SAAS,CAACvC;YACpBiB,eACE,OAAOA,kBAAkB,cACrB,cACAE,KAAKoB,SAAS,CAACtB;QACvB,GACA;YACE8B,yBAAyBlC,gBAAgB;QAC3C;IAEJ,OAAO;QACL,OAAO,MAAM3B,eACX,YACA;YACE0D,cAAcJ;YACdK,UAAUtD;YACVyD,qBAAqBZ;YACrBa,gBAAgBf;YAChBgB,yBAAyBf;QAC3B,GACA;YACExB,WAAWQ,KAAKoB,SAAS,CAAC5B;YAC1BC,YAAYO,KAAKoB,SAAS,CAAC3B;YAC3BkC,YAAYvC;YACZP,KAAKmB,KAAKoB,SAAS,CAACvC;YACpBmD,wBAAwBhC,KAAKoB,SAAS,CAACjD,sBAAsBC;YAC7D6D,yBAAyBjC,KAAKoB,SAAS,CACrCjD,sBAAsB;YAExB+D,2BAA2BlC,KAAKoB,SAAS,CACvCjD,sBAAsB;QAE1B,GACA;YACEgE,iBAAiBjB;YACjBU,yBAAyBlC,gBAAgB;QAC3C;IAEJ;AACF;AACF,eAAed,cAAa"}