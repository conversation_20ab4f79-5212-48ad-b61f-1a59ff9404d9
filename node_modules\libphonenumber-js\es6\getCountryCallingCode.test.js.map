{"version": 3, "file": "getCountryCallingCode.test.js", "names": ["metadata", "type", "getCountryCallingCode", "describe", "it", "expect", "to", "equal"], "sources": ["../source/getCountryCallingCode.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\nimport getCountryCallingCode from './getCountryCallingCode.js'\r\n\r\ndescribe('getCountryCallingCode', () => {\r\n\tit('should get country calling code', () => {\r\n\t\texpect(getCountryCallingCode('US', metadata)).to.equal('1')\r\n\t})\r\n\r\n\tit('should throw if country is unknown', () => {\r\n\t\texpect(() => getCountryCallingCode('ZZ', metadata)).to.throw('Unknown country: ZZ')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAE/D,OAAOC,qBAAqB,MAAM,4BAA4B;AAE9DC,QAAQ,CAAC,uBAAuB,EAAE,YAAM;EACvCC,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3CC,MAAM,CAACH,qBAAqB,CAAC,IAAI,EAAEF,QAAQ,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EAC5D,CAAC,CAAC;EAEFH,EAAE,CAAC,oCAAoC,EAAE,YAAM;IAC9CC,MAAM,CAAC;MAAA,OAAMH,qBAAqB,CAAC,IAAI,EAAEF,QAAQ,CAAC;IAAA,EAAC,CAACM,EAAE,SAAM,CAAC,qBAAqB,CAAC;EACpF,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}