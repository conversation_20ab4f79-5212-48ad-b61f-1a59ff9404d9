{"version": 3, "file": "IsISRC.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISRC.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,eAAe,MAAM,sBAAsB,CAAC;AAEnD,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,iBAAqC;IAC1D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,MAAM,CAAC,KAAK,CAAC,EAAb,CAAa;YACjD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,2BAA2B,EAAxC,CAAwC,EAAE,iBAAiB,CAAC;SACxG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isISRCValidator from 'validator/lib/isISRC';\n\nexport const IS_ISRC = 'isISRC';\n\n/**\n * Check if the string is a ISRC.\n * If given value is not a string, then it returns false.\n */\nexport function isISRC(value: unknown): boolean {\n  return typeof value === 'string' && isISRCValidator(value);\n}\n\n/**\n * Check if the string is a ISRC.\n * If given value is not a string, then it returns false.\n */\nexport function IsISRC(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISRC,\n      validator: {\n        validate: (value, args): boolean => isISRC(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an ISRC', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}