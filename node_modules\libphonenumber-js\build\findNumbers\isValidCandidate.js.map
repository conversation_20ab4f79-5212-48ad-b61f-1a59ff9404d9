{"version": 3, "file": "isValidCandidate.js", "names": ["_constants", "require", "_util", "_utf", "OPENING_PARENS", "CLOSING_PARENS", "NON_PARENS", "concat", "LEAD_CLASS", "exports", "PLUS_CHARS", "LEAD_CLASS_LEADING", "RegExp", "BRACKET_PAIR_LIMIT", "limit", "MATCHING_BRACKETS_ENTIRE", "PUB_PAGES", "isValidCandidate", "candidate", "offset", "text", "leniency", "test", "previousChar", "isInvalidPunctuationSymbol", "isLatinLetter", "lastCharIndex", "length", "nextChar"], "sources": ["../../source/findNumbers/isValidCandidate.js"], "sourcesContent": ["// Copy-pasted from `PhoneNumberMatcher.js`.\r\n\r\nimport { PLUS_CHARS } from '../constants.js'\r\nimport { limit } from './util.js'\r\n\r\nimport {\r\n\tisLatinLetter,\r\n\tisInvalidPunctuationSymbol\r\n} from './utf-8.js'\r\n\r\nconst OPENING_PARENS = '(\\\\[\\uFF08\\uFF3B'\r\nconst CLOSING_PARENS = ')\\\\]\\uFF09\\uFF3D'\r\nconst NON_PARENS = `[^${OPENING_PARENS}${CLOSING_PARENS}]`\r\n\r\nexport const LEAD_CLASS = `[${OPENING_PARENS}${PLUS_CHARS}]`\r\n\r\n// Punctuation that may be at the start of a phone number - brackets and plus signs.\r\nconst LEAD_CLASS_LEADING = new RegExp('^' + LEAD_CLASS)\r\n\r\n// Limit on the number of pairs of brackets in a phone number.\r\nconst BRACKET_PAIR_LIMIT = limit(0, 3)\r\n\r\n/**\r\n * Pattern to check that brackets match. Opening brackets should be closed within a phone number.\r\n * This also checks that there is something inside the brackets. Having no brackets at all is also\r\n * fine.\r\n *\r\n * An opening bracket at the beginning may not be closed, but subsequent ones should be.  It's\r\n * also possible that the leading bracket was dropped, so we shouldn't be surprised if we see a\r\n * closing bracket first. We limit the sets of brackets in a phone number to four.\r\n */\r\nconst MATCHING_BRACKETS_ENTIRE = new RegExp\r\n(\r\n\t'^'\r\n\t+ \"(?:[\" + OPENING_PARENS + \"])?\" + \"(?:\" + NON_PARENS + \"+\" + \"[\" + CLOSING_PARENS + \"])?\"\r\n\t+ NON_PARENS + \"+\"\r\n\t+ \"(?:[\" + OPENING_PARENS + \"]\" + NON_PARENS + \"+[\" + CLOSING_PARENS + \"])\" + BRACKET_PAIR_LIMIT\r\n\t+ NON_PARENS + \"*\"\r\n\t+ '$'\r\n)\r\n\r\n/**\r\n * Matches strings that look like publication pages. Example:\r\n * <pre>Computing Complete Answers to Queries in the Presence of Limited Access Patterns.\r\n * Chen Li. VLDB J. 12(3): 211-227 (2003).</pre>\r\n *\r\n * The string \"211-227 (2003)\" is not a telephone number.\r\n */\r\nconst PUB_PAGES = /\\d{1,5}-+\\d{1,5}\\s{0,4}\\(\\d{1,4}/\r\n\r\nexport default function isValidCandidate(candidate, offset, text, leniency)\r\n{\r\n\t// Check the candidate doesn't contain any formatting\r\n\t// which would indicate that it really isn't a phone number.\r\n\tif (!MATCHING_BRACKETS_ENTIRE.test(candidate) || PUB_PAGES.test(candidate)) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// If leniency is set to VALID or stricter, we also want to skip numbers that are surrounded\r\n\t// by Latin alphabetic characters, to skip cases like abc8005001234 or 8005001234def.\r\n\tif (leniency !== 'POSSIBLE')\r\n\t{\r\n\t\t// If the candidate is not at the start of the text,\r\n\t\t// and does not start with phone-number punctuation,\r\n\t\t// check the previous character.\r\n\t\tif (offset > 0 && !LEAD_CLASS_LEADING.test(candidate))\r\n\t\t{\r\n\t\t\tconst previousChar = text[offset - 1]\r\n\t\t\t// We return null if it is a latin letter or an invalid punctuation symbol.\r\n\t\t\tif (isInvalidPunctuationSymbol(previousChar) || isLatinLetter(previousChar)) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst lastCharIndex = offset + candidate.length\r\n\t\tif (lastCharIndex < text.length)\r\n\t\t{\r\n\t\t\tconst nextChar = text[lastCharIndex]\r\n\t\t\tif (isInvalidPunctuationSymbol(nextChar) || isLatinLetter(nextChar)) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn true\r\n}"], "mappings": ";;;;;;;AAEA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,IAAA,GAAAF,OAAA;AALA;;AAUA,IAAMG,cAAc,GAAG,kBAAkB;AACzC,IAAMC,cAAc,GAAG,kBAAkB;AACzC,IAAMC,UAAU,QAAAC,MAAA,CAAQH,cAAc,EAAAG,MAAA,CAAGF,cAAc,MAAG;AAEnD,IAAMG,UAAU,GAAAC,OAAA,CAAAD,UAAA,OAAAD,MAAA,CAAOH,cAAc,EAAAG,MAAA,CAAGG,qBAAU,MAAG;;AAE5D;AACA,IAAMC,kBAAkB,GAAG,IAAIC,MAAM,CAAC,GAAG,GAAGJ,UAAU,CAAC;;AAEvD;AACA,IAAMK,kBAAkB,GAAG,IAAAC,WAAK,EAAC,CAAC,EAAE,CAAC,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,wBAAwB,GAAG,IAAIH,MAAM,CAE1C,GAAG,GACD,MAAM,GAAGR,cAAc,GAAG,KAAK,GAAG,KAAK,GAAGE,UAAU,GAAG,GAAG,GAAG,GAAG,GAAGD,cAAc,GAAG,KAAK,GACzFC,UAAU,GAAG,GAAG,GAChB,MAAM,GAAGF,cAAc,GAAG,GAAG,GAAGE,UAAU,GAAG,IAAI,GAAGD,cAAc,GAAG,IAAI,GAAGQ,kBAAkB,GAC9FP,UAAU,GAAG,GAAG,GAChB,GACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMU,SAAS,GAAG,kCAAkC;AAErC,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAC1E;EACC;EACA;EACA,IAAI,CAACN,wBAAwB,CAACO,IAAI,CAACJ,SAAS,CAAC,IAAIF,SAAS,CAACM,IAAI,CAACJ,SAAS,CAAC,EAAE;IAC3E;EACD;;EAEA;EACA;EACA,IAAIG,QAAQ,KAAK,UAAU,EAC3B;IACC;IACA;IACA;IACA,IAAIF,MAAM,GAAG,CAAC,IAAI,CAACR,kBAAkB,CAACW,IAAI,CAACJ,SAAS,CAAC,EACrD;MACC,IAAMK,YAAY,GAAGH,IAAI,CAACD,MAAM,GAAG,CAAC,CAAC;MACrC;MACA,IAAI,IAAAK,+BAA0B,EAACD,YAAY,CAAC,IAAI,IAAAE,kBAAa,EAACF,YAAY,CAAC,EAAE;QAC5E,OAAO,KAAK;MACb;IACD;IAEA,IAAMG,aAAa,GAAGP,MAAM,GAAGD,SAAS,CAACS,MAAM;IAC/C,IAAID,aAAa,GAAGN,IAAI,CAACO,MAAM,EAC/B;MACC,IAAMC,QAAQ,GAAGR,IAAI,CAACM,aAAa,CAAC;MACpC,IAAI,IAAAF,+BAA0B,EAACI,QAAQ,CAAC,IAAI,IAAAH,kBAAa,EAACG,QAAQ,CAAC,EAAE;QACpE,OAAO,KAAK;MACb;IACD;EACD;EAEA,OAAO,IAAI;AACZ", "ignoreList": []}