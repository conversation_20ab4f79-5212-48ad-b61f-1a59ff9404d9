{"version": 3, "sources": ["../../../../src/build/webpack/config/index.ts"], "names": ["base", "css", "images", "pipe", "buildConfiguration", "config", "hasAppDir", "supportedBrowsers", "rootDirectory", "customAppFile", "isDevelopment", "isServer", "isEdgeRuntime", "targetWeb", "assetPrefix", "sassOptions", "productionBrowserSourceMaps", "future", "transpilePackages", "experimental", "disableStaticImages", "serverSourceMaps", "ctx", "isProduction", "isClient", "endsWith", "slice", "fns", "push", "fn"], "mappings": "AAIA,SAASA,IAAI,QAAQ,gBAAe;AACpC,SAASC,GAAG,QAAQ,eAAc;AAClC,SAASC,MAAM,QAAQ,kBAAiB;AACxC,SAASC,IAAI,QAAQ,UAAS;AAE9B,OAAO,eAAeC,mBACpBC,MAA6B,EAC7B,EACEC,SAAS,EACTC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,WAAW,EACXC,2BAA2B,EAC3BC,MAAM,EACNC,iBAAiB,EACjBC,YAAY,EACZC,mBAAmB,EACnBC,gBAAgB,EAkBjB;IAED,MAAMC,MAA4B;QAChChB;QACAC;QACAC;QACAC;QACAC;QACAa,cAAc,CAACb;QACfC;QACAC;QACAY,UAAU,CAACb;QACXE;QACAC,aAAaA,cACTA,YAAYW,QAAQ,CAAC,OACnBX,YAAYY,KAAK,CAAC,GAAG,CAAC,KACtBZ,cACF;QACJC;QACAC;QACAE;QACAD;QACAE;QACAE,kBAAkBA,oBAAoB;IACxC;IAEA,IAAIM,MAAM;QAAC3B,KAAKsB;QAAMrB,IAAIqB;KAAK;IAC/B,IAAI,CAACF,qBAAqB;QACxBO,IAAIC,IAAI,CAAC1B,OAAOoB;IAClB;IACA,MAAMO,KAAK1B,QAAQwB;IACnB,OAAOE,GAAGxB;AACZ"}