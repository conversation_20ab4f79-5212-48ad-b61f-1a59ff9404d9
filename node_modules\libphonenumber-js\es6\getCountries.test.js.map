{"version": 3, "file": "getCountries.test.js", "names": ["metadata", "type", "getCountries", "describe", "it", "expect", "indexOf", "to", "equal"], "sources": ["../source/getCountries.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\nimport getCountries from './getCountries.js'\r\n\r\ndescribe('getCountries', () => {\r\n\tit('should get countries list', () => {\r\n\t\texpect(getCountries(metadata).indexOf('RU') > 0).to.equal(true);\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAE/D,OAAOC,YAAY,MAAM,mBAAmB;AAE5CC,QAAQ,CAAC,cAAc,EAAE,YAAM;EAC9BC,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrCC,MAAM,CAACH,YAAY,CAACF,QAAQ,CAAC,CAACM,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAChE,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}