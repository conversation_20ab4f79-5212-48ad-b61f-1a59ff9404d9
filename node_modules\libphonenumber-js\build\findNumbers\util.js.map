{"version": 3, "file": "util.js", "names": ["limit", "lower", "upper", "TypeError", "concat", "trimAfterFirstMatch", "regexp", "string", "index", "search", "slice", "startsWith", "substring", "indexOf", "endsWith", "length"], "sources": ["../../source/findNumbers/util.js"], "sourcesContent": ["/** Returns a regular expression quantifier with an upper and lower limit. */\r\nexport function limit(lower, upper)\r\n{\r\n\tif ((lower < 0) || (upper <= 0) || (upper < lower)) {\r\n\t\tthrow new TypeError()\r\n\t}\r\n\treturn `{${lower},${upper}}`\r\n}\r\n\r\n/**\r\n * Trims away any characters after the first match of {@code pattern} in {@code candidate},\r\n * returning the trimmed version.\r\n */\r\nexport function trimAfterFirstMatch(regexp, string)\r\n{\r\n\tconst index = string.search(regexp)\r\n\r\n\tif (index >= 0) {\r\n\t\treturn string.slice(0, index)\r\n\t}\r\n\r\n\treturn string\r\n}\r\n\r\nexport function startsWith(string, substring)\r\n{\r\n\treturn string.indexOf(substring) === 0\r\n}\r\n\r\nexport function endsWith(string, substring)\r\n{\r\n\treturn string.indexOf(substring, string.length - substring.length) === string.length - substring.length\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA;AACO,SAASA,KAAKA,CAACC,KAAK,EAAEC,KAAK,EAClC;EACC,IAAKD,KAAK,GAAG,CAAC,IAAMC,KAAK,IAAI,CAAE,IAAKA,KAAK,GAAGD,KAAM,EAAE;IACnD,MAAM,IAAIE,SAAS,CAAC,CAAC;EACtB;EACA,WAAAC,MAAA,CAAWH,KAAK,OAAAG,MAAA,CAAIF,KAAK;AAC1B;;AAEA;AACA;AACA;AACA;AACO,SAASG,mBAAmBA,CAACC,MAAM,EAAEC,MAAM,EAClD;EACC,IAAMC,KAAK,GAAGD,MAAM,CAACE,MAAM,CAACH,MAAM,CAAC;EAEnC,IAAIE,KAAK,IAAI,CAAC,EAAE;IACf,OAAOD,MAAM,CAACG,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC;EAC9B;EAEA,OAAOD,MAAM;AACd;AAEO,SAASI,UAAUA,CAACJ,MAAM,EAAEK,SAAS,EAC5C;EACC,OAAOL,MAAM,CAACM,OAAO,CAACD,SAAS,CAAC,KAAK,CAAC;AACvC;AAEO,SAASE,QAAQA,CAACP,MAAM,EAAEK,SAAS,EAC1C;EACC,OAAOL,MAAM,CAACM,OAAO,CAACD,SAAS,EAAEL,MAAM,CAACQ,MAAM,GAAGH,SAAS,CAACG,MAAM,CAAC,KAAKR,MAAM,CAACQ,MAAM,GAAGH,SAAS,CAACG,MAAM;AACxG", "ignoreList": []}