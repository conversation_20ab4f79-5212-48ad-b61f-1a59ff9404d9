{"version": 3, "file": "AsYouTypeFormatter.PatternParser.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports", "_classCallCheck", "_createClass", "key", "value", "parse", "pattern", "context", "or", "instructions", "parsePattern", "length", "Error", "_this$context$", "branches", "op", "args", "concat", "expandSingleElementArray", "startContext", "push", "endContext", "pop", "getContext", "match", "OPERATOR", "ILLEGAL_CHARACTER_REGEXP", "test", "split", "operator", "before", "slice", "index", "rightPart", "_this$getContext", "oneOfSet", "parseOneOfSet", "values", "i", "prevValue", "charCodeAt", "nextValue", "String", "fromCharCode", "RegExp", "array"], "sources": ["../source/AsYouTypeFormatter.PatternParser.js"], "sourcesContent": ["export default class PatternParser {\r\n\tparse(pattern) {\r\n\t\tthis.context = [{\r\n\t\t\tor: true,\r\n\t\t\tinstructions: []\r\n\t\t}]\r\n\r\n\t\tthis.parsePattern(pattern)\r\n\r\n\t\tif (this.context.length !== 1) {\r\n\t\t\tthrow new Error('Non-finalized contexts left when pattern parse ended')\r\n\t\t}\r\n\r\n\t\tconst { branches, instructions } = this.context[0]\r\n\r\n\t\tif (branches) {\r\n\t\t\treturn {\r\n\t\t\t\top: '|',\r\n\t\t\t\targs: branches.concat([\r\n\t\t\t\t\texpandSingleElementArray(instructions)\r\n\t\t\t\t])\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* istanbul ignore if */\r\n\t\tif (instructions.length === 0) {\r\n\t\t\tthrow new Error('Pattern is required')\r\n\t\t}\r\n\r\n\t\tif (instructions.length === 1) {\r\n\t\t\treturn instructions[0]\r\n\t\t}\r\n\r\n\t\treturn instructions\r\n\t}\r\n\r\n\tstartContext(context) {\r\n\t\tthis.context.push(context)\r\n\t}\r\n\r\n\tendContext() {\r\n\t\tthis.context.pop()\r\n\t}\r\n\r\n\tgetContext() {\r\n\t\treturn this.context[this.context.length - 1]\r\n\t}\r\n\r\n\tparsePattern(pattern) {\r\n\t\tif (!pattern) {\r\n\t\t\tthrow new Error('Pattern is required')\r\n\t\t}\r\n\r\n\t\tconst match = pattern.match(OPERATOR)\r\n\t\tif (!match) {\r\n\t\t\tif (ILLEGAL_CHARACTER_REGEXP.test(pattern)) {\r\n\t\t\t\tthrow new Error(`Illegal characters found in a pattern: ${pattern}`)\r\n\t\t\t}\r\n\t\t\tthis.getContext().instructions = this.getContext().instructions.concat(\r\n\t\t\t\tpattern.split('')\r\n\t\t\t)\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tconst operator = match[1]\r\n\t\tconst before = pattern.slice(0, match.index)\r\n\t\tconst rightPart = pattern.slice(match.index + operator.length)\r\n\r\n\t\tswitch (operator) {\r\n\t\t\tcase '(?:':\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tthis.startContext({\r\n\t\t\t\t\tor: true,\r\n\t\t\t\t\tinstructions: [],\r\n\t\t\t\t\tbranches: []\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase ')':\r\n\t\t\t\tif (!this.getContext().or) {\r\n\t\t\t\t\tthrow new Error('\")\" operator must be preceded by \"(?:\" operator')\r\n\t\t\t\t}\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.getContext().instructions.length === 0) {\r\n\t\t\t\t\tthrow new Error('No instructions found after \"|\" operator in an \"or\" group')\r\n\t\t\t\t}\r\n\t\t\t\tconst { branches } = this.getContext()\r\n\t\t\t\tbranches.push(\r\n\t\t\t\t\texpandSingleElementArray(\r\n\t\t\t\t\t\tthis.getContext().instructions\r\n\t\t\t\t\t)\r\n\t\t\t\t)\r\n\t\t\t\tthis.endContext()\r\n\t\t\t\tthis.getContext().instructions.push({\r\n\t\t\t\t\top: '|',\r\n\t\t\t\t\targs: branches\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase '|':\r\n\t\t\t\tif (!this.getContext().or) {\r\n\t\t\t\t\tthrow new Error('\"|\" operator can only be used inside \"or\" groups')\r\n\t\t\t\t}\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\t// The top-level is an implicit \"or\" group, if required.\r\n\t\t\t\tif (!this.getContext().branches) {\r\n\t\t\t\t\t// `branches` are not defined only for the root implicit \"or\" operator.\r\n\t\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\t\tif (this.context.length === 1) {\r\n\t\t\t\t\t\tthis.getContext().branches = []\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow new Error('\"branches\" not found in an \"or\" group context')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.getContext().branches.push(\r\n\t\t\t\t\texpandSingleElementArray(\r\n\t\t\t\t\t\tthis.getContext().instructions\r\n\t\t\t\t\t)\r\n\t\t\t\t)\r\n\t\t\t\tthis.getContext().instructions = []\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase '[':\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tthis.startContext({\r\n\t\t\t\t\toneOfSet: true\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase ']':\r\n\t\t\t\tif (!this.getContext().oneOfSet) {\r\n\t\t\t\t\tthrow new Error('\"]\" operator must be preceded by \"[\" operator')\r\n\t\t\t\t}\r\n\t\t\t\tthis.endContext()\r\n\t\t\t\tthis.getContext().instructions.push({\r\n\t\t\t\t\top: '[]',\r\n\t\t\t\t\targs: parseOneOfSet(before)\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\tdefault:\r\n\t\t\t\tthrow new Error(`Unknown operator: ${operator}`)\r\n\t\t}\r\n\r\n\t\tif (rightPart) {\r\n\t\t\tthis.parsePattern(rightPart)\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction parseOneOfSet(pattern) {\r\n\tconst values = []\r\n\tlet i = 0\r\n\twhile (i < pattern.length) {\r\n\t\tif (pattern[i] === '-') {\r\n\t\t\tif (i === 0 || i === pattern.length - 1) {\r\n\t\t\t\tthrow new Error(`Couldn't parse a one-of set pattern: ${pattern}`)\r\n\t\t\t}\r\n\t\t\tconst prevValue = pattern[i - 1].charCodeAt(0) + 1\r\n\t\t\tconst nextValue = pattern[i + 1].charCodeAt(0) - 1\r\n\t\t\tlet value = prevValue\r\n\t\t\twhile (value <= nextValue) {\r\n\t\t\t\tvalues.push(String.fromCharCode(value))\r\n\t\t\t\tvalue++\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tvalues.push(pattern[i])\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn values\r\n}\r\n\r\nconst ILLEGAL_CHARACTER_REGEXP = /[\\(\\)\\[\\]\\?\\:\\|]/\r\n\r\nconst OPERATOR = new RegExp(\r\n\t// any of:\r\n\t'(' +\r\n\t\t// or operator\r\n\t\t'\\\\|' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// or group start\r\n\t\t'\\\\(\\\\?\\\\:' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// or group end\r\n\t\t'\\\\)' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// one-of set start\r\n\t\t'\\\\[' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// one-of set end\r\n\t\t'\\\\]' +\r\n\t')'\r\n)\r\n\r\nfunction expandSingleElementArray(array) {\r\n\tif (array.length === 1) {\r\n\t\treturn array[0]\r\n\t}\r\n\treturn array\r\n}"], "mappings": ";;;;;;;;;;;;IAAqBA,aAAa,GAAAC,OAAA;EAAA,SAAAD,cAAA;IAAAE,eAAA,OAAAF,aAAA;EAAA;EAAA,OAAAG,YAAA,CAAAH,aAAA;IAAAI,GAAA;IAAAC,KAAA,EACjC,SAAAC,KAAKA,CAACC,OAAO,EAAE;MACd,IAAI,CAACC,OAAO,GAAG,CAAC;QACfC,EAAE,EAAE,IAAI;QACRC,YAAY,EAAE;MACf,CAAC,CAAC;MAEF,IAAI,CAACC,YAAY,CAACJ,OAAO,CAAC;MAE1B,IAAI,IAAI,CAACC,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;QAC9B,MAAM,IAAIC,KAAK,CAAC,sDAAsD,CAAC;MACxE;MAEA,IAAAC,cAAA,GAAmC,IAAI,CAACN,OAAO,CAAC,CAAC,CAAC;QAA1CO,QAAQ,GAAAD,cAAA,CAARC,QAAQ;QAAEL,YAAY,GAAAI,cAAA,CAAZJ,YAAY;MAE9B,IAAIK,QAAQ,EAAE;QACb,OAAO;UACNC,EAAE,EAAE,GAAG;UACPC,IAAI,EAAEF,QAAQ,CAACG,MAAM,CAAC,CACrBC,wBAAwB,CAACT,YAAY,CAAC,CACtC;QACF,CAAC;MACF;;MAEA;MACA,IAAIA,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;QAC9B,MAAM,IAAIC,KAAK,CAAC,qBAAqB,CAAC;MACvC;MAEA,IAAIH,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;QAC9B,OAAOF,YAAY,CAAC,CAAC,CAAC;MACvB;MAEA,OAAOA,YAAY;IACpB;EAAC;IAAAN,GAAA;IAAAC,KAAA,EAED,SAAAe,YAAYA,CAACZ,OAAO,EAAE;MACrB,IAAI,CAACA,OAAO,CAACa,IAAI,CAACb,OAAO,CAAC;IAC3B;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAAiB,UAAUA,CAAA,EAAG;MACZ,IAAI,CAACd,OAAO,CAACe,GAAG,CAAC,CAAC;IACnB;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAED,SAAAmB,UAAUA,CAAA,EAAG;MACZ,OAAO,IAAI,CAAChB,OAAO,CAAC,IAAI,CAACA,OAAO,CAACI,MAAM,GAAG,CAAC,CAAC;IAC7C;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAED,SAAAM,YAAYA,CAACJ,OAAO,EAAE;MACrB,IAAI,CAACA,OAAO,EAAE;QACb,MAAM,IAAIM,KAAK,CAAC,qBAAqB,CAAC;MACvC;MAEA,IAAMY,KAAK,GAAGlB,OAAO,CAACkB,KAAK,CAACC,QAAQ,CAAC;MACrC,IAAI,CAACD,KAAK,EAAE;QACX,IAAIE,wBAAwB,CAACC,IAAI,CAACrB,OAAO,CAAC,EAAE;UAC3C,MAAM,IAAIM,KAAK,2CAAAK,MAAA,CAA2CX,OAAO,CAAE,CAAC;QACrE;QACA,IAAI,CAACiB,UAAU,CAAC,CAAC,CAACd,YAAY,GAAG,IAAI,CAACc,UAAU,CAAC,CAAC,CAACd,YAAY,CAACQ,MAAM,CACrEX,OAAO,CAACsB,KAAK,CAAC,EAAE,CACjB,CAAC;QACD;MACD;MAEA,IAAMC,QAAQ,GAAGL,KAAK,CAAC,CAAC,CAAC;MACzB,IAAMM,MAAM,GAAGxB,OAAO,CAACyB,KAAK,CAAC,CAAC,EAAEP,KAAK,CAACQ,KAAK,CAAC;MAC5C,IAAMC,SAAS,GAAG3B,OAAO,CAACyB,KAAK,CAACP,KAAK,CAACQ,KAAK,GAAGH,QAAQ,CAAClB,MAAM,CAAC;MAE9D,QAAQkB,QAAQ;QACf,KAAK,KAAK;UACT,IAAIC,MAAM,EAAE;YACX,IAAI,CAACpB,YAAY,CAACoB,MAAM,CAAC;UAC1B;UACA,IAAI,CAACX,YAAY,CAAC;YACjBX,EAAE,EAAE,IAAI;YACRC,YAAY,EAAE,EAAE;YAChBK,QAAQ,EAAE;UACX,CAAC,CAAC;UACF;QAED,KAAK,GAAG;UACP,IAAI,CAAC,IAAI,CAACS,UAAU,CAAC,CAAC,CAACf,EAAE,EAAE;YAC1B,MAAM,IAAII,KAAK,CAAC,iDAAiD,CAAC;UACnE;UACA,IAAIkB,MAAM,EAAE;YACX,IAAI,CAACpB,YAAY,CAACoB,MAAM,CAAC;UAC1B;UACA,IAAI,IAAI,CAACP,UAAU,CAAC,CAAC,CAACd,YAAY,CAACE,MAAM,KAAK,CAAC,EAAE;YAChD,MAAM,IAAIC,KAAK,CAAC,2DAA2D,CAAC;UAC7E;UACA,IAAAsB,gBAAA,GAAqB,IAAI,CAACX,UAAU,CAAC,CAAC;YAA9BT,QAAQ,GAAAoB,gBAAA,CAARpB,QAAQ;UAChBA,QAAQ,CAACM,IAAI,CACZF,wBAAwB,CACvB,IAAI,CAACK,UAAU,CAAC,CAAC,CAACd,YACnB,CACD,CAAC;UACD,IAAI,CAACY,UAAU,CAAC,CAAC;UACjB,IAAI,CAACE,UAAU,CAAC,CAAC,CAACd,YAAY,CAACW,IAAI,CAAC;YACnCL,EAAE,EAAE,GAAG;YACPC,IAAI,EAAEF;UACP,CAAC,CAAC;UACF;QAED,KAAK,GAAG;UACP,IAAI,CAAC,IAAI,CAACS,UAAU,CAAC,CAAC,CAACf,EAAE,EAAE;YAC1B,MAAM,IAAII,KAAK,CAAC,kDAAkD,CAAC;UACpE;UACA,IAAIkB,MAAM,EAAE;YACX,IAAI,CAACpB,YAAY,CAACoB,MAAM,CAAC;UAC1B;UACA;UACA,IAAI,CAAC,IAAI,CAACP,UAAU,CAAC,CAAC,CAACT,QAAQ,EAAE;YAChC;YACA;YACA,IAAI,IAAI,CAACP,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;cAC9B,IAAI,CAACY,UAAU,CAAC,CAAC,CAACT,QAAQ,GAAG,EAAE;YAChC,CAAC,MAAM;cACN,MAAM,IAAIF,KAAK,CAAC,+CAA+C,CAAC;YACjE;UACD;UACA,IAAI,CAACW,UAAU,CAAC,CAAC,CAACT,QAAQ,CAACM,IAAI,CAC9BF,wBAAwB,CACvB,IAAI,CAACK,UAAU,CAAC,CAAC,CAACd,YACnB,CACD,CAAC;UACD,IAAI,CAACc,UAAU,CAAC,CAAC,CAACd,YAAY,GAAG,EAAE;UACnC;QAED,KAAK,GAAG;UACP,IAAIqB,MAAM,EAAE;YACX,IAAI,CAACpB,YAAY,CAACoB,MAAM,CAAC;UAC1B;UACA,IAAI,CAACX,YAAY,CAAC;YACjBgB,QAAQ,EAAE;UACX,CAAC,CAAC;UACF;QAED,KAAK,GAAG;UACP,IAAI,CAAC,IAAI,CAACZ,UAAU,CAAC,CAAC,CAACY,QAAQ,EAAE;YAChC,MAAM,IAAIvB,KAAK,CAAC,+CAA+C,CAAC;UACjE;UACA,IAAI,CAACS,UAAU,CAAC,CAAC;UACjB,IAAI,CAACE,UAAU,CAAC,CAAC,CAACd,YAAY,CAACW,IAAI,CAAC;YACnCL,EAAE,EAAE,IAAI;YACRC,IAAI,EAAEoB,aAAa,CAACN,MAAM;UAC3B,CAAC,CAAC;UACF;;QAED;QACA;UACC,MAAM,IAAIlB,KAAK,sBAAAK,MAAA,CAAsBY,QAAQ,CAAE,CAAC;MAClD;MAEA,IAAII,SAAS,EAAE;QACd,IAAI,CAACvB,YAAY,CAACuB,SAAS,CAAC;MAC7B;IACD;EAAC;AAAA;AAGF,SAASG,aAAaA,CAAC9B,OAAO,EAAE;EAC/B,IAAM+B,MAAM,GAAG,EAAE;EACjB,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGhC,OAAO,CAACK,MAAM,EAAE;IAC1B,IAAIL,OAAO,CAACgC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvB,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAKhC,OAAO,CAACK,MAAM,GAAG,CAAC,EAAE;QACxC,MAAM,IAAIC,KAAK,yCAAAK,MAAA,CAAyCX,OAAO,CAAE,CAAC;MACnE;MACA,IAAMiC,SAAS,GAAGjC,OAAO,CAACgC,CAAC,GAAG,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MAClD,IAAMC,SAAS,GAAGnC,OAAO,CAACgC,CAAC,GAAG,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MAClD,IAAIpC,KAAK,GAAGmC,SAAS;MACrB,OAAOnC,KAAK,IAAIqC,SAAS,EAAE;QAC1BJ,MAAM,CAACjB,IAAI,CAACsB,MAAM,CAACC,YAAY,CAACvC,KAAK,CAAC,CAAC;QACvCA,KAAK,EAAE;MACR;IACD,CAAC,MAAM;MACNiC,MAAM,CAACjB,IAAI,CAACd,OAAO,CAACgC,CAAC,CAAC,CAAC;IACxB;IACAA,CAAC,EAAE;EACJ;EACA,OAAOD,MAAM;AACd;AAEA,IAAMX,wBAAwB,GAAG,kBAAkB;AAEnD,IAAMD,QAAQ,GAAG,IAAImB,MAAM;AAC1B;AACA,GAAG;AACF;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA,WAAW;AACX;AACA,GAAG;AACH;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA,KAAK,GACN,GACD,CAAC;AAED,SAAS1B,wBAAwBA,CAAC2B,KAAK,EAAE;EACxC,IAAIA,KAAK,CAAClC,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOkC,KAAK,CAAC,CAAC,CAAC;EAChB;EACA,OAAOA,KAAK;AACb", "ignoreList": []}