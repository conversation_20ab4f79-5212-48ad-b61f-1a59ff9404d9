{"version": 3, "sources": ["../../../../src/server/future/helpers/i18n-provider.ts"], "names": ["I18NProvider", "constructor", "config", "locales", "length", "Error", "lowerCaseLocales", "map", "locale", "toLowerCase", "lowerCaseDomains", "domains", "domainLocale", "domain", "defaultLocale", "hostname", "split", "http", "detectDomainLocale", "detectedLocale", "i", "some", "fromQuery", "pathname", "query", "__next<PERSON><PERSON><PERSON>", "analysis", "analyze", "inferredFromDefault", "__nextInferredLocaleFromDefault", "validate", "includes", "validate<PERSON><PERSON>y", "__nextDefaultLocale", "options", "segments", "segment", "index", "indexOf", "slice"], "mappings": "AA+BA;;;;CAIC,GACD,OAAO,MAAMA;IAWXC,YAAY,AAAgBC,MAA4B,CAAE;YAMhCA;aANEA,SAAAA;QAC1B,IAAI,CAACA,OAAOC,OAAO,CAACC,MAAM,EAAE;YAC1B,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAI,CAACC,gBAAgB,GAAGJ,OAAOC,OAAO,CAACI,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;QACzE,IAAI,CAACC,gBAAgB,IAAGR,kBAAAA,OAAOS,OAAO,qBAAdT,gBAAgBK,GAAG,CAAC,CAACK;gBAMhCA;YALX,MAAMC,SAASD,aAAaC,MAAM,CAACJ,WAAW;YAC9C,OAAO;gBACLK,eAAeF,aAAaE,aAAa,CAACL,WAAW;gBACrDM,UAAUF,OAAOG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;gBACjCH;gBACAV,OAAO,GAAES,wBAAAA,aAAaT,OAAO,qBAApBS,sBAAsBL,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;gBACjEQ,MAAML,aAAaK,IAAI;YACzB;QACF;IACF;IAEA;;;;;;;GAOC,GACD,AAAOC,mBACLH,QAAiB,EACjBI,cAAuB,EACG;QAC1B,IAAI,CAACJ,YAAY,CAAC,IAAI,CAACL,gBAAgB,IAAI,CAAC,IAAI,CAACR,MAAM,CAACS,OAAO,EAAE;QAEjE,IAAIQ,gBAAgBA,iBAAiBA,eAAeV,WAAW;QAE/D,IAAK,IAAIW,IAAI,GAAGA,IAAI,IAAI,CAACV,gBAAgB,CAACN,MAAM,EAAEgB,IAAK;gBAKnD,sEAAsE;YACtE,yBAAyB;YACzBR;YANF,MAAMA,eAAe,IAAI,CAACF,gBAAgB,CAACU,EAAE;YAC7C,IACE,qDAAqD;YACrDR,aAAaG,QAAQ,KAAKA,cAG1BH,wBAAAA,aAAaT,OAAO,qBAApBS,sBAAsBS,IAAI,CAAC,CAACb,SAAWA,WAAWW,kBAClD;gBACA,OAAO,IAAI,CAACjB,MAAM,CAACS,OAAO,CAACS,EAAE;YAC/B;QACF;QAEA;IACF;IAEA;;;;;;;GAOC,GACD,AAAOE,UACLC,QAAgB,EAChBC,KAAyB,EACH;QACtB,MAAML,iBAAiBK,MAAMC,YAAY;QAEzC,wEAAwE;QACxE,2BAA2B;QAC3B,IAAIN,gBAAgB;YAClB,MAAMO,WAAW,IAAI,CAACC,OAAO,CAACJ;YAE9B,uEAAuE;YACvE,wCAAwC;YACxC,IAAIG,SAASP,cAAc,EAAE;gBAC3B,IAAIO,SAASP,cAAc,KAAKA,gBAAgB;oBAC9C,MAAM,IAAId,MACR,CAAC,yFAAyF,EAAEc,eAAe,MAAM,EAAEI,SAAS,aAAa,EAAEG,SAASP,cAAc,CAAC,EAAE,CAAC;gBAE1K;gBAEAI,WAAWG,SAASH,QAAQ;YAC9B;QACF;QAEA,OAAO;YACLA;YACAJ;YACAS,qBAAqBJ,MAAMK,+BAA+B,KAAK;QACjE;IACF;IAEA;;;;;GAKC,GACD,AAAQC,SAAStB,MAAc,EAAW;QACxC,OAAO,IAAI,CAACF,gBAAgB,CAACyB,QAAQ,CAACvB,OAAOC,WAAW;IAC1D;IAEA;;;;;GAKC,GACD,AAAOuB,cAAcR,KAAyB,EAAE;QAC9C,IAAIA,MAAMC,YAAY,IAAI,CAAC,IAAI,CAACK,QAAQ,CAACN,MAAMC,YAAY,GAAG;YAC5D,OAAO;QACT;QAEA,IACED,MAAMS,mBAAmB,IACzB,CAAC,IAAI,CAACH,QAAQ,CAACN,MAAMS,mBAAmB,GACxC;YACA,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;;;;;;GAOC,GACD,AAAON,QACLJ,QAAgB,EAChBW,UAAiC,CAAC,CAAC,EACb;QACtB,IAAIf,iBAAqCe,QAAQpB,aAAa;QAE9D,0EAA0E;QAC1E,sBAAsB;QACtB,IAAIc,sBAAsB,OAAOT,mBAAmB;QAEpD,oEAAoE;QACpE,yEAAyE;QACzE,MAAMgB,WAAWZ,SAASP,KAAK,CAAC,KAAK;QACrC,IAAI,CAACmB,QAAQ,CAAC,EAAE,EACd,OAAO;YACLhB;YACAI;YACAK;QACF;QAEF,0DAA0D;QAC1D,MAAMQ,UAAUD,QAAQ,CAAC,EAAE,CAAC1B,WAAW;QAEvC,yEAAyE;QACzE,mCAAmC;QACnC,MAAM4B,QAAQ,IAAI,CAAC/B,gBAAgB,CAACgC,OAAO,CAACF;QAC5C,IAAIC,QAAQ,GACV,OAAO;YACLlB;YACAI;YACAK;QACF;QAEF,oCAAoC;QACpCT,iBAAiB,IAAI,CAACjB,MAAM,CAACC,OAAO,CAACkC,MAAM;QAC3CT,sBAAsB;QAEtB,gDAAgD;QAChDL,WAAWA,SAASgB,KAAK,CAACpB,eAAef,MAAM,GAAG,MAAM;QAExD,OAAO;YACLe;YACAI;YACAK;QACF;IACF;AACF"}