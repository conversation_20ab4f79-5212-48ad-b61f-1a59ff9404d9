{"version": 3, "file": "isPossibleNumber.test.js", "names": ["_metadataMin", "_interopRequireDefault", "require", "_isPossibleNumber2", "e", "__esModule", "isPossibleNumber", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "metadata", "_isPossibleNumber", "apply", "describe", "it", "expect", "to", "equal", "phone", "country", "countryCallingCode", "nationalNumber", "v2"], "sources": ["../../source/legacy/isPossibleNumber.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' with { type: 'json' }\r\nimport _isPossibleNumber from './isPossibleNumber.js'\r\n\r\nfunction isPossibleNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isPossibleNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isPossibleNumber', () => {\r\n\tit('should work', function()\r\n\t{\r\n\t\texpect(isPossibleNumber('+79992223344')).to.equal(true)\r\n\r\n\t\texpect(isPossibleNumber({ phone: '1112223344', country: 'RU' })).to.equal(true)\r\n\t\texpect(isPossibleNumber({ phone: '111222334', country: 'RU' })).to.equal(false)\r\n\t\texpect(isPossibleNumber({ phone: '11122233445', country: 'RU' })).to.equal(false)\r\n\r\n\t\texpect(isPossibleNumber({ phone: '1112223344', countryCallingCode: 7 })).to.equal(true)\r\n\t})\r\n\r\n\tit('should work v2', () => {\r\n\t\texpect(\r\n            isPossibleNumber({ nationalNumber: '111222334', countryCallingCode: 7 }, { v2: true })\r\n        ).to.equal(false)\r\n\t\texpect(\r\n            isPossibleNumber({ nationalNumber: '1112223344', countryCallingCode: 7 }, { v2: true })\r\n        ).to.equal(true)\r\n\t\texpect(\r\n            isPossibleNumber({ nationalNumber: '11122233445', countryCallingCode: 7 }, { v2: true })\r\n        ).to.equal(false)\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// Invalid `PhoneNumber` argument.\r\n\t\texpect(() => isPossibleNumber({}, { v2: true })).to.throw('Invalid phone number object passed')\r\n\r\n\t\t// Empty input is passed.\r\n\t\t// This is just to support `isValidNumber({})`\r\n\t\t// for cases when `parseNumber()` returns `{}`.\r\n\t\texpect(isPossibleNumber({})).to.equal(false)\r\n\t\texpect(() => isPossibleNumber({ phone: '1112223344' })).to.throw('Invalid phone number object passed')\r\n\r\n\t\t// Incorrect country.\r\n\t\texpect(() => isPossibleNumber({ phone: '1112223344', country: 'XX' })).to.throw('Unknown country')\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAqD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAErD,SAASE,gBAAgBA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACtCF,UAAU,CAACG,IAAI,CAACC,uBAAQ,CAAC;EACzB,OAAOC,6BAAiB,CAACC,KAAK,CAAC,IAAI,EAAEN,UAAU,CAAC;AACjD;AAEAO,QAAQ,CAAC,kBAAkB,EAAE,YAAM;EAClCC,EAAE,CAAC,aAAa,EAAE,YAClB;IACCC,MAAM,CAACb,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAEvDF,MAAM,CAACb,gBAAgB,CAAC;MAAEgB,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC/EF,MAAM,CAACb,gBAAgB,CAAC;MAAEgB,KAAK,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC/EF,MAAM,CAACb,gBAAgB,CAAC;MAAEgB,KAAK,EAAE,aAAa;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAEjFF,MAAM,CAACb,gBAAgB,CAAC;MAAEgB,KAAK,EAAE,YAAY;MAAEE,kBAAkB,EAAE;IAAE,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACxF,CAAC,CAAC;EAEFH,EAAE,CAAC,gBAAgB,EAAE,YAAM;IAC1BC,MAAM,CACIb,gBAAgB,CAAC;MAAEmB,cAAc,EAAE,WAAW;MAAED,kBAAkB,EAAE;IAAE,CAAC,EAAE;MAAEE,EAAE,EAAE;IAAK,CAAC,CACzF,CAAC,CAACN,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACvBF,MAAM,CACIb,gBAAgB,CAAC;MAAEmB,cAAc,EAAE,YAAY;MAAED,kBAAkB,EAAE;IAAE,CAAC,EAAE;MAAEE,EAAE,EAAE;IAAK,CAAC,CAC1F,CAAC,CAACN,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACtBF,MAAM,CACIb,gBAAgB,CAAC;MAAEmB,cAAc,EAAE,aAAa;MAAED,kBAAkB,EAAE;IAAE,CAAC,EAAE;MAAEE,EAAE,EAAE;IAAK,CAAC,CAC3F,CAAC,CAACN,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EACxB,CAAC,CAAC;EAEFH,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC;IACAC,MAAM,CAAC;MAAA,OAAMb,gBAAgB,CAAC,CAAC,CAAC,EAAE;QAAEoB,EAAE,EAAE;MAAK,CAAC,CAAC;IAAA,EAAC,CAACN,EAAE,SAAM,CAAC,oCAAoC,CAAC;;IAE/F;IACA;IACA;IACAD,MAAM,CAACb,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CF,MAAM,CAAC;MAAA,OAAMb,gBAAgB,CAAC;QAAEgB,KAAK,EAAE;MAAa,CAAC,CAAC;IAAA,EAAC,CAACF,EAAE,SAAM,CAAC,oCAAoC,CAAC;;IAEtG;IACAD,MAAM,CAAC;MAAA,OAAMb,gBAAgB,CAAC;QAAEgB,KAAK,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IAAA,EAAC,CAACH,EAAE,SAAM,CAAC,iBAAiB,CAAC;EACnG,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}