{"version": 3, "sources": ["../../../src/server/app-render/encryption.ts"], "names": ["renderToReadableStream", "decodeReply", "createFromReadableStream", "encodeReply", "streamToString", "arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestSingleton", "getServerModuleMap", "stringToUint8Array", "textEncoder", "TextEncoder", "textDecoder", "TextDecoder", "decodeActionBoundArg", "actionId", "arg", "key", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "decrypted", "decode", "startsWith", "length", "encodeActionBoundArg", "undefined", "randomBytes", "Uint8Array", "crypto", "getRandomValues", "buffer", "encrypted", "encode", "btoa", "encryptActionBoundArgs", "args", "clientReferenceManifestSingleton", "serialized", "clientModules", "decryptActionBoundArgs", "decryped", "deserialized", "ReadableStream", "start", "controller", "enqueue", "close", "ssrManifest", "moduleLoading", "moduleMap", "serverModuleMap", "transformed"], "mappings": "AAAA,oDAAoD,GACpD,OAAO,cAAa;AAEpB,oDAAoD,GACpD,SACEA,sBAAsB,EACtBC,WAAW,QACN,uCAAsC;AAC7C,oDAAoD,GACpD,SACEC,wBAAwB,EACxBC,WAAW,QACN,uCAAsC;AAE7C,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SACEC,mBAAmB,EACnBC,OAAO,EACPC,OAAO,EACPC,sBAAsB,EACtBC,mCAAmC,EACnCC,kBAAkB,EAClBC,kBAAkB,QACb,qBAAoB;AAE3B,MAAMC,cAAc,IAAIC;AACxB,MAAMC,cAAc,IAAIC;AAExB,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMX;IAClB,IAAI,OAAOW,QAAQ,aAAa;QAC9B,MAAM,IAAIC,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKJ;IAC7B,MAAMK,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IAEtC,MAAME,YAAYZ,YAAYa,MAAM,CAClC,MAAMrB,QAAQa,KAAKR,mBAAmBY,UAAUZ,mBAAmBc;IAGrE,IAAI,CAACC,UAAUE,UAAU,CAACX,WAAW;QACnC,MAAM,IAAIG,MAAM;IAClB;IAEA,OAAOM,UAAUF,KAAK,CAACP,SAASY,MAAM;AACxC;AAEA,eAAeC,qBAAqBb,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMX;IAClB,IAAIW,QAAQY,WAAW;QACrB,MAAM,IAAIX,MACR,CAAC,kEAAkE,CAAC;IAExE;IAEA,6BAA6B;IAC7B,MAAMY,cAAc,IAAIC,WAAW;IACnCC,OAAOC,eAAe,CAACH;IACvB,MAAMT,UAAUlB,oBAAoB2B,YAAYI,MAAM;IAEtD,MAAMC,YAAY,MAAM9B,QACtBY,KACAa,aACApB,YAAY0B,MAAM,CAACrB,WAAWC;IAGhC,OAAOqB,KAAKhB,UAAUlB,oBAAoBgC;AAC5C;AAEA,kDAAkD;AAClD,OAAO,eAAeG,uBAAuBvB,QAAgB,EAAEwB,IAAW;IACxE,MAAMC,mCAAmCjC;IAEzC,oDAAoD;IACpD,MAAMkC,aAAa,MAAMvC,eACvBJ,uBAAuByC,MAAMC,iCAAiCE,aAAa;IAG7E,gEAAgE;IAChE,gFAAgF;IAChF,iBAAiB;IACjB,MAAMP,YAAY,MAAMP,qBAAqBb,UAAU0B;IAEvD,OAAON;AACT;AAEA,8DAA8D;AAC9D,OAAO,eAAeQ,uBACpB5B,QAAgB,EAChBoB,SAA0B;IAE1B,gEAAgE;IAChE,MAAMS,WAAW,MAAM9B,qBAAqBC,UAAU,MAAMoB;IAE5D,wDAAwD;IACxD,MAAMU,eAAe,MAAM7C,yBACzB,IAAI8C,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACvC,YAAY0B,MAAM,CAACQ;YACtCI,WAAWE,KAAK;QAClB;IACF,IACA;QACEC,aAAa;YACX,0EAA0E;YAC1E,uEAAuE;YACvE,8BAA8B;YAC9B,+CAA+C;YAC/CC,eAAe,CAAC;YAChBC,WAAW,CAAC;QACd;IACF;IAGF,oEAAoE;IACpE,MAAMC,kBAAkB9C;IACxB,MAAM+C,cAAc,MAAMxD,YACxB,MAAME,YAAY4C,eAClBS;IAGF,OAAOC;AACT"}