{"version": 3, "file": "applyInternationalSeparatorStyle.test.js", "names": ["_applyInternationalSeparatorStyle", "_interopRequireDefault", "require", "e", "__esModule", "describe", "it", "expect", "applyInternationalSeparatorStyle", "to", "equal"], "sources": ["../../source/helpers/applyInternationalSeparatorStyle.test.js"], "sourcesContent": ["import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'\r\n\r\ndescribe('applyInternationalSeparatorStyle', () => {\r\n\tit('should change Google\\'s international format style', () => {\r\n\t\texpect(applyInternationalSeparatorStyle('(xxx) xxx-xx-xx')).to.equal('xxx xxx xx xx')\r\n\t\texpect(applyInternationalSeparatorStyle('(xxx)xxx')).to.equal('xxx xxx')\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,iCAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAoF,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEpFE,QAAQ,CAAC,kCAAkC,EAAE,YAAM;EAClDC,EAAE,CAAC,oDAAoD,EAAE,YAAM;IAC9DC,MAAM,CAAC,IAAAC,4CAAgC,EAAC,iBAAiB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IACrFH,MAAM,CAAC,IAAAC,4CAAgC,EAAC,UAAU,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;EACzE,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}