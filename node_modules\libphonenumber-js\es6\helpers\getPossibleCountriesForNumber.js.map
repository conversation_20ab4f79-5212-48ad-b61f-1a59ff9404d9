{"version": 3, "file": "getPossibleCountriesForNumber.js", "names": ["<PERSON><PERSON><PERSON>", "getPossibleCountriesForNumber", "callingCode", "nationalNumber", "metadata", "_metadata", "possibleCountries", "getCountryCodesForCallingCode", "filter", "country", "couldNationalNumberBelongToCountry", "selectNumberingPlan", "numberingPlan", "possibleLengths", "indexOf", "length"], "sources": ["../../source/helpers/getPossibleCountriesForNumber.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\n\r\n/**\r\n * Returns a list of countries that the phone number could potentially belong to.\r\n * @param  {string} callingCode — Calling code.\r\n * @param  {string} nationalNumber — National (significant) number.\r\n * @param  {object} metadata — Metadata.\r\n * @return {string[]} A list of possible countries.\r\n */\r\nexport default function getPossibleCountriesForNumber(callingCode, nationalNumber, metadata) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\tlet possibleCountries = _metadata.getCountryCodesForCallingCode(callingCode)\r\n\tif (!possibleCountries) {\r\n\t\treturn []\r\n\t}\r\n\treturn possibleCountries.filter((country) => {\r\n\t\treturn couldNationalNumberBelongToCountry(nationalNumber, country, metadata)\r\n\t})\r\n}\r\n\r\nfunction couldNationalNumberBelongToCountry(nationalNumber, country, metadata) {\r\n\tconst _metadata = new Metadata(metadata)\r\n\t_metadata.selectNumberingPlan(country)\r\n\tif (_metadata.numberingPlan.possibleLengths().indexOf(nationalNumber.length) >= 0) {\r\n\t\treturn true\r\n\t}\r\n\treturn false\r\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,6BAA6BA,CAACC,WAAW,EAAEC,cAAc,EAAEC,QAAQ,EAAE;EAC5F,IAAMC,SAAS,GAAG,IAAIL,QAAQ,CAACI,QAAQ,CAAC;EACxC,IAAIE,iBAAiB,GAAGD,SAAS,CAACE,6BAA6B,CAACL,WAAW,CAAC;EAC5E,IAAI,CAACI,iBAAiB,EAAE;IACvB,OAAO,EAAE;EACV;EACA,OAAOA,iBAAiB,CAACE,MAAM,CAAC,UAACC,OAAO,EAAK;IAC5C,OAAOC,kCAAkC,CAACP,cAAc,EAAEM,OAAO,EAAEL,QAAQ,CAAC;EAC7E,CAAC,CAAC;AACH;AAEA,SAASM,kCAAkCA,CAACP,cAAc,EAAEM,OAAO,EAAEL,QAAQ,EAAE;EAC9E,IAAMC,SAAS,GAAG,IAAIL,QAAQ,CAACI,QAAQ,CAAC;EACxCC,SAAS,CAACM,mBAAmB,CAACF,OAAO,CAAC;EACtC,IAAIJ,SAAS,CAACO,aAAa,CAACC,eAAe,CAAC,CAAC,CAACC,OAAO,CAACX,cAAc,CAACY,MAAM,CAAC,IAAI,CAAC,EAAE;IAClF,OAAO,IAAI;EACZ;EACA,OAAO,KAAK;AACb", "ignoreList": []}