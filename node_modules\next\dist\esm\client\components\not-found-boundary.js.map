{"version": 3, "sources": ["../../../src/client/components/not-found-boundary.tsx"], "names": ["React", "useContext", "usePathname", "isNotFoundError", "warnOnce", "MissingSlotContext", "NotFoundErrorBoundary", "Component", "componentDidCatch", "process", "env", "NODE_ENV", "props", "missingSlots", "has", "warningMessage", "size", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "getDerivedStateFromError", "error", "notFoundTriggered", "getDerivedStateFromProps", "state", "pathname", "previousPathname", "render", "meta", "name", "content", "notFoundStyles", "notFound", "children", "constructor", "asNotFound", "NotFoundBoundary"], "mappings": "AAAA;;AAEA,OAAOA,SAASC,UAAU,QAAQ,QAAO;AACzC,SAASC,WAAW,QAAQ,eAAc;AAC1C,SAASC,eAAe,QAAQ,cAAa;AAC7C,SAASC,QAAQ,QAAQ,mCAAkC;AAC3D,SAASC,kBAAkB,QAAQ,qDAAoD;AAmBvF,MAAMC,8BAA8BN,MAAMO,SAAS;IAYjDC,oBAA0B;QACxB,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,4EAA4E;QAC5E,CAAC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,GAAG,CAAC,aAC7B;YACA,IAAIC,iBACF,4HACA;YAEF,IAAI,IAAI,CAACH,KAAK,CAACC,YAAY,CAACG,IAAI,GAAG,GAAG;gBACpC,MAAMC,iBAAiBC,MAAMC,IAAI,CAAC,IAAI,CAACP,KAAK,CAACC,YAAY,EACtDO,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,OAAS,AAAC,MAAGA,MAClBC,IAAI,CAAC;gBAERX,kBAAkB,oBAAoBE;YACxC;YAEAb,SAASW;QACX;IACF;IAEA,OAAOY,yBAAyBC,KAAU,EAAE;QAC1C,IAAIzB,gBAAgByB,QAAQ;YAC1B,OAAO;gBACLC,mBAAmB;YACrB;QACF;QACA,mCAAmC;QACnC,MAAMD;IACR;IAEA,OAAOE,yBACLlB,KAAiC,EACjCmB,KAAiC,EACE;QACnC;;;;;KAKC,GACD,IAAInB,MAAMoB,QAAQ,KAAKD,MAAME,gBAAgB,IAAIF,MAAMF,iBAAiB,EAAE;YACxE,OAAO;gBACLA,mBAAmB;gBACnBI,kBAAkBrB,MAAMoB,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,mBAAmBE,MAAMF,iBAAiB;YAC1CI,kBAAkBrB,MAAMoB,QAAQ;QAClC;IACF;IAEAE,SAAS;QACP,IAAI,IAAI,CAACH,KAAK,CAACF,iBAAiB,EAAE;YAChC,qBACE;;kCACE,KAACM;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3B5B,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,KAACwB;wBAAKC,MAAK;wBAAaC,SAAQ;;oBAEjC,IAAI,CAACzB,KAAK,CAAC0B,cAAc;oBACzB,IAAI,CAAC1B,KAAK,CAAC2B,QAAQ;;;QAG1B;QAEA,OAAO,IAAI,CAAC3B,KAAK,CAAC4B,QAAQ;IAC5B;IA9EAC,YAAY7B,KAAiC,CAAE;QAC7C,KAAK,CAACA;QACN,IAAI,CAACmB,KAAK,GAAG;YACXF,mBAAmB,CAAC,CAACjB,MAAM8B,UAAU;YACrCT,kBAAkBrB,MAAMoB,QAAQ;QAClC;IACF;AAyEF;AAEA,OAAO,SAASW,iBAAiB,KAKT;IALS,IAAA,EAC/BJ,QAAQ,EACRD,cAAc,EACdI,UAAU,EACVF,QAAQ,EACc,GALS;IAM/B,MAAMR,WAAW9B;IACjB,MAAMW,eAAeZ,WAAWI;IAChC,OAAOkC,yBACL,KAACjC;QACC0B,UAAUA;QACVO,UAAUA;QACVD,gBAAgBA;QAChBI,YAAYA;QACZ7B,cAAcA;kBAEb2B;uBAGH;kBAAGA;;AAEP"}