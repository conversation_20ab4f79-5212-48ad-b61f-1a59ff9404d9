{"version": 3, "file": "fromEvent.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/fromEvent.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAG5D,IAAM,uBAAuB,GAAG,CAAC,aAAa,EAAE,gBAAgB,CAAU,CAAC;AAC3E,IAAM,kBAAkB,GAAG,CAAC,kBAAkB,EAAE,qBAAqB,CAAU,CAAC;AAChF,IAAM,aAAa,GAAG,CAAC,IAAI,EAAE,KAAK,CAAU,CAAC;AAkO7C,MAAM,UAAU,SAAS,CACvB,MAAW,EACX,SAAiB,EACjB,OAAwD,EACxD,cAAsC;IAEtC,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;QACvB,cAAc,GAAG,OAAO,CAAC;QACzB,OAAO,GAAG,SAAS,CAAC;KACrB;IACD,IAAI,cAAc,EAAE;QAClB,OAAO,SAAS,CAAI,MAAM,EAAE,SAAS,EAAE,OAA+B,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC;KAChH;IASK,IAAA,KAAA,OAEJ,aAAa,CAAC,MAAM,CAAC;QACnB,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAC,UAAU,IAAK,OAAA,UAAC,OAAY,IAAK,OAAA,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,OAAO,EAAE,OAA+B,CAAC,EAAvE,CAAuE,EAAzF,CAAyF,CAAC;QACnI,CAAC;YACD,uBAAuB,CAAC,MAAM,CAAC;gBAC/B,CAAC,CAAC,uBAAuB,CAAC,GAAG,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBACzE,CAAC,CAAC,yBAAyB,CAAC,MAAM,CAAC;oBACnC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;oBAC/D,CAAC,CAAC,EAAE,IAAA,EATD,GAAG,QAAA,EAAE,MAAM,QASV,CAAC;IAOT,IAAI,CAAC,GAAG,EAAE;QACR,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;YACvB,OAAO,QAAQ,CAAC,UAAC,SAAc,IAAK,OAAA,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,OAA+B,CAAC,EAAhE,CAAgE,CAAC,CACnG,SAAS,CAAC,MAAM,CAAC,CACD,CAAC;SACpB;KACF;IAID,IAAI,CAAC,GAAG,EAAE;QACR,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC;KAC7C;IAED,OAAO,IAAI,UAAU,CAAI,UAAC,UAAU;QAIlC,IAAM,OAAO,GAAG;YAAC,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YAAK,OAAA,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAAjD,CAAiD,CAAC;QAEtF,GAAG,CAAC,OAAO,CAAC,CAAC;QAEb,OAAO,cAAM,OAAA,MAAO,CAAC,OAAO,CAAC,EAAhB,CAAgB,CAAC;IAChC,CAAC,CAAC,CAAC;AACL,CAAC;AASD,SAAS,uBAAuB,CAAC,MAAW,EAAE,SAAiB;IAC7D,OAAO,UAAC,UAAkB,IAAK,OAAA,UAAC,OAAY,IAAK,OAAA,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,EAAtC,CAAsC,EAAxD,CAAwD,CAAC;AAC1F,CAAC;AAOD,SAAS,uBAAuB,CAAC,MAAW;IAC1C,OAAO,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAC7E,CAAC;AAOD,SAAS,yBAAyB,CAAC,MAAW;IAC5C,OAAO,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACzD,CAAC;AAOD,SAAS,aAAa,CAAC,MAAW;IAChC,OAAO,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACvF,CAAC"}