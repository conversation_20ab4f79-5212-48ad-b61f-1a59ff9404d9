{"version": 3, "file": "IsEAN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsEAN.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,cAAc,MAAM,qBAAqB,CAAC;AAEjD,MAAM,CAAC,IAAM,MAAM,GAAG,OAAO,CAAC;AAE9B;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,iBAAqC;IACzD,OAAO,UAAU,CACf;QACE,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,KAAK,CAAC,KAAK,CAAC,EAAZ,CAAY;YAChD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,oDAAoD,EAAjE,CAAiE,EAC/E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isEANValidator from 'validator/lib/isEAN';\n\nexport const IS_EAN = 'isEAN';\n\n/**\n * Check if the string is an EAN (European Article Number).\n * If given value is not a string, then it returns false.\n */\nexport function isEAN(value: unknown): boolean {\n  return typeof value === 'string' && isEANValidator(value);\n}\n\n/**\n * Check if the string is an EAN (European Article Number).\n * If given value is not a string, then it returns false.\n */\nexport function IsEAN(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_EAN,\n      validator: {\n        validate: (value, args): boolean => isEAN(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be an EAN (European Article Number)',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}