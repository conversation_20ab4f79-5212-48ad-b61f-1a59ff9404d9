{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "names": ["ComponentMod", "nH<PERSON><PERSON>", "self", "Document", "appMod", "errorMod", "error500Mod", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "setReferenceManifestsSingleton", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "createServerModuleMap", "pageName", "render", "getRender", "pagesType", "PAGE_TYPES", "APP", "dev", "page", "pageMod", "renderToHTML", "isServerComponent", "serverActions", "config", "nextConfig", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "opts", "adapter", "IncrementalCache", "handler"], "mappings": ";;;;;;;;;;;;;;;IAoFaA,YAAY;eAAZA;;IAEb,OAMC;eANuBC;;;QAtFjB;yBACiB;wBACE;kCACO;2BAEoB;sEAC5B;2BAME;iCACoB;6BACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BlBC;AAvBpB,0CAA0C;AAE1C,MAAMC,WAAyB;AAC/B,MAAMC,SAAS;AACf,MAAMC,WAAW;AACjB,MAAMC,cAAc;AAQpB,oBAAoB;AACpB,2BAA2B;AAC3B,aAAa;AACb,uBAAuB;AACvB,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BV,KAAKW,gBAAgB;AAC1D,MAAMC,wBAAwBP,eAAeL,KAAKa,yBAAyB;AAC3E,MAAMC,eAAcd,uBAAAA,KAAKe,cAAc,qBAAnBf,oBAAqB,CAAC,WAAW;AACrD,MAAMgB,oBAAoBX,eAAeL,KAAKiB,qBAAqB;AACnE,MAAMC,+BAA+BC,aACjCd,eAAeL,KAAKoB,gCAAgC,IACpDX;AACJ,MAAMY,mBAAmBhB,eAAeL,KAAKsB,oBAAoB;AAEjE,MAAMC,4BACJlB,eAAeL,KAAKwB,qCAAqC,KAAK,EAAE;AAElE,IAAIV,eAAeE,mBAAmB;IACpCS,IAAAA,+CAA8B,EAAC;QAC7BC,yBAAyBZ;QACzBa,uBAAuBX;QACvBY,iBAAiBC,IAAAA,kCAAqB,EAAC;YACrCF,uBAAuBX;YACvBc,UAAU;QACZ;IACF;AACF;AAEA,MAAMC,SAASC,IAAAA,iBAAS,EAAC;IACvBC,WAAWC,qBAAU,CAACC,GAAG;IACzBC;IACAC,MAAM;IACNnC;IACAoC,SAAAA;IACAnC;IACAC;IACAH;IACAS;IACA6B,cAAAA,+BAAY;IACZ3B;IACAc,yBAAyBc,oBAAoB1B,cAAc;IAC3Da,uBAAuBa,oBAAoBxB,oBAAoB;IAC/DyB,eAAeD,oBAAoBC,gBAAgBhC;IACnDS;IACAwB,QAAQC;IACRC,SAASC,QAAQC,GAAG,CAACC,eAAe;IACpC1B;IACA2B;IACAzB;AACF;AAEO,MAAMzB,eAAewC;AAEb,SAASvC,SAASkD,IAA4C;IAC3E,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACPE,kBAAAA,kCAAgB;QAChBC,SAASrB;IACX;AACF"}