{"version": 3, "file": "parse.js", "names": ["_parse", "_interopRequireDefault", "require", "_normalizeArguments2", "e", "__esModule", "parseNumber", "_normalizeArguments", "normalizeArguments", "arguments", "text", "options", "metadata", "_parseNumber"], "sources": ["../../source/legacy/parse.js"], "sourcesContent": ["import _parseNumber from '../parse.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function parseNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _parseNumber(text, options, metadata)\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAyD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE1C,SAASE,WAAWA,CAAA,EAAG;EACrC,IAAAC,mBAAA,GAAoC,IAAAC,+BAAkB,EAACC,SAAS,CAAC;IAAzDC,IAAI,GAAAH,mBAAA,CAAJG,IAAI;IAAEC,OAAO,GAAAJ,mBAAA,CAAPI,OAAO;IAAEC,QAAQ,GAAAL,mBAAA,CAARK,QAAQ;EAC/B,OAAO,IAAAC,iBAAY,EAACH,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAC7C", "ignoreList": []}