{"version": 3, "file": "findPhoneNumbers.js", "names": ["_findPhoneNumbersInitialImplementation", "_interopRequireWildcard", "require", "_normalizeArguments3", "_interopRequireDefault", "e", "__esModule", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "_typeof", "has", "get", "set", "_t", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "findPhoneNumbers", "_normalizeArguments", "normalizeArguments", "arguments", "text", "options", "metadata", "_findPhoneNumbers", "searchPhoneNumbers", "_normalizeArguments2", "_searchPhoneNumbers"], "sources": ["../../source/legacy/findPhoneNumbers.js"], "sourcesContent": ["// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport _findPhoneNumbers, { searchPhoneNumbers as _searchPhoneNumbers } from './findPhoneNumbersInitialImplementation.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _findPhoneNumbers(text, options, metadata)\r\n}\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport function searchPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _searchPhoneNumbers(text, options, metadata)\r\n}"], "mappings": ";;;;;;;;AAGA,IAAAA,sCAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAyD,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,mBAAAT,CAAA,iBAAAA,CAAA,gBAAAU,OAAA,CAAAV,CAAA,0BAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,cAAAM,EAAA,IAAAd,CAAA,gBAAAc,EAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,EAAA,OAAAP,CAAA,IAAAD,CAAA,GAAAW,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAnB,CAAA,EAAAc,EAAA,OAAAP,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAM,EAAA,EAAAP,CAAA,IAAAC,CAAA,CAAAM,EAAA,IAAAd,CAAA,CAAAc,EAAA,WAAAN,CAAA,KAAAR,CAAA,EAAAE,CAAA;AAJzD;AACA;;AAKe,SAASkB,gBAAgBA,CAAA,EACxC;EACC,IAAAC,mBAAA,GAAoC,IAAAC,+BAAkB,EAACC,SAAS,CAAC;IAAzDC,IAAI,GAAAH,mBAAA,CAAJG,IAAI;IAAEC,OAAO,GAAAJ,mBAAA,CAAPI,OAAO;IAAEC,QAAQ,GAAAL,mBAAA,CAARK,QAAQ;EAC/B,OAAO,IAAAC,iDAAiB,EAACH,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAClD;;AAEA;AACA;AACA;AACO,SAASE,kBAAkBA,CAAA,EAClC;EACC,IAAAC,oBAAA,GAAoC,IAAAP,+BAAkB,EAACC,SAAS,CAAC;IAAzDC,IAAI,GAAAK,oBAAA,CAAJL,IAAI;IAAEC,OAAO,GAAAI,oBAAA,CAAPJ,OAAO;IAAEC,QAAQ,GAAAG,oBAAA,CAARH,QAAQ;EAC/B,OAAO,IAAAI,yDAAmB,EAACN,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACpD", "ignoreList": []}