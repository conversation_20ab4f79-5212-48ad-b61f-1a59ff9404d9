{"version": 3, "file": "RegExpCache.js", "names": ["_LR<PERSON>ache", "_interopRequireDefault", "require", "e", "__esModule", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "a", "n", "TypeError", "_defineProperties", "r", "t", "length", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "i", "_toPrimitive", "toPrimitive", "call", "String", "Number", "RegExpCache", "exports", "size", "cache", "L<PERSON><PERSON><PERSON>", "value", "getPatternForRegExp", "pattern", "regExp", "get", "RegExp", "put"], "sources": ["../../source/findNumbers/RegExpCache.js"], "sourcesContent": ["import LRUCache from './LRUCache.js'\r\n\r\n// A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\r\n// countries being used for the same doc with ~10 patterns for each country. Some pages will have\r\n// a lot more countries in use, but typically fewer numbers for each so expanding the cache for\r\n// that use-case won't have a lot of benefit.\r\nexport default class RegExpCache {\r\n\tconstructor(size) {\r\n\t\tthis.cache = new LRUCache(size)\r\n\t}\r\n\r\n\tgetPatternForRegExp(pattern) {\r\n\t\tlet regExp = this.cache.get(pattern)\r\n\t\tif (!regExp) {\r\n\t\t\tregExp = new RegExp('^' + pattern)\r\n\t\t\tthis.cache.put(pattern, regExp)\r\n\t\t}\r\n\t\treturn regExp\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAoC,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,gBAAAC,CAAA,EAAAC,CAAA,UAAAD,CAAA,YAAAC,CAAA,aAAAC,SAAA;AAAA,SAAAC,kBAAAZ,CAAA,EAAAa,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAD,CAAA,CAAAE,MAAA,EAAAD,CAAA,UAAAX,CAAA,GAAAU,CAAA,CAAAC,CAAA,GAAAX,CAAA,CAAAa,UAAA,GAAAb,CAAA,CAAAa,UAAA,QAAAb,CAAA,CAAAc,YAAA,kBAAAd,CAAA,KAAAA,CAAA,CAAAe,QAAA,QAAAC,MAAA,CAAAC,cAAA,CAAApB,CAAA,EAAAqB,cAAA,CAAAlB,CAAA,CAAAmB,GAAA,GAAAnB,CAAA;AAAA,SAAAoB,aAAAvB,CAAA,EAAAa,CAAA,EAAAC,CAAA,WAAAD,CAAA,IAAAD,iBAAA,CAAAZ,CAAA,CAAAO,SAAA,EAAAM,CAAA,GAAAC,CAAA,IAAAF,iBAAA,CAAAZ,CAAA,EAAAc,CAAA,GAAAK,MAAA,CAAAC,cAAA,CAAApB,CAAA,iBAAAkB,QAAA,SAAAlB,CAAA;AAAA,SAAAqB,eAAAP,CAAA,QAAAU,CAAA,GAAAC,YAAA,CAAAX,CAAA,gCAAAZ,OAAA,CAAAsB,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAX,CAAA,EAAAD,CAAA,oBAAAX,OAAA,CAAAY,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAd,CAAA,GAAAc,CAAA,CAAAV,MAAA,CAAAsB,WAAA,kBAAA1B,CAAA,QAAAwB,CAAA,GAAAxB,CAAA,CAAA2B,IAAA,CAAAb,CAAA,EAAAD,CAAA,gCAAAX,OAAA,CAAAsB,CAAA,UAAAA,CAAA,YAAAb,SAAA,yEAAAE,CAAA,GAAAe,MAAA,GAAAC,MAAA,EAAAf,CAAA;AAEpC;AACA;AACA;AACA;AAAA,IACqBgB,WAAW,GAAAC,OAAA;EAC/B,SAAAD,YAAYE,IAAI,EAAE;IAAAxB,eAAA,OAAAsB,WAAA;IACjB,IAAI,CAACG,KAAK,GAAG,IAAIC,oBAAQ,CAACF,IAAI,CAAC;EAChC;EAAC,OAAAT,YAAA,CAAAO,WAAA;IAAAR,GAAA;IAAAa,KAAA,EAED,SAAAC,mBAAmBA,CAACC,OAAO,EAAE;MAC5B,IAAIC,MAAM,GAAG,IAAI,CAACL,KAAK,CAACM,GAAG,CAACF,OAAO,CAAC;MACpC,IAAI,CAACC,MAAM,EAAE;QACZA,MAAM,GAAG,IAAIE,MAAM,CAAC,GAAG,GAAGH,OAAO,CAAC;QAClC,IAAI,CAACJ,KAAK,CAACQ,GAAG,CAACJ,OAAO,EAAEC,MAAM,CAAC;MAChC;MACA,OAAOA,MAAM;IACd;EAAC;AAAA", "ignoreList": []}