{"version": 3, "file": "AsYouTypeFormatter.js", "names": ["DIGIT_PLACEHOLDER", "countOccurences", "repeat", "cutAndStripNonPairedParens", "closeNonPairedParens", "stripNonPairedParens", "populateTemplateWithDigits", "formatCompleteNumber", "canFormatCompleteNumber", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseDigits", "FIRST_GROUP_PATTERN", "VALID_PUNCTUATION", "applyInternationalSeparatorStyle", "DUMMY_DIGIT", "LONGEST_NATIONAL_PHONE_NUMBER_LENGTH", "LONGEST_DUMMY_PHONE_NUMBER", "NATIONAL_PREFIX_SEPARATORS_PATTERN", "SUPPORT_LEGACY_FORMATTING_PATTERNS", "CREATE_CHARACTER_CLASS_PATTERN", "CREATE_STANDALONE_DIGIT_PATTERN", "NON_ALTERING_FORMAT_REG_EXP", "RegExp", "MIN_LEADING_DIGITS_LENGTH", "AsYouTypeFormatter", "_ref", "state", "metadata", "_classCallCheck", "resetFormat", "_createClass", "key", "value", "chosenFormat", "undefined", "template", "nationalNumberTemplate", "populatedNationalNumberTemplate", "populatedNationalNumberTemplatePosition", "reset", "numberingPlan", "isNANP", "callingCode", "matchingFormats", "formats", "nationalSignificantNumber", "narrowDownMatchingFormats", "format", "nextDigits", "_this", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "formattedCompleteNumber", "shouldTryNationalPrefixFormattingRule", "international", "nationalPrefix", "getSeparatorAfterNationalPrefix", "setNationalNumberTemplate", "replace", "lastIndexOf", "formatNationalNumberWithNextDigits", "previouslyChosenFormat", "newlyChosenFormat", "chooseFormat", "formatNextNationalNumberDigits", "getNationalDigits", "_ref2", "_this2", "leadingDigits", "leadingDigitsPatternIndex", "length", "filter", "formatSuits", "formatMatches", "indexOf", "usesNationalPrefix", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefixIsMandatoryWhenFormattingInNationalFormat", "leadingDigitsPatternsCount", "leadingDigitsPatterns", "Math", "min", "leadingDigitsPattern", "match", "allowOverflow", "error", "console", "concat", "test", "getFormatFormat", "internationalFormat", "_this3", "_loop", "_step2", "createTemplateForFormat", "_", "_ret", "_iterator2", "slice", "pattern", "getTemplateForFormat", "nationalPrefixFormattingRule", "getInternationalPrefixBeforeCountryCallingCode", "_ref3", "options", "IDDPrefix", "missingPlus", "spacing", "getTemplate", "index", "i", "internationalPrefix", "getDigitsWithoutInternationalPrefix", "_ref4", "complexPrefixBeforeNationalSignificantNumber", "digits", "strictPattern", "nationalNumberDummyDigits", "numberFormat", "nationalPrefixIncludedInTemplate", "numberFormatWithNationalPrefix", "result", "_ref5", "default"], "sources": ["../source/AsYouTypeFormatter.js"], "sourcesContent": ["import {\r\n\tDIGIT_PLACEHOLDER,\r\n\tcountOccurences,\r\n\trepeat,\r\n\tcutAndStripNonPairedParens,\r\n\tcloseNonPairedParens,\r\n\tstripNonPairedParens,\r\n\tpopulateTemplateWithDigits\r\n} from './AsYouTypeFormatter.util.js'\r\n\r\nimport formatCompleteNumber, {\r\n\tcanFormatCompleteNumber\r\n} from './AsYouTypeFormatter.complete.js'\r\n\r\nimport PatternMatcher from './AsYouTypeFormatter.PatternMatcher.js'\r\n\r\nimport parseDigits from './helpers/parseDigits.js'\r\nexport { DIGIT_PLACEHOLDER } from './AsYouTypeFormatter.util.js'\r\nimport { FIRST_GROUP_PATTERN } from './helpers/formatNationalNumberUsingFormat.js'\r\nimport { VALID_PUNCTUATION } from './constants.js'\r\nimport applyInternationalSeparatorStyle from './helpers/applyInternationalSeparatorStyle.js'\r\n\r\n// Used in phone number format template creation.\r\n// Could be any digit, I guess.\r\nconst DUMMY_DIGIT = '9'\r\n// I don't know why is it exactly `15`\r\nconst LONGEST_NATIONAL_PHONE_NUMBER_LENGTH = 15\r\n// Create a phone number consisting only of the digit 9 that matches the\r\n// `number_pattern` by applying the pattern to the \"longest phone number\" string.\r\nconst LONGEST_DUMMY_PHONE_NUMBER = repeat(DUMMY_DIGIT, LONGEST_NATIONAL_PHONE_NUMBER_LENGTH)\r\n\r\n// A set of characters that, if found in a national prefix formatting rules, are an indicator to\r\n// us that we should separate the national prefix from the number when formatting.\r\nconst NATIONAL_PREFIX_SEPARATORS_PATTERN = /[- ]/\r\n\r\n// Deprecated: Google has removed some formatting pattern related code from their repo.\r\n// https://github.com/googlei18n/libphonenumber/commit/a395b4fef3caf57c4bc5f082e1152a4d2bd0ba4c\r\n// \"We no longer have numbers in formatting matching patterns, only \\d.\"\r\n// Because this library supports generating custom metadata\r\n// some users may still be using old metadata so the relevant\r\n// code seems to stay until some next major version update.\r\nconst SUPPORT_LEGACY_FORMATTING_PATTERNS = true\r\n\r\n// A pattern that is used to match character classes in regular expressions.\r\n// An example of a character class is \"[1-4]\".\r\nconst CREATE_CHARACTER_CLASS_PATTERN = SUPPORT_LEGACY_FORMATTING_PATTERNS && (() => /\\[([^\\[\\]])*\\]/g)\r\n\r\n// Any digit in a regular expression that actually denotes a digit. For\r\n// example, in the regular expression \"80[0-2]\\d{6,10}\", the first 2 digits\r\n// (8 and 0) are standalone digits, but the rest are not.\r\n// Two look-aheads are needed because the number following \\\\d could be a\r\n// two-digit number, since the phone number can be as long as 15 digits.\r\nconst CREATE_STANDALONE_DIGIT_PATTERN = SUPPORT_LEGACY_FORMATTING_PATTERNS && (() => /\\d(?=[^,}][^,}])/g)\r\n\r\n// A regular expression that is used to determine if a `format` is\r\n// suitable to be used in the \"as you type formatter\".\r\n// A `format` is suitable when the resulting formatted number has\r\n// the same digits as the user has entered.\r\n//\r\n// In the simplest case, that would mean that the format\r\n// doesn't add any additional digits when formatting a number.\r\n// Google says that it also shouldn't add \"star\" (`*`) characters,\r\n// like it does in some Israeli formats.\r\n// Such basic format would only contain \"valid punctuation\"\r\n// and \"captured group\" identifiers ($1, $2, etc).\r\n//\r\n// An example of a format that adds additional digits:\r\n//\r\n// Country: `AR` (Argentina).\r\n// Format:\r\n// {\r\n//    \"pattern\": \"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\r\n//    \"leading_digits_patterns\": [\"91\"],\r\n//    \"national_prefix_formatting_rule\": \"0$1\",\r\n//    \"format\": \"$2 15-$3-$4\",\r\n//    \"international_format\": \"$1 $2 $3-$4\"\r\n// }\r\n//\r\n// In the format above, the `format` adds `15` to the digits when formatting a number.\r\n// A sidenote: this format actually is suitable because `national_prefix_for_parsing`\r\n// has previously removed `15` from a national number, so re-adding `15` in `format`\r\n// doesn't actually result in any extra digits added to user's input.\r\n// But verifying that would be a complex procedure, so the code chooses a simpler path:\r\n// it simply filters out all `format`s that contain anything but \"captured group\" ids.\r\n//\r\n// This regular expression is called `ELIGIBLE_FORMAT_PATTERN` in Google's\r\n// `libphonenumber` code.\r\n//\r\nconst NON_ALTERING_FORMAT_REG_EXP = new RegExp(\r\n\t'[' + VALID_PUNCTUATION + ']*' +\r\n\t// Google developers say:\r\n\t// \"We require that the first matching group is present in the\r\n\t//  output pattern to ensure no data is lost while formatting.\"\r\n\t'\\\\$1' +\r\n\t'[' + VALID_PUNCTUATION + ']*' +\r\n\t'(\\\\$\\\\d[' + VALID_PUNCTUATION + ']*)*' +\r\n\t'$'\r\n)\r\n\r\n// This is the minimum length of the leading digits of a phone number\r\n// to guarantee the first \"leading digits pattern\" for a phone number format\r\n// to be preemptive.\r\nconst MIN_LEADING_DIGITS_LENGTH = 3\r\n\r\nexport default class AsYouTypeFormatter {\r\n\tconstructor({\r\n\t\tstate,\r\n\t\tmetadata\r\n\t}) {\r\n\t\tthis.metadata = metadata\r\n\t\tthis.resetFormat()\r\n\t}\r\n\r\n\tresetFormat() {\r\n\t\tthis.chosenFormat = undefined\r\n\t\tthis.template = undefined\r\n\t\tthis.nationalNumberTemplate = undefined\r\n\t\tthis.populatedNationalNumberTemplate = undefined\r\n\t\tthis.populatedNationalNumberTemplatePosition = -1\r\n\t}\r\n\r\n\treset(numberingPlan, state) {\r\n\t\tthis.resetFormat()\r\n\t\tif (numberingPlan) {\r\n\t\t\tthis.isNANP = numberingPlan.callingCode() === '1'\r\n\t\t\tthis.matchingFormats = numberingPlan.formats()\r\n\t\t\tif (state.nationalSignificantNumber) {\r\n\t\t\t\tthis.narrowDownMatchingFormats(state)\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tthis.isNANP = undefined\r\n\t\t\tthis.matchingFormats = []\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Formats an updated phone number.\r\n\t * @param  {string} nextDigits — Additional phone number digits.\r\n\t * @param  {object} state — `AsYouType` state.\r\n\t * @return {[string]} Returns undefined if the updated phone number can't be formatted using any of the available formats.\r\n\t */\r\n\tformat(nextDigits, state) {\r\n\t\t// See if the phone number digits can be formatted as a complete phone number.\r\n\t\t// If not, use the results from `formatNationalNumberWithNextDigits()`,\r\n\t\t// which formats based on the chosen formatting pattern.\r\n\t\t//\r\n\t\t// Attempting to format complete phone number first is how it's done\r\n\t\t// in Google's `libphonenumber`, so this library just follows it.\r\n\t\t// Google's `libphonenumber` code doesn't explain in detail why does it\r\n\t\t// attempt to format digits as a complete phone number\r\n\t\t// instead of just going with a previoulsy (or newly) chosen `format`:\r\n\t\t//\r\n\t\t// \"Checks to see if there is an exact pattern match for these digits.\r\n\t\t//  If so, we should use this instead of any other formatting template\r\n\t\t//  whose leadingDigitsPattern also matches the input.\"\r\n\t\t//\r\n\t\tif (canFormatCompleteNumber(state.nationalSignificantNumber, this.metadata)) {\r\n\t\t\tfor (const format of this.matchingFormats) {\r\n\t\t\t\tconst formattedCompleteNumber = formatCompleteNumber(\r\n\t\t\t\t\tstate,\r\n\t\t\t\t\tformat,\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tmetadata: this.metadata,\r\n\t\t\t\t\t\tshouldTryNationalPrefixFormattingRule: (format) => this.shouldTryNationalPrefixFormattingRule(format, {\r\n\t\t\t\t\t\t\tinternational: state.international,\r\n\t\t\t\t\t\t\tnationalPrefix: state.nationalPrefix\r\n\t\t\t\t\t\t}),\r\n\t\t\t\t\t\tgetSeparatorAfterNationalPrefix: (format) => this.getSeparatorAfterNationalPrefix(format)\r\n\t\t\t\t\t}\r\n\t\t\t\t)\r\n\t\t\t\tif (formattedCompleteNumber) {\r\n\t\t\t\t\tthis.resetFormat()\r\n\t\t\t\t\tthis.chosenFormat = format\r\n\t\t\t\t\tthis.setNationalNumberTemplate(formattedCompleteNumber.replace(/\\d/g, DIGIT_PLACEHOLDER), state)\r\n\t\t\t\t\tthis.populatedNationalNumberTemplate = formattedCompleteNumber\r\n\t\t\t\t\t// With a new formatting template, the matched position\r\n\t\t\t\t\t// using the old template needs to be reset.\r\n\t\t\t\t\tthis.populatedNationalNumberTemplatePosition = this.template.lastIndexOf(DIGIT_PLACEHOLDER)\r\n\t\t\t\t\treturn formattedCompleteNumber\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Format the digits as a partial (incomplete) phone number\r\n\t\t// using the previously chosen formatting pattern (or a newly chosen one).\r\n\t\treturn this.formatNationalNumberWithNextDigits(nextDigits, state)\r\n\t}\r\n\r\n\t// Formats the next phone number digits.\r\n\tformatNationalNumberWithNextDigits(nextDigits, state) {\r\n\t\tconst previouslyChosenFormat = this.chosenFormat\r\n\r\n\t\t// Choose a format from the list of matching ones.\r\n\t\tconst newlyChosenFormat = this.chooseFormat(state)\r\n\r\n\t\tif (newlyChosenFormat) {\r\n\t\t\tif (newlyChosenFormat === previouslyChosenFormat) {\r\n\t\t\t\t// If it can format the next (current) digits\r\n\t\t\t\t// using the previously chosen phone number format\r\n\t\t\t\t// then return the updated formatted number.\r\n\t\t\t\treturn this.formatNextNationalNumberDigits(nextDigits)\r\n\t\t\t} else {\r\n\t\t\t\t// If a more appropriate phone number format\r\n\t\t\t\t// has been chosen for these \"leading digits\",\r\n\t\t\t\t// then re-format the national phone number part\r\n\t\t\t\t// using the newly selected format.\r\n\t\t\t\treturn this.formatNextNationalNumberDigits(state.getNationalDigits())\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tnarrowDownMatchingFormats({\r\n\t\tnationalSignificantNumber,\r\n\t\tnationalPrefix,\r\n\t\tinternational\r\n\t}) {\r\n\t\tconst leadingDigits = nationalSignificantNumber\r\n\r\n\t\t// \"leading digits\" pattern list starts with a\r\n\t\t// \"leading digits\" pattern fitting a maximum of 3 leading digits.\r\n\t\t// So, after a user inputs 3 digits of a national (significant) phone number\r\n\t\t// this national (significant) number can already be formatted.\r\n\t\t// The next \"leading digits\" pattern is for 4 leading digits max,\r\n\t\t// and the \"leading digits\" pattern after it is for 5 leading digits max, etc.\r\n\r\n\t\t// This implementation is different from Google's\r\n\t\t// in that it searches for a fitting format\r\n\t\t// even if the user has entered less than\r\n\t\t// `MIN_LEADING_DIGITS_LENGTH` digits of a national number.\r\n\t\t// Because some leading digit patterns already match for a single first digit.\r\n\t\tlet leadingDigitsPatternIndex = leadingDigits.length - MIN_LEADING_DIGITS_LENGTH\r\n\t\tif (leadingDigitsPatternIndex < 0) {\r\n\t\t\tleadingDigitsPatternIndex = 0\r\n\t\t}\r\n\r\n\t\tthis.matchingFormats = this.matchingFormats.filter(\r\n\t\t\tformat => this.formatSuits(format, international, nationalPrefix)\r\n\t\t\t\t&& this.formatMatches(format, leadingDigits, leadingDigitsPatternIndex)\r\n\t\t)\r\n\r\n\t\t// If there was a phone number format chosen\r\n\t\t// and it no longer holds given the new leading digits then reset it.\r\n\t\t// The test for this `if` condition is marked as:\r\n\t\t// \"Reset a chosen format when it no longer holds given the new leading digits\".\r\n\t\t// To construct a valid test case for this one can find a country\r\n\t\t// in `PhoneNumberMetadata.xml` yielding one format for 3 `<leadingDigits>`\r\n\t\t// and yielding another format for 4 `<leadingDigits>` (Australia in this case).\r\n\t\tif (this.chosenFormat && this.matchingFormats.indexOf(this.chosenFormat) === -1) {\r\n\t\t\tthis.resetFormat()\r\n\t\t}\r\n\t}\r\n\r\n\tformatSuits(format, international, nationalPrefix) {\r\n\t\t// When a prefix before a national (significant) number is\r\n\t\t// simply a national prefix, then it's parsed as `this.nationalPrefix`.\r\n\t\t// In more complex cases, a prefix before national (significant) number\r\n\t\t// could include a national prefix as well as some \"capturing groups\",\r\n\t\t// and in that case there's no info whether a national prefix has been parsed.\r\n\t\t// If national prefix is not used when formatting a phone number\r\n\t\t// using this format, but a national prefix has been entered by the user,\r\n\t\t// and was extracted, then discard such phone number format.\r\n\t\t// In Google's \"AsYouType\" formatter code, the equivalent would be this part:\r\n\t\t// https://github.com/google/libphonenumber/blob/0a45cfd96e71cad8edb0e162a70fcc8bd9728933/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L175-L184\r\n\t\tif (nationalPrefix &&\r\n\t\t\t!format.usesNationalPrefix() &&\r\n\t\t\t// !format.domesticCarrierCodeFormattingRule() &&\r\n\t\t\t!format.nationalPrefixIsOptionalWhenFormattingInNationalFormat()) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\t// If national prefix is mandatory for this phone number format\r\n\t\t// and there're no guarantees that a national prefix is present in user input\r\n\t\t// then discard this phone number format as not suitable.\r\n\t\t// In Google's \"AsYouType\" formatter code, the equivalent would be this part:\r\n\t\t// https://github.com/google/libphonenumber/blob/0a45cfd96e71cad8edb0e162a70fcc8bd9728933/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L185-L193\r\n\t\tif (!international &&\r\n\t\t\t!nationalPrefix &&\r\n\t\t\tformat.nationalPrefixIsMandatoryWhenFormattingInNationalFormat()) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\treturn true\r\n\t}\r\n\r\n\tformatMatches(format, leadingDigits, leadingDigitsPatternIndex) {\r\n\t\tconst leadingDigitsPatternsCount = format.leadingDigitsPatterns().length\r\n\r\n\t\t// If this format is not restricted to a certain\r\n\t\t// leading digits pattern then it fits.\r\n\t\t// The test case could be found by searching for \"leadingDigitsPatternsCount === 0\".\r\n\t\tif (leadingDigitsPatternsCount === 0) {\r\n\t\t\treturn true\r\n\t\t}\r\n\r\n\t\t// Start narrowing down the list of possible formats based on the leading digits.\r\n\t\t// (only previously matched formats take part in the narrowing down process)\r\n\r\n\t\t// `leading_digits_patterns` start with 3 digits min\r\n\t\t// and then go up from there one digit at a time.\r\n\t\tleadingDigitsPatternIndex = Math.min(leadingDigitsPatternIndex, leadingDigitsPatternsCount - 1)\r\n\t\tconst leadingDigitsPattern = format.leadingDigitsPatterns()[leadingDigitsPatternIndex]\r\n\r\n\t\t// Google imposes a requirement on the leading digits\r\n\t\t// to be minimum 3 digits long in order to be eligible\r\n\t\t// for checking those with a leading digits pattern.\r\n\t\t//\r\n\t\t// Since `leading_digits_patterns` start with 3 digits min,\r\n\t\t// Google's original `libphonenumber` library only starts\r\n\t\t// excluding any non-matching formats only when the\r\n\t\t// national number entered so far is at least 3 digits long,\r\n\t\t// otherwise format matching would give false negatives.\r\n\t\t//\r\n\t\t// For example, when the digits entered so far are `2`\r\n\t\t// and the leading digits pattern is `21` –\r\n\t\t// it's quite obvious in this case that the format could be the one\r\n\t\t// but due to the absence of further digits it would give false negative.\r\n\t\t//\r\n\t\t// Also, `leading_digits_patterns` doesn't always correspond to a single\r\n\t\t// digits count. For example, `60|8` pattern would already match `8`\r\n\t\t// but the `60` part would require having at least two leading digits,\r\n\t\t// so the whole pattern would require inputting two digits first in order to\r\n\t\t// decide on whether it matches the input, even when the input is \"80\".\r\n\t\t//\r\n\t\t// This library — `libphonenumber-js` — allows filtering by `leading_digits_patterns`\r\n\t\t// even when there's only 1 or 2 digits of the national (significant) number.\r\n\t\t// To do that, it uses a non-strict pattern matcher written specifically for that.\r\n\t\t//\r\n\t\tif (leadingDigits.length < MIN_LEADING_DIGITS_LENGTH) {\r\n\t\t\t// Before leading digits < 3 matching was implemented:\r\n\t\t\t// return true\r\n\t\t\t//\r\n\t\t\t// After leading digits < 3 matching was implemented:\r\n\t\t\ttry {\r\n\t\t\t\treturn new PatternMatcher(leadingDigitsPattern).match(leadingDigits, { allowOverflow: true }) !== undefined\r\n\t\t\t} catch (error) /* istanbul ignore next */ {\r\n\t\t\t\t// There's a slight possibility that there could be some undiscovered bug\r\n\t\t\t\t// in the pattern matcher code. Since the \"leading digits < 3 matching\"\r\n\t\t\t\t// feature is not \"essential\" for operation, it can fall back to the old way\r\n\t\t\t\t// in case of any issues rather than halting the application's execution.\r\n\t\t\t\tconsole.error(error)\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// If at least `MIN_LEADING_DIGITS_LENGTH` digits of a national number are\r\n\t\t// available then use the usual regular expression matching.\r\n\t\t//\r\n\t\t// The whole pattern is wrapped in round brackets (`()`) because\r\n\t\t// the pattern can use \"or\" operator (`|`) at the top level of the pattern.\r\n\t\t//\r\n\t\treturn new RegExp(`^(${leadingDigitsPattern})`).test(leadingDigits)\r\n\t}\r\n\r\n\tgetFormatFormat(format, international) {\r\n\t\treturn international ? format.internationalFormat() : format.format()\r\n\t}\r\n\r\n\tchooseFormat(state) {\r\n\t\t// When there are multiple available formats, the formatter uses the first\r\n\t\t// format where a formatting template could be created.\r\n\t\t//\r\n\t\t// For some weird reason, `istanbul` says \"else path not taken\"\r\n\t\t// for the `for of` line below. Supposedly that means that\r\n\t\t// the loop doesn't ever go over the last element in the list.\r\n\t\t// That's true because there always is `this.chosenFormat`\r\n\t\t// when `this.matchingFormats` is non-empty.\r\n\t\t// And, for some weird reason, it doesn't think that the case\r\n\t\t// with empty `this.matchingFormats` qualifies for a valid \"else\" path.\r\n\t\t// So simply muting this `istanbul` warning.\r\n\t\t// It doesn't skip the contents of the `for of` loop,\r\n\t\t// it just skips the `for of` line.\r\n\t\t//\r\n\t\t/* istanbul ignore next */\r\n\t\tfor (const format of this.matchingFormats.slice()) {\r\n\t\t\t// If this format is currently being used\r\n\t\t\t// and is still suitable, then stick to it.\r\n\t\t\tif (this.chosenFormat === format) {\r\n\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t\t// Sometimes, a formatting rule inserts additional digits in a phone number,\r\n\t\t\t// and \"as you type\" formatter can't do that: it should only use the digits\r\n\t\t\t// that the user has input.\r\n\t\t\t//\r\n\t\t\t// For example, in Argentina, there's a format for mobile phone numbers:\r\n\t\t\t//\r\n\t\t\t// {\r\n\t\t\t//    \"pattern\": \"(\\\\d)(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\r\n\t\t\t//    \"leading_digits_patterns\": [\"91\"],\r\n\t\t\t//    \"national_prefix_formatting_rule\": \"0$1\",\r\n\t\t\t//    \"format\": \"$2 15-$3-$4\",\r\n\t\t\t//    \"international_format\": \"$1 $2 $3-$4\"\r\n\t\t\t// }\r\n\t\t\t//\r\n\t\t\t// In that format, `international_format` is used instead of `format`\r\n\t\t\t// because `format` inserts `15` in the formatted number,\r\n\t\t\t// and `AsYouType` formatter should only use the digits\r\n\t\t\t// the user has actually input, without adding any extra digits.\r\n\t\t\t// In this case, it wouldn't make a difference, because the `15`\r\n\t\t\t// is first stripped when applying `national_prefix_for_parsing`\r\n\t\t\t// and then re-added when using `format`, so in reality it doesn't\r\n\t\t\t// add any new digits to the number, but to detect that, the code\r\n\t\t\t// would have to be more complex: it would have to try formatting\r\n\t\t\t// the digits using the format and then see if any digits have\r\n\t\t\t// actually been added or removed, and then, every time a new digit\r\n\t\t\t// is input, it should re-check whether the chosen format doesn't\r\n\t\t\t// alter the digits.\r\n\t\t\t//\r\n\t\t\t// Google's code doesn't go that far, and so does this library:\r\n\t\t\t// it simply requires that a `format` doesn't add any additonal\r\n\t\t\t// digits to user's input.\r\n\t\t\t//\r\n\t\t\t// Also, people in general should move from inputting phone numbers\r\n\t\t\t// in national format (possibly with national prefixes)\r\n\t\t\t// and use international phone number format instead:\r\n\t\t\t// it's a logical thing in the modern age of mobile phones,\r\n\t\t\t// globalization and the internet.\r\n\t\t\t//\r\n\t\t\t/* istanbul ignore if */\r\n\t\t\tif (!NON_ALTERING_FORMAT_REG_EXP.test(this.getFormatFormat(format, state.international))) {\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\t\t\tif (!this.createTemplateForFormat(format, state)) {\r\n\t\t\t\t// Remove the format if it can't generate a template.\r\n\t\t\t\tthis.matchingFormats = this.matchingFormats.filter(_ => _ !== format)\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\t\t\tthis.chosenFormat = format\r\n\t\t\tbreak\r\n\t\t}\r\n\t\tif (!this.chosenFormat) {\r\n\t\t\t// No format matches the national (significant) phone number.\r\n\t\t\tthis.resetFormat()\r\n\t\t}\r\n\t\treturn this.chosenFormat\r\n\t}\r\n\r\n\tcreateTemplateForFormat(format, state) {\r\n\t\t// The formatter doesn't format numbers when numberPattern contains '|', e.g.\r\n\t\t// (20|3)\\d{4}. In those cases we quickly return.\r\n\t\t// (Though there's no such format in current metadata)\r\n\t\t/* istanbul ignore if */\r\n\t\tif (SUPPORT_LEGACY_FORMATTING_PATTERNS && format.pattern().indexOf('|') >= 0) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Get formatting template for this phone number format\r\n\t\tconst template = this.getTemplateForFormat(format, state)\r\n\t\t// If the national number entered is too long\r\n\t\t// for any phone number format, then abort.\r\n\t\tif (template) {\r\n\t\t\tthis.setNationalNumberTemplate(template, state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\tgetSeparatorAfterNationalPrefix(format) {\r\n\t\t// `US` metadata doesn't have a `national_prefix_formatting_rule`,\r\n\t\t// so the `if` condition below doesn't apply to `US`,\r\n\t\t// but in reality there shoudl be a separator\r\n\t\t// between a national prefix and a national (significant) number.\r\n\t\t// So `US` national prefix separator is a \"special\" \"hardcoded\" case.\r\n\t\tif (this.isNANP) {\r\n\t\t\treturn ' '\r\n\t\t}\r\n\t\t// If a `format` has a `national_prefix_formatting_rule`\r\n\t\t// and that rule has a separator after a national prefix,\r\n\t\t// then it means that there should be a separator\r\n\t\t// between a national prefix and a national (significant) number.\r\n\t\tif (format &&\r\n\t\t\tformat.nationalPrefixFormattingRule() &&\r\n\t\t\tNATIONAL_PREFIX_SEPARATORS_PATTERN.test(format.nationalPrefixFormattingRule())) {\r\n\t\t\treturn ' '\r\n\t\t}\r\n\t\t// At this point, there seems to be no clear evidence that\r\n\t\t// there should be a separator between a national prefix\r\n\t\t// and a national (significant) number. So don't insert one.\r\n\t\treturn ''\r\n\t}\r\n\r\n\tgetInternationalPrefixBeforeCountryCallingCode({ IDDPrefix, missingPlus }, options) {\r\n\t\tif (IDDPrefix) {\r\n\t\t\treturn options && options.spacing === false ? IDDPrefix : IDDPrefix + ' '\r\n\t\t}\r\n\t\tif (missingPlus) {\r\n\t\t\treturn ''\r\n\t\t}\r\n\t\treturn '+'\r\n\t}\r\n\r\n\tgetTemplate(state) {\r\n\t\tif (!this.template) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// `this.template` holds the template for a \"complete\" phone number.\r\n\t\t// The currently entered phone number is most likely not \"complete\",\r\n\t\t// so trim all non-populated digits.\r\n\t\tlet index = -1\r\n\t\tlet i = 0\r\n\t\tconst internationalPrefix = state.international ? this.getInternationalPrefixBeforeCountryCallingCode(state, { spacing: false }) : ''\r\n\t\twhile (i < internationalPrefix.length + state.getDigitsWithoutInternationalPrefix().length) {\r\n\t\t\tindex = this.template.indexOf(DIGIT_PLACEHOLDER, index + 1)\r\n\t\t\ti++\r\n\t\t}\r\n\t\treturn cutAndStripNonPairedParens(this.template, index + 1)\r\n\t}\r\n\r\n\tsetNationalNumberTemplate(template, state) {\r\n\t\tthis.nationalNumberTemplate = template\r\n\t\tthis.populatedNationalNumberTemplate = template\r\n\t\t// With a new formatting template, the matched position\r\n\t\t// using the old template needs to be reset.\r\n\t\tthis.populatedNationalNumberTemplatePosition = -1\r\n\t\t// For convenience, the public `.template` property\r\n\t\t// contains the whole international number\r\n\t\t// if the phone number being input is international:\r\n\t\t// 'x' for the '+' sign, 'x'es for the country phone code,\r\n\t\t// a spacebar and then the template for the formatted national number.\r\n\t\tif (state.international) {\r\n\t\t\tthis.template =\r\n\t\t\t\tthis.getInternationalPrefixBeforeCountryCallingCode(state).replace(/[\\d\\+]/g, DIGIT_PLACEHOLDER) +\r\n\t\t\t\trepeat(DIGIT_PLACEHOLDER, state.callingCode.length) +\r\n\t\t\t\t' ' +\r\n\t\t\t\ttemplate\r\n\t\t} else {\r\n\t\t\tthis.template = template\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Generates formatting template for a national phone number,\r\n\t * optionally containing a national prefix, for a format.\r\n\t * @param  {Format} format\r\n\t * @param  {string} nationalPrefix\r\n\t * @return {string}\r\n\t */\r\n\tgetTemplateForFormat(format, {\r\n\t\tnationalSignificantNumber,\r\n\t\tinternational,\r\n\t\tnationalPrefix,\r\n\t\tcomplexPrefixBeforeNationalSignificantNumber\r\n\t}) {\r\n\t\tlet pattern = format.pattern()\r\n\r\n\t\t/* istanbul ignore else */\r\n\t\tif (SUPPORT_LEGACY_FORMATTING_PATTERNS) {\r\n\t\t\tpattern = pattern\r\n\t\t\t\t// Replace anything in the form of [..] with \\d\r\n\t\t\t\t.replace(CREATE_CHARACTER_CLASS_PATTERN(), '\\\\d')\r\n\t\t\t\t// Replace any standalone digit (not the one in `{}`) with \\d\r\n\t\t\t\t.replace(CREATE_STANDALONE_DIGIT_PATTERN(), '\\\\d')\r\n\t\t}\r\n\r\n\t\t// Generate a dummy national number (consisting of `9`s)\r\n\t\t// that fits this format's `pattern`.\r\n\t\t//\r\n\t\t// This match will always succeed,\r\n\t\t// because the \"longest dummy phone number\"\r\n\t\t// has enough length to accomodate any possible\r\n\t\t// national phone number format pattern.\r\n\t\t//\r\n\t\tlet digits = LONGEST_DUMMY_PHONE_NUMBER.match(pattern)[0]\r\n\r\n\t\t// If the national number entered is too long\r\n\t\t// for any phone number format, then abort.\r\n\t\tif (nationalSignificantNumber.length > digits.length) {\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\t// Get a formatting template which can be used to efficiently format\r\n\t\t// a partial number where digits are added one by one.\r\n\r\n\t\t// Below `strictPattern` is used for the\r\n\t\t// regular expression (with `^` and `$`).\r\n\t\t// This wasn't originally in Google's `libphonenumber`\r\n\t\t// and I guess they don't really need it\r\n\t\t// because they're not using \"templates\" to format phone numbers\r\n\t\t// but I added `strictPattern` after encountering\r\n\t\t// South Korean phone number formatting bug.\r\n\t\t//\r\n\t\t// Non-strict regular expression bug demonstration:\r\n\t\t//\r\n\t\t// this.nationalSignificantNumber : `111111111` (9 digits)\r\n\t\t//\r\n\t\t// pattern : (\\d{2})(\\d{3,4})(\\d{4})\r\n\t\t// format : `$1 $2 $3`\r\n\t\t// digits : `9999999999` (10 digits)\r\n\t\t//\r\n\t\t// '9999999999'.replace(new RegExp(/(\\d{2})(\\d{3,4})(\\d{4})/g), '$1 $2 $3') = \"99 9999 9999\"\r\n\t\t//\r\n\t\t// template : xx xxxx xxxx\r\n\t\t//\r\n\t\t// But the correct template in this case is `xx xxx xxxx`.\r\n\t\t// The template was generated incorrectly because of the\r\n\t\t// `{3,4}` variability in the `pattern`.\r\n\t\t//\r\n\t\t// The fix is, if `this.nationalSignificantNumber` has already sufficient length\r\n\t\t// to satisfy the `pattern` completely then `this.nationalSignificantNumber`\r\n\t\t// is used instead of `digits`.\r\n\r\n\t\tconst strictPattern = new RegExp('^' + pattern + '$')\r\n\t\tconst nationalNumberDummyDigits = nationalSignificantNumber.replace(/\\d/g, DUMMY_DIGIT)\r\n\r\n\t\t// If `this.nationalSignificantNumber` has already sufficient length\r\n\t\t// to satisfy the `pattern` completely then use it\r\n\t\t// instead of `digits`.\r\n\t\tif (strictPattern.test(nationalNumberDummyDigits)) {\r\n\t\t\tdigits = nationalNumberDummyDigits\r\n\t\t}\r\n\r\n\t\tlet numberFormat = this.getFormatFormat(format, international)\r\n\t\tlet nationalPrefixIncludedInTemplate\r\n\r\n\t\t// If a user did input a national prefix (and that's guaranteed),\r\n\t\t// and if a `format` does have a national prefix formatting rule,\r\n\t\t// then see if that national prefix formatting rule\r\n\t\t// prepends exactly the same national prefix the user has input.\r\n\t\t// If that's the case, then use the `format` with the national prefix formatting rule.\r\n\t\t// Otherwise, use  the `format` without the national prefix formatting rule,\r\n\t\t// and prepend a national prefix manually to it.\r\n\t\tif (this.shouldTryNationalPrefixFormattingRule(format, { international, nationalPrefix })) {\r\n\t\t\tconst numberFormatWithNationalPrefix = numberFormat.replace(\r\n\t\t\t\tFIRST_GROUP_PATTERN,\r\n\t\t\t\tformat.nationalPrefixFormattingRule()\r\n\t\t\t)\r\n\t\t\t// If `national_prefix_formatting_rule` of a `format` simply prepends\r\n\t\t\t// national prefix at the start of a national (significant) number,\r\n\t\t\t// then such formatting can be used with `AsYouType` formatter.\r\n\t\t\t// There seems to be no `else` case: everywhere in metadata,\r\n\t\t\t// national prefix formatting rule is national prefix + $1,\r\n\t\t\t// or `($1)`, in which case such format isn't even considered\r\n\t\t\t// when the user has input a national prefix.\r\n\t\t\t/* istanbul ignore else */\r\n\t\t\tif (parseDigits(format.nationalPrefixFormattingRule()) === (nationalPrefix || '') + parseDigits('$1')) {\r\n\t\t\t\tnumberFormat = numberFormatWithNationalPrefix\r\n\t\t\t\tnationalPrefixIncludedInTemplate = true\r\n\t\t\t\t// Replace all digits of the national prefix in the formatting template\r\n\t\t\t\t// with `DIGIT_PLACEHOLDER`s.\r\n\t\t\t\tif (nationalPrefix) {\r\n\t\t\t\t\tlet i = nationalPrefix.length\r\n\t\t\t\t\twhile (i > 0) {\r\n\t\t\t\t\t\tnumberFormat = numberFormat.replace(/\\d/, DIGIT_PLACEHOLDER)\r\n\t\t\t\t\t\ti--\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Generate formatting template for this phone number format.\r\n\t\tlet template = digits\r\n\t\t\t// Format the dummy phone number according to the format.\r\n\t\t\t.replace(new RegExp(pattern), numberFormat)\r\n\t\t\t// Replace each dummy digit with a DIGIT_PLACEHOLDER.\r\n\t\t\t.replace(new RegExp(DUMMY_DIGIT, 'g'), DIGIT_PLACEHOLDER)\r\n\r\n\t\t// If a prefix of a national (significant) number is not as simple\r\n\t\t// as just a basic national prefix, then just prepend such prefix\r\n\t\t// before the national (significant) number, optionally spacing\r\n\t\t// the two with a whitespace.\r\n\t\tif (!nationalPrefixIncludedInTemplate) {\r\n\t\t\tif (complexPrefixBeforeNationalSignificantNumber) {\r\n\t\t\t\t// Prepend the prefix to the template manually.\r\n\t\t\t\ttemplate = repeat(DIGIT_PLACEHOLDER, complexPrefixBeforeNationalSignificantNumber.length) +\r\n\t\t\t\t\t' ' +\r\n\t\t\t\t\ttemplate\r\n\t\t\t} else if (nationalPrefix) {\r\n\t\t\t\t// Prepend national prefix to the template manually.\r\n\t\t\t\ttemplate = repeat(DIGIT_PLACEHOLDER, nationalPrefix.length) +\r\n\t\t\t\t\tthis.getSeparatorAfterNationalPrefix(format) +\r\n\t\t\t\t\ttemplate\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (international) {\r\n\t\t\ttemplate = applyInternationalSeparatorStyle(template)\r\n\t\t}\r\n\r\n\t\treturn template\r\n\t}\r\n\r\n\tformatNextNationalNumberDigits(digits) {\r\n\t\tconst result = populateTemplateWithDigits(\r\n\t\t\tthis.populatedNationalNumberTemplate,\r\n\t\t\tthis.populatedNationalNumberTemplatePosition,\r\n\t\t\tdigits\r\n\t\t)\r\n\r\n\t\tif (!result) {\r\n\t\t\t// Reset the format.\r\n\t\t\tthis.resetFormat()\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tthis.populatedNationalNumberTemplate = result[0]\r\n\t\tthis.populatedNationalNumberTemplatePosition = result[1]\r\n\r\n\t\t// Return the formatted phone number so far.\r\n\t\treturn cutAndStripNonPairedParens(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition + 1)\r\n\r\n\t\t// The old way which was good for `input-format` but is not so good\r\n\t\t// for `react-phone-number-input`'s default input (`InputBasic`).\r\n\t\t// return closeNonPairedParens(this.populatedNationalNumberTemplate, this.populatedNationalNumberTemplatePosition + 1)\r\n\t\t// \t.replace(new RegExp(DIGIT_PLACEHOLDER, 'g'), ' ')\r\n\t}\r\n\r\n\tshouldTryNationalPrefixFormattingRule(format, { international, nationalPrefix }) {\r\n\t\tif (format.nationalPrefixFormattingRule()) {\r\n\t\t\t// In some countries, `national_prefix_formatting_rule` is `($1)`,\r\n\t\t\t// so it applies even if the user hasn't input a national prefix.\r\n\t\t\t// `format.usesNationalPrefix()` detects such cases.\r\n\t\t\tconst usesNationalPrefix = format.usesNationalPrefix()\r\n\t\t\tif ((usesNationalPrefix && nationalPrefix) ||\r\n\t\t\t\t(!usesNationalPrefix && !international)) {\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}"], "mappings": ";;;;;;;;;AAAA,SACCA,iBAAiB,EACjBC,eAAe,EACfC,MAAM,EACNC,0BAA0B,EAC1BC,oBAAoB,EACpBC,oBAAoB,EACpBC,0BAA0B,QACpB,8BAA8B;AAErC,OAAOC,oBAAoB,IAC1BC,uBAAuB,QACjB,kCAAkC;AAEzC,OAAOC,cAAc,MAAM,wCAAwC;AAEnE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,SAASV,iBAAiB,QAAQ,8BAA8B;AAChE,SAASW,mBAAmB,QAAQ,8CAA8C;AAClF,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAOC,gCAAgC,MAAM,+CAA+C;;AAE5F;AACA;AACA,IAAMC,WAAW,GAAG,GAAG;AACvB;AACA,IAAMC,oCAAoC,GAAG,EAAE;AAC/C;AACA;AACA,IAAMC,0BAA0B,GAAGd,MAAM,CAACY,WAAW,EAAEC,oCAAoC,CAAC;;AAE5F;AACA;AACA,IAAME,kCAAkC,GAAG,MAAM;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,kCAAkC,GAAG,IAAI;;AAE/C;AACA;AACA,IAAMC,8BAA8B,GAAGD,kCAAkC,IAAK;EAAA,OAAM,iBAAiB;AAAA,CAAC;;AAEtG;AACA;AACA;AACA;AACA;AACA,IAAME,+BAA+B,GAAGF,kCAAkC,IAAK;EAAA,OAAM,mBAAmB;AAAA,CAAC;;AAEzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMG,2BAA2B,GAAG,IAAIC,MAAM,CAC7C,GAAG,GAAGV,iBAAiB,GAAG,IAAI;AAC9B;AACA;AACA;AACA,MAAM,GACN,GAAG,GAAGA,iBAAiB,GAAG,IAAI,GAC9B,UAAU,GAAGA,iBAAiB,GAAG,MAAM,GACvC,GACD,CAAC;;AAED;AACA;AACA;AACA,IAAMW,yBAAyB,GAAG,CAAC;AAAA,IAEdC,kBAAkB;EACtC,SAAAA,mBAAAC,IAAA,EAGG;IAAA,IAFFC,KAAK,GAAAD,IAAA,CAALC,KAAK;MACLC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;IAAAC,eAAA,OAAAJ,kBAAA;IAER,IAAI,CAACG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,WAAW,CAAC,CAAC;EACnB;EAAC,OAAAC,YAAA,CAAAN,kBAAA;IAAAO,GAAA;IAAAC,KAAA,EAED,SAAAH,WAAWA,CAAA,EAAG;MACb,IAAI,CAACI,YAAY,GAAGC,SAAS;MAC7B,IAAI,CAACC,QAAQ,GAAGD,SAAS;MACzB,IAAI,CAACE,sBAAsB,GAAGF,SAAS;MACvC,IAAI,CAACG,+BAA+B,GAAGH,SAAS;MAChD,IAAI,CAACI,uCAAuC,GAAG,CAAC,CAAC;IAClD;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAED,SAAAO,KAAKA,CAACC,aAAa,EAAEd,KAAK,EAAE;MAC3B,IAAI,CAACG,WAAW,CAAC,CAAC;MAClB,IAAIW,aAAa,EAAE;QAClB,IAAI,CAACC,MAAM,GAAGD,aAAa,CAACE,WAAW,CAAC,CAAC,KAAK,GAAG;QACjD,IAAI,CAACC,eAAe,GAAGH,aAAa,CAACI,OAAO,CAAC,CAAC;QAC9C,IAAIlB,KAAK,CAACmB,yBAAyB,EAAE;UACpC,IAAI,CAACC,yBAAyB,CAACpB,KAAK,CAAC;QACtC;MACD,CAAC,MAAM;QACN,IAAI,CAACe,MAAM,GAAGP,SAAS;QACvB,IAAI,CAACS,eAAe,GAAG,EAAE;MAC1B;IACD;;IAEA;AACD;AACA;AACA;AACA;AACA;EALC;IAAAZ,GAAA;IAAAC,KAAA,EAMA,SAAAe,MAAMA,CAACC,UAAU,EAAEtB,KAAK,EAAE;MAAA,IAAAuB,KAAA;MACzB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIzC,uBAAuB,CAACkB,KAAK,CAACmB,yBAAyB,EAAE,IAAI,CAAClB,QAAQ,CAAC,EAAE;QAC5E,SAAAuB,SAAA,GAAAC,+BAAA,CAAqB,IAAI,CAACR,eAAe,GAAAS,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAE;UAAA,IAAhCN,OAAM,GAAAK,KAAA,CAAApB,KAAA;UAChB,IAAMsB,uBAAuB,GAAG/C,oBAAoB,CACnDmB,KAAK,EACLqB,OAAM,EACN;YACCpB,QAAQ,EAAE,IAAI,CAACA,QAAQ;YACvB4B,qCAAqC,EAAE,SAAvCA,qCAAqCA,CAAGR,MAAM;cAAA,OAAKE,KAAI,CAACM,qCAAqC,CAACR,MAAM,EAAE;gBACrGS,aAAa,EAAE9B,KAAK,CAAC8B,aAAa;gBAClCC,cAAc,EAAE/B,KAAK,CAAC+B;cACvB,CAAC,CAAC;YAAA;YACFC,+BAA+B,EAAE,SAAjCA,+BAA+BA,CAAGX,MAAM;cAAA,OAAKE,KAAI,CAACS,+BAA+B,CAACX,MAAM,CAAC;YAAA;UAC1F,CACD,CAAC;UACD,IAAIO,uBAAuB,EAAE;YAC5B,IAAI,CAACzB,WAAW,CAAC,CAAC;YAClB,IAAI,CAACI,YAAY,GAAGc,OAAM;YAC1B,IAAI,CAACY,yBAAyB,CAACL,uBAAuB,CAACM,OAAO,CAAC,KAAK,EAAE5D,iBAAiB,CAAC,EAAE0B,KAAK,CAAC;YAChG,IAAI,CAACW,+BAA+B,GAAGiB,uBAAuB;YAC9D;YACA;YACA,IAAI,CAAChB,uCAAuC,GAAG,IAAI,CAACH,QAAQ,CAAC0B,WAAW,CAAC7D,iBAAiB,CAAC;YAC3F,OAAOsD,uBAAuB;UAC/B;QAED;MACD;MACA;MACA;MACA,OAAO,IAAI,CAACQ,kCAAkC,CAACd,UAAU,EAAEtB,KAAK,CAAC;IAClE;;IAEA;EAAA;IAAAK,GAAA;IAAAC,KAAA,EACA,SAAA8B,kCAAkCA,CAACd,UAAU,EAAEtB,KAAK,EAAE;MACrD,IAAMqC,sBAAsB,GAAG,IAAI,CAAC9B,YAAY;;MAEhD;MACA,IAAM+B,iBAAiB,GAAG,IAAI,CAACC,YAAY,CAACvC,KAAK,CAAC;MAElD,IAAIsC,iBAAiB,EAAE;QACtB,IAAIA,iBAAiB,KAAKD,sBAAsB,EAAE;UACjD;UACA;UACA;UACA,OAAO,IAAI,CAACG,8BAA8B,CAAClB,UAAU,CAAC;QACvD,CAAC,MAAM;UACN;UACA;UACA;UACA;UACA,OAAO,IAAI,CAACkB,8BAA8B,CAACxC,KAAK,CAACyC,iBAAiB,CAAC,CAAC,CAAC;QACtE;MACD;IACD;EAAC;IAAApC,GAAA;IAAAC,KAAA,EAED,SAAAc,yBAAyBA,CAAAsB,KAAA,EAItB;MAAA,IAAAC,MAAA;MAAA,IAHFxB,yBAAyB,GAAAuB,KAAA,CAAzBvB,yBAAyB;QACzBY,cAAc,GAAAW,KAAA,CAAdX,cAAc;QACdD,aAAa,GAAAY,KAAA,CAAbZ,aAAa;MAEb,IAAMc,aAAa,GAAGzB,yBAAyB;;MAE/C;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA,IAAI0B,yBAAyB,GAAGD,aAAa,CAACE,MAAM,GAAGjD,yBAAyB;MAChF,IAAIgD,yBAAyB,GAAG,CAAC,EAAE;QAClCA,yBAAyB,GAAG,CAAC;MAC9B;MAEA,IAAI,CAAC5B,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC8B,MAAM,CACjD,UAAA1B,MAAM;QAAA,OAAIsB,MAAI,CAACK,WAAW,CAAC3B,MAAM,EAAES,aAAa,EAAEC,cAAc,CAAC,IAC7DY,MAAI,CAACM,aAAa,CAAC5B,MAAM,EAAEuB,aAAa,EAAEC,yBAAyB,CAAC;MAAA,CACzE,CAAC;;MAED;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACtC,YAAY,IAAI,IAAI,CAACU,eAAe,CAACiC,OAAO,CAAC,IAAI,CAAC3C,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;QAChF,IAAI,CAACJ,WAAW,CAAC,CAAC;MACnB;IACD;EAAC;IAAAE,GAAA;IAAAC,KAAA,EAED,SAAA0C,WAAWA,CAAC3B,MAAM,EAAES,aAAa,EAAEC,cAAc,EAAE;MAClD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIA,cAAc,IACjB,CAACV,MAAM,CAAC8B,kBAAkB,CAAC,CAAC;MAC5B;MACA,CAAC9B,MAAM,CAAC+B,sDAAsD,CAAC,CAAC,EAAE;QAClE,OAAO,KAAK;MACb;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACtB,aAAa,IACjB,CAACC,cAAc,IACfV,MAAM,CAACgC,uDAAuD,CAAC,CAAC,EAAE;QAClE,OAAO,KAAK;MACb;MACA,OAAO,IAAI;IACZ;EAAC;IAAAhD,GAAA;IAAAC,KAAA,EAED,SAAA2C,aAAaA,CAAC5B,MAAM,EAAEuB,aAAa,EAAEC,yBAAyB,EAAE;MAC/D,IAAMS,0BAA0B,GAAGjC,MAAM,CAACkC,qBAAqB,CAAC,CAAC,CAACT,MAAM;;MAExE;MACA;MACA;MACA,IAAIQ,0BAA0B,KAAK,CAAC,EAAE;QACrC,OAAO,IAAI;MACZ;;MAEA;MACA;;MAEA;MACA;MACAT,yBAAyB,GAAGW,IAAI,CAACC,GAAG,CAACZ,yBAAyB,EAAES,0BAA0B,GAAG,CAAC,CAAC;MAC/F,IAAMI,oBAAoB,GAAGrC,MAAM,CAACkC,qBAAqB,CAAC,CAAC,CAACV,yBAAyB,CAAC;;MAEtF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAID,aAAa,CAACE,MAAM,GAAGjD,yBAAyB,EAAE;QACrD;QACA;QACA;QACA;QACA,IAAI;UACH,OAAO,IAAId,cAAc,CAAC2E,oBAAoB,CAAC,CAACC,KAAK,CAACf,aAAa,EAAE;YAAEgB,aAAa,EAAE;UAAK,CAAC,CAAC,KAAKpD,SAAS;QAC5G,CAAC,CAAC,OAAOqD,KAAK,EAAE,0BAA2B;UAC1C;UACA;UACA;UACA;UACAC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;UACpB,OAAO,IAAI;QACZ;MACD;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAIjE,MAAM,MAAAmE,MAAA,CAAML,oBAAoB,MAAG,CAAC,CAACM,IAAI,CAACpB,aAAa,CAAC;IACpE;EAAC;IAAAvC,GAAA;IAAAC,KAAA,EAED,SAAA2D,eAAeA,CAAC5C,MAAM,EAAES,aAAa,EAAE;MACtC,OAAOA,aAAa,GAAGT,MAAM,CAAC6C,mBAAmB,CAAC,CAAC,GAAG7C,MAAM,CAACA,MAAM,CAAC,CAAC;IACtE;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAED,SAAAiC,YAAYA,CAACvC,KAAK,EAAE;MAAA,IAAAmE,MAAA;MAAA,IAAAC,KAAA,YAAAA,MAAA,EAgBgC;UAAA,IAAxC/C,MAAM,GAAAgD,MAAA,CAAA/D,KAAA;UAChB;UACA;UACA,IAAI6D,MAAI,CAAC5D,YAAY,KAAKc,MAAM,EAAE;YAAA;UAElC;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAAC1B,2BAA2B,CAACqE,IAAI,CAACG,MAAI,CAACF,eAAe,CAAC5C,MAAM,EAAErB,KAAK,CAAC8B,aAAa,CAAC,CAAC,EAAE;YAAA;UAE1F;UACA,IAAI,CAACqC,MAAI,CAACG,uBAAuB,CAACjD,MAAM,EAAErB,KAAK,CAAC,EAAE;YACjD;YACAmE,MAAI,CAAClD,eAAe,GAAGkD,MAAI,CAAClD,eAAe,CAAC8B,MAAM,CAAC,UAAAwB,CAAC;cAAA,OAAIA,CAAC,KAAKlD,MAAM;YAAA,EAAC;YAAA;UAEtE;UACA8C,MAAI,CAAC5D,YAAY,GAAGc,MAAM;UAAA;QAE3B,CAAC;QAAAmD,IAAA;MAtED;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,SAAAC,UAAA,GAAAhD,+BAAA,CAAqB,IAAI,CAACR,eAAe,CAACyD,KAAK,CAAC,CAAC,GAAAL,MAAA,IAAAA,MAAA,GAAAI,UAAA,IAAA9C,IAAA;QAAA6C,IAAA,GAAAJ,KAAA;QAAA,IAAAI,IAAA,QAI/C;QAAK,IAAAA,IAAA,QA0CL;MAAQ;MAUV,IAAI,CAAC,IAAI,CAACjE,YAAY,EAAE;QACvB;QACA,IAAI,CAACJ,WAAW,CAAC,CAAC;MACnB;MACA,OAAO,IAAI,CAACI,YAAY;IACzB;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAED,SAAAgE,uBAAuBA,CAACjD,MAAM,EAAErB,KAAK,EAAE;MACtC;MACA;MACA;MACA;MACA,IAAIR,kCAAkC,IAAI6B,MAAM,CAACsD,OAAO,CAAC,CAAC,CAACzB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC7E;MACD;MACA;MACA,IAAMzC,QAAQ,GAAG,IAAI,CAACmE,oBAAoB,CAACvD,MAAM,EAAErB,KAAK,CAAC;MACzD;MACA;MACA,IAAIS,QAAQ,EAAE;QACb,IAAI,CAACwB,yBAAyB,CAACxB,QAAQ,EAAET,KAAK,CAAC;QAC/C,OAAO,IAAI;MACZ;IACD;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAA0B,+BAA+BA,CAACX,MAAM,EAAE;MACvC;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACN,MAAM,EAAE;QAChB,OAAO,GAAG;MACX;MACA;MACA;MACA;MACA;MACA,IAAIM,MAAM,IACTA,MAAM,CAACwD,4BAA4B,CAAC,CAAC,IACrCtF,kCAAkC,CAACyE,IAAI,CAAC3C,MAAM,CAACwD,4BAA4B,CAAC,CAAC,CAAC,EAAE;QAChF,OAAO,GAAG;MACX;MACA;MACA;MACA;MACA,OAAO,EAAE;IACV;EAAC;IAAAxE,GAAA;IAAAC,KAAA,EAED,SAAAwE,8CAA8CA,CAAAC,KAAA,EAA6BC,OAAO,EAAE;MAAA,IAAnCC,SAAS,GAAAF,KAAA,CAATE,SAAS;QAAEC,WAAW,GAAAH,KAAA,CAAXG,WAAW;MACtE,IAAID,SAAS,EAAE;QACd,OAAOD,OAAO,IAAIA,OAAO,CAACG,OAAO,KAAK,KAAK,GAAGF,SAAS,GAAGA,SAAS,GAAG,GAAG;MAC1E;MACA,IAAIC,WAAW,EAAE;QAChB,OAAO,EAAE;MACV;MACA,OAAO,GAAG;IACX;EAAC;IAAA7E,GAAA;IAAAC,KAAA,EAED,SAAA8E,WAAWA,CAACpF,KAAK,EAAE;MAClB,IAAI,CAAC,IAAI,CAACS,QAAQ,EAAE;QACnB;MACD;MACA;MACA;MACA;MACA,IAAI4E,KAAK,GAAG,CAAC,CAAC;MACd,IAAIC,CAAC,GAAG,CAAC;MACT,IAAMC,mBAAmB,GAAGvF,KAAK,CAAC8B,aAAa,GAAG,IAAI,CAACgD,8CAA8C,CAAC9E,KAAK,EAAE;QAAEmF,OAAO,EAAE;MAAM,CAAC,CAAC,GAAG,EAAE;MACrI,OAAOG,CAAC,GAAGC,mBAAmB,CAACzC,MAAM,GAAG9C,KAAK,CAACwF,mCAAmC,CAAC,CAAC,CAAC1C,MAAM,EAAE;QAC3FuC,KAAK,GAAG,IAAI,CAAC5E,QAAQ,CAACyC,OAAO,CAAC5E,iBAAiB,EAAE+G,KAAK,GAAG,CAAC,CAAC;QAC3DC,CAAC,EAAE;MACJ;MACA,OAAO7G,0BAA0B,CAAC,IAAI,CAACgC,QAAQ,EAAE4E,KAAK,GAAG,CAAC,CAAC;IAC5D;EAAC;IAAAhF,GAAA;IAAAC,KAAA,EAED,SAAA2B,yBAAyBA,CAACxB,QAAQ,EAAET,KAAK,EAAE;MAC1C,IAAI,CAACU,sBAAsB,GAAGD,QAAQ;MACtC,IAAI,CAACE,+BAA+B,GAAGF,QAAQ;MAC/C;MACA;MACA,IAAI,CAACG,uCAAuC,GAAG,CAAC,CAAC;MACjD;MACA;MACA;MACA;MACA;MACA,IAAIZ,KAAK,CAAC8B,aAAa,EAAE;QACxB,IAAI,CAACrB,QAAQ,GACZ,IAAI,CAACqE,8CAA8C,CAAC9E,KAAK,CAAC,CAACkC,OAAO,CAAC,SAAS,EAAE5D,iBAAiB,CAAC,GAChGE,MAAM,CAACF,iBAAiB,EAAE0B,KAAK,CAACgB,WAAW,CAAC8B,MAAM,CAAC,GACnD,GAAG,GACHrC,QAAQ;MACV,CAAC,MAAM;QACN,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACzB;IACD;;IAEA;AACD;AACA;AACA;AACA;AACA;AACA;EANC;IAAAJ,GAAA;IAAAC,KAAA,EAOA,SAAAsE,oBAAoBA,CAACvD,MAAM,EAAAoE,KAAA,EAKxB;MAAA,IAJFtE,yBAAyB,GAAAsE,KAAA,CAAzBtE,yBAAyB;QACzBW,aAAa,GAAA2D,KAAA,CAAb3D,aAAa;QACbC,cAAc,GAAA0D,KAAA,CAAd1D,cAAc;QACd2D,4CAA4C,GAAAD,KAAA,CAA5CC,4CAA4C;MAE5C,IAAIf,OAAO,GAAGtD,MAAM,CAACsD,OAAO,CAAC,CAAC;;MAE9B;MACA,IAAInF,kCAAkC,EAAE;QACvCmF,OAAO,GAAGA;QACT;QAAA,CACCzC,OAAO,CAACzC,8BAA8B,CAAC,CAAC,EAAE,KAAK;QAChD;QAAA,CACCyC,OAAO,CAACxC,+BAA+B,CAAC,CAAC,EAAE,KAAK,CAAC;MACpD;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIiG,MAAM,GAAGrG,0BAA0B,CAACqE,KAAK,CAACgB,OAAO,CAAC,CAAC,CAAC,CAAC;;MAEzD;MACA;MACA,IAAIxD,yBAAyB,CAAC2B,MAAM,GAAG6C,MAAM,CAAC7C,MAAM,EAAE;QACrD;MACD;;MAEA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA,IAAM8C,aAAa,GAAG,IAAIhG,MAAM,CAAC,GAAG,GAAG+E,OAAO,GAAG,GAAG,CAAC;MACrD,IAAMkB,yBAAyB,GAAG1E,yBAAyB,CAACe,OAAO,CAAC,KAAK,EAAE9C,WAAW,CAAC;;MAEvF;MACA;MACA;MACA,IAAIwG,aAAa,CAAC5B,IAAI,CAAC6B,yBAAyB,CAAC,EAAE;QAClDF,MAAM,GAAGE,yBAAyB;MACnC;MAEA,IAAIC,YAAY,GAAG,IAAI,CAAC7B,eAAe,CAAC5C,MAAM,EAAES,aAAa,CAAC;MAC9D,IAAIiE,gCAAgC;;MAEpC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAClE,qCAAqC,CAACR,MAAM,EAAE;QAAES,aAAa,EAAbA,aAAa;QAAEC,cAAc,EAAdA;MAAe,CAAC,CAAC,EAAE;QAC1F,IAAMiE,8BAA8B,GAAGF,YAAY,CAAC5D,OAAO,CAC1DjD,mBAAmB,EACnBoC,MAAM,CAACwD,4BAA4B,CAAC,CACrC,CAAC;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI7F,WAAW,CAACqC,MAAM,CAACwD,4BAA4B,CAAC,CAAC,CAAC,KAAK,CAAC9C,cAAc,IAAI,EAAE,IAAI/C,WAAW,CAAC,IAAI,CAAC,EAAE;UACtG8G,YAAY,GAAGE,8BAA8B;UAC7CD,gCAAgC,GAAG,IAAI;UACvC;UACA;UACA,IAAIhE,cAAc,EAAE;YACnB,IAAIuD,CAAC,GAAGvD,cAAc,CAACe,MAAM;YAC7B,OAAOwC,CAAC,GAAG,CAAC,EAAE;cACbQ,YAAY,GAAGA,YAAY,CAAC5D,OAAO,CAAC,IAAI,EAAE5D,iBAAiB,CAAC;cAC5DgH,CAAC,EAAE;YACJ;UACD;QACD;MACD;;MAEA;MACA,IAAI7E,QAAQ,GAAGkF;MACd;MAAA,CACCzD,OAAO,CAAC,IAAItC,MAAM,CAAC+E,OAAO,CAAC,EAAEmB,YAAY;MAC1C;MAAA,CACC5D,OAAO,CAAC,IAAItC,MAAM,CAACR,WAAW,EAAE,GAAG,CAAC,EAAEd,iBAAiB,CAAC;;MAE1D;MACA;MACA;MACA;MACA,IAAI,CAACyH,gCAAgC,EAAE;QACtC,IAAIL,4CAA4C,EAAE;UACjD;UACAjF,QAAQ,GAAGjC,MAAM,CAACF,iBAAiB,EAAEoH,4CAA4C,CAAC5C,MAAM,CAAC,GACxF,GAAG,GACHrC,QAAQ;QACV,CAAC,MAAM,IAAIsB,cAAc,EAAE;UAC1B;UACAtB,QAAQ,GAAGjC,MAAM,CAACF,iBAAiB,EAAEyD,cAAc,CAACe,MAAM,CAAC,GAC1D,IAAI,CAACd,+BAA+B,CAACX,MAAM,CAAC,GAC5CZ,QAAQ;QACV;MACD;MAEA,IAAIqB,aAAa,EAAE;QAClBrB,QAAQ,GAAGtB,gCAAgC,CAACsB,QAAQ,CAAC;MACtD;MAEA,OAAOA,QAAQ;IAChB;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAAkC,8BAA8BA,CAACmD,MAAM,EAAE;MACtC,IAAMM,MAAM,GAAGrH,0BAA0B,CACxC,IAAI,CAAC+B,+BAA+B,EACpC,IAAI,CAACC,uCAAuC,EAC5C+E,MACD,CAAC;MAED,IAAI,CAACM,MAAM,EAAE;QACZ;QACA,IAAI,CAAC9F,WAAW,CAAC,CAAC;QAClB;MACD;MAEA,IAAI,CAACQ,+BAA+B,GAAGsF,MAAM,CAAC,CAAC,CAAC;MAChD,IAAI,CAACrF,uCAAuC,GAAGqF,MAAM,CAAC,CAAC,CAAC;;MAExD;MACA,OAAOxH,0BAA0B,CAAC,IAAI,CAACkC,+BAA+B,EAAE,IAAI,CAACC,uCAAuC,GAAG,CAAC,CAAC;;MAEzH;MACA;MACA;MACA;IACD;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAED,SAAAuB,qCAAqCA,CAACR,MAAM,EAAA6E,KAAA,EAAqC;MAAA,IAAjCpE,aAAa,GAAAoE,KAAA,CAAbpE,aAAa;QAAEC,cAAc,GAAAmE,KAAA,CAAdnE,cAAc;MAC5E,IAAIV,MAAM,CAACwD,4BAA4B,CAAC,CAAC,EAAE;QAC1C;QACA;QACA;QACA,IAAM1B,kBAAkB,GAAG9B,MAAM,CAAC8B,kBAAkB,CAAC,CAAC;QACtD,IAAKA,kBAAkB,IAAIpB,cAAc,IACvC,CAACoB,kBAAkB,IAAI,CAACrB,aAAc,EAAE;UACzC,OAAO,IAAI;QACZ;MACD;IACD;EAAC;AAAA;AAAA,SAhmBmBhC,kBAAkB,IAAAqG,OAAA", "ignoreList": []}