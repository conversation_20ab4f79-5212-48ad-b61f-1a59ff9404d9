{"version": 3, "file": "metadata.test.js", "names": ["metadata", "type", "metadataV1", "metadataV2", "metadataV3", "metadataV4", "<PERSON><PERSON><PERSON>", "validateMetadata", "getExtPrefix", "isSupportedCountry", "describe", "it", "FR", "country", "expect", "to", "equal", "thrower", "getNumberingPlanMetadata", "nationalPrefixForParsing", "chooseCountryByCountryCallingCode", "meta", "numberingPlan", "formats", "nationalPrefixIsMandatoryWhenFormattingInNationalFormat", "a", "b", "countries", "country_calling_codes", "selectNumberingPlan", "nonGeographic", "possibleLengths", "deep", "length", "nationalPrefix", "pattern", "leadingDigits", "nationalPrefixTransformRule", "nationalPrefixFormattingRule", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "ext", "metaNew", "defaultIDDPrefix", "something", "_typeof"], "sources": ["../source/metadata.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\nimport metadataV1 from '../test/metadata/1.0.0/metadata.min.json' with { type: 'json' }\r\nimport metadataV2 from '../test/metadata/1.1.11/metadata.min.json' with { type: 'json' }\r\nimport metadataV3 from '../test/metadata/1.7.34/metadata.min.json' with { type: 'json' }\r\nimport metadataV4 from '../test/metadata/1.7.37/metadata.min.json' with { type: 'json' }\r\n\r\nimport Metadata, { validateMetadata, getExtPrefix, isSupportedCountry } from './metadata.js'\r\n\r\ndescribe('metadata', () => {\r\n\tit('should return undefined for non-defined types', () => {\r\n\t\tconst FR = new Metadata(metadata).country('FR')\r\n\t\texpect(type(FR.type('FIXED_LINE'))).to.equal('undefined')\r\n\t})\r\n\r\n\tit('should validate country', () => {\r\n\t\tconst thrower = () => new Metadata(metadata).country('RUS')\r\n\t\texpect(thrower).to.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should tell if a country is supported', () => {\r\n\t\texpect(isSupportedCountry('RU', metadata)).to.equal(true)\r\n\t\texpect(isSupportedCountry('XX', metadata)).to.equal(false)\r\n\t})\r\n\r\n\tit('should return ext prefix for a country', () => {\r\n\t\texpect(getExtPrefix('US', metadata)).to.equal(' ext. ')\r\n\t\texpect(getExtPrefix('CA', metadata)).to.equal(' ext. ')\r\n\t\texpect(getExtPrefix('GB', metadata)).to.equal(' x')\r\n\t\t// expect(getExtPrefix('XX', metadata)).to.equal(undefined)\r\n\t\texpect(getExtPrefix('XX', metadata)).to.equal(' ext. ')\r\n\t})\r\n\r\n\tit('should cover non-occuring edge cases', () => {\r\n\t\tnew Metadata(metadata).getNumberingPlanMetadata('999')\r\n\t})\r\n\r\n\tit('should support deprecated methods', () => {\r\n\t\texpect(new Metadata(metadata).country('US').nationalPrefixForParsing()).to.equal('1')\r\n\t\texpect(\r\n            new Metadata(metadata).chooseCountryByCountryCallingCode('1').nationalPrefixForParsing()\r\n        ).to.equal('1')\r\n\t})\r\n\r\n\tit('should tell if a national prefix is mandatory when formatting a national number', () => {\r\n\t\tconst meta = new Metadata(metadata)\r\n\t\t// No \"national_prefix_formatting_rule\".\r\n\t\t// \"national_prefix_is_optional_when_formatting\": true\r\n\t\tmeta.country('US')\r\n\t\texpect(\r\n            meta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat()\r\n        ).to.equal(false)\r\n\t\t// \"national_prefix_formatting_rule\": \"8 ($1)\"\r\n\t\t// \"national_prefix_is_optional_when_formatting\": true\r\n\t\tmeta.country('RU')\r\n\t\texpect(\r\n            meta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat()\r\n        ).to.equal(false)\r\n\t\t// \"national_prefix\": \"0\"\r\n\t\t// \"national_prefix_formatting_rule\": \"0 $1\"\r\n\t\tmeta.country('FR')\r\n\t\texpect(\r\n            meta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat()\r\n        ).to.equal(true)\r\n\t})\r\n\r\n\tit('should validate metadata', () => {\r\n\t\tlet thrower = () => validateMetadata()\r\n\t\texpect(thrower).to.throw('`metadata` argument not passed')\r\n\r\n\t\tthrower = () => validateMetadata(123)\r\n\t\texpect(thrower).to.throw('Got a number: 123.')\r\n\r\n\t\tthrower = () => validateMetadata('abc')\r\n\t\texpect(thrower).to.throw('Got a string: abc.')\r\n\r\n\t\tthrower = () => validateMetadata({ a: true, b: 2 })\r\n\t\texpect(thrower).to.throw('Got an object of shape: { a, b }.')\r\n\r\n\t\tthrower = () => validateMetadata({ a: true, countries: 2 })\r\n\t\texpect(thrower).to.throw('Got an object of shape: { a, countries }.')\r\n\r\n\t\tthrower = () => validateMetadata({ country_calling_codes: true, countries: 2 })\r\n\t\texpect(thrower).to.throw('Got an object of shape')\r\n\r\n\t\tthrower = () => validateMetadata({ country_calling_codes: {}, countries: 2 })\r\n\t\texpect(thrower).to.throw('Got an object of shape')\r\n\r\n\t\tvalidateMetadata({ country_calling_codes: {}, countries: {}, b: 3 })\r\n\t})\r\n\r\n\tit('should work around `nonGeographical` typo in metadata generated from `1.7.35` to `1.7.37`', function() {\r\n\t\tconst meta = new Metadata(metadataV4)\r\n\t\tmeta.selectNumberingPlan('888')\r\n\t\texpect(type(meta.nonGeographic())).to.equal('object')\r\n\t})\r\n\r\n\tit('should work around `nonGeographic` metadata not existing before `1.7.35`', function() {\r\n\t\tconst meta = new Metadata(metadataV3)\r\n\t\texpect(type(meta.getNumberingPlanMetadata('800'))).to.equal('object')\r\n\t\texpect(type(meta.getNumberingPlanMetadata('000'))).to.equal('undefined')\r\n\t})\r\n\r\n\tit('should work with metadata from version `1.1.11`', function() {\r\n\t\tconst meta = new Metadata(metadataV2)\r\n\r\n\t\tmeta.selectNumberingPlan('US')\r\n\t\texpect(meta.numberingPlan.possibleLengths()).to.deep.equal([10])\r\n\t\texpect(meta.numberingPlan.formats().length).to.equal(1)\r\n\t\texpect(meta.numberingPlan.nationalPrefix()).to.equal('1')\r\n\t\texpect(meta.numberingPlan.nationalPrefixForParsing()).to.equal('1')\r\n\t\texpect(meta.numberingPlan.type('MOBILE').pattern()).to.equal('')\r\n\r\n\t\tmeta.selectNumberingPlan('AG')\r\n\t\texpect(meta.numberingPlan.leadingDigits()).to.equal('268')\r\n\t\t// Should've been \"268$1\" but apparently there was a bug in metadata generator\r\n\t\t// and no national prefix transform rules were written.\r\n\t\texpect(meta.numberingPlan.nationalPrefixTransformRule()).to.equal(null)\r\n\r\n\t\tmeta.selectNumberingPlan('AF')\r\n\t\texpect(meta.numberingPlan.formats()[0].nationalPrefixFormattingRule()).to.equal('0$1')\r\n\r\n\t\tmeta.selectNumberingPlan('RU')\r\n\t\texpect(\r\n            meta.numberingPlan.formats()[0].nationalPrefixIsOptionalWhenFormattingInNationalFormat()\r\n        ).to.equal(true)\r\n\t})\r\n\r\n\tit('should work with metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\r\n\t\tmeta.selectNumberingPlan('US')\r\n\t\texpect(meta.numberingPlan.formats().length).to.equal(1)\r\n\t\texpect(meta.numberingPlan.nationalPrefix()).to.equal('1')\r\n\t\texpect(meta.numberingPlan.nationalPrefixForParsing()).to.equal('1')\r\n\t\texpect(type(meta.numberingPlan.type('MOBILE'))).to.equal('undefined')\r\n\r\n\t\tmeta.selectNumberingPlan('AG')\r\n\t\texpect(meta.numberingPlan.leadingDigits()).to.equal('268')\r\n\t\t// Should've been \"268$1\" but apparently there was a bug in metadata generator\r\n\t\t// and no national prefix transform rules were written.\r\n\t\texpect(meta.numberingPlan.nationalPrefixTransformRule()).to.equal(null)\r\n\r\n\t\tmeta.selectNumberingPlan('AF')\r\n\t\texpect(meta.numberingPlan.formats()[0].nationalPrefixFormattingRule()).to.equal('0$1')\r\n\r\n\t\tmeta.selectNumberingPlan('RU')\r\n\t\texpect(\r\n            meta.numberingPlan.formats()[0].nationalPrefixIsOptionalWhenFormattingInNationalFormat()\r\n        ).to.equal(true)\r\n\t})\r\n\r\n\tit('should work around \"ext\" data not present in metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\t\tmeta.selectNumberingPlan('GB')\r\n\t\texpect(meta.ext()).to.equal(' ext. ')\r\n\r\n\t\tconst metaNew = new Metadata(metadata)\r\n\t\tmetaNew.selectNumberingPlan('GB')\r\n\t\texpect(metaNew.ext()).to.equal(' x')\r\n\t})\r\n\r\n\tit('should work around \"default IDD prefix\" data not present in metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\t\tmeta.selectNumberingPlan('AU')\r\n\t\texpect(type(meta.defaultIDDPrefix())).to.equal('undefined')\r\n\r\n\t\tconst metaNew = new Metadata(metadata)\r\n\t\tmetaNew.selectNumberingPlan('AU')\r\n\t\texpect(metaNew.defaultIDDPrefix()).to.equal('0011')\r\n\t})\r\n})\r\n\r\nfunction type(something) {\r\n\treturn typeof something\r\n}"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAC/D,OAAOC,UAAU,MAAM,0CAA0C,QAAQD,IAAI,EAAE,MAAM;AACrF,OAAOE,UAAU,MAAM,2CAA2C,QAAQF,IAAI,EAAE,MAAM;AACtF,OAAOG,UAAU,MAAM,2CAA2C,QAAQH,IAAI,EAAE,MAAM;AACtF,OAAOI,UAAU,MAAM,2CAA2C,QAAQJ,IAAI,EAAE,MAAM;AAEtF,OAAOK,QAAQ,IAAIC,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB,QAAQ,eAAe;AAE5FC,QAAQ,CAAC,UAAU,EAAE,YAAM;EAC1BC,EAAE,CAAC,+CAA+C,EAAE,YAAM;IACzD,IAAMC,EAAE,GAAG,IAAIN,QAAQ,CAACN,QAAQ,CAAC,CAACa,OAAO,CAAC,IAAI,CAAC;IAC/CC,MAAM,CAACb,IAAI,CAACW,EAAE,CAACX,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EAC1D,CAAC,CAAC;EAEFL,EAAE,CAAC,yBAAyB,EAAE,YAAM;IACnC,IAAMM,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAIX,QAAQ,CAACN,QAAQ,CAAC,CAACa,OAAO,CAAC,KAAK,CAAC;IAAA;IAC3DC,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,iBAAiB,CAAC;EAC5C,CAAC,CAAC;EAEFJ,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjDG,MAAM,CAACL,kBAAkB,CAAC,IAAI,EAAET,QAAQ,CAAC,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACzDF,MAAM,CAACL,kBAAkB,CAAC,IAAI,EAAET,QAAQ,CAAC,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC3D,CAAC,CAAC;EAEFL,EAAE,CAAC,wCAAwC,EAAE,YAAM;IAClDG,MAAM,CAACN,YAAY,CAAC,IAAI,EAAER,QAAQ,CAAC,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IACvDF,MAAM,CAACN,YAAY,CAAC,IAAI,EAAER,QAAQ,CAAC,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IACvDF,MAAM,CAACN,YAAY,CAAC,IAAI,EAAER,QAAQ,CAAC,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACnD;IACAF,MAAM,CAACN,YAAY,CAAC,IAAI,EAAER,QAAQ,CAAC,CAAC,CAACe,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;EACxD,CAAC,CAAC;EAEFL,EAAE,CAAC,sCAAsC,EAAE,YAAM;IAChD,IAAIL,QAAQ,CAACN,QAAQ,CAAC,CAACkB,wBAAwB,CAAC,KAAK,CAAC;EACvD,CAAC,CAAC;EAEFP,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7CG,MAAM,CAAC,IAAIR,QAAQ,CAACN,QAAQ,CAAC,CAACa,OAAO,CAAC,IAAI,CAAC,CAACM,wBAAwB,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACrFF,MAAM,CACI,IAAIR,QAAQ,CAACN,QAAQ,CAAC,CAACoB,iCAAiC,CAAC,GAAG,CAAC,CAACD,wBAAwB,CAAC,CAC3F,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EACtB,CAAC,CAAC;EAEFL,EAAE,CAAC,iFAAiF,EAAE,YAAM;IAC3F,IAAMU,IAAI,GAAG,IAAIf,QAAQ,CAACN,QAAQ,CAAC;IACnC;IACA;IACAqB,IAAI,CAACR,OAAO,CAAC,IAAI,CAAC;IAClBC,MAAM,CACIO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,uDAAuD,CAAC,CAC5F,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACvB;IACA;IACAK,IAAI,CAACR,OAAO,CAAC,IAAI,CAAC;IAClBC,MAAM,CACIO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,uDAAuD,CAAC,CAC5F,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACvB;IACA;IACAK,IAAI,CAACR,OAAO,CAAC,IAAI,CAAC;IAClBC,MAAM,CACIO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,uDAAuD,CAAC,CAC5F,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACvB,CAAC,CAAC;EAEFL,EAAE,CAAC,0BAA0B,EAAE,YAAM;IACpC,IAAIM,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASV,gBAAgB,CAAC,CAAC;IAAA;IACtCO,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,gCAAgC,CAAC;IAE1DE,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASV,gBAAgB,CAAC,GAAG,CAAC;IAAA;IACrCO,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,oBAAoB,CAAC;IAE9CE,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASV,gBAAgB,CAAC,KAAK,CAAC;IAAA;IACvCO,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,oBAAoB,CAAC;IAE9CE,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASV,gBAAgB,CAAC;QAAEkB,CAAC,EAAE,IAAI;QAAEC,CAAC,EAAE;MAAE,CAAC,CAAC;IAAA;IACnDZ,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,mCAAmC,CAAC;IAE7DE,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASV,gBAAgB,CAAC;QAAEkB,CAAC,EAAE,IAAI;QAAEE,SAAS,EAAE;MAAE,CAAC,CAAC;IAAA;IAC3Db,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,2CAA2C,CAAC;IAErEE,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASV,gBAAgB,CAAC;QAAEqB,qBAAqB,EAAE,IAAI;QAAED,SAAS,EAAE;MAAE,CAAC,CAAC;IAAA;IAC/Eb,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,wBAAwB,CAAC;IAElDE,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASV,gBAAgB,CAAC;QAAEqB,qBAAqB,EAAE,CAAC,CAAC;QAAED,SAAS,EAAE;MAAE,CAAC,CAAC;IAAA;IAC7Eb,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,wBAAwB,CAAC;IAElDR,gBAAgB,CAAC;MAAEqB,qBAAqB,EAAE,CAAC,CAAC;MAAED,SAAS,EAAE,CAAC,CAAC;MAAED,CAAC,EAAE;IAAE,CAAC,CAAC;EACrE,CAAC,CAAC;EAEFf,EAAE,CAAC,2FAA2F,EAAE,YAAW;IAC1G,IAAMU,IAAI,GAAG,IAAIf,QAAQ,CAACD,UAAU,CAAC;IACrCgB,IAAI,CAACQ,mBAAmB,CAAC,KAAK,CAAC;IAC/Bf,MAAM,CAACb,IAAI,CAACoB,IAAI,CAACS,aAAa,CAAC,CAAC,CAAC,CAAC,CAACf,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;EACtD,CAAC,CAAC;EAEFL,EAAE,CAAC,0EAA0E,EAAE,YAAW;IACzF,IAAMU,IAAI,GAAG,IAAIf,QAAQ,CAACF,UAAU,CAAC;IACrCU,MAAM,CAACb,IAAI,CAACoB,IAAI,CAACH,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IACrEF,MAAM,CAACb,IAAI,CAACoB,IAAI,CAACH,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EACzE,CAAC,CAAC;EAEFL,EAAE,CAAC,iDAAiD,EAAE,YAAW;IAChE,IAAMU,IAAI,GAAG,IAAIf,QAAQ,CAACH,UAAU,CAAC;IAErCkB,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CAACO,IAAI,CAACC,aAAa,CAACS,eAAe,CAAC,CAAC,CAAC,CAAChB,EAAE,CAACiB,IAAI,CAAChB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAChEF,MAAM,CAACO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAACU,MAAM,CAAC,CAAClB,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC;IACvDF,MAAM,CAACO,IAAI,CAACC,aAAa,CAACY,cAAc,CAAC,CAAC,CAAC,CAACnB,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACzDF,MAAM,CAACO,IAAI,CAACC,aAAa,CAACH,wBAAwB,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACnEF,MAAM,CAACO,IAAI,CAACC,aAAa,CAACrB,IAAI,CAAC,QAAQ,CAAC,CAACkC,OAAO,CAAC,CAAC,CAAC,CAACpB,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IAEhEK,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CAACO,IAAI,CAACC,aAAa,CAACc,aAAa,CAAC,CAAC,CAAC,CAACrB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC1D;IACA;IACAF,MAAM,CAACO,IAAI,CAACC,aAAa,CAACe,2BAA2B,CAAC,CAAC,CAAC,CAACtB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAEvEK,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CAACO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACe,4BAA4B,CAAC,CAAC,CAAC,CAACvB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAEtFK,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CACIO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACgB,sDAAsD,CAAC,CAC3F,CAAC,CAACxB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACvB,CAAC,CAAC;EAEFL,EAAE,CAAC,gDAAgD,EAAE,YAAW;IAC/D,IAAMU,IAAI,GAAG,IAAIf,QAAQ,CAACJ,UAAU,CAAC;IAErCmB,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CAACO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAACU,MAAM,CAAC,CAAClB,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC;IACvDF,MAAM,CAACO,IAAI,CAACC,aAAa,CAACY,cAAc,CAAC,CAAC,CAAC,CAACnB,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACzDF,MAAM,CAACO,IAAI,CAACC,aAAa,CAACH,wBAAwB,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACnEF,MAAM,CAACb,IAAI,CAACoB,IAAI,CAACC,aAAa,CAACrB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAErEK,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CAACO,IAAI,CAACC,aAAa,CAACc,aAAa,CAAC,CAAC,CAAC,CAACrB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC1D;IACA;IACAF,MAAM,CAACO,IAAI,CAACC,aAAa,CAACe,2BAA2B,CAAC,CAAC,CAAC,CAACtB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAEvEK,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CAACO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACe,4BAA4B,CAAC,CAAC,CAAC,CAACvB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAEtFK,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CACIO,IAAI,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAACgB,sDAAsD,CAAC,CAC3F,CAAC,CAACxB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACvB,CAAC,CAAC;EAEFL,EAAE,CAAC,4EAA4E,EAAE,YAAW;IAC3F,IAAMU,IAAI,GAAG,IAAIf,QAAQ,CAACJ,UAAU,CAAC;IACrCmB,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CAACO,IAAI,CAACmB,GAAG,CAAC,CAAC,CAAC,CAACzB,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAErC,IAAMyB,OAAO,GAAG,IAAInC,QAAQ,CAACN,QAAQ,CAAC;IACtCyC,OAAO,CAACZ,mBAAmB,CAAC,IAAI,CAAC;IACjCf,MAAM,CAAC2B,OAAO,CAACD,GAAG,CAAC,CAAC,CAAC,CAACzB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACrC,CAAC,CAAC;EAEFL,EAAE,CAAC,2FAA2F,EAAE,YAAW;IAC1G,IAAMU,IAAI,GAAG,IAAIf,QAAQ,CAACJ,UAAU,CAAC;IACrCmB,IAAI,CAACQ,mBAAmB,CAAC,IAAI,CAAC;IAC9Bf,MAAM,CAACb,IAAI,CAACoB,IAAI,CAACqB,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC3B,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAE3D,IAAMyB,OAAO,GAAG,IAAInC,QAAQ,CAACN,QAAQ,CAAC;IACtCyC,OAAO,CAACZ,mBAAmB,CAAC,IAAI,CAAC;IACjCf,MAAM,CAAC2B,OAAO,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAAC3B,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;EACpD,CAAC,CAAC;AACH,CAAC,CAAC;AAEF,SAASf,IAAIA,CAAC0C,SAAS,EAAE;EACxB,OAAAC,OAAA,CAAcD,SAAS;AACxB", "ignoreList": []}