{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoLinkTag.ts"], "names": ["get<PERSON><PERSON><PERSON>", "memo", "memorize", "target", "styleTarget", "document", "querySelector", "window", "HTMLIFrameElement", "contentDocument", "head", "e", "module", "exports", "url", "options", "attributes", "nonce", "__webpack_nonce__", "link", "createElement", "rel", "href", "Object", "keys", "for<PERSON>ach", "key", "setAttribute", "insert", "Error", "append<PERSON><PERSON><PERSON>", "newUrl", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,MAAMA,YAAY,AAAC,CAAA;IACjB,MAAMC,OAAY,CAAC;IAEnB,OAAO,SAASC,SAASC,MAAW;QAClC,IAAI,OAAOF,IAAI,CAACE,OAAO,KAAK,aAAa;YACvC,IAAIC,cAAcC,SAASC,aAAa,CAACH;YAEzC,iEAAiE;YACjE,IACEI,OAAOC,iBAAiB,IACxBJ,uBAAuBG,OAAOC,iBAAiB,EAC/C;gBACA,IAAI;oBACF,8DAA8D;oBAC9D,mCAAmC;oBACnCJ,cAAc,AAACA,YAAoBK,eAAe,CAACC,IAAI;gBACzD,EAAE,OAAOC,GAAG;oBACV,uBAAuB;oBACvBP,cAAc;gBAChB;YACF;YAEAH,IAAI,CAACE,OAAO,GAAGC;QACjB;QAEA,OAAOH,IAAI,CAACE,OAAO;IACrB;AACF,CAAA;AAIAS,OAAOC,OAAO,GAAG,CAACC,KAAUC;IAC1BA,UAAUA,WAAW,CAAC;IACtBA,QAAQC,UAAU,GAChB,OAAOD,QAAQC,UAAU,KAAK,WAAWD,QAAQC,UAAU,GAAG,CAAC;IAEjE,IAAI,OAAOD,QAAQC,UAAU,CAACC,KAAK,KAAK,aAAa;QACnD,MAAMA,QACJ,oCAAoC;QACpC,OAAOC,sBAAsB,cAAcA,oBAAoB;QAEjE,IAAID,OAAO;YACTF,QAAQC,UAAU,CAACC,KAAK,GAAGA;QAC7B;IACF;IAEA,MAAME,OAAOd,SAASe,aAAa,CAAC;IAEpCD,KAAKE,GAAG,GAAG;IACXF,KAAKG,IAAI,GAAGR;IAEZS,OAAOC,IAAI,CAACT,QAAQC,UAAU,EAAES,OAAO,CAAC,CAACC;QACvCP,KAAKQ,YAAY,CAACD,KAAKX,QAAQC,UAAU,CAACU,IAAI;IAChD;IAEA,IAAI,OAAOX,QAAQa,MAAM,KAAK,YAAY;QACxCb,QAAQa,MAAM,CAACT;IACjB,OAAO;QACL,MAAMhB,SAASH,UAAUe,QAAQa,MAAM,IAAI;QAE3C,IAAI,CAACzB,QAAQ;YACX,MAAM,IAAI0B,MACR;QAEJ;QAEA1B,OAAO2B,WAAW,CAACX;IACrB;IAEA,OAAO,CAACY;QACN,IAAI,OAAOA,WAAW,UAAU;YAC9BZ,KAAKG,IAAI,GAAGS;QACd,OAAO;YACLZ,KAAKa,UAAU,CAAEC,WAAW,CAACd;QAC/B;IACF;AACF"}