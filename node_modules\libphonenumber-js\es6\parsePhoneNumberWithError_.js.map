{"version": 3, "file": "parsePhoneNumberWithError_.js", "names": ["parse", "parsePhoneNumberWithError", "text", "options", "metadata", "_objectSpread", "v2"], "sources": ["../source/parsePhoneNumberWithError_.js"], "sourcesContent": ["import parse from './parse.js'\r\n\r\nexport default function parsePhoneNumberWithError(text, options, metadata) {\r\n\treturn parse(text, { ...options, v2: true }, metadata)\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,MAAM,YAAY;AAE9B,eAAe,SAASC,yBAAyBA,CAACC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC1E,OAAOJ,KAAK,CAACE,IAAI,EAAAG,aAAA,CAAAA,aAAA,KAAOF,OAAO;IAAEG,EAAE,EAAE;EAAI,IAAIF,QAAQ,CAAC;AACvD", "ignoreList": []}