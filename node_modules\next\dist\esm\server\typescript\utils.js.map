{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "names": ["path", "ts", "info", "appDirRegExp", "log", "message", "project", "projectService", "logger", "init", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "getTs", "getInfo", "getType<PERSON><PERSON>cker", "languageService", "getProgram", "getSource", "fileName", "getSourceFile", "removeStringQuotes", "str", "isPositionInsideNode", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefaultFunctionExport", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "isInsideApp", "filePath", "test", "isAppEntryFile", "basename", "isPageFile", "getEntryInfo", "throwOnInvalidDirective", "source", "isDirective", "isClientEntry", "isServerEntry", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth", "client", "server"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAKvB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEJ,OAAO,SAASC,IAAIC,OAAe;IACjCH,KAAKI,OAAO,CAACC,cAAc,CAACC,MAAM,CAACN,IAAI,CAACG;AAC1C;AAEA,4CAA4C;AAC5C,OAAO,SAASI,KAAKC,IAGpB;IACCT,KAAKS,KAAKT,EAAE;IACZC,OAAOQ,KAAKR,IAAI;IAChB,MAAMS,aAAaT,KAAKI,OAAO,CAACM,mBAAmB;IACnDT,eAAe,IAAIU,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAEvDV,IAAI,yCAAyCO;AAC/C;AAEA,OAAO,SAASI;IACd,OAAOd;AACT;AAEA,OAAO,SAASe;IACd,OAAOd;AACT;AAEA,OAAO,SAASe;QACPf;IAAP,QAAOA,mCAAAA,KAAKgB,eAAe,CAACC,UAAU,uBAA/BjB,iCAAmCe,cAAc;AAC1D;AAEA,OAAO,SAASG,UAAUC,QAAgB;QACjCnB;IAAP,QAAOA,mCAAAA,KAAKgB,eAAe,CAACC,UAAU,uBAA/BjB,iCAAmCoB,aAAa,CAACD;AAC1D;AAEA,OAAO,SAASE,mBAAmBC,GAAW;IAC5C,OAAOA,IAAIV,OAAO,CAAC,kBAAkB;AACvC;AAEA,OAAO,MAAMW,uBAAuB,CAACC,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE,EAAC;AAED,OAAO,MAAMG,0BAA0B,CACrCJ;IAEA,IAAI1B,GAAG+B,qBAAqB,CAACL,OAAO;QAClC,IAAIM,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIP,KAAKQ,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYT,KAAKQ,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAKpC,GAAGqC,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAKpC,GAAGqC,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT,EAAC;AAED,OAAO,MAAMO,cAAc,CAACC;IAC1B,OAAOvC,aAAawC,IAAI,CAACD;AAC3B,EAAC;AACD,OAAO,MAAME,iBAAiB,CAACF;IAC7B,OACEvC,aAAawC,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAAC3C,KAAK6C,QAAQ,CAACH;AAE9D,EAAC;AACD,OAAO,MAAMI,aAAa,CAACJ;IACzB,OACEvC,aAAawC,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAAC3C,KAAK6C,QAAQ,CAACH;AAErD,EAAC;AAED,uCAAuC;AACvC,OAAO,SAASK,aACd1B,QAAgB,EAChB2B,uBAAiC;IAEjC,MAAMC,SAAS7B,UAAUC;IACzB,IAAI4B,QAAQ;QACV,IAAIC,cAAc;QAClB,IAAIC,gBAAgB;QACpB,IAAIC,gBAAgB;QAEpBnD,GAAGoD,YAAY,CAACJ,QAAS,CAACtB;YACxB,IACE1B,GAAGqD,qBAAqB,CAAC3B,SACzB1B,GAAGsD,eAAe,CAAC5B,KAAK6B,UAAU,GAClC;gBACA,IAAI7B,KAAK6B,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIP,aAAa;wBACfC,gBAAgB;oBAClB,OAAO;wBACL,IAAIH,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF,OAAO,IAAI/B,KAAK6B,UAAU,CAACC,IAAI,KAAK,cAAc;oBAChD,IAAIP,aAAa;wBACfE,gBAAgB;oBAClB,OAAO;wBACL,IAAIJ,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;gBAEA,IAAIP,iBAAiBC,eAAe;oBAClC,MAAMM,IAAI;wBACRC,aACE;wBACF/B,OAAOD,KAAK6B,UAAU,CAACI,QAAQ;wBAC/BC,QAAQlC,KAAK6B,UAAU,CAACM,QAAQ;oBAClC;oBACA,MAAMJ;gBACR;YACF,OAAO;gBACLR,cAAc;YAChB;QACF;QAEA,OAAO;YAAEa,QAAQZ;YAAea,QAAQZ;QAAc;IACxD;IAEA,OAAO;QAAEW,QAAQ;QAAOC,QAAQ;IAAM;AACxC"}