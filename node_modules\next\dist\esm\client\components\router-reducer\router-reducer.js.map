{"version": 3, "sources": ["../../../../src/client/components/router-reducer/router-reducer.ts"], "names": ["ACTION_NAVIGATE", "ACTION_SERVER_PATCH", "ACTION_RESTORE", "ACTION_REFRESH", "ACTION_PREFETCH", "ACTION_FAST_REFRESH", "ACTION_SERVER_ACTION", "navigateReducer", "serverPatchReducer", "restoreReducer", "refreshReducer", "prefetchReducer", "fastRefreshReducer", "serverActionReducer", "clientReducer", "state", "action", "type", "Error", "serverReducer", "_action", "reducer", "window"], "mappings": "AAAA,SACEA,eAAe,EACfC,mBAAmB,EACnBC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,mBAAmB,EACnBC,oBAAoB,QACf,yBAAwB;AAM/B,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SAASC,kBAAkB,QAAQ,kCAAiC;AACpE,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SAASC,kBAAkB,QAAQ,kCAAiC;AACpE,SAASC,mBAAmB,QAAQ,mCAAkC;AAEtE;;CAEC,GACD,SAASC,cACPC,KAA2B,EAC3BC,MAAsB;IAEtB,OAAQA,OAAOC,IAAI;QACjB,KAAKjB;YAAiB;gBACpB,OAAOO,gBAAgBQ,OAAOC;YAChC;QACA,KAAKf;YAAqB;gBACxB,OAAOO,mBAAmBO,OAAOC;YACnC;QACA,KAAKd;YAAgB;gBACnB,OAAOO,eAAeM,OAAOC;YAC/B;QACA,KAAKb;YAAgB;gBACnB,OAAOO,eAAeK,OAAOC;YAC/B;QACA,KAAKX;YAAqB;gBACxB,OAAOO,mBAAmBG,OAAOC;YACnC;QACA,KAAKZ;YAAiB;gBACpB,OAAOO,gBAAgBI,OAAOC;YAChC;QACA,KAAKV;YAAsB;gBACzB,OAAOO,oBAAoBE,OAAOC;YACpC;QACA,+DAA+D;QAC/D;YACE,MAAM,IAAIE,MAAM;IACpB;AACF;AAEA,SAASC,cACPJ,KAA2B,EAC3BK,OAAuB;IAEvB,OAAOL;AACT;AAEA,mGAAmG;AACnG,OAAO,MAAMM,UACX,OAAOC,WAAW,cAAcH,gBAAgBL,cAAa"}