'use strict';Object.defineProperty(exports, "__esModule", { value: true });var _slicedToArray = function () {function sliceIterator(arr, i) {var _arr = [];var _n = true;var _d = false;var _e = undefined;try {for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {_arr.push(_s.value);if (i && _arr.length === i) break;}} catch (err) {_d = true;_e = err;} finally {try {if (!_n && _i["return"]) _i["return"]();} finally {if (_d) throw _e;}}return _arr;}return function (arr, i) {if (Array.isArray(arr)) {return arr;} else if (Symbol.iterator in Object(arr)) {return sliceIterator(arr, i);} else {throw new TypeError("Invalid attempt to destructure non-iterable instance");}};}();exports.






isScoped = isScoped;exports.

















isAbsolute = isAbsolute;exports.




isBuiltIn = isBuiltIn;exports.










































































isExternalModule = isExternalModule;exports.






isExternalModuleMain = isExternalModuleMain;exports.







isScopedMain = isScopedMain;exports['default'] =



resolveImportType;var _path = require('path');var _isCoreModule = require('is-core-module');var _isCoreModule2 = _interopRequireDefault(_isCoreModule);var _resolve = require('eslint-module-utils/resolve');var _resolve2 = _interopRequireDefault(_resolve);var _packagePath = require('./packagePath');function _interopRequireDefault(obj) {return obj && obj.__esModule ? obj : { 'default': obj };}var scopedRegExp = /^@[^/]+\/?[^/]+/;function isScoped(name) {return name && scopedRegExp.test(name);}function baseModule(name) {if (isScoped(name)) {var _name$split = name.split('/'),_name$split2 = _slicedToArray(_name$split, 2),scope = _name$split2[0],_pkg = _name$split2[1];return String(scope) + '/' + String(_pkg);}var _name$split3 = name.split('/'),_name$split4 = _slicedToArray(_name$split3, 1),pkg = _name$split4[0];return pkg;}function isInternalRegexMatch(name, settings) {var internalScope = settings && settings['import/internal-regex'];return internalScope && new RegExp(internalScope).test(name);}function isAbsolute(name) {return typeof name === 'string' && (0, _path.isAbsolute)(name);} // path is defined only when a resolver resolves to a non-standard path
function isBuiltIn(name, settings, path) {if (path || !name) {return false;}var base = baseModule(name);var extras = settings && settings['import/core-modules'] || [];return (0, _isCoreModule2['default'])(base) || extras.indexOf(base) > -1;}var moduleRegExp = /^\w/;function isModule(name) {return name && moduleRegExp.test(name);}var moduleMainRegExp = /^[\w]((?!\/).)*$/;function isModuleMain(name) {return name && moduleMainRegExp.test(name);}function isRelativeToParent(name) {return (/^\.\.$|^\.\.[\\/]/.test(name));}var indexFiles = ['.', './', './index', './index.js'];function isIndex(name) {return indexFiles.indexOf(name) !== -1;}function isRelativeToSibling(name) {return (/^\.[\\/]/.test(name));}function isExternalPath(path, context) {if (!path) {return false;}var settings = context.settings;var packagePath = (0, _packagePath.getContextPackagePath)(context);if ((0, _path.relative)(packagePath, path).startsWith('..')) {return true;}var folders = settings && settings['import/external-module-folders'] || ['node_modules'];return folders.some(function (folder) {var folderPath = (0, _path.resolve)(packagePath, folder);var relativePath = (0, _path.relative)(folderPath, path);return !relativePath.startsWith('..');});}function isInternalPath(path, context) {if (!path) {return false;}var packagePath = (0, _packagePath.getContextPackagePath)(context);return !(0, _path.relative)(packagePath, path).startsWith('../');}function isExternalLookingName(name) {return isModule(name) || isScoped(name);}function typeTest(name, context, path) {var settings = context.settings;if (isInternalRegexMatch(name, settings)) {return 'internal';}if (isAbsolute(name, settings, path)) {return 'absolute';}if (isBuiltIn(name, settings, path)) {return 'builtin';}if (isRelativeToParent(name, settings, path)) {return 'parent';}if (isIndex(name, settings, path)) {return 'index';}if (isRelativeToSibling(name, settings, path)) {return 'sibling';}if (isExternalPath(path, context)) {return 'external';}if (isInternalPath(path, context)) {return 'internal';}if (isExternalLookingName(name)) {return 'external';}return 'unknown';}function isExternalModule(name, path, context) {if (arguments.length < 3) {throw new TypeError('isExternalModule: name, path, and context are all required');}return (isModule(name) || isScoped(name)) && typeTest(name, context, path) === 'external';}function isExternalModuleMain(name, path, context) {if (arguments.length < 3) {throw new TypeError('isExternalModule: name, path, and context are all required');}return isModuleMain(name) && typeTest(name, context, path) === 'external';}var scopedMainRegExp = /^@[^/]+\/?[^/]+$/;function isScopedMain(name) {return name && scopedMainRegExp.test(name);}function resolveImportType(name, context) {return typeTest(name, context, (0, _resolve2['default'])(name, context));
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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