{"version": 3, "sources": ["../../../../src/build/webpack/loaders/utils.ts"], "names": ["decodeFromBase64", "encodeToBase64", "generateActionId", "getActionsFromBuildInfo", "getLoaderModuleNamedExports", "isActionServerLayerEntryModule", "isCSSMod", "isClientComponentEntryModule", "regexCSS", "imageExtensions", "imageRegex", "RegExp", "join", "mod", "rscInfo", "buildInfo", "rsc", "actions", "type", "RSC_MODULE_TYPES", "server", "isActionClientLayerModule", "client", "hasClientDirective", "isClientRef", "isActionLayerEntry", "test", "resource", "loaders", "some", "loader", "includes", "filePath", "exportName", "createHash", "update", "digest", "obj", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "toString", "str", "parse", "resourcePath", "context", "Promise", "res", "rej", "loadModule", "err", "_source", "_sourceMap", "module", "exportNames", "dependencies", "filter", "dep", "constructor", "name", "map"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAwEgBA,gBAAgB;eAAhBA;;IAJAC,cAAc;eAAdA;;IANAC,gBAAgB;eAAhBA;;IAPAC,uBAAuB;eAAvBA;;IAqBMC,2BAA2B;eAA3BA;;IApENC,8BAA8B;eAA9BA;;IA8BAC,QAAQ;eAARA;;IAhBAC,4BAA4B;eAA5BA;;IAYHC,QAAQ;eAARA;;;wBAjCc;2BACM;AAEjC,MAAMC,kBAAkB;IAAC;IAAO;IAAQ;IAAO;IAAQ;IAAQ;IAAO;CAAM;AAC5E,MAAMC,aAAa,IAAIC,OAAO,CAAC,IAAI,EAAEF,gBAAgBG,IAAI,CAAC,KAAK,EAAE,CAAC;AAG3D,SAASP,+BAA+BQ,GAG9C;IACC,MAAMC,UAAUD,IAAIE,SAAS,CAACC,GAAG;IACjC,OAAO,CAAC,CAAEF,CAAAA,CAAAA,2BAAAA,QAASG,OAAO,KAAIH,CAAAA,2BAAAA,QAASI,IAAI,MAAKC,2BAAgB,CAACC,MAAM,AAAD;AACxE;AAEA,sGAAsG;AACtG,SAASC,0BAA0BR,GAA0C;IAC3E,MAAMC,UAAUD,IAAIE,SAAS,CAACC,GAAG;IACjC,OAAO,CAAC,CAAEF,CAAAA,CAAAA,2BAAAA,QAASG,OAAO,KAAIH,CAAAA,2BAAAA,QAASI,IAAI,MAAKC,2BAAgB,CAACG,MAAM,AAAD;AACxE;AAEO,SAASf,6BAA6BM,GAG5C;IACC,MAAMC,UAAUD,IAAIE,SAAS,CAACC,GAAG;IACjC,MAAMO,qBAAqBT,2BAAAA,QAASU,WAAW;IAC/C,MAAMC,qBAAqBJ,0BAA0BR;IACrD,OACEU,sBAAsBE,sBAAsBf,WAAWgB,IAAI,CAACb,IAAIc,QAAQ;AAE5E;AAEO,MAAMnB,WAAW;AAIjB,SAASF,SAASO,GAIxB;QAIGA;IAHF,OAAO,CAAC,CACNA,CAAAA,IAAIK,IAAI,KAAK,sBACZL,IAAIc,QAAQ,IAAInB,SAASkB,IAAI,CAACb,IAAIc,QAAQ,OAC3Cd,eAAAA,IAAIe,OAAO,qBAAXf,aAAagB,IAAI,CACf,CAAC,EAAEC,MAAM,EAAE,GACTA,OAAOC,QAAQ,CAAC,iCAChBD,OAAOC,QAAQ,CAAC,wCAChBD,OAAOC,QAAQ,CAAC,4CACpB;AAEJ;AAEO,SAAS5B,wBAAwBU,GAGvC;QACQA,oBAAAA;IAAP,QAAOA,iBAAAA,IAAIE,SAAS,sBAAbF,qBAAAA,eAAeG,GAAG,qBAAlBH,mBAAoBI,OAAO;AACpC;AAEO,SAASf,iBAAiB8B,QAAgB,EAAEC,UAAkB;IACnE,OAAOC,IAAAA,kBAAU,EAAC,QACfC,MAAM,CAACH,WAAW,MAAMC,YACxBG,MAAM,CAAC;AACZ;AAEO,SAASnC,eAA6BoC,GAAM;IACjD,OAAOC,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ,MAAMK,QAAQ,CAAC;AACnD;AAEO,SAAS1C,iBAA+B2C,GAAW;IACxD,OAAOH,KAAKI,KAAK,CAACN,OAAOC,IAAI,CAACI,KAAK,UAAUD,QAAQ,CAAC;AACxD;AAEO,eAAetC,4BACpByC,YAAoB,EACpBC,OAAmC;QAejCjC;IAbF,MAAMA,MAAM,MAAM,IAAIkC,QAA8B,CAACC,KAAKC;QACxDH,QAAQI,UAAU,CAChBL,cACA,CAACM,KAAmBC,SAAcC,YAAiBC;YACjD,IAAIH,KAAK;gBACP,OAAOF,IAAIE;YACb;YACAH,IAAIM;QACN;IAEJ;IAEA,MAAMC,cACJ1C,EAAAA,oBAAAA,IAAI2C,YAAY,qBAAhB3C,kBACI4C,MAAM,CAAC,CAACC;QACR,OACE;YACE;YACA;SACD,CAAC3B,QAAQ,CAAC2B,IAAIC,WAAW,CAACC,IAAI,KAC/B,UAAUF,OACVA,IAAIE,IAAI,KAAK;IAEjB,GACCC,GAAG,CAAC,CAACH;QACJ,OAAOA,IAAIE,IAAI;IACjB,OAAM,EAAE;IACZ,OAAOL;AACT"}