{"version": 3, "sources": ["../../../src/build/webpack-config-rules/resolve.ts"], "names": ["COMPILER_NAMES", "edgeConditionNames", "mainFieldsPerCompiler", "server", "client", "edgeServer", "getMainField", "compilerType", "preferEsm"], "mappings": "AAAA,SACEA,cAAc,QAET,6BAA4B;AAEnC,0BAA0B;AAC1B,OAAO,MAAMC,qBAAqB;IAChC;IACA;IACA,kCAAkC;IAClC;CACD,CAAA;AAED,MAAMC,wBAGF;IACF,2EAA2E;IAC3E,CAACF,eAAeG,MAAM,CAAC,EAAE;QAAC;QAAQ;KAAS;IAC3C,CAACH,eAAeI,MAAM,CAAC,EAAE;QAAC;QAAW;QAAU;KAAO;IACtD,CAACJ,eAAeK,UAAU,CAAC,EAAEJ;IAC7B,iDAAiD;IACjD,cAAc;QAAC;QAAU;KAAO;AAClC;AAEA,OAAO,SAASK,aACdC,YAAgC,EAChCC,SAAkB;IAElB,IAAID,iBAAiBP,eAAeK,UAAU,EAAE;QAC9C,OAAOJ;IACT,OAAO,IAAIM,iBAAiBP,eAAeI,MAAM,EAAE;QACjD,OAAOF,qBAAqB,CAACF,eAAeI,MAAM,CAAC;IACrD;IAEA,gFAAgF;IAChF,OAAOI,YACHN,qBAAqB,CAAC,aAAa,GACnCA,qBAAqB,CAACF,eAAeG,MAAM,CAAC;AAClD"}