/**
 * @license React
 * react-jsx-dev-runtime.profiling.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

'use strict';

// ATTENTION
const REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');

const jsxDEV = undefined;

exports.Fragment = REACT_FRAGMENT_TYPE;
exports.jsxDEV = jsxDEV;