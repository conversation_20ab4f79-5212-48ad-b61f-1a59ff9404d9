{"version": 3, "sources": ["../../src/build/build-context.ts"], "names": ["NextBuildContext", "getPluginState", "getProxiedPluginState", "resumePluginState", "pluginState", "resumedState", "Object", "assign", "initialState", "Proxy", "get", "target", "key", "set", "value"], "mappings": ";;;;;;;;;;;;;;;;;IAkDaA,gBAAgB;eAAhBA;;IAZGC,cAAc;eAAdA;;IAjBAC,qBAAqB;eAArBA;;IAPAC,iBAAiB;eAAjBA;;;AALhB,4EAA4E;AAC5E,4EAA4E;AAC5E,+DAA+D;AAC/D,+CAA+C;AAC/C,IAAIC,cAAmC,CAAC;AACjC,SAASD,kBAAkBE,YAAkC;IAClEC,OAAOC,MAAM,CAACH,aAAaC;AAC7B;AAKO,SAASH,sBACdM,YAAmB;IAEnB,OAAO,IAAIC,MAAML,aAAa;QAC5BM,KAAIC,MAAM,EAAEC,GAAW;YACrB,IAAI,OAAOD,MAAM,CAACC,IAAI,KAAK,aAAa;gBACtC,OAAQD,MAAM,CAACC,IAAI,GAAGJ,YAAY,CAACI,IAAI;YACzC;YACA,OAAOD,MAAM,CAACC,IAAI;QACpB;QACAC,KAAIF,MAAM,EAAEC,GAAW,EAAEE,KAAK;YAC5BH,MAAM,CAACC,IAAI,GAAGE;YACd,OAAO;QACT;IACF;AACF;AAEO,SAASb;IACd,OAAOG;AACT;AAUO,MAAMJ,mBA2CR,CAAC"}