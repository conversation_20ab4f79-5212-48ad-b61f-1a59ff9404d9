{"version": 3, "file": "getExampleNumber.js", "names": ["_PhoneNumber", "_interopRequireDefault", "require", "e", "__esModule", "getExampleNumber", "country", "examples", "metadata", "PhoneNumber"], "sources": ["../source/getExampleNumber.js"], "sourcesContent": ["import PhoneNumber from './PhoneNumber.js'\r\n\r\nexport default function getExampleNumber(country, examples, metadata) {\r\n\tif (examples[country]) {\r\n\t\treturn new PhoneNumber(country, examples[country], metadata)\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE3B,SAASE,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACrE,IAAID,QAAQ,CAACD,OAAO,CAAC,EAAE;IACtB,OAAO,IAAIG,uBAAW,CAACH,OAAO,EAAEC,QAAQ,CAACD,OAAO,CAAC,EAAEE,QAAQ,CAAC;EAC7D;AACD", "ignoreList": []}