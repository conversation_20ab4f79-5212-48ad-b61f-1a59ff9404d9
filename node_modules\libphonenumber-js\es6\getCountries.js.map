{"version": 3, "file": "getCountries.js", "names": ["<PERSON><PERSON><PERSON>", "getCountries", "metadata"], "sources": ["../source/getCountries.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\n\r\nexport default function getCountries(metadata) {\r\n\treturn new Metadata(metadata).getCountries()\r\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC9C,OAAO,IAAIF,QAAQ,CAACE,QAAQ,CAAC,CAACD,YAAY,CAAC,CAAC;AAC7C", "ignoreList": []}