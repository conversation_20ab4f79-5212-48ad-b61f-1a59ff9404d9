{"version": 3, "sources": ["../../../src/build/output/index.ts"], "names": ["bold", "red", "yellow", "stripAnsi", "textTable", "createStore", "formatWebpackMessages", "store", "consoleStore", "COMPILER_NAMES", "startedDevelopmentServer", "appUrl", "bindAddr", "setState", "formatAmpMessages", "amp", "output", "messages", "chalkError", "ampError", "page", "error", "push", "message", "specUrl", "chalk<PERSON>arn", "ampWarn", "warn", "errors", "warnings", "dev<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "code", "filter", "length", "index", "align", "stringLength", "str", "buildStore", "client", "server", "edgeServer", "buildWasDone", "clientWasLoading", "serverWasLoading", "edgeServerWasLoading", "subscribe", "state", "trigger", "url", "getState", "loading", "bootstrap", "partialState", "typeChecking", "totalModulesCount", "hasEdgeServer", "concat", "ampValidation", "Object", "keys", "k", "sort", "reduce", "a", "c", "newAmp", "watchCompilers", "undefined", "tapCompiler", "key", "compiler", "onEvent", "hooks", "invalid", "tap", "done", "stats", "to<PERSON><PERSON>", "preset", "moduleTrace", "hasErrors", "hasWarnings", "compilation", "modules", "size", "status", "reportTrigger"], "mappings": "AAAA,SAASA,IAAI,EAAEC,GAAG,EAAEC,MAAM,QAAQ,uBAAsB;AACxD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,eAAe,gCAA+B;AACrD,OAAOC,iBAAiB,8BAA6B;AACrD,OAAOC,2BAA2B,qFAAoF;AACtH,SAASC,SAASC,YAAY,QAAQ,UAAS;AAG/C,SAASC,cAAc,QAAQ,6BAA4B;AAG3D,OAAO,SAASC,yBAAyBC,MAAc,EAAEC,QAAgB;IACvEJ,aAAaK,QAAQ,CAAC;QAAEF;QAAQC;IAAS;AAC3C;AAiCA,OAAO,SAASE,kBAAkBC,GAAkB;IAClD,IAAIC,SAAShB,KAAK,oBAAoB;IACtC,IAAIiB,WAAuB,EAAE;IAE7B,MAAMC,aAAajB,IAAI;IACvB,SAASkB,SAASC,IAAY,EAAEC,KAAgB;QAC9CJ,SAASK,IAAI,CAAC;YAACF;YAAMF;YAAYG,MAAME,OAAO;YAAEF,MAAMG,OAAO,IAAI;SAAG;IACtE;IAEA,MAAMC,YAAYvB,OAAO;IACzB,SAASwB,QAAQN,IAAY,EAAEO,IAAe;QAC5CV,SAASK,IAAI,CAAC;YAACF;YAAMK;YAAWE,KAAKJ,OAAO;YAAEI,KAAKH,OAAO,IAAI;SAAG;IACnE;IAEA,IAAK,MAAMJ,QAAQL,IAAK;QACtB,IAAI,EAAEa,MAAM,EAAEC,QAAQ,EAAE,GAAGd,GAAG,CAACK,KAAK;QAEpC,MAAMU,gBAAgB,CAACC,MAAmBA,IAAIC,IAAI,KAAK;QACvDJ,SAASA,OAAOK,MAAM,CAACH;QACvBD,WAAWA,SAASI,MAAM,CAACH;QAC3B,IAAI,CAAEF,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;YAEvC;QACF;QAEA,IAAIN,OAAOM,MAAM,EAAE;YACjBf,SAASC,MAAMQ,MAAM,CAAC,EAAE;YACxB,IAAK,IAAIO,QAAQ,GAAGA,QAAQP,OAAOM,MAAM,EAAE,EAAEC,MAAO;gBAClDhB,SAAS,IAAIS,MAAM,CAACO,MAAM;YAC5B;QACF;QACA,IAAIN,SAASK,MAAM,EAAE;YACnBR,QAAQE,OAAOM,MAAM,GAAG,KAAKd,MAAMS,QAAQ,CAAC,EAAE;YAC9C,IAAK,IAAIM,QAAQ,GAAGA,QAAQN,SAASK,MAAM,EAAE,EAAEC,MAAO;gBACpDT,QAAQ,IAAIG,QAAQ,CAACM,MAAM;YAC7B;QACF;QACAlB,SAASK,IAAI,CAAC;YAAC;YAAI;YAAI;YAAI;SAAG;IAChC;IAEA,IAAI,CAACL,SAASiB,MAAM,EAAE;QACpB,OAAO;IACT;IAEAlB,UAAUZ,UAAUa,UAAU;QAC5BmB,OAAO;YAAC;YAAK;YAAK;YAAK;SAAI;QAC3BC,cAAaC,GAAW;YACtB,OAAOnC,UAAUmC,KAAKJ,MAAM;QAC9B;IACF;IAEA,OAAOlB;AACT;AAEA,MAAMuB,aAAalC,YAA8B;IAC/C,iCAAiC;IACjCmC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,QAAQ,CAAC;IACT,iCAAiC;IACjCC,YAAY,CAAC;AACf;AACA,IAAIC,eAAe;AACnB,IAAIC,mBAAmB;AACvB,IAAIC,mBAAmB;AACvB,IAAIC,uBAAuB;AAE3BP,WAAWQ,SAAS,CAAC,CAACC;IACpB,MAAM,EAAEjC,GAAG,EAAEyB,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEO,OAAO,EAAEC,GAAG,EAAE,GAAGF;IAE1D,MAAM,EAAErC,MAAM,EAAE,GAAGH,aAAa2C,QAAQ;IAExC,IAAIX,OAAOY,OAAO,IAAIX,OAAOW,OAAO,KAAIV,8BAAAA,WAAYU,OAAO,GAAE;QAC3D5C,aAAaK,QAAQ,CACnB;YACEwC,WAAW;YACX1C,QAAQA;YACR,wEAAwE;YACxEyC,SAAS;YACTH;YACAC;QACF,GACA;QAEFN,mBAAmB,AAAC,CAACD,gBAAgBC,oBAAqBJ,OAAOY,OAAO;QACxEP,mBAAmB,AAAC,CAACF,gBAAgBE,oBAAqBJ,OAAOW,OAAO;QACxEN,uBACE,AAAC,CAACH,gBAAgBG,wBAAyBJ,WAAWU,OAAO;QAC/DT,eAAe;QACf;IACF;IAEAA,eAAe;IAEf,IAAIW,eAAqC;QACvCD,WAAW;QACX1C,QAAQA;QACRyC,SAAS;QACTG,cAAc;QACdC,mBACE,AAACZ,CAAAA,mBAAmBJ,OAAOgB,iBAAiB,GAAG,CAAA,IAC9CX,CAAAA,mBAAmBJ,OAAOe,iBAAiB,GAAG,CAAA,IAC9CV,CAAAA,uBAAuBJ,CAAAA,8BAAAA,WAAYc,iBAAiB,KAAI,IAAI,CAAA;QAC/DC,eAAe,CAAC,CAACf;IACnB;IACA,IAAIF,OAAOZ,MAAM,IAAIgB,kBAAkB;QACrC,0BAA0B;QAC1BpC,aAAaK,QAAQ,CACnB;YACE,GAAGyC,YAAY;YACf1B,QAAQY,OAAOZ,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIY,OAAOb,MAAM,IAAIiB,kBAAkB;QAC5CrC,aAAaK,QAAQ,CACnB;YACE,GAAGyC,YAAY;YACf1B,QAAQa,OAAOb,MAAM;YACrBC,UAAU;QACZ,GACA;IAEJ,OAAO,IAAIa,WAAWd,MAAM,IAAIkB,sBAAsB;QACpDtC,aAAaK,QAAQ,CACnB;YACE,GAAGyC,YAAY;YACf1B,QAAQc,WAAWd,MAAM;YACzBC,UAAU;QACZ,GACA;IAEJ,OAAO;QACL,iCAAiC;QACjC,MAAMA,WAAW;eACXW,OAAOX,QAAQ,IAAI,EAAE;eACrBY,OAAOZ,QAAQ,IAAI,EAAE;eACrBa,WAAWb,QAAQ,IAAI,EAAE;SAC9B,CAAC6B,MAAM,CAAC5C,kBAAkBC,QAAQ,EAAE;QAErCP,aAAaK,QAAQ,CACnB;YACE,GAAGyC,YAAY;YACf1B,QAAQ;YACRC,UAAUA,SAASK,MAAM,KAAK,IAAI,OAAOL;QAC3C,GACA;IAEJ;AACF;AAEA,OAAO,SAAS8B,cACdvC,IAAY,EACZQ,MAAmB,EACnBC,QAAqB;IAErB,MAAM,EAAEd,GAAG,EAAE,GAAGwB,WAAWY,QAAQ;IACnC,IAAI,CAAEvB,CAAAA,OAAOM,MAAM,IAAIL,SAASK,MAAM,AAAD,GAAI;QACvCK,WAAW1B,QAAQ,CAAC;YAClBE,KAAK6C,OAAOC,IAAI,CAAC9C,KACdkB,MAAM,CAAC,CAAC6B,IAAMA,MAAM1C,MACpB2C,IAAI,EACL,wCAAwC;aACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGnD,GAAG,CAACmD,EAAE,EAAGD,CAAAA,GAAI,CAAC;QAC7C;QACA;IACF;IAEA,MAAME,SAAwB;QAAE,GAAGpD,GAAG;QAAE,CAACK,KAAK,EAAE;YAAEQ;YAAQC;QAAS;IAAE;IACrEU,WAAW1B,QAAQ,CAAC;QAClBE,KAAK6C,OAAOC,IAAI,CAACM,QACdJ,IAAI,EACL,wCAAwC;SACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGC,MAAM,CAACD,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAChD;AACF;AAEA,OAAO,SAASG,eACd5B,MAAwB,EACxBC,MAAwB,EACxBC,UAA4B;IAE5BH,WAAW1B,QAAQ,CAAC;QAClB2B,QAAQ;YAAEY,SAAS;QAAK;QACxBX,QAAQ;YAAEW,SAAS;QAAK;QACxBV,YAAY;YAAEU,SAAS;QAAK;QAC5BH,SAAS;QACTC,KAAKmB;IACP;IAEA,SAASC,YACPC,GAAuB,EACvBC,QAA0B,EAC1BC,OAAwC;QAExCD,SAASE,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,CAAC,cAAc,EAAEL,IAAI,CAAC,EAAE;YACjDE,QAAQ;gBAAErB,SAAS;YAAK;QAC1B;QAEAoB,SAASE,KAAK,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,WAAW,EAAEL,IAAI,CAAC,EAAE,CAACO;YAC5CvC,WAAW1B,QAAQ,CAAC;gBAAEE,KAAK,CAAC;YAAE;YAE9B,MAAM,EAAEa,MAAM,EAAEC,QAAQ,EAAE,GAAGvB,sBAC3BwE,MAAMC,MAAM,CAAC;gBACXC,QAAQ;gBACRC,aAAa;YACf;YAGF,MAAMC,YAAY,CAAC,EAACtD,0BAAAA,OAAQM,MAAM;YAClC,MAAMiD,cAAc,CAAC,EAACtD,4BAAAA,SAAUK,MAAM;YAEtCuC,QAAQ;gBACNrB,SAAS;gBACTI,mBAAmBsB,MAAMM,WAAW,CAACC,OAAO,CAACC,IAAI;gBACjD1D,QAAQsD,YAAYtD,SAAS;gBAC7BC,UAAUsD,cAActD,WAAW;YACrC;QACF;IACF;IAEAyC,YAAY7D,eAAe+B,MAAM,EAAEA,QAAQ,CAAC+C;QAC1C,IACE,CAACA,OAAOnC,OAAO,IACf,CAACb,WAAWY,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrC,CAACb,WAAWY,QAAQ,GAAGT,UAAU,CAACU,OAAO,IACzCmC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAjB,WAAW1B,QAAQ,CAAC;gBAClB2B,QAAQ+C;gBACRtC,SAASoB;gBACTnB,KAAKmB;YACP;QACF,OAAO;YACL9B,WAAW1B,QAAQ,CAAC;gBAClB2B,QAAQ+C;YACV;QACF;IACF;IACAjB,YAAY7D,eAAegC,MAAM,EAAEA,QAAQ,CAAC8C;QAC1C,IACE,CAACA,OAAOnC,OAAO,IACf,CAACb,WAAWY,QAAQ,GAAGX,MAAM,CAACY,OAAO,IACrC,CAACb,WAAWY,QAAQ,GAAGT,UAAU,CAACU,OAAO,IACzCmC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAjB,WAAW1B,QAAQ,CAAC;gBAClB4B,QAAQ8C;gBACRtC,SAASoB;gBACTnB,KAAKmB;YACP;QACF,OAAO;YACL9B,WAAW1B,QAAQ,CAAC;gBAClB4B,QAAQ8C;YACV;QACF;IACF;IACAjB,YAAY7D,eAAeiC,UAAU,EAAEA,YAAY,CAAC6C;QAClD,IACE,CAACA,OAAOnC,OAAO,IACf,CAACb,WAAWY,QAAQ,GAAGX,MAAM,CAACY,OAAO,IACrC,CAACb,WAAWY,QAAQ,GAAGV,MAAM,CAACW,OAAO,IACrCmC,OAAO/B,iBAAiB,GAAG,GAC3B;YACAjB,WAAW1B,QAAQ,CAAC;gBAClB6B,YAAY6C;gBACZtC,SAASoB;gBACTnB,KAAKmB;YACP;QACF,OAAO;YACL9B,WAAW1B,QAAQ,CAAC;gBAClB6B,YAAY6C;YACd;QACF;IACF;AACF;AAEA,OAAO,SAASC,cAAcvC,OAAe,EAAEC,GAAY;IACzDX,WAAW1B,QAAQ,CAAC;QAClBoC;QACAC;IACF;AACF"}