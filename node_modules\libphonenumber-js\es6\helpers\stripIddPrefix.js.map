{"version": 3, "file": "stripIddPrefix.js", "names": ["<PERSON><PERSON><PERSON>", "VALID_DIGITS", "CAPTURING_DIGIT_PATTERN", "RegExp", "stripIddPrefix", "number", "country", "callingCode", "metadata", "countryMetadata", "selectNumberingPlan", "IDDPrefixPattern", "IDDPrefix", "search", "slice", "match", "length", "matchedGroups"], "sources": ["../../source/helpers/stripIddPrefix.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport { VALID_DIGITS } from '../constants.js'\r\n\r\nconst CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])')\r\n\r\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\r\n\tif (!country) {\r\n\t\treturn\r\n\t}\r\n\t// Check if the number is IDD-prefixed.\r\n\tconst countryMetadata = new Metadata(metadata)\r\n\tcountryMetadata.selectNumberingPlan(country, callingCode)\r\n\tconst IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix())\r\n\tif (number.search(IDDPrefixPattern) !== 0) {\r\n\t\treturn\r\n\t}\r\n\t// Strip IDD prefix.\r\n\tnumber = number.slice(number.match(IDDPrefixPattern)[0].length)\r\n\t// If there're any digits after an IDD prefix,\r\n\t// then those digits are a country calling code.\r\n\t// Since no country code starts with a `0`,\r\n\t// the code below validates that the next digit (if present) is not `0`.\r\n\tconst matchedGroups = number.match(CAPTURING_DIGIT_PATTERN)\r\n\tif (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\r\n\t\tif (matchedGroups[1] === '0') {\r\n\t\t\treturn\r\n\t\t}\r\n\t}\r\n\treturn number\r\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,IAAMC,uBAAuB,GAAG,IAAIC,MAAM,CAAC,IAAI,GAAGF,YAAY,GAAG,IAAI,CAAC;AAEtE,eAAe,SAASG,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EAC9E,IAAI,CAACF,OAAO,EAAE;IACb;EACD;EACA;EACA,IAAMG,eAAe,GAAG,IAAIT,QAAQ,CAACQ,QAAQ,CAAC;EAC9CC,eAAe,CAACC,mBAAmB,CAACJ,OAAO,EAAEC,WAAW,CAAC;EACzD,IAAMI,gBAAgB,GAAG,IAAIR,MAAM,CAACM,eAAe,CAACG,SAAS,CAAC,CAAC,CAAC;EAChE,IAAIP,MAAM,CAACQ,MAAM,CAACF,gBAAgB,CAAC,KAAK,CAAC,EAAE;IAC1C;EACD;EACA;EACAN,MAAM,GAAGA,MAAM,CAACS,KAAK,CAACT,MAAM,CAACU,KAAK,CAACJ,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAACK,MAAM,CAAC;EAC/D;EACA;EACA;EACA;EACA,IAAMC,aAAa,GAAGZ,MAAM,CAACU,KAAK,CAACb,uBAAuB,CAAC;EAC3D,IAAIe,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,aAAa,CAAC,CAAC,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;IAC7E,IAAIC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC7B;IACD;EACD;EACA,OAAOZ,MAAM;AACd", "ignoreList": []}