{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-font-loader/postcss-next-font.ts"], "names": ["postcss", "postcssNextFontPlugin", "exports", "fontFamilyHash", "fallbackFonts", "adjustFontFallback", "variable", "weight", "style", "postcssPlugin", "Once", "root", "fontFamily", "normalizeFamily", "family", "replace", "formatFamily", "node", "nodes", "type", "name", "familyNode", "find", "decl", "prop", "value", "Error", "adjustFontFallbackFamily", "fallbackFontFace", "atRule", "fallbackFont", "ascentOverride", "descentOverride", "lineGapOverride", "sizeAdjust", "Declaration", "push", "isRange", "trim", "includes", "formattedFontFamilies", "join", "classRule", "Rule", "selector", "varialbeRule", "fontWeight", "Number", "isNaN", "undefined", "fontStyle"], "mappings": "AAEA,OAAOA,aAAa,UAAS;AAE7B;;;;;;;;;;;;;;;CAeC,GACD,MAAMC,wBAAwB,CAAC,EAC7BC,OAAO,EACPC,cAAc,EACdC,gBAAgB,EAAE,EAClBC,kBAAkB,EAClBC,QAAQ,EACRC,MAAM,EACNC,KAAK,EASN;IACC,OAAO;QACLC,eAAe;QACfC,MAAKC,IAAS;YACZ,IAAIC;YAEJ,MAAMC,kBAAkB,CAACC;gBACvB,OAAOA,OAAOC,OAAO,CAAC,SAAS;YACjC;YAEA,MAAMC,eAAe,CAACF;gBACpB,6DAA6D;gBAC7D,OAAO,CAAC,GAAG,EAAEA,OAAOC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAEZ,eAAe,CAAC,CAAC;YAC7D;YAEA,yBAAyB;YACzB,KAAK,MAAMc,QAAQN,KAAKO,KAAK,CAAE;gBAC7B,IAAID,KAAKE,IAAI,KAAK,YAAYF,KAAKG,IAAI,KAAK,aAAa;oBACvD,MAAMC,aAAaJ,KAAKC,KAAK,CAACI,IAAI,CAChC,CAACC,OAAsBA,KAAKC,IAAI,KAAK;oBAEvC,IAAI,CAACH,YAAY;wBACf;oBACF;oBAEA,IAAI,CAACT,YAAY;wBACfA,aAAaC,gBAAgBQ,WAAWI,KAAK;oBAC/C;oBAEAJ,WAAWI,KAAK,GAAGT,aAAaJ;gBAClC;YACF;YAEA,IAAI,CAACA,YAAY;gBACf,MAAM,IAAIc,MAAM;YAClB;YAEA,4DAA4D;YAC5D,IAAIC;YACJ,IAAItB,oBAAoB;gBACtBsB,2BAA2BX,aAAa,CAAC,EAAEJ,WAAW,SAAS,CAAC;gBAChE,MAAMgB,mBAAmB5B,QAAQ6B,MAAM,CAAC;oBAAET,MAAM;gBAAY;gBAC5D,MAAM,EACJU,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,UAAU,EACX,GAAG7B;gBACJuB,iBAAiBV,KAAK,GAAG;oBACvB,IAAIlB,QAAQmC,WAAW,CAAC;wBACtBX,MAAM;wBACNC,OAAOE;oBACT;oBACA,IAAI3B,QAAQmC,WAAW,CAAC;wBACtBX,MAAM;wBACNC,OAAO,CAAC,OAAO,EAAEK,aAAa,EAAE,CAAC;oBACnC;uBACIC,iBACA;wBACE,IAAI/B,QAAQmC,WAAW,CAAC;4BACtBX,MAAM;4BACNC,OAAOM;wBACT;qBACD,GACD,EAAE;uBACFC,kBACA;wBACE,IAAIhC,QAAQmC,WAAW,CAAC;4BACtBX,MAAM;4BACNC,OAAOO;wBACT;qBACD,GACD,EAAE;uBACFC,kBACA;wBACE,IAAIjC,QAAQmC,WAAW,CAAC;4BACtBX,MAAM;4BACNC,OAAOQ;wBACT;qBACD,GACD,EAAE;uBACFC,aACA;wBACE,IAAIlC,QAAQmC,WAAW,CAAC;4BACtBX,MAAM;4BACNC,OAAOS;wBACT;qBACD,GACD,EAAE;iBACP;gBACDvB,KAAKO,KAAK,CAACkB,IAAI,CAACR;YAClB;YAEA,6CAA6C;YAC7C,MAAMS,UAAU,CAACZ,QAAkBA,MAAMa,IAAI,GAAGC,QAAQ,CAAC;YAEzD,iDAAiD;YACjD,MAAMC,wBAAwB;gBAC5BxB,aAAaJ;mBACTe,2BAA2B;oBAACA;iBAAyB,GAAG,EAAE;mBAC3DvB;aACJ,CAACqC,IAAI,CAAC;YAEP,0CAA0C;YAC1C,MAAMC,YAAY,IAAI1C,QAAQ2C,IAAI,CAAC;gBAAEC,UAAU;YAAa;YAC5DF,UAAUxB,KAAK,GAAG;gBAChB,IAAIlB,QAAQmC,WAAW,CAAC;oBACtBX,MAAM;oBACNC,OAAOe;gBACT;gBACA,uEAAuE;mBACnEjC,UAAU,CAAC8B,QAAQ9B,UACnB;oBACE,IAAIP,QAAQmC,WAAW,CAAC;wBACtBX,MAAM;wBACNC,OAAOlB;oBACT;iBACD,GACD,EAAE;mBACFC,SAAS,CAAC6B,QAAQ7B,SAClB;oBACE,IAAIR,QAAQmC,WAAW,CAAC;wBACtBX,MAAM;wBACNC,OAAOjB;oBACT;iBACD,GACD,EAAE;aACP;YACDG,KAAKO,KAAK,CAACkB,IAAI,CAACM;YAEhB,+DAA+D;YAC/D,IAAIpC,UAAU;gBACZ,MAAMuC,eAAe,IAAI7C,QAAQ2C,IAAI,CAAC;oBAAEC,UAAU;gBAAY;gBAC9DC,aAAa3B,KAAK,GAAG;oBACnB,IAAIlB,QAAQmC,WAAW,CAAC;wBACtBX,MAAMlB;wBACNmB,OAAOe;oBACT;iBACD;gBACD7B,KAAKO,KAAK,CAACkB,IAAI,CAACS;YAClB;YAEA,iCAAiC;YACjC3C,QAAQkC,IAAI,CAAC;gBACXhB,MAAM;gBACNK,OAAO;oBACLb,YAAY4B;oBACZM,YAAY,CAACC,OAAOC,KAAK,CAACD,OAAOxC,WAC7BwC,OAAOxC,UACP0C;oBACJC,WAAW1C,SAAS,CAAC6B,QAAQ7B,SAASA,QAAQyC;gBAChD;YACF;QACF;IACF;AACF;AAEAhD,sBAAsBD,OAAO,GAAG;AAEhC,eAAeC,sBAAqB"}