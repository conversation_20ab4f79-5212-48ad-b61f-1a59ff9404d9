{"version": 3, "file": "formatNationalNumberUsingFormat.js", "names": ["applyInternationalSeparatorStyle", "FIRST_GROUP_PATTERN", "formatNationalNumberUsingFormat", "number", "format", "_ref", "useInternationalFormat", "withNationalPrefix", "carrierCode", "metadata", "formattedNumber", "replace", "RegExp", "pattern", "internationalFormat", "nationalPrefixFormattingRule"], "sources": ["../../source/helpers/formatNationalNumberUsingFormat.js"], "sourcesContent": ["import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'\r\n\r\n// This was originally set to $1 but there are some countries for which the\r\n// first group is not used in the national pattern (e.g. Argentina) so the $1\r\n// group does not match correctly. Therefore, we use `\\d`, so that the first\r\n// group actually used in the pattern will be matched.\r\nexport const FIRST_GROUP_PATTERN = /(\\$\\d)/\r\n\r\nexport default function formatNationalNumberUsingFormat(\r\n\tnumber,\r\n\tformat,\r\n\t{\r\n\t\tuseInternationalFormat,\r\n\t\twithNationalPrefix,\r\n\t\tcarrierCode,\r\n\t\tmetadata\r\n\t}\r\n) {\r\n\tconst formattedNumber = number.replace(\r\n\t\tnew RegExp(format.pattern()),\r\n\t\tuseInternationalFormat\r\n\t\t\t? format.internationalFormat()\r\n\t\t\t: (\r\n\t\t\t\t// This library doesn't use `domestic_carrier_code_formatting_rule`,\r\n\t\t\t\t// because that one is only used when formatting phone numbers\r\n\t\t\t\t// for dialing from a mobile phone, and this is not a dialing library.\r\n\t\t\t\t// carrierCode && format.domesticCarrierCodeFormattingRule()\r\n\t\t\t\t// \t// First, replace the $CC in the formatting rule with the desired carrier code.\r\n\t\t\t\t// \t// Then, replace the $FG in the formatting rule with the first group\r\n\t\t\t\t// \t// and the carrier code combined in the appropriate way.\r\n\t\t\t\t// \t? format.format().replace(FIRST_GROUP_PATTERN, format.domesticCarrierCodeFormattingRule().replace('$CC', carrierCode))\r\n\t\t\t\t// \t: (\r\n\t\t\t\t// \t\twithNationalPrefix && format.nationalPrefixFormattingRule()\r\n\t\t\t\t// \t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\r\n\t\t\t\t// \t\t\t: format.format()\r\n\t\t\t\t// \t)\r\n\t\t\t\twithNationalPrefix && format.nationalPrefixFormattingRule()\r\n\t\t\t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\r\n\t\t\t\t\t: format.format()\r\n\t\t\t)\r\n\t)\r\n\tif (useInternationalFormat) {\r\n\t\treturn applyInternationalSeparatorStyle(formattedNumber)\r\n\t}\r\n\treturn formattedNumber\r\n}"], "mappings": "AAAA,OAAOA,gCAAgC,MAAM,uCAAuC;;AAEpF;AACA;AACA;AACA;AACA,OAAO,IAAMC,mBAAmB,GAAG,QAAQ;AAE3C,eAAe,SAASC,+BAA+BA,CACtDC,MAAM,EACNC,MAAM,EAAAC,IAAA,EAOL;EAAA,IALAC,sBAAsB,GAAAD,IAAA,CAAtBC,sBAAsB;IACtBC,kBAAkB,GAAAF,IAAA,CAAlBE,kBAAkB;IAClBC,WAAW,GAAAH,IAAA,CAAXG,WAAW;IACXC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;EAGT,IAAMC,eAAe,GAAGP,MAAM,CAACQ,OAAO,CACrC,IAAIC,MAAM,CAACR,MAAM,CAACS,OAAO,CAAC,CAAC,CAAC,EAC5BP,sBAAsB,GACnBF,MAAM,CAACU,mBAAmB,CAAC,CAAC;EAE7B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAP,kBAAkB,IAAIH,MAAM,CAACW,4BAA4B,CAAC,CAAC,GACxDX,MAAM,CAACA,MAAM,CAAC,CAAC,CAACO,OAAO,CAACV,mBAAmB,EAAEG,MAAM,CAACW,4BAA4B,CAAC,CAAC,CAAC,GACnFX,MAAM,CAACA,MAAM,CAAC,CAEpB,CAAC;EACD,IAAIE,sBAAsB,EAAE;IAC3B,OAAON,gCAAgC,CAACU,eAAe,CAAC;EACzD;EACA,OAAOA,eAAe;AACvB", "ignoreList": []}