{"version": 3, "file": "AsYouType.test.js", "names": ["metadata", "type", "AsYouType_", "AsYouType", "_AsYouType_", "country_code", "_classCallCheck", "_callSuper", "_inherits", "_createClass", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "describe", "it", "expect", "input", "to", "equal", "formatter", "template", "populatedNationalNumberTemplate", "be", "undefined", "getTemplate", "getCountry", "getCountryCallingCode", "reset", "getNationalNumber", "asYouType", "thrower", "getNumber", "phoneNumber", "country", "countryCallingCode", "number", "nationalNumber", "state", "nationalSignificantNumber", "nationalPrefix", "isPossible", "<PERSON><PERSON><PERSON><PERSON>", "getChars", "complexPrefixBeforeNationalSignificantNumber", "carrierCode", "defaultCallingCode", "defaultCountry", "chosenFormat", "format", "formats", "nationalPrefixFormattingRule", "getSeparatorAfterNationalPrefix", "formatter2", "format2", "isInternational", "formatterInt", "formatterIntRu", "getNumberValue", "not", "formatter3", "something", "_typeof"], "sources": ["../source/AsYouType.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\nimport AsYouType_ from './AsYouType.js'\r\n\r\nclass AsYouType extends AsYouType_ {\r\n\tconstructor(country_code) {\r\n\t\tsuper(country_code, metadata)\r\n\t}\r\n}\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\ndescribe('AsYouType', () => {\r\n\tit('should use \"national_prefix_formatting_rule\"', () => {\r\n\t\t// With national prefix (full).\r\n\t\texpect(new AsYouType('RU').input('88005553535')).to.equal('8 (800) 555-35-35')\r\n\t\t// With national prefix (partial).\r\n\t\texpect(new AsYouType('RU').input('880055535')).to.equal('8 (800) 555-35')\r\n\t})\r\n\r\n\tit('should populate national number template (digit by digit)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\tformatter.input('1')\r\n\t\t// formatter.formatter.template.should.equal('x (xxx) xxx-xxxx')\r\n\t\texpect(formatter.formatter.template).to.equal('x xxx-xxxx')\r\n\t\t// formatter.formatter.populatedNationalNumberTemplate.should.equal('1 (xxx) xxx-xxxx')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('1 xxx-xxxx')\r\n\t\tformatter.input('213')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('1 (213) xxx-xxxx')\r\n\t\tformatter.input('3734253')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('****************')\r\n\t})\r\n\r\n\tit('should populate international number template (digit by digit) (default country)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\texpect(formatter.input('')).to.equal('')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\texpect(formatter.input('+')).to.equal('+')\r\n\t\texpect(formatter.getTemplate()).to.equal('x')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\texpect(formatter.input('1')).to.equal('+1')\r\n\t\texpect(formatter.getTemplate()).to.equal('xx')\r\n\t\t// Hasn't started formatting the phone number using the template yet.\r\n\t\t// formatter.formatter.template.should.equal('xx xxx xxx xxxx')\r\n\t\texpect(formatter.formatter.template).to.equal('xx xxx xxxx')\r\n\t\t// formatter.formatter.populatedNationalNumberTemplate.should.equal('xxx xxx xxxx')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('xxx xxxx')\r\n\t\t// Has some national number digits, starts formatting the phone number using the template.\r\n\t\tformatter.input('213')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('213 xxx xxxx')\r\n\t\tformatter.input('3734253')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('************')\r\n\t})\r\n\r\n\tit('should populate international number template (digit by digit)', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\texpect(formatter.input('')).to.equal('')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\texpect(formatter.input('+')).to.equal('+')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.be.undefined\r\n\t\texpect(formatter.input('1')).to.equal('+1')\r\n\t\t// formatter.formatter.template.should.equal('xx xxx xxx xxxx')\r\n\t\texpect(formatter.formatter.template).to.equal('xx xxx xxxx')\r\n\t\t// Hasn't yet started formatting the phone number using the template.\r\n\t\t// formatter.formatter.populatedNationalNumberTemplate.should.equal('xxx xxx xxxx')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('xxx xxxx')\r\n\t\t// Has some national number digits, starts formatting the phone number using the template.\r\n\t\tformatter.input('213')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('213 xxx xxxx')\r\n\t\tformatter.input('3734253')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('************')\r\n\t})\r\n\r\n\tit('should populate national number template (attempt to format complete number)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.input('1**********')).to.equal('****************')\r\n\t\texpect(formatter.formatter.template).to.equal('x (xxx) xxx-xxxx')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('****************')\r\n\t})\r\n\r\n\tit('should parse and format phone numbers as you type', () => {\r\n\t\t// International number test\r\n\t\texpect(new AsYouType().input('+12133734')).to.equal('****** 373 4')\r\n\t\t// Local number test\r\n\t\texpect(new AsYouType('US').input('2133734')).to.equal('(213) 373-4')\r\n\r\n\t\t// US national number retains national prefix.\r\n\t\texpect(new AsYouType('US').input('12133734')).to.equal('1 (213) 373-4')\r\n\r\n\t\t// US national number retains national prefix (full number).\r\n\t\texpect(new AsYouType('US').input('1**********')).to.equal('****************')\r\n\r\n\t\tlet formatter\r\n\r\n\t\t// // Should discard national prefix from a \"complete\" phone number.\r\n\t\t// new AsYouType('RU').input('8800555353').should.equal('880 055-53-53')\r\n\r\n\t\t// Shouldn't extract national prefix when inputting in international format.\r\n\t\texpect(new AsYouType('RU').input('+7800555353')).to.equal('****** 555 35 3')\r\n\r\n\t\texpect(new AsYouType('CH').input('044-668-1')).to.equal('044 668 1')\r\n\r\n\t\t// Test International phone number (international)\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(type(formatter.getCountry())).to.equal('undefined')\r\n\t\texpect(type(formatter.getCountryCallingCode())).to.equal('undefined')\r\n\t\texpect(formatter.getTemplate()).to.equal('')\r\n\r\n\t\texpect(formatter.input('+')).to.equal('+')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(type(formatter.getCountry())).to.equal('undefined')\r\n\t\texpect(type(formatter.getCountryCallingCode())).to.equal('undefined')\r\n\t\texpect(formatter.getTemplate()).to.equal('x')\r\n\r\n\t\texpect(formatter.input('1')).to.equal('+1')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(type(formatter.getCountry())).to.equal('undefined')\r\n\t\texpect(formatter.getCountryCallingCode()).to.equal('1')\r\n\t\texpect(formatter.getTemplate()).to.equal('xx')\r\n\r\n\t\texpect(formatter.input('2')).to.equal('****')\r\n\t\texpect(formatter.getTemplate()).to.equal('xx x')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(type(formatter.getCountry())).to.equal('undefined')\r\n\r\n\t\texpect(formatter.input('1')).to.equal('****1')\r\n\t\texpect(formatter.input('3')).to.equal('******')\r\n\t\texpect(formatter.input(' ')).to.equal('******')\r\n\t\texpect(formatter.input('3')).to.equal('****** 3')\r\n\t\texpect(formatter.input('3')).to.equal('****** 33')\r\n\t\texpect(formatter.input('3')).to.equal('****** 333')\r\n\t\texpect(formatter.input('4')).to.equal('****** 333 4')\r\n\t\texpect(formatter.input('4')).to.equal('****** 333 44')\r\n\t\texpect(formatter.input('4')).to.equal('****** 333 444')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(type(formatter.getCountry())).to.equal('undefined')\r\n\r\n\t\texpect(formatter.input('4')).to.equal('****** 333 4444')\r\n\r\n\t\t// formatter.valid.should.be.true\r\n\t\texpect(formatter.getCountry()).to.equal('US')\r\n\t\t// This one below contains \"punctuation spaces\"\r\n\t\t// along with the regular spaces\r\n\t\texpect(formatter.getTemplate()).to.equal('xx xxx xxx xxxx')\r\n\r\n\t\texpect(formatter.input('5')).to.equal('******33344445')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\texpect(formatter.getCountryCallingCode()).to.equal('1')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\r\n\t\t// Check that clearing an international formatter\r\n\t\t// also clears country metadata.\r\n\r\n\t\tformatter.reset()\r\n\r\n\t\texpect(formatter.input('+')).to.equal('+')\r\n\t\texpect(formatter.input('7')).to.equal('+7')\r\n\t\texpect(formatter.input('9')).to.equal('****')\r\n\t\texpect(formatter.input('99 111 22 33')).to.equal('****** 111 22 33')\r\n\r\n\t\t// Test Switzerland phone numbers\r\n\r\n\t\tformatter = new AsYouType('CH')\r\n\r\n\t\texpect(formatter.input(' ')).to.equal('')\r\n\t\texpect(formatter.input('0')).to.equal('0')\r\n\t\texpect(formatter.input('4')).to.equal('04')\r\n\t\texpect(formatter.input(' ')).to.equal('04')\r\n\t\texpect(formatter.input('-')).to.equal('04')\r\n\t\texpect(formatter.input('4')).to.equal('044')\r\n\t\texpect(formatter.input('-')).to.equal('044')\r\n\t\texpect(formatter.input('6')).to.equal('044 6')\r\n\t\texpect(formatter.input('6')).to.equal('044 66')\r\n\t\texpect(formatter.input('8')).to.equal('044 668')\r\n\t\texpect(formatter.input('-')).to.equal('044 668')\r\n\t\texpect(formatter.input('1')).to.equal('044 668 1')\r\n\t\texpect(formatter.input('8')).to.equal('044 668 18')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.getCountry()).to.equal('CH')\r\n\t\texpect(formatter.formatter.template).to.equal('xxx xxx xx xx')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxx xx')\r\n\r\n\t\texpect(formatter.input(' 00')).to.equal('044 668 18 00')\r\n\r\n\t\t// formatter.valid.should.be.true\r\n\t\texpect(formatter.getCountry()).to.equal('CH')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxx xx xx')\r\n\r\n\t\texpect(formatter.input('9')).to.equal('04466818009')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.getCountry()).to.equal('CH')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\r\n\t\t// Kazakhstan (non-main country for +7 country phone code)\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\tformatter.input('+77172580659')\r\n\t\texpect(formatter.getCountry()).to.equal('KZ')\r\n\r\n\t\t// Brazil\r\n\r\n\t\tformatter = new AsYouType('BR')\r\n\t\texpect(formatter.input('11987654321')).to.equal('(11) 98765-4321')\r\n\r\n\t\t// UK (Jersey) (non-main country for +44 country phone code)\r\n\r\n\t\tformatter = new AsYouType()\r\n\t\texpect(formatter.input('+447700300000')).to.equal('+44 7700 300000')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxxx xxxxxx')\r\n\t\texpect(formatter.getCountry()).to.equal('JE')\r\n\r\n\t\t// Braces must be part of the template.\r\n\t\tformatter = new AsYouType('RU')\r\n\t\texpect(formatter.input('88005553535')).to.equal('8 (800) 555-35-35')\r\n\t\texpect(formatter.getTemplate()).to.equal('x (xxx) xxx-xx-xx')\r\n\r\n\t\t// Test Russian phone numbers\r\n\t\t// (with optional national prefix `8`)\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\texpect(formatter.input('8')).to.equal('8')\r\n\t\texpect(formatter.input('9')).to.equal('8 9')\r\n\t\texpect(formatter.input('9')).to.equal('8 99')\r\n\t\texpect(formatter.input('9')).to.equal('8 (999)')\r\n\t\texpect(formatter.input('-')).to.equal('8 (999)')\r\n\t\texpect(formatter.input('1234')).to.equal('8 (999) 123-4')\r\n\t\texpect(formatter.input('567')).to.equal('8 (999) 123-45-67')\r\n\t\texpect(formatter.input('8')).to.equal('899912345678')\r\n\r\n\t\t// Shouldn't strip national prefix if it is optional\r\n\t\t// and if it's a valid phone number (international).\r\n\t\tformatter = new AsYouType('RU')\r\n\t\t// formatter.input('8005553535').should.equal('(800) 555-35-35')\r\n\t\texpect(formatter.input('+78005553535')).to.equal('****** 555 35 35')\r\n\t\texpect(formatter.getNationalNumber()).to.equal('8005553535')\r\n\r\n\t\t// Check that clearing an national formatter:\r\n\t\t//  * doesn't clear country metadata\r\n\t\t//  * clears all other things\r\n\r\n\t\tformatter.reset()\r\n\r\n\t\texpect(formatter.input('8')).to.equal('8')\r\n\t\texpect(formatter.input('9')).to.equal('8 9')\r\n\t\texpect(formatter.input('9')).to.equal('8 99')\r\n\t\texpect(formatter.input('9')).to.equal('8 (999)')\r\n\t\texpect(formatter.input('-')).to.equal('8 (999)')\r\n\t\texpect(formatter.input('1234')).to.equal('8 (999) 123-4')\r\n\t\texpect(formatter.input('567')).to.equal('8 (999) 123-45-67')\r\n\t\texpect(formatter.input('8')).to.equal('899912345678')\r\n\r\n\t\t// National prefix should not be prepended\r\n\t\t// when formatting local NANPA phone numbers.\r\n\t\texpect(new AsYouType('US').input('1')).to.equal('1')\r\n\t\texpect(new AsYouType('US').input('12')).to.equal('1 2')\r\n\t\texpect(new AsYouType('US').input('123')).to.equal('1 23')\r\n\r\n\t\t// Bulgaria\r\n\t\t// (should not prepend national prefix `0`)\r\n\t\texpect(new AsYouType('BG').input('111 222 3')).to.equal('1112223')\r\n\r\n\t\t// Deutchland\r\n\t\texpect(new AsYouType().input('+4915539898001')).to.equal('+49 15539 898001')\r\n\r\n\t\t// KZ detection\r\n\t\tformatter = new AsYouType()\r\n\t\tformatter.input('****** 211 1111')\r\n\t\texpect(formatter.getCountry()).to.equal('KZ')\r\n\t\t// formatter.valid.should.equal(true)\r\n\r\n\t\t// New Zealand formatting fix (issue #89)\r\n\t\texpect(new AsYouType('NZ').input('0212')).to.equal('021 2')\r\n\r\n\t\t// South Korea\r\n\t\tformatter = new AsYouType()\r\n\t\texpect(formatter.input('+82111111111')).to.equal('+82 11 111 1111')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xx xxx xxxx')\r\n\t})\r\n\r\n\tit('should filter out formats that require a national prefix and no national prefix has been input', () => {\r\n\t\t// Afghanistan.\r\n\t\tconst formatter = new AsYouType('AF')\r\n\r\n\t\t// No national prefix, and national prefix is required in the format.\r\n\t\t// (not `\"national_prefix_is_optional_when_formatting\": true`)\r\n\t\texpect(formatter.input('44444444')).to.equal('44444444')\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\r\n\t\t// With national prefix\r\n\t\texpect(formatter.reset().input('044444444')).to.equal('044 444 444')\r\n\t\texpect(formatter.formatter.template).to.equal('xxx xxx xxxx')\r\n\t})\r\n\r\n\tit('should work when a digit is not a national prefix but a part of a valid national number', () => {\r\n\t\t// In Russia, `8` could be both a valid national prefix\r\n\t\t// and a part of a valid national number.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\t// The formatter could try both variants:\r\n\t\t// with extracting national prefix\r\n\t\t// and without extracting it,\r\n\t\t// and then choose whichever way has `this.matchingFormats`.\r\n\t\t// Or there could be two instances of the formatter:\r\n\t\t// one that extracts national prefix and one that doesn't,\r\n\t\t// and then the one that has `this.matchingFormats` would be\r\n\t\t// used to format the phone number.\r\n\t\t// Something like an option `extractNationalPrefix: false`\r\n\t\t// and creating `this.withNationalPrefixFormatter = new AsYouType(this.defaultCountry || this.defaultCallingCode, { metadata, extractNationalPrefix: false })`\r\n\t\t// and something like `this.withNationalPrefixFormatter.input(nextDigits)` in `input(nextDigits)`.\r\n\t\t// But, for this specific case, it's not required:\r\n\t\t// in Russia, people are used to inputting `800` numbers with national prefix `8`:\r\n\t\t// `8 800 555 35 35`.\r\n\t\t// formatter.input('8005553535').should.equal('(800) 555-35-35')\r\n\t\texpect(formatter.input('8005553535')).to.equal('8005553535')\r\n\t\tformatter.reset()\r\n\t\texpect(formatter.input('+78005553535')).to.equal('****** 555 35 35')\r\n\t})\r\n\r\n\tit('should match formats that require a national prefix and no national prefix has been input (national prefix is mandatory for a format)', () => {\r\n\t\tconst formatter = new AsYouType('FR')\r\n\t\texpect(formatter.input('612345678')).to.equal('612345678')\r\n\t\tformatter.reset()\r\n\t\texpect(formatter.input('0612345678')).to.equal('06 12 34 56 78')\r\n\t})\r\n\r\n\tit('should match formats that require a national prefix and no national prefix has been input (national prefix is not mandatory for a format)', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\t// Without national prefix.\r\n\t\texpect(formatter.input('9991234567')).to.equal('999 123-45-67')\r\n\t\tformatter.reset()\r\n\t\t// With national prefix.\r\n\t\texpect(formatter.input('89991234567')).to.equal('8 (999) 123-45-67')\r\n\t})\r\n\r\n\tit('should not use `national_prefix_formatting_rule` when formatting international phone numbers', () => {\r\n\t\t// Brazil.\r\n\t\t// `national_prefix_formatting_rule` is `($1)`.\r\n\t\t// Should not add braces around `12` when being input in international format.\r\n\t\texpect(new AsYouType().input('+55123456789')).to.equal('+55 12 3456 789')\r\n\t\texpect(new AsYouType('BR').input('+55123456789')).to.equal('+55 12 3456 789')\r\n\t\texpect(new AsYouType('BR').input('123456789')).to.equal('(12) 3456-789')\r\n\t})\r\n\r\n\tit('should support incorrectly entered international phone numbers (with a national prefix)', () => {\r\n\t\tlet formatter\r\n\r\n\t\tformatter = new AsYouType()\r\n\t\texpect(formatter.input('**** ************')).to.equal('**** ************')\r\n\t\t// formatter.input('**** ************').should.equal('**** 8772155230')\r\n\t\texpect(formatter.getNationalNumber()).to.equal('8772155230')\r\n\r\n\t\t// They've added another number format that has `8` leading digit\r\n\t\t// and 14 digits. Maybe it's something related to Kazakhstan.\r\n\t\t// formatter = new AsYouType()\r\n\t\t// formatter.input('+78800555353').should.equal('****80 055 53 53')\r\n\t\t// formatter.input('5').should.equal('**** 800 555 35 35')\r\n\t\t// formatter.getNationalNumber().should.equal('8005553535')\r\n\t})\r\n\r\n\tit('should return a partial template for current value', () => {\r\n\t\tconst asYouType = new AsYouType('US')\r\n\r\n\t\texpect(asYouType.input('')).to.equal('')\r\n\t\texpect(asYouType.getTemplate()).to.equal('')\r\n\r\n\t\texpect(asYouType.input('2')).to.equal('2')\r\n\t\t// asYouType.getTemplate().should.equal('x')\r\n\t\t// Doesn't format for a single digit.\r\n\t\texpect(asYouType.getTemplate()).to.equal('x')\r\n\r\n\t\texpect(asYouType.input('1')).to.equal('21')\r\n\t\texpect(asYouType.getTemplate()).to.equal('xx')\r\n\r\n\t\texpect(asYouType.input('3')).to.equal('(213)')\r\n\t\texpect(asYouType.getTemplate()).to.equal('(xxx)')\r\n\t})\r\n\r\n\tit(`should fall back to the default country`, () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\r\n\t\texpect(formatter.input('8')).to.equal('8')\r\n\t\texpect(formatter.input('9')).to.equal('8 9')\r\n\t\texpect(formatter.input('9')).to.equal('8 99')\r\n\t\texpect(formatter.input('9')).to.equal('8 (999)')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.formatter.template).to.equal('x (xxx) xxx-xx-xx')\r\n\t\texpect(formatter.getCountry()).to.equal('RU')\r\n\t\t// formatter.getCountryCallingCode().should.equal('7')\r\n\r\n\t\texpect(formatter.input('000000000000')).to.equal('8999000000000000')\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.getCountry()).to.equal('RU')\r\n\t\t// formatter.getCountryCallingCode().should.equal('7')\r\n\r\n\t\tformatter.reset()\r\n\r\n\t\t// formatter.valid.should.be.false\r\n\t\texpect(formatter.formatter.template).to.be.undefined\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t// formatter.getCountryCallingCode().should.equal('7')\r\n\r\n\t\texpect(formatter.input('******-373-4253')).to.equal('****** 373 4253')\r\n\r\n\t\t// formatter.valid.should.be.true\r\n\t\texpect(formatter.getTemplate()).to.equal('xx xxx xxx xxxx')\r\n\t\texpect(formatter.getCountry()).to.equal('US')\r\n\t\texpect(formatter.getCountryCallingCode()).to.equal('1')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet formatter\r\n\t\tlet thrower\r\n\r\n\t\t// No metadata\r\n\t\tthrower = () => new AsYouType_('RU')\r\n\t\texpect(thrower).to.throw('`metadata` argument not passed')\r\n\r\n\t\t// Second '+' sign\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\texpect(formatter.input('+')).to.equal('+')\r\n\t\texpect(formatter.input('7')).to.equal('+7')\r\n\t\texpect(formatter.input('+')).to.equal('+7')\r\n\r\n\t\t// Out-of-position '+' sign\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\texpect(formatter.input('8')).to.equal('8')\r\n\t\texpect(formatter.input('+')).to.equal('8')\r\n\r\n\t\t// No format matched\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\texpect(formatter.input('88005553535')).to.equal('8 (800) 555-35-35')\r\n\t\texpect(formatter.input('0')).to.equal('880055535350')\r\n\r\n\t\t// Invalid country phone code\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\texpect(formatter.input('+0123')).to.equal('+0123')\r\n\r\n\t\t// No country specified and not an international number\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\texpect(formatter.input('88005553535')).to.equal('88005553535')\r\n\r\n\t\t// Extract national prefix when no `national_prefix` is set\r\n\r\n\t\tformatter = new AsYouType('AD')\r\n\r\n\t\texpect(formatter.input('155555')).to.equal('155 555')\r\n\r\n\t\t// Typing nonsense\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\texpect(formatter.input('+1abc2')).to.equal('+1')\r\n\r\n\t\t// Should reset default country when explicitly\r\n\t\t// typing in an international phone number\r\n\r\n\t\tformatter = new AsYouType('RU')\r\n\r\n\t\tformatter.input('+')\r\n\t\texpect(type(formatter.getCountry())).to.equal('undefined')\r\n\t\texpect(type(formatter.getCountryCallingCode())).to.equal('undefined')\r\n\r\n\t\t// Country not inferrable from the phone number,\r\n\t\t// while the phone number itself can already be formatted \"completely\".\r\n\r\n\t\tformatter = new AsYouType()\r\n\r\n\t\tformatter.input('+12223333333')\r\n\t\texpect(type(formatter.getCountry())).to.equal('undefined')\r\n\t\texpect(formatter.getCountryCallingCode()).to.equal('1')\r\n\r\n\t\t// Reset a chosen format when it no longer applies given the new leading digits.\r\n\t\t// If Google changes metadata for England then this test might not cover the case.\r\n\t\tformatter = new AsYouType('GB')\r\n\t\texpect(formatter.input('0845')).to.equal('0845')\r\n\t\t// New leading digits don't match the format previously chosen.\r\n\t\t// Reset the format.\r\n\t\texpect(formatter.input('0')).to.equal('0845 0')\r\n\t})\r\n\r\n\tit('should choose between matching formats based on the absence or presence of a national prefix', () => {\r\n\t\t// The first matching format:\r\n\t\t// {\r\n\t\t//    \"pattern\": \"(\\\\d{2})(\\\\d{5,6})\",\r\n\t\t//    \"leading_digits_patterns\": [\r\n\t\t//       \"(?:10|2[0-57-9])[19]\",\r\n\t\t//       \"(?:10|2[0-57-9])(?:10|9[56])\",\r\n\t\t//       \"(?:10|2[0-57-9])(?:100|9[56])\"\r\n\t\t//    ],\r\n\t\t//    \"national_prefix_formatting_rule\": \"0$1\",\r\n\t\t//    \"format\": \"$1 $2\",\r\n\t\t//    \"domestic_carrier_code_formatting_rule\": \"$CC $FG\"\r\n\t\t// }\r\n\t\t//\r\n\t\t// The second matching format:\r\n\t\t// {\r\n\t\t//    \"pattern\": \"(\\\\d{2})(\\\\d{4})(\\\\d{4})\",\r\n\t\t//    \"leading_digits_patterns\": [\r\n\t\t//       \"10|2(?:[02-57-9]|1[1-9])\",\r\n\t\t//       \"10|2(?:[02-57-9]|1[1-9])\",\r\n\t\t//       \"10[0-79]|2(?:[02-57-9]|1[1-79])|(?:10|21)8(?:0[1-9]|[1-9])\"\r\n\t\t//    ],\r\n\t\t//    \"national_prefix_formatting_rule\": \"0$1\",\r\n\t\t//    \"national_prefix_is_optional_when_formatting\": true,\r\n\t\t//    \"format\": \"$1 $2 $3\",\r\n\t\t//    \"domestic_carrier_code_formatting_rule\": \"$CC $FG\"\r\n\t\t// }\r\n\t\t//\r\n\t\tconst formatter = new AsYouType('CN')\r\n\t\t// National prefix has been input.\r\n\t\t// Chooses the first format.\r\n\t\texpect(formatter.input('01010000')).to.equal('010 10000')\r\n\t\tformatter.reset()\r\n\t\t// No national prefix has been input,\r\n\t\t// and `national_prefix_for_parsing` not matched.\r\n\t\t// The first format won't match, because it doesn't have\r\n\t\t// `\"national_prefix_is_optional_when_formatting\": true`.\r\n\t\t// The second format will match, because it does have\r\n\t\t// `\"national_prefix_is_optional_when_formatting\": true`.\r\n\t\texpect(formatter.input('1010000')).to.equal('10 1000 0')\r\n\t})\r\n\r\n\tit('should not accept phone number extensions', () => {\r\n\t\texpect(new AsYouType().input('******-373-4253 ext. 123')).to.equal('****** 373 4253')\r\n\t})\r\n\r\n\tit('should parse non-European digits', () => {\r\n\t\texpect(new AsYouType().input('+١٢١٢٢٣٢٣٢٣٢')).to.equal('****** 232 3232')\r\n\t})\r\n\r\n\tit('should return a PhoneNumber instance', () => {\r\n\t\tconst formatter = new AsYouType('BR')\r\n\r\n\t\t// No country calling code.\r\n\t\texpect(formatter.getNumber()).to.be.undefined\r\n\r\n\t\tformatter.input('+1')\r\n\t\t// No national number digits.\r\n\t\texpect(formatter.getNumber()).to.be.undefined\r\n\r\n\t\tformatter.input('************')\r\n\r\n\t\tlet phoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber.country).to.equal('US')\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('1')\r\n\t\texpect(phoneNumber.number).to.equal('+1**********')\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('**********')\r\n\r\n\t\tformatter.reset()\r\n\t\tformatter.input('******-373-4253')\r\n\r\n\t\tphoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('1')\r\n\r\n\t\t// An incorrect NANPA international phone number.\r\n\t\t// (contains national prefix in an international phone number)\r\n\r\n\t\tformatter.reset()\r\n\t\tformatter.input('****')\r\n\r\n\t\t// Before leading digits < 3 matching was implemented:\r\n\t\t//\r\n\t\t// phoneNumber = formatter.getNumber()\r\n\t\t// expect(phoneNumber).to.not.be.undefined\r\n\t\t//\r\n\t\t// formatter.input('1')\r\n\t\t// phoneNumber = formatter.getNumber()\r\n\t\t// expect(phoneNumber.country).to.be.undefined\r\n\t\t// phoneNumber.countryCallingCode.should.equal('1')\r\n\t\t// phoneNumber.number.should.equal('+111')\r\n\r\n\t\t// After leading digits < 3 matching was implemented:\r\n\t\t//\r\n\t\tphoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber).to.be.undefined\r\n\t\t//\r\n\t\tformatter.input('1')\r\n\t\tphoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('1')\r\n\t\texpect(phoneNumber.number).to.equal('+11')\r\n\t})\r\n\r\n\tit('should work with countries that add digits to national (significant) number', () => {\r\n\t\t// When formatting Argentinian mobile numbers in international format,\r\n\t\t// a `9` is prepended, when compared to national format.\r\n\t\tconst asYouType = new AsYouType('AR')\r\n\t\texpect(asYouType.input('+5493435551212')).to.equal('+54 9 3435 55 1212')\r\n\t\tasYouType.reset()\r\n\t\t// Digits shouldn't be changed when formatting in national format.\r\n\t\t// (no `9` is prepended).\r\n\t\t// First parses national (significant) number by prepending `9` to it\r\n\t\t// and stripping `15` from it.\r\n\t\t// Then uses `$2 15-$3-$4` format that strips the leading `9`\r\n\t\t// and adds `15`.\r\n\t\texpect(asYouType.input('0343515551212')).to.equal('03435 15-55-1212')\r\n\t})\r\n\r\n\tit('should return non-formatted phone number when no format matches and national (significant) number has digits added', () => {\r\n\t\t// When formatting Argentinian mobile numbers in international format,\r\n\t\t// a `9` is prepended, when compared to national format.\r\n\t\tconst asYouType = new AsYouType('AR')\r\n\t\t// Digits shouldn't be changed when formatting in national format.\r\n\t\t// (no `9` is prepended).\r\n\t\t// First parses national (significant) number by prepending `9` to it\r\n\t\t// and stripping `15` from it.\r\n\t\t// Then uses `$2 15-$3-$4` format that strips the leading `9`\r\n\t\t// and adds `15`.\r\n\t\t// `this.nationalSignificantNumberMatchesInput` is `false` in this case,\r\n\t\t// so `getNonFormattedNumber()` returns `getFullNumber(getNationalDigits())`.\r\n\t\texpect(asYouType.input('0343515551212999')).to.equal('0343515551212999')\r\n\t})\r\n\r\n\tit('should format Argentina numbers (starting with 011) (digit by digit)', () => {\r\n\t\t// Inputting a number digit-by-digit and as a whole a two different cases\r\n\t\t// in case of this library compared to Google's `libphonenumber`\r\n\t\t// that always inputs a number digit-by-digit.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/23\r\n\t\t// nextDigits 0111523456789\r\n\t\t// nationalNumber 91123456789\r\n\t\tconst formatter = new AsYouType('AR')\r\n\t\texpect(formatter.input('0')).to.equal('0')\r\n\t\texpect(formatter.getTemplate()).to.equal('x')\r\n\t\texpect(formatter.input('1')).to.equal('01')\r\n\t\texpect(formatter.getTemplate()).to.equal('xx')\r\n\t\texpect(formatter.input('1')).to.equal('011')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx')\r\n\t\texpect(formatter.input('1')).to.equal('011 1')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx x')\r\n\t\texpect(formatter.input('5')).to.equal('011 15')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xx')\r\n\t\texpect(formatter.input('2')).to.equal('011 152')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxx')\r\n\t\texpect(formatter.input('3')).to.equal('011 1523')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxxx')\r\n\t\texpect(formatter.input('4')).to.equal('011 1523-4')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxxx-x')\r\n\t\texpect(formatter.input('5')).to.equal('011 1523-45')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxxx-xx')\r\n\t\texpect(formatter.input('6')).to.equal('011 1523-456')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxxx-xxx')\r\n\t\texpect(formatter.input('7')).to.equal('011 1523-4567')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxxx-xxxx')\r\n\t\texpect(formatter.input('8')).to.equal('011152345678')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxxxxxxxxxxx')\r\n\t\texpect(formatter.input('9')).to.equal('011 15-2345-6789')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xx-xxxx-xxxx')\r\n\t\t// Private property (not public API).\r\n\t\texpect(formatter.state.nationalSignificantNumber).to.equal('91123456789')\r\n\t\t// Private property (not public API).\r\n\t\t// `formatter.digits` is not always `formatter.nationalPrefix`\r\n\t\t// plus `formatter.nationalNumberDigits`.\r\n\t\texpect(formatter.state.nationalPrefix).to.equal('0')\r\n\t\texpect(formatter.isPossible()).to.equal(true)\r\n\t\texpect(formatter.isValid()).to.equal(true)\r\n\t})\r\n\r\n\tit('should format Argentina numbers (starting with 011)', () => {\r\n\t\t// Inputting a number digit-by-digit and as a whole a two different cases\r\n\t\t// in case of this library compared to Google's `libphonenumber`\r\n\t\t// that always inputs a number digit-by-digit.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/23\r\n\t\t// nextDigits 0111523456789\r\n\t\t// nationalNumber 91123456789\r\n\t\tconst formatter = new AsYouType('AR')\r\n\t\texpect(formatter.input('0111523456789')).to.equal('011 15-2345-6789')\r\n\t\t// Private property (not public API).\r\n\t\texpect(formatter.state.nationalSignificantNumber).to.equal('91123456789')\r\n\t\t// Private property (not public API).\r\n\t\t// `formatter.digits` is not always `formatter.nationalPrefix`\r\n\t\t// plus `formatter.nationalNumberDigits`.\r\n\t\texpect(formatter.state.nationalPrefix).to.equal('0')\r\n\t\t// expect(formatter.nationalPrefix).to.be.undefined\r\n\t\texpect(formatter.isPossible()).to.equal(true)\r\n\t\texpect(formatter.isValid()).to.equal(true)\r\n\t})\r\n\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/93\r\n\tit('should format Indonesian numbers', () => {\r\n\t\tconst formatter = new AsYouType('ID')\r\n\t\texpect(formatter.getChars()).to.equal('')\r\n\t\t// Before leading digits < 3 matching was implemented:\r\n\t\t// formatter.input('081').should.equal('(081)')\r\n\t\t// After leading digits < 3 matching was implemented:\r\n\t\texpect(formatter.input('081')).to.equal('081')\r\n\t})\r\n\r\n\tit('should prepend `complexPrefixBeforeNationalSignificantNumber` (not a complete number)', () => {\r\n\t\t// A country having `national_prefix_for_parsing` with a \"capturing group\".\r\n\t\t// National prefix is either not used in a format or is optional.\r\n\t\t// Input phone number without a national prefix.\r\n\t\tconst formatter = new AsYouType('AU')\r\n\t\texpect(formatter.input('1831130345678')).to.equal('1831 1303 456 78')\r\n\t\t// Private property (not public API).\r\n\t\texpect(formatter.state.nationalSignificantNumber).to.equal('130345678')\r\n\t\t// Private property (not public API).\r\n\t\t// `formatter.digits` is not always `formatter.nationalPrefix`\r\n\t\t// plus `formatter.nationalNumberDigits`.\r\n\t\texpect(formatter.state.nationalPrefix).to.be.undefined\r\n\t\texpect(formatter.state.complexPrefixBeforeNationalSignificantNumber).to.equal('1831')\r\n\t})\r\n\r\n\tit('should prepend `complexPrefixBeforeNationalSignificantNumber` (complete number)', () => {\r\n\t\t// A country having `national_prefix_for_parsing` with a \"capturing group\".\r\n\t\t// National prefix is either not used in a format or is optional.\r\n\t\t// Input phone number without a national prefix.\r\n\t\tconst formatter = new AsYouType('AU')\r\n\t\texpect(formatter.input('18311303456789')).to.equal('1831 1303 456 789')\r\n\t\t// Private property (not public API).\r\n\t\texpect(formatter.state.nationalSignificantNumber).to.equal('1303456789')\r\n\t\t// Private property (not public API).\r\n\t\t// `formatter.digits` is not always `formatter.nationalPrefix`\r\n\t\t// plus `formatter.nationalNumberDigits`.\r\n\t\texpect(formatter.state.nationalPrefix).to.be.undefined\r\n\t\texpect(formatter.state.complexPrefixBeforeNationalSignificantNumber).to.equal('1831')\r\n\t})\r\n\r\n\tit('should work with Mexico numbers', () => {\r\n\t\tconst asYouType = new AsYouType('MX')\r\n\r\n\t\t// Fixed line. International.\r\n\t\texpect(asYouType.input('+52(449)978-000')).to.equal('+52 449 978 000')\r\n\t\texpect(asYouType.input('1')).to.equal('+52 ************')\r\n\t\tasYouType.reset()\r\n\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t// // Fixed line. National. With national prefix \"01\".\r\n\t\t// asYouType.input('01449978000').should.equal('01449 978 000')\r\n\t\t// asYouType.getTemplate().should.equal('xxxxx xxx xxx')\r\n\t\t// asYouType.input('1').should.equal('01************')\r\n\t\t// asYouType.getTemplate().should.equal('xxxxx xxx xxxx')\r\n\t\t// asYouType.reset()\r\n\r\n\t\t// Fixed line. National. Without national prefix.\r\n\t\texpect(asYouType.input('(449)978-000')).to.equal('449 978 000')\r\n\t\texpect(asYouType.getTemplate()).to.equal('xxx xxx xxx')\r\n\t\texpect(asYouType.input('1')).to.equal('************')\r\n\t\texpect(asYouType.getTemplate()).to.equal('xxx xxx xxxx')\r\n\t\tasYouType.reset()\r\n\r\n\t\t// Mobile.\r\n\t\texpect(asYouType.input('+52331234567')).to.equal('+52 33 1234 567')\r\n\t\texpect(asYouType.input('8')).to.equal('+52 33 1234 5678')\r\n\t\tasYouType.reset()\r\n\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t// // Mobile.\r\n\t\t// // With `1` prepended before area code to mobile numbers in international format.\r\n\t\t// asYouType.input('+521331234567').should.equal('+52 133 1234 567')\r\n\t\t// asYouType.getTemplate().should.equal('xxx xxx xxxx xxx')\r\n\t\t// // Google's `libphonenumber` seems to not able to format this type of number.\r\n\t\t// // https://issuetracker.google.com/issues/147938979\r\n\t\t// asYouType.input('8').should.equal('+52 133 1234 5678')\r\n\t\t// asYouType.getTemplate().should.equal('xxx xxx xxxx xxxx')\r\n\t\t// asYouType.reset()\r\n\t\t//\r\n\t\t// // Mobile. National. With \"044\" prefix.\r\n\t\t// asYouType.input('044331234567').should.equal('04433 1234 567')\r\n\t\t// asYouType.input('8').should.equal('04433 1234 5678')\r\n\t\t// asYouType.reset()\r\n\t\t//\r\n\t\t// // Mobile. National. With \"045\" prefix.\r\n\t\t// asYouType.input('045331234567').should.equal('04533 1234 567')\r\n\t\t// asYouType.input('8').should.equal('04533 1234 5678')\r\n\t})\r\n\r\n\tit('should just prepend national prefix if national_prefix_formatting_rule does not produce a suitable number', () => {\r\n\t\t// \"national_prefix\": \"8\"\r\n\t\t// \"national_prefix_for_parsing\": \"0|80?\"\r\n\t\tconst formatter = new AsYouType('BY')\r\n\t\t// \"national_prefix_formatting_rule\": \"8 $1\"\r\n\t\t// That `national_prefix_formatting_rule` isn't used\r\n\t\t// because the user didn't input national prefix `8`.\r\n\t\texpect(formatter.input('0800123')).to.equal('0 800 123')\r\n\t\texpect(formatter.getTemplate()).to.equal('x xxx xxx')\r\n\t})\r\n\r\n\tit('should not duplicate area code for certain countries', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/318\r\n\t\tconst asYouType = new AsYouType('VI')\r\n\t\t// Even though `parse(\"3406934\")` would return a\r\n\t\t// \"(*************\" national number, still\r\n\t\t// \"As You Type\" formatter should leave it as \"(340) 6934\".\r\n\t\texpect(asYouType.input('340693')).to.equal('(340) 693')\r\n\t\texpect(asYouType.input('4')).to.equal('(340) 693-4')\r\n\t\texpect(asYouType.input('123')).to.equal('(*************')\r\n\t})\r\n\r\n\tit('shouldn\\'t throw when passed a non-existent default country', () => {\r\n\t\texpect(new AsYouType('XX').input('+78005553535')).to.equal('****** 555 35 35')\r\n\t\texpect(new AsYouType('XX').input('88005553535')).to.equal('88005553535')\r\n\t})\r\n\r\n\tit('should parse carrier codes', () => {\r\n\t\tconst formatter = new AsYouType('BR')\r\n\r\n\t\tformatter.input('0 15 21 5555-5555')\r\n\t\tlet phoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber.carrierCode).to.equal('15')\r\n\r\n\t\tformatter.reset()\r\n\t\tformatter.input('******-373-4253')\r\n\t\tphoneNumber = formatter.getNumber()\r\n\t\texpect(phoneNumber.carrierCode).to.be.undefined\r\n\t})\r\n\r\n\tit('should format when default country calling code is configured', () => {\r\n\t\tconst formatter = new AsYouType({ defaultCallingCode: '7' })\r\n\t\texpect(formatter.input('88005553535')).to.equal('8 (800) 555-35-35')\r\n\t\texpect(formatter.getNumber().countryCallingCode).to.equal('7')\r\n\t\texpect(formatter.getNumber().country).to.equal('RU')\r\n\t})\r\n\r\n\tit('shouldn\\'t return PhoneNumber if country calling code hasn\\'t been input yet', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\tformatter.input('+80')\r\n\t\texpect(formatter.getNumber()).to.be.undefined\r\n\t})\r\n\r\n\tit('should format non-geographic numbering plan phone numbers', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.input('+')).to.equal('+')\r\n\t\texpect(formatter.input('8')).to.equal('+8')\r\n\t\texpect(formatter.input('7')).to.equal('+87')\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\texpect(formatter.input('0')).to.equal('+870')\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\texpect(formatter.getCountry()).to.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t}\r\n\t\texpect(formatter.input('7')).to.equal('+870 7')\r\n\t\texpect(formatter.input('7')).to.equal('+870 77')\r\n\t\texpect(formatter.input('3')).to.equal('+870 773')\r\n\t\texpect(formatter.input('1')).to.equal('+870 773 1')\r\n\t\texpect(formatter.input('1')).to.equal('+870 773 11')\r\n\t\texpect(formatter.input('1')).to.equal('+870 773 111')\r\n\t\texpect(formatter.input('6')).to.equal('+870 773 111 6')\r\n\t\texpect(formatter.input('3')).to.equal('+870 773 111 63')\r\n\t\texpect(formatter.input('2')).to.equal('+870 773 111 632')\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\texpect(formatter.getNumber().country).to.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t}\r\n\t\texpect(formatter.getNumber().countryCallingCode).to.equal('870')\r\n\t})\r\n\r\n\tit('should format non-geographic numbering plan phone numbers (default country calling code)', () => {\r\n\t\tconst formatter = new AsYouType({ defaultCallingCode: '870' })\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\texpect(formatter.getNumber().country).to.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t}\r\n\t\texpect(formatter.input('7')).to.equal('7')\r\n\t\texpect(formatter.input('7')).to.equal('77')\r\n\t\texpect(formatter.input('3')).to.equal('773')\r\n\t\texpect(formatter.input('1')).to.equal('773 1')\r\n\t\texpect(formatter.input('1')).to.equal('773 11')\r\n\t\texpect(formatter.input('1')).to.equal('773 111')\r\n\t\texpect(formatter.input('6')).to.equal('773 111 6')\r\n\t\texpect(formatter.input('3')).to.equal('773 111 63')\r\n\t\texpect(formatter.input('2')).to.equal('773 111 632')\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\texpect(formatter.getNumber().country).to.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\t}\r\n\t\texpect(formatter.getNumber().countryCallingCode).to.equal('870')\r\n\t})\r\n\r\n\tit('should not format non-geographic numbering plan phone numbers (default country 001)', () => {\r\n\t\tconst formatter = new AsYouType('001')\r\n\t\texpect(formatter.defaultCountry).to.be.undefined\r\n\t\texpect(formatter.defaultCallingCode).to.be.undefined\r\n\t\texpect(formatter.input('7')).to.equal('7')\r\n\t\texpect(formatter.input('7')).to.equal('77')\r\n\t\texpect(formatter.input('3')).to.equal('773')\r\n\t\texpect(formatter.input('1')).to.equal('7731')\r\n\t\texpect(formatter.input('1')).to.equal('77311')\r\n\t\texpect(formatter.input('1')).to.equal('773111')\r\n\t\texpect(formatter.input('6')).to.equal('7731116')\r\n\t\texpect(formatter.input('3')).to.equal('77311163')\r\n\t\texpect(formatter.input('2')).to.equal('773111632')\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\texpect(formatter.getNumber()).to.be.undefined\r\n\t})\r\n\r\n\tit('should return PhoneNumber (should strip national prefix `1` in E.164 value)', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tformatter.input('+1111')\r\n\t\texpect(formatter.getNumber().number).to.equal('+111')\r\n\t})\r\n\r\n\tit('should return PhoneNumber with autocorrected international numbers without leading +', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/316\r\n\t\tconst formatter = new AsYouType('FR')\r\n\t\texpect(formatter.input('33612902554')).to.equal('33 6 12 90 25 54')\r\n\t\texpect(formatter.getNumber().country).to.equal('FR')\r\n\t\texpect(formatter.getNumber().nationalNumber).to.equal('612902554')\r\n\t\texpect(formatter.getNumber().number).to.equal('+33612902554')\r\n\t\t// Should also strip national prefix.\r\n\t\tformatter.reset()\r\n\t\texpect(formatter.input('330612902554')).to.equal('33 06 12 90 25 54')\r\n\t\texpect(formatter.getNumber().country).to.equal('FR')\r\n\t\texpect(formatter.getNumber().nationalNumber).to.equal('612902554')\r\n\t\texpect(formatter.getNumber().number).to.equal('+33612902554')\r\n\t\t// On second thought, this \"prepend default area code\" feature won't be added,\r\n\t\t// because when a user selects \"British Virgin Islands\" and inputs\r\n\t\t// \"2291234\", then they see \"(229) 123-4\" which clearly indicates that\r\n\t\t// they should input the complete phone number (with area code).\r\n\t\t// So, unless a user completely doesn't understand what they're doing,\r\n\t\t// they'd input the complete phone number (with area code).\r\n\t\t// // Should prepend the default area code in British Virgin Islands.\r\n\t\t// // https://github.com/catamphetamine/react-phone-number-input/issues/335\r\n\t\t// const formatter2 = new AsYouType('VG')\r\n\t\t// formatter2.input('2291234').should.equal('(229) 123-4')\r\n\t\t// formatter2.getNumber().country.should.equal('VG')\r\n\t\t// formatter2.getNumber().nationalNumber.should.equal('2842291234')\r\n\t\t// formatter2.getNumber().number.should.equal('+12842291234')\r\n\t})\r\n\r\n\tit('should work with out-of-country dialing prefix (like 00)', () => {\r\n\t\tconst formatter = new AsYouType('DE')\r\n\t\texpect(formatter.input('00498911196611')).to.equal('00 49 89 11196611')\r\n\t\texpect(formatter.getCountry()).to.equal('DE')\r\n\t\texpect(formatter.formatter.template).to.equal('xx xx xx xxxxxxxx')\r\n\t\texpect(formatter.formatter.populatedNationalNumberTemplate).to.equal('89 11196611')\r\n\t\texpect(formatter.getTemplate()).to.equal('xx xx xx xxxxxxxx')\r\n\t\texpect(formatter.getNumber().country).to.equal('DE')\r\n\t\texpect(formatter.getNumber().nationalNumber).to.equal('8911196611')\r\n\t\texpect(formatter.getNumber().number).to.equal('+498911196611')\r\n\t})\r\n\r\n\tit('shouldn\\'t choose a format when there\\'re too many digits for any of them', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tformatter.input('89991112233')\r\n\t\texpect(formatter.formatter.chosenFormat.format()).to.equal('$1 $2-$3-$4')\r\n\t\tformatter.reset()\r\n\t\tformatter.input('899911122334')\r\n\t\texpect(formatter.formatter.chosenFormat).to.be.undefined\r\n\t})\r\n\r\n\tit('should get separator after national prefix', () => {\r\n\t\t// Russia.\r\n\t\t// Has separator after national prefix.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\tconst format = formatter.metadata.formats()[0]\r\n\t\texpect(format.nationalPrefixFormattingRule()).to.equal('8 ($1)')\r\n\t\texpect(formatter.formatter.getSeparatorAfterNationalPrefix(format)).to.equal(' ')\r\n\t\t// Britain.\r\n\t\t// Has no separator after national prefix.\r\n\t\tconst formatter2 = new AsYouType('GB')\r\n\t\tconst format2 = formatter2.metadata.formats()[0]\r\n\t\texpect(format2.nationalPrefixFormattingRule()).to.equal('0$1')\r\n\t\texpect(formatter2.formatter.getSeparatorAfterNationalPrefix(format2)).to.equal('')\r\n\t})\r\n\r\n\tit('should return if the number is possible', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.isPossible()).to.equal(false)\r\n\t\tformatter.input('8')\r\n\t\texpect(formatter.isPossible()).to.equal(false)\r\n\t\tformatter.input('8005553535')\r\n\t\texpect(formatter.isPossible()).to.equal(true)\r\n\t\tformatter.input('5')\r\n\t\texpect(formatter.isPossible()).to.equal(false)\r\n\t})\r\n\r\n\tit('should return if the number is valid', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.isValid()).to.equal(false)\r\n\t\tformatter.input('88005553535')\r\n\t\texpect(formatter.isValid()).to.equal(true)\r\n\t\tformatter.input('5')\r\n\t\texpect(formatter.isValid()).to.equal(false)\r\n\t})\r\n\r\n\tit('should return if the number is international', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.isInternational()).to.equal(false)\r\n\t\tformatter.input('88005553535')\r\n\t\texpect(formatter.isInternational()).to.equal(false)\r\n\t\t// International. Russia.\r\n\t\tconst formatterInt = new AsYouType()\r\n\t\texpect(formatterInt.isInternational()).to.equal(false)\r\n\t\tformatterInt.input('+')\r\n\t\texpect(formatterInt.isInternational()).to.equal(true)\r\n\t\tformatterInt.input('78005553535')\r\n\t\texpect(formatterInt.isInternational()).to.equal(true)\r\n\t})\r\n\r\n\tit('should return country calling code part of the number', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.getCountryCallingCode()).to.be.undefined\r\n\t\tformatter.input('88005553535')\r\n\t\texpect(formatter.getCountryCallingCode()).to.be.undefined\r\n\t\t// International. Russia.\r\n\t\tconst formatterInt = new AsYouType()\r\n\t\texpect(formatterInt.getCountryCallingCode()).to.be.undefined\r\n\t\tformatterInt.input('+')\r\n\t\texpect(formatterInt.getCountryCallingCode()).to.be.undefined\r\n\t\tformatterInt.input('7')\r\n\t\texpect(formatterInt.getCountryCallingCode()).to.equal('7')\r\n\t\tformatterInt.input('8005553535')\r\n\t\texpect(formatterInt.getCountryCallingCode()).to.equal('7')\r\n\t})\r\n\r\n\tit('should return the country of the number', () => {\r\n\t\t// National. Russia.\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.getCountry()).to.be.undefined\r\n\t\tformatter.input('8')\r\n\t\texpect(formatter.getCountry()).to.equal('RU')\r\n\t\tformatter.input('8005553535')\r\n\t\texpect(formatter.getCountry()).to.equal('RU')\r\n\t\t// International. Austria.\r\n\t\tconst formatterInt = new AsYouType()\r\n\t\texpect(formatterInt.getCountry()).to.be.undefined\r\n\t\tformatterInt.input('+')\r\n\t\texpect(formatterInt.getCountry()).to.be.undefined\r\n\t\tformatterInt.input('43')\r\n\t\texpect(formatterInt.getCountry()).to.equal('AT')\r\n\t\t// International. USA.\r\n\t\tconst formatterIntRu = new AsYouType()\r\n\t\texpect(formatterIntRu.getCountry()).to.be.undefined\r\n\t\tformatterIntRu.input('+')\r\n\t\texpect(formatterIntRu.getCountry()).to.be.undefined\r\n\t\tformatterIntRu.input('1')\r\n\t\texpect(formatterIntRu.getCountry()).to.be.undefined\r\n\t\tformatterIntRu.input('**********')\r\n\t\texpect(formatterIntRu.getCountry()).to.equal('US')\r\n\t\tformatterIntRu.input('1')\r\n\t\texpect(formatterIntRu.getCountry()).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse a long IDD prefix', () => {\r\n\t\tconst formatter = new AsYouType('AU')\r\n\t\t// `14880011` is a long IDD prefix in Australia.\r\n\t\texpect(formatter.input('1')).to.equal('1')\r\n\t\texpect(formatter.input('4')).to.equal('14')\r\n\t\texpect(formatter.input('8')).to.equal('148')\r\n\t\texpect(formatter.input('8')).to.equal('1488')\r\n\t\texpect(formatter.input('0')).to.equal('14880')\r\n\t\texpect(formatter.input('0')).to.equal('148800')\r\n\t\texpect(formatter.input('1')).to.equal('1488001')\r\n\t\texpect(formatter.input('1')).to.equal('14880011')\r\n\t\t// As if were calling US using `14880011` IDD prefix,\r\n\t\t// though that prefix could mean something else.\r\n\t\texpect(formatter.input('1')).to.equal('14880011 1')\r\n\t\texpect(formatter.input('2')).to.equal('14880011 1 2')\r\n\t\texpect(formatter.input('1')).to.equal('14880011 1 21')\r\n\t\texpect(formatter.input('3')).to.equal('14880011 1 213')\r\n\t})\r\n\r\n\tit('should return the phone number characters entered by the user', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.getChars()).to.equal('')\r\n\t\tformatter.input('+123')\r\n\t\texpect(formatter.getChars()).to.equal('+123')\r\n\t\tformatter.reset()\r\n\t\tformatter.input('123')\r\n\t\texpect(formatter.getChars()).to.equal('123')\r\n\t})\r\n\r\n\t// A test confirming the case when input `\"11\"` for country `\"US\"`\r\n\t// produces `value` `\"+11\"`.\r\n\t// https://gitlab.com/catamphetamine/react-phone-number-input/-/issues/113\r\n\tit('should determine the national (significant) part correctly when input with national prefix in US', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\t// As soon as the user has input `\"11\"`, no `format` matches\r\n\t\t// those \"national number\" digits in the `\"US\"` country metadata.\r\n\t\t// Since no `format` matches, the number doesn't seem like a valid one,\r\n\t\t// so it attempts to see if the user \"forgot\" to input a `\"+\"` at the start.\r\n\t\t// And it looks like they might've to.\r\n\t\t// So it acts as if the leading `\"+\"` is there,\r\n\t\t// as if the user's input is `\"+11\"`.\r\n\t\t// See `AsYouType.fixMissingPlus()` function.\r\n\t\texpect(formatter.input('************** 3')).to.equal('1 **************')\r\n\t\texpect(formatter.getNumber().nationalNumber).to.equal('2222222223')\r\n\t})\r\n})\r\n\r\ndescribe('AsYouType.getNumberValue()', () => {\r\n\tit('should return E.164 number value (national number, with national prefix, default country: US)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (national number, with national prefix, default calling code: 1)', () => {\r\n\t\tconst formatter = new AsYouType({ defaultCallingCode: '1' })\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (national number, default country: US)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (national number, default calling code: 1)', () => {\r\n\t\tconst formatter = new AsYouType({ defaultCallingCode: '1' })\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, not a valid calling code)', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('2150')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+2150')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, default country: US)', () => {\r\n\t\tconst formatter = new AsYouType('US')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, other default country: RU)', () => {\r\n\t\tconst formatter = new AsYouType('RU')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, default calling code: 1)', () => {\r\n\t\tconst formatter = new AsYouType('US', { defaultCallingCode: '1' })\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number, other default calling code: 7)', () => {\r\n\t\tconst formatter = new AsYouType('US', { defaultCallingCode: '7' })\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (international number)', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('+')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1')\r\n\t\tformatter.input('2')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+12')\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+121')\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1213')\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********')\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.equal('+1**********4')\r\n\t})\r\n\r\n\tit('should return E.164 number value (national number) (no default country or calling code)', () => {\r\n\t\tconst formatter = new AsYouType()\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('1')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('12')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('3')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('373-4253')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t\tformatter.input('4')\r\n\t\texpect(formatter.getNumberValue()).to.be.undefined\r\n\t})\r\n\r\n\tit('should not drop any input digits', () => {\r\n\t\t// Test \"+529011234567\" number, proactively ensuring that no formatting is applied,\r\n\t\t// where a format is chosen that would otherwise have led to some digits being dropped.\r\n\t\tconst formatter = new AsYouType('MX')\r\n\t\texpect(formatter.input('9')).to.equal('9')\r\n\t\texpect(formatter.input('0')).to.equal('90')\r\n\t\texpect(formatter.input('1')).to.equal('901')\r\n\t\texpect(formatter.input('1')).to.equal('901 1')\r\n\t\texpect(formatter.input('2')).to.equal('901 12')\r\n\t\texpect(formatter.input('3')).to.equal('901 123')\r\n\t\texpect(formatter.input('4')).to.equal('901 123 4')\r\n\t\texpect(formatter.input('5')).to.equal('901 123 45')\r\n\t\texpect(formatter.input('6')).to.equal('901 123 456')\r\n\t\texpect(formatter.input('7')).to.equal('************')\r\n\t})\r\n\r\n\tit('should work for formats with no leading digits (`leadingDigitsPatternsCount === 0`)', function() {\r\n\t\tconst formatter = new AsYouType({\r\n\t\t\tdefaultCallingCode: 888\r\n\t\t})\r\n\t\texpect(formatter.input('1')).to.equal('1')\r\n\t})\r\n\r\n\tit('should work for SK phone numbers', function() {\r\n\t\t// There was a bug: \"leading digits\" `\"2\"` matched \"leading digits pattern\" `\"90\"`.\r\n\t\t// The incorrect `.match()` function result was `{ oveflow: true }`\r\n\t\t// while it should've been `undefined`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/66\r\n\t\tconst formatter = new AsYouType('SK')\r\n\t\texpect(formatter.input('090')).to.equal('090')\r\n\t\tformatter.reset()\r\n\t\texpect(formatter.input('080')).to.equal('080')\r\n\t\tformatter.reset()\r\n\t\texpect(formatter.input('059')).to.equal('059')\r\n\t})\r\n\r\n\tit('should work for SK phone numbers (2)', function() {\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/69\r\n\t\tconst formatter = new AsYouType('SK')\r\n\t\texpect(formatter.input('421901222333')).to.equal('421 901 222 333')\r\n\t\texpect(formatter.getTemplate()).to.equal('xxx xxx xxx xxx')\r\n\t})\r\n\r\n\tit('should not choose `defaultCountry` over the \"main\" one when both the `defaultCountry` and the \"main\" one match the phone number', function() {\r\n\t\t// This phone number matches both US and CA because they have the same\r\n\t\t// regular expression for some weird reason.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/103\r\n\t\tconst formatter = new AsYouType('CA')\r\n\t\tformatter.input('8004001000')\r\n\t\texpect(formatter.getNumber().country).not.to.equal('CA')\r\n\t\texpect(formatter.getNumber().country).to.equal('US')\r\n\r\n\t\t// This phone number is specific to CA.\r\n\t\tconst formatter2 = new AsYouType('US')\r\n\t\tformatter2.input('4389999999')\r\n\t\texpect(formatter2.getNumber().country).to.equal('CA')\r\n\r\n\t\t// This phone number doesn't belong neither to CA nor to US.\r\n\t\t// In fact, it doesn't belong to any country from the \"NANPA\" zone.\r\n\t\tconst formatter3 = new AsYouType('US')\r\n\t\tformatter3.input('1111111111')\r\n\t\texpect(formatter3.getNumber().country).to.equal('US')\r\n\t})\r\n})\r\n\r\nfunction type(something) {\r\n\treturn typeof something\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA,OAAOA,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAC/D,OAAOC,UAAU,MAAM,gBAAgB;AAAA,IAEjCC,SAAS,0BAAAC,WAAA;EACd,SAAAD,UAAYE,YAAY,EAAE;IAAAC,eAAA,OAAAH,SAAA;IAAA,OAAAI,UAAA,OAAAJ,SAAA,GACnBE,YAAY,EAAEL,QAAQ;EAC7B;EAACQ,SAAA,CAAAL,SAAA,EAAAC,WAAA;EAAA,OAAAK,YAAA,CAAAN,SAAA;AAAA,EAHsBD,UAAU;AAMlC,IAAMQ,+BAA+B,GAAG,KAAK;AAE7CC,QAAQ,CAAC,WAAW,EAAE,YAAM;EAC3BC,EAAE,CAAC,8CAA8C,EAAE,YAAM;IACxD;IACAC,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IAC9E;IACAH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;EAC1E,CAAC,CAAC;EAEFJ,EAAE,CAAC,2DAA2D,EAAE,YAAM;IACrE,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCc,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpB;IACAD,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IAC3D;IACAH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IAClFC,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC;IACtBD,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACxFC,SAAS,CAACH,KAAK,CAAC,SAAS,CAAC;IAC1BD,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EACzF,CAAC,CAAC;EAEFJ,EAAE,CAAC,kFAAkF,EAAE,YAAM;IAC5F,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACK,EAAE,CAACC,SAAS;IAC3ER,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACxCH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACK,EAAE,CAACC,SAAS;IAC3ER,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACK,EAAE,CAACC,SAAS;IAC3ER,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC9C;IACA;IACAH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IAC5D;IACAH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IAChF;IACAC,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC;IACtBD,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACpFC,SAAS,CAACH,KAAK,CAAC,SAAS,CAAC;IAC1BD,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;EACrF,CAAC,CAAC;EAEFJ,EAAE,CAAC,gEAAgE,EAAE,YAAM;IAC1E,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IACjCU,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACK,EAAE,CAACC,SAAS;IAC3ER,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACxCH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACK,EAAE,CAACC,SAAS;IAC3ER,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACK,EAAE,CAACC,SAAS;IAC3ER,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3C;IACAH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IAC5D;IACA;IACAH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IAChF;IACAC,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC;IACtBD,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACpFC,SAAS,CAACH,KAAK,CAAC,SAAS,CAAC;IAC1BD,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;EACrF,CAAC,CAAC;EAEFJ,EAAE,CAAC,8EAA8E,EAAE,YAAM;IACxF,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACnEH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACjEH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EACzF,CAAC,CAAC;EAEFJ,EAAE,CAAC,mDAAmD,EAAE,YAAM;IAC7D;IACAC,MAAM,CAAC,IAAIV,SAAS,CAAC,CAAC,CAACW,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACnE;IACAH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,SAAS,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;;IAEpE;IACAH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,UAAU,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;;IAEvE;IACAH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IAE7E,IAAIC,SAAS;;IAEb;IACA;;IAEA;IACAJ,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IAE5EH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;;IAEpE;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;;IAE3B;IACAU,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC1DH,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACrEH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IAE5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;;IAE1C;IACAH,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC1DH,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACrEH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAE7CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE3C;IACAH,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC1DH,MAAM,CAACI,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAE9CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;;IAEhD;IACAH,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAE1DH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IAC9CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IACjDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAClDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACnDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACrDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IACtDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;;IAEvD;IACAH,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAE1DH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;;IAExD;IACAH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C;IACA;IACAH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IAE3DH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;;IAEvD;IACAH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC9CR,MAAM,CAACI,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvDH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;;IAEpD;IACA;;IAEAJ,SAAS,CAACQ,KAAK,CAAC,CAAC;IAEjBZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;;IAEpE;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAE/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACzCH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IAC9CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAClDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;;IAEnD;IACAH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IAC9DH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IAEtDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;;IAExD;IACAH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IAEzDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;;IAEpD;IACAH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;;IAEpD;;IAEAJ,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IAE3Bc,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC;IAC/BD,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE7C;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAC/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;;IAElE;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IAC3BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,eAAe,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IACpEH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IAC3DH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE7C;IACAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAC/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACpEH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;;IAE7D;IACA;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAE/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,MAAM,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IACzDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IAC5DH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;;IAErD;IACA;IACAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAC/B;IACAU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACpEH,MAAM,CAACI,SAAS,CAACS,iBAAiB,CAAC,CAAC,CAAC,CAACX,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;;IAE5D;IACA;IACA;;IAEAC,SAAS,CAACQ,KAAK,CAAC,CAAC;IAEjBZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,MAAM,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IACzDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IAC5DH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;;IAErD;IACA;IACAH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpDH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,IAAI,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACvDH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;;IAEzD;IACA;IACAH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;;IAElE;IACAH,MAAM,CAAC,IAAIV,SAAS,CAAC,CAAC,CAACW,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;;IAE5E;IACAC,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IAC3Bc,SAAS,CAACH,KAAK,CAAC,iBAAiB,CAAC;IAClCD,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C;;IAEA;IACAH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,MAAM,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;;IAE3D;IACAC,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IAC3BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IACnEH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EAC5D,CAAC,CAAC;EAEFJ,EAAE,CAAC,gGAAgG,EAAE,YAAM;IAC1G;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;;IAErC;IACA;IACAU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IACxDH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;;IAEpD;IACAR,MAAM,CAACI,SAAS,CAACQ,KAAK,CAAC,CAAC,CAACX,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACpEH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;EAC9D,CAAC,CAAC;EAEFJ,EAAE,CAAC,yFAAyF,EAAE,YAAM;IACnG;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,YAAY,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IAC5DC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EACrE,CAAC,CAAC;EAEFJ,EAAE,CAAC,uIAAuI,EAAE,YAAM;IACjJ,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC1DC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,YAAY,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;EACjE,CAAC,CAAC;EAEFJ,EAAE,CAAC,2IAA2I,EAAE,YAAM;IACrJ,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrC;IACAU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,YAAY,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IAC/DC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjB;IACAZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;EACrE,CAAC,CAAC;EAEFJ,EAAE,CAAC,8FAA8F,EAAE,YAAM;IACxG;IACA;IACA;IACAC,MAAM,CAAC,IAAIV,SAAS,CAAC,CAAC,CAACW,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IACzEH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IAC7EH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EACzE,CAAC,CAAC;EAEFJ,EAAE,CAAC,yFAAyF,EAAE,YAAM;IACnG,IAAIK,SAAS;IAEbA,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IAC3BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IAC1E;IACAH,MAAM,CAACI,SAAS,CAACS,iBAAiB,CAAC,CAAC,CAAC,CAACX,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;;IAE5D;IACA;IACA;IACA;IACA;IACA;EACD,CAAC,CAAC;EAEFJ,EAAE,CAAC,oDAAoD,EAAE,YAAM;IAC9D,IAAMe,SAAS,GAAG,IAAIxB,SAAS,CAAC,IAAI,CAAC;IAErCU,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACxCH,MAAM,CAACc,SAAS,CAACL,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IAE5CH,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1C;IACA;IACAH,MAAM,CAACc,SAAS,CAACL,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAE7CH,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACc,SAAS,CAACL,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAE9CH,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IAC9CH,MAAM,CAACc,SAAS,CAACL,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;EAClD,CAAC,CAAC;EAEFJ,EAAE,4CAA4C,YAAM;IACnD,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAErCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;;IAEhD;IACAH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IAClEH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C;;IAEAH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;;IAEpE;IACAH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C;;IAEAC,SAAS,CAACQ,KAAK,CAAC,CAAC;;IAEjB;IACAZ,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC9C;;IAEAR,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;;IAEtE;IACAH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IAC3DH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EACxD,CAAC,CAAC;EAEFJ,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC,IAAIK,SAAS;IACb,IAAIW,OAAO;;IAEX;IACAA,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAI1B,UAAU,CAAC,IAAI,CAAC;IAAA;IACpCW,MAAM,CAACe,OAAO,CAAC,CAACb,EAAE,SAAM,CAAC,gCAAgC,CAAC;;IAE1D;;IAEAE,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAE/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE3C;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAE/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;;IAE1C;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAE/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACpEH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;;IAErD;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IAE3BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,OAAO,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;;IAElD;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IAE3BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;;IAE9D;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAE/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,QAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;;IAErD;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAE/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,QAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAEhD;IACA;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAE/Bc,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC1DH,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;;IAErE;IACA;;IAEAC,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IAE3Bc,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC;IAC/BD,MAAM,CAACZ,IAAI,CAACgB,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC1DH,MAAM,CAACI,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;;IAEvD;IACA;IACAC,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAC/BU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,MAAM,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAChD;IACA;IACAH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;EAChD,CAAC,CAAC;EAEFJ,EAAE,CAAC,8FAA8F,EAAE,YAAM;IACxG;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrC;IACA;IACAU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACzDC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjB;IACA;IACA;IACA;IACA;IACA;IACAZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,SAAS,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EACzD,CAAC,CAAC;EAEFJ,EAAE,CAAC,2CAA2C,EAAE,YAAM;IACrDC,MAAM,CAAC,IAAIV,SAAS,CAAC,CAAC,CAACW,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EACtF,CAAC,CAAC;EAEFJ,EAAE,CAAC,kCAAkC,EAAE,YAAM;IAC5CC,MAAM,CAAC,IAAIV,SAAS,CAAC,CAAC,CAACW,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EAC1E,CAAC,CAAC;EAEFJ,EAAE,CAAC,sCAAsC,EAAE,YAAM;IAChD,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;;IAErC;IACAU,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAAC,CAACd,EAAE,CAACK,EAAE,CAACC,SAAS;IAE7CJ,SAAS,CAACH,KAAK,CAAC,IAAI,CAAC;IACrB;IACAD,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAAC,CAACd,EAAE,CAACK,EAAE,CAACC,SAAS;IAE7CJ,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC;IAE/B,IAAIgB,WAAW,GAAGb,SAAS,CAACY,SAAS,CAAC,CAAC;IACvChB,MAAM,CAACiB,WAAW,CAACC,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC1CH,MAAM,CAACiB,WAAW,CAACE,kBAAkB,CAAC,CAACjB,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpDH,MAAM,CAACiB,WAAW,CAACG,MAAM,CAAC,CAAClB,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACnDH,MAAM,CAACiB,WAAW,CAACI,cAAc,CAAC,CAACnB,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IAEzDC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBR,SAAS,CAACH,KAAK,CAAC,iBAAiB,CAAC;IAElCgB,WAAW,GAAGb,SAAS,CAACY,SAAS,CAAC,CAAC;IACnChB,MAAM,CAACiB,WAAW,CAACC,OAAO,CAAC,CAAChB,EAAE,CAACK,EAAE,CAACC,SAAS;IAC3CR,MAAM,CAACiB,WAAW,CAACE,kBAAkB,CAAC,CAACjB,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;;IAEpD;IACA;;IAEAC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBR,SAAS,CAACH,KAAK,CAAC,MAAM,CAAC;;IAEvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACAgB,WAAW,GAAGb,SAAS,CAACY,SAAS,CAAC,CAAC;IACnChB,MAAM,CAACiB,WAAW,CAAC,CAACf,EAAE,CAACK,EAAE,CAACC,SAAS;IACnC;IACAJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBgB,WAAW,GAAGb,SAAS,CAACY,SAAS,CAAC,CAAC;IACnChB,MAAM,CAACiB,WAAW,CAACC,OAAO,CAAC,CAAChB,EAAE,CAACK,EAAE,CAACC,SAAS;IAC3CR,MAAM,CAACiB,WAAW,CAACE,kBAAkB,CAAC,CAACjB,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpDH,MAAM,CAACiB,WAAW,CAACG,MAAM,CAAC,CAAClB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC3C,CAAC,CAAC;EAEFJ,EAAE,CAAC,6EAA6E,EAAE,YAAM;IACvF;IACA;IACA,IAAMe,SAAS,GAAG,IAAIxB,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,oBAAoB,CAAC;IACxEW,SAAS,CAACF,KAAK,CAAC,CAAC;IACjB;IACA;IACA;IACA;IACA;IACA;IACAZ,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,eAAe,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EACtE,CAAC,CAAC;EAEFJ,EAAE,CAAC,oHAAoH,EAAE,YAAM;IAC9H;IACA;IACA,IAAMe,SAAS,GAAG,IAAIxB,SAAS,CAAC,IAAI,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAU,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EACzE,CAAC,CAAC;EAEFJ,EAAE,CAAC,sEAAsE,EAAE,YAAM;IAChF;IACA;IACA;IACA;IACA;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC9CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IAC9CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACjDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAClDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IACnDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IACjDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IACpDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACnDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACtDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACpDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACvDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACrDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACxDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IACtDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IACzDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACrDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACxDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACzDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IAC5D;IACAH,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACC,yBAAyB,CAAC,CAACrB,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACzE;IACA;IACA;IACAH,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACE,cAAc,CAAC,CAACtB,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpDH,MAAM,CAACI,SAAS,CAACqB,UAAU,CAAC,CAAC,CAAC,CAACvB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACsB,OAAO,CAAC,CAAC,CAAC,CAACxB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAC3C,CAAC,CAAC;EAEFJ,EAAE,CAAC,qDAAqD,EAAE,YAAM;IAC/D;IACA;IACA;IACA;IACA;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,eAAe,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACrE;IACAH,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACC,yBAAyB,CAAC,CAACrB,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACzE;IACA;IACA;IACAH,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACE,cAAc,CAAC,CAACtB,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpD;IACAH,MAAM,CAACI,SAAS,CAACqB,UAAU,CAAC,CAAC,CAAC,CAACvB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACsB,OAAO,CAAC,CAAC,CAAC,CAACxB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAC3C,CAAC,CAAC;;EAEF;EACAJ,EAAE,CAAC,kCAAkC,EAAE,YAAM;IAC5C,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACzB,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACzC;IACA;IACA;IACAH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC/C,CAAC,CAAC;EAEFJ,EAAE,CAAC,uFAAuF,EAAE,YAAM;IACjG;IACA;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,eAAe,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACrE;IACAH,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACC,yBAAyB,CAAC,CAACrB,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACvE;IACA;IACA;IACAH,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACE,cAAc,CAAC,CAACtB,EAAE,CAACK,EAAE,CAACC,SAAS;IACtDR,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACM,4CAA4C,CAAC,CAAC1B,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;EACtF,CAAC,CAAC;EAEFJ,EAAE,CAAC,iFAAiF,EAAE,YAAM;IAC3F;IACA;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACvE;IACAH,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACC,yBAAyB,CAAC,CAACrB,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACxE;IACA;IACA;IACAH,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACE,cAAc,CAAC,CAACtB,EAAE,CAACK,EAAE,CAACC,SAAS;IACtDR,MAAM,CAACI,SAAS,CAACkB,KAAK,CAACM,4CAA4C,CAAC,CAAC1B,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;EACtF,CAAC,CAAC;EAEFJ,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3C,IAAMe,SAAS,GAAG,IAAIxB,SAAS,CAAC,IAAI,CAAC;;IAErC;IACAU,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IACtEH,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACzDW,SAAS,CAACF,KAAK,CAAC,CAAC;;IAEjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAZ,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IAC/DH,MAAM,CAACc,SAAS,CAACL,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACvDH,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACrDH,MAAM,CAACc,SAAS,CAACL,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACxDW,SAAS,CAACF,KAAK,CAAC,CAAC;;IAEjB;IACAZ,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IACnEH,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACzDW,SAAS,CAACF,KAAK,CAAC,CAAC;;IAEjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD,CAAC,CAAC;EAEFb,EAAE,CAAC,2GAA2G,EAAE,YAAM;IACrH;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrC;IACA;IACA;IACAU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,SAAS,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACxDH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EACtD,CAAC,CAAC;EAEFJ,EAAE,CAAC,sDAAsD,EAAE,YAAM;IAChE;IACA,IAAMe,SAAS,GAAG,IAAIxB,SAAS,CAAC,IAAI,CAAC;IACrC;IACA;IACA;IACAU,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,QAAQ,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACvDH,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACpDH,MAAM,CAACc,SAAS,CAACb,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;EAC1D,CAAC,CAAC;EAEFJ,EAAE,CAAC,6DAA6D,EAAE,YAAM;IACvEC,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IAC9EH,MAAM,CAAC,IAAIV,SAAS,CAAC,IAAI,CAAC,CAACW,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;EACzE,CAAC,CAAC;EAEFJ,EAAE,CAAC,4BAA4B,EAAE,YAAM;IACtC,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IAErCc,SAAS,CAACH,KAAK,CAAC,mBAAmB,CAAC;IACpC,IAAIgB,WAAW,GAAGb,SAAS,CAACY,SAAS,CAAC,CAAC;IACvChB,MAAM,CAACiB,WAAW,CAACY,WAAW,CAAC,CAAC3B,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAE9CC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBR,SAAS,CAACH,KAAK,CAAC,iBAAiB,CAAC;IAClCgB,WAAW,GAAGb,SAAS,CAACY,SAAS,CAAC,CAAC;IACnChB,MAAM,CAACiB,WAAW,CAACY,WAAW,CAAC,CAAC3B,EAAE,CAACK,EAAE,CAACC,SAAS;EAChD,CAAC,CAAC;EAEFT,EAAE,CAAC,+DAA+D,EAAE,YAAM;IACzE,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC;MAAEwC,kBAAkB,EAAE;IAAI,CAAC,CAAC;IAC5D9B,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACpEH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACG,kBAAkB,CAAC,CAACjB,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC9DH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACrD,CAAC,CAAC;EAEFJ,EAAE,CAAC,8EAA8E,EAAE,YAAM;IACxF,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IACjCc,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC;IACtBD,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAAC,CAACd,EAAE,CAACK,EAAE,CAACC,SAAS;EAC9C,CAAC,CAAC;EAEFT,EAAE,CAAC,2DAA2D,EAAE,YAAM;IACrE,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IACjCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC9CR,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7C,IAAIN,+BAA+B,EAAE;MACpCG,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC/C,CAAC,MAAM;MACNH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC/C;IACAR,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IACjDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACnDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACpDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACrDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IACvDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IACxDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACzD,IAAIN,+BAA+B,EAAE;MACpCG,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACtD,CAAC,MAAM;MACNH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC/C;IACAR,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACG,kBAAkB,CAAC,CAACjB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EACjE,CAAC,CAAC;EAEFJ,EAAE,CAAC,0FAA0F,EAAE,YAAM;IACpG,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC;MAAEwC,kBAAkB,EAAE;IAAM,CAAC,CAAC;IAC9D,IAAIjC,+BAA+B,EAAE;MACpCG,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACtD,CAAC,MAAM;MACNH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC/C;IACAR,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IAC9CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAClDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACnDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACpD,IAAIN,+BAA+B,EAAE;MACpCG,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACtD,CAAC,MAAM;MACNH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC/C;IACAR,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACG,kBAAkB,CAAC,CAACjB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EACjE,CAAC,CAAC;EAEFJ,EAAE,CAAC,qFAAqF,EAAE,YAAM;IAC/F,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,KAAK,CAAC;IACtCU,MAAM,CAACI,SAAS,CAAC2B,cAAc,CAAC,CAAC7B,EAAE,CAACK,EAAE,CAACC,SAAS;IAChDR,MAAM,CAACI,SAAS,CAAC0B,kBAAkB,CAAC,CAAC5B,EAAE,CAACK,EAAE,CAACC,SAAS;IACpDR,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IAC9CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IACjDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAClDH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC9CR,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAAC,CAACd,EAAE,CAACK,EAAE,CAACC,SAAS;EAC9C,CAAC,CAAC;EAEFT,EAAE,CAAC,6EAA6E,EAAE,YAAM;IACvF,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCc,SAAS,CAACH,KAAK,CAAC,OAAO,CAAC;IACxBD,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACI,MAAM,CAAC,CAAClB,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;EACtD,CAAC,CAAC;EAEFJ,EAAE,CAAC,sFAAsF,EAAE,YAAM;IAChG;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACnEH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACpDH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACK,cAAc,CAAC,CAACnB,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAClEH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACI,MAAM,CAAC,CAAClB,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC7D;IACAC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACrEH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACpDH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACK,cAAc,CAAC,CAACnB,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAClEH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACI,MAAM,CAAC,CAAClB,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC7D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD,CAAC,CAAC;EAEFJ,EAAE,CAAC,0DAA0D,EAAE,YAAM;IACpE,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACvEH,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACC,QAAQ,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IAClEH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACE,+BAA+B,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACnFH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IAC7DH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACpDH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACK,cAAc,CAAC,CAACnB,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACnEH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACI,MAAM,CAAC,CAAClB,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC/D,CAAC,CAAC;EAEFJ,EAAE,CAAC,2EAA2E,EAAE,YAAM;IACrF,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCc,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC;IAC9BD,MAAM,CAACI,SAAS,CAACA,SAAS,CAAC4B,YAAY,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC/B,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACzEC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBR,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC;IAC/BD,MAAM,CAACI,SAAS,CAACA,SAAS,CAAC4B,YAAY,CAAC,CAAC9B,EAAE,CAACK,EAAE,CAACC,SAAS;EACzD,CAAC,CAAC;EAEFT,EAAE,CAAC,4CAA4C,EAAE,YAAM;IACtD;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrC,IAAM2C,MAAM,GAAG7B,SAAS,CAACjB,QAAQ,CAAC+C,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9ClC,MAAM,CAACiC,MAAM,CAACE,4BAA4B,CAAC,CAAC,CAAC,CAACjC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAChEH,MAAM,CAACI,SAAS,CAACA,SAAS,CAACgC,+BAA+B,CAACH,MAAM,CAAC,CAAC,CAAC/B,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACjF;IACA;IACA,IAAMkC,UAAU,GAAG,IAAI/C,SAAS,CAAC,IAAI,CAAC;IACtC,IAAMgD,OAAO,GAAGD,UAAU,CAAClD,QAAQ,CAAC+C,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAChDlC,MAAM,CAACsC,OAAO,CAACH,4BAA4B,CAAC,CAAC,CAAC,CAACjC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC9DH,MAAM,CAACqC,UAAU,CAACjC,SAAS,CAACgC,+BAA+B,CAACE,OAAO,CAAC,CAAC,CAACpC,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;EACnF,CAAC,CAAC;EAEFJ,EAAE,CAAC,yCAAyC,EAAE,YAAM;IACnD;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACqB,UAAU,CAAC,CAAC,CAAC,CAACvB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC9CC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACqB,UAAU,CAAC,CAAC,CAAC,CAACvB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC9CC,SAAS,CAACH,KAAK,CAAC,YAAY,CAAC;IAC7BD,MAAM,CAACI,SAAS,CAACqB,UAAU,CAAC,CAAC,CAAC,CAACvB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACqB,UAAU,CAAC,CAAC,CAAC,CAACvB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC/C,CAAC,CAAC;EAEFJ,EAAE,CAAC,sCAAsC,EAAE,YAAM;IAChD;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACsB,OAAO,CAAC,CAAC,CAAC,CAACxB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC3CC,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC;IAC9BD,MAAM,CAACI,SAAS,CAACsB,OAAO,CAAC,CAAC,CAAC,CAACxB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC1CC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsB,OAAO,CAAC,CAAC,CAAC,CAACxB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC5C,CAAC,CAAC;EAEFJ,EAAE,CAAC,8CAA8C,EAAE,YAAM;IACxD;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACmC,eAAe,CAAC,CAAC,CAAC,CAACrC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC;IAC9BD,MAAM,CAACI,SAAS,CAACmC,eAAe,CAAC,CAAC,CAAC,CAACrC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACnD;IACA,IAAMqC,YAAY,GAAG,IAAIlD,SAAS,CAAC,CAAC;IACpCU,MAAM,CAACwC,YAAY,CAACD,eAAe,CAAC,CAAC,CAAC,CAACrC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACtDqC,YAAY,CAACvC,KAAK,CAAC,GAAG,CAAC;IACvBD,MAAM,CAACwC,YAAY,CAACD,eAAe,CAAC,CAAC,CAAC,CAACrC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACrDqC,YAAY,CAACvC,KAAK,CAAC,aAAa,CAAC;IACjCD,MAAM,CAACwC,YAAY,CAACD,eAAe,CAAC,CAAC,CAAC,CAACrC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACtD,CAAC,CAAC;EAEFJ,EAAE,CAAC,uDAAuD,EAAE,YAAM;IACjE;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACK,EAAE,CAACC,SAAS;IACzDJ,SAAS,CAACH,KAAK,CAAC,aAAa,CAAC;IAC9BD,MAAM,CAACI,SAAS,CAACO,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACK,EAAE,CAACC,SAAS;IACzD;IACA,IAAMgC,YAAY,GAAG,IAAIlD,SAAS,CAAC,CAAC;IACpCU,MAAM,CAACwC,YAAY,CAAC7B,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACK,EAAE,CAACC,SAAS;IAC5DgC,YAAY,CAACvC,KAAK,CAAC,GAAG,CAAC;IACvBD,MAAM,CAACwC,YAAY,CAAC7B,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACK,EAAE,CAACC,SAAS;IAC5DgC,YAAY,CAACvC,KAAK,CAAC,GAAG,CAAC;IACvBD,MAAM,CAACwC,YAAY,CAAC7B,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1DqC,YAAY,CAACvC,KAAK,CAAC,YAAY,CAAC;IAChCD,MAAM,CAACwC,YAAY,CAAC7B,qBAAqB,CAAC,CAAC,CAAC,CAACT,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EAC3D,CAAC,CAAC;EAEFJ,EAAE,CAAC,yCAAyC,EAAE,YAAM;IACnD;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IAC9CJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7CC,SAAS,CAACH,KAAK,CAAC,YAAY,CAAC;IAC7BD,MAAM,CAACI,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C;IACA,IAAMqC,YAAY,GAAG,IAAIlD,SAAS,CAAC,CAAC;IACpCU,MAAM,CAACwC,YAAY,CAAC9B,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IACjDgC,YAAY,CAACvC,KAAK,CAAC,GAAG,CAAC;IACvBD,MAAM,CAACwC,YAAY,CAAC9B,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IACjDgC,YAAY,CAACvC,KAAK,CAAC,IAAI,CAAC;IACxBD,MAAM,CAACwC,YAAY,CAAC9B,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAChD;IACA,IAAMsC,cAAc,GAAG,IAAInD,SAAS,CAAC,CAAC;IACtCU,MAAM,CAACyC,cAAc,CAAC/B,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IACnDiC,cAAc,CAACxC,KAAK,CAAC,GAAG,CAAC;IACzBD,MAAM,CAACyC,cAAc,CAAC/B,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IACnDiC,cAAc,CAACxC,KAAK,CAAC,GAAG,CAAC;IACzBD,MAAM,CAACyC,cAAc,CAAC/B,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;IACnDiC,cAAc,CAACxC,KAAK,CAAC,YAAY,CAAC;IAClCD,MAAM,CAACyC,cAAc,CAAC/B,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAClDsC,cAAc,CAACxC,KAAK,CAAC,GAAG,CAAC;IACzBD,MAAM,CAACyC,cAAc,CAAC/B,UAAU,CAAC,CAAC,CAAC,CAACR,EAAE,CAACK,EAAE,CAACC,SAAS;EACpD,CAAC,CAAC;EAEFT,EAAE,CAAC,gCAAgC,EAAE,YAAM;IAC1C,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrC;IACAU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IAC9CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;IACjD;IACA;IACAH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACnDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACrDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;IACtDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;EACxD,CAAC,CAAC;EAEFJ,EAAE,CAAC,+DAA+D,EAAE,YAAM;IACzE,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACzB,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACzCC,SAAS,CAACH,KAAK,CAAC,MAAM,CAAC;IACvBD,MAAM,CAACI,SAAS,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACzB,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IAC7CC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBR,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC;IACtBD,MAAM,CAACI,SAAS,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAACzB,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC7C,CAAC,CAAC;;EAEF;EACA;EACA;EACAJ,EAAE,CAAC,kGAAkG,EAAE,YAAM;IAC5G,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACxEH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACK,cAAc,CAAC,CAACnB,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;EACpE,CAAC,CAAC;AACH,CAAC,CAAC;AAEFL,QAAQ,CAAC,4BAA4B,EAAE,YAAM;EAC5CC,EAAE,CAAC,+FAA+F,EAAE,YAAM;IACzG,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,mGAAmG,EAAE,YAAM;IAC7G,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC;MAAEwC,kBAAkB,EAAE;IAAI,CAAC,CAAC;IAC5D9B,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,yEAAyE,EAAE,YAAM;IACnF,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,6EAA6E,EAAE,YAAM;IACvF,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC;MAAEwC,kBAAkB,EAAE;IAAI,CAAC,CAAC;IAC5D9B,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,mFAAmF,EAAE,YAAM;IAC7F,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IACjCU,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,MAAM,CAAC;IACvBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;EACrD,CAAC,CAAC;EAEFJ,EAAE,CAAC,8EAA8E,EAAE,YAAM;IACxF,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,oFAAoF,EAAE,YAAM;IAC9F,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,kFAAkF,EAAE,YAAM;IAC5F,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,EAAE;MAAEwC,kBAAkB,EAAE;IAAI,CAAC,CAAC;IAClE9B,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,wFAAwF,EAAE,YAAM;IAClG,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,EAAE;MAAEwC,kBAAkB,EAAE;IAAI,CAAC,CAAC;IAClE9B,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,yDAAyD,EAAE,YAAM;IACnE,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IACjCU,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACjDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;IACnDC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IACpDC,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC3DC,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC7D,CAAC,CAAC;EAEFJ,EAAE,CAAC,yFAAyF,EAAE,YAAM;IACnG,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,CAAC;IACjCU,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,EAAE,CAAC;IACnBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,IAAI,CAAC;IACrBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3BD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;IAClDJ,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC;IACpBD,MAAM,CAACI,SAAS,CAACsC,cAAc,CAAC,CAAC,CAAC,CAACxC,EAAE,CAACK,EAAE,CAACC,SAAS;EACnD,CAAC,CAAC;EAEFT,EAAE,CAAC,kCAAkC,EAAE,YAAM;IAC5C;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IAC1CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC5CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;IAC9CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC/CH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;IAChDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAClDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACnDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;IACpDH,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;EACtD,CAAC,CAAC;EAEFJ,EAAE,CAAC,qFAAqF,EAAE,YAAW;IACpG,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC;MAC/BwC,kBAAkB,EAAE;IACrB,CAAC,CAAC;IACF9B,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EAC3C,CAAC,CAAC;EAEFJ,EAAE,CAAC,kCAAkC,EAAE,YAAW;IACjD;IACA;IACA;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC9CC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC9CC,SAAS,CAACQ,KAAK,CAAC,CAAC;IACjBZ,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,KAAK,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAC/C,CAAC,CAAC;EAEFJ,EAAE,CAAC,sCAAsC,EAAE,YAAW;IACrD;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCU,MAAM,CAACI,SAAS,CAACH,KAAK,CAAC,cAAc,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;IACnEH,MAAM,CAACI,SAAS,CAACK,WAAW,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EAC5D,CAAC,CAAC;EAEFJ,EAAE,CAAC,iIAAiI,EAAE,YAAW;IAChJ;IACA;IACA;IACA,IAAMK,SAAS,GAAG,IAAId,SAAS,CAAC,IAAI,CAAC;IACrCc,SAAS,CAACH,KAAK,CAAC,YAAY,CAAC;IAC7BD,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAACyB,GAAG,CAACzC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACxDH,MAAM,CAACI,SAAS,CAACY,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAEpD;IACA,IAAMkC,UAAU,GAAG,IAAI/C,SAAS,CAAC,IAAI,CAAC;IACtC+C,UAAU,CAACpC,KAAK,CAAC,YAAY,CAAC;IAC9BD,MAAM,CAACqC,UAAU,CAACrB,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAErD;IACA;IACA,IAAMyC,UAAU,GAAG,IAAItD,SAAS,CAAC,IAAI,CAAC;IACtCsD,UAAU,CAAC3C,KAAK,CAAC,YAAY,CAAC;IAC9BD,MAAM,CAAC4C,UAAU,CAAC5B,SAAS,CAAC,CAAC,CAACE,OAAO,CAAC,CAAChB,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACtD,CAAC,CAAC;AACH,CAAC,CAAC;AAEF,SAASf,IAAIA,CAACyD,SAAS,EAAE;EACxB,OAAAC,OAAA,CAAcD,SAAS;AACxB", "ignoreList": []}