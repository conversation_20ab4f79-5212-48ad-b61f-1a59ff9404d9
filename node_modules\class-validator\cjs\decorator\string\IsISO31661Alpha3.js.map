{"version": 3, "file": "IsISO31661Alpha3.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISO31661Alpha3.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sFAAuE;AAE1D,QAAA,mBAAmB,GAAG,kBAAkB,CAAC;AAEtD;;GAEG;AACH,SAAgB,gBAAgB,CAAC,KAAc;IAC7C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,0BAAyB,EAAC,KAAK,CAAC,CAAC;AACvE,CAAC;AAFD,4CAEC;AAED;;GAEG;AACH,SAAgB,gBAAgB,CAAC,iBAAqC;IACpE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,2BAAmB;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAC3D,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,gDAAgD,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,4CAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isISO31661Alpha3Validator from 'validator/lib/isISO31661Alpha3';\n\nexport const IS_ISO31661_ALPHA_3 = 'isISO31661Alpha3';\n\n/**\n * Check if the string is a valid [ISO 3166-1 alpha-3](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3) officially assigned country code.\n */\nexport function isISO31661Alpha3(value: unknown): boolean {\n  return typeof value === 'string' && isISO31661Alpha3Validator(value);\n}\n\n/**\n * Check if the string is a valid [ISO 3166-1 alpha-3](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3) officially assigned country code.\n */\nexport function IsISO31661Alpha3(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISO31661_ALPHA_3,\n      validator: {\n        validate: (value, args): boolean => isISO31661Alpha3(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid ISO31661 Alpha3 code',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}