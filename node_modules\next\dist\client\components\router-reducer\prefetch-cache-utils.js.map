{"version": 3, "sources": ["../../../../src/client/components/router-reducer/prefetch-cache-utils.ts"], "names": ["createPrefetchCacheEntryForInitialLoad", "getOrCreatePrefetchCacheEntry", "prune<PERSON><PERSON><PERSON>tch<PERSON><PERSON>", "createPrefetchCacheKey", "url", "nextUrl", "pathnameFromUrl", "createHrefFromUrl", "tree", "buildId", "prefetchCache", "kind", "existingCacheEntry", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interceptionData", "get", "prefetchCache<PERSON>ey", "prefetchData", "status", "getPrefetchEntryCacheStatus", "switchedToFullPrefetch", "PrefetchKind", "FULL", "createLazyPrefetchEntry", "TEMPORARY", "process", "env", "NODE_ENV", "AUTO", "prefixExistingPrefetchCacheEntry", "existingCacheKey", "newCache<PERSON>ey", "set", "delete", "data", "intercept", "prefetchEntry", "treeAtTimeOfPrefetch", "Promise", "resolve", "prefetchTime", "Date", "now", "lastUsedTime", "key", "PrefetchCacheEntryStatus", "fresh", "prefetchQueue", "enqueue", "fetchServerResponse", "then", "prefetchResponse", "intercepted", "href", "prefetchCacheEntry", "expired", "DYNAMIC_STALETIME_MS", "Number", "__NEXT_CLIENT_ROUTER_DYNAMIC_STALETIME", "STATIC_STALETIME_MS", "__NEXT_CLIENT_ROUTER_STATIC_STALETIME", "reusable", "stale"], "mappings": ";;;;;;;;;;;;;;;;IAmJgBA,sCAAsC;eAAtCA;;IA5GAC,6BAA6B;eAA7BA;;IAiMAC,kBAAkB;eAAlBA;;;mCAxOkB;qCAI3B;oCAMA;iCACuB;AAE9B;;;;;;CAMC,GACD,SAASC,uBAAuBC,GAAQ,EAAEC,OAAuB;IAC/D,MAAMC,kBAAkBC,IAAAA,oCAAiB,EACvCH,KACA,uFAAuF;IACvF;IAGF,+FAA+F;IAC/F,IAAIC,SAAS;QACX,OAAO,AAAGA,UAAQ,MAAGC;IACvB;IAEA,OAAOA;AACT;AAMO,SAASL,8BAA8B,KAa7C;IAb6C,IAAA,EAC5CG,GAAG,EACHC,OAAO,EACPG,IAAI,EACJC,OAAO,EACPC,aAAa,EACbC,IAAI,EAOL,GAb6C;IAc5C,IAAIC,qBAAqDC;IACzD,8EAA8E;IAC9E,kJAAkJ;IAClJ,iIAAiI;IACjI,MAAMC,uBAAuBX,uBAAuBC,KAAKC;IACzD,MAAMU,mBAAmBL,cAAcM,GAAG,CAACF;IAE3C,IAAIC,kBAAkB;QACpBH,qBAAqBG;IACvB,OAAO;QACL,2GAA2G;QAC3G,MAAME,mBAAmBd,uBAAuBC;QAChD,MAAMc,eAAeR,cAAcM,GAAG,CAACC;QACvC,IAAIC,cAAc;YAChBN,qBAAqBM;QACvB;IACF;IAEA,IAAIN,oBAAoB;QACtB,0DAA0D;QAC1DA,mBAAmBO,MAAM,GAAGC,4BAA4BR;QAExD,+DAA+D;QAC/D,qHAAqH;QACrH,MAAMS,yBACJT,mBAAmBD,IAAI,KAAKW,gCAAY,CAACC,IAAI,IAC7CZ,SAASW,gCAAY,CAACC,IAAI;QAE5B,IAAIF,wBAAwB;YAC1B,OAAOG,wBAAwB;gBAC7BhB;gBACAJ;gBACAK;gBACAJ;gBACAK;gBACA,8EAA8E;gBAC9E,2FAA2F;gBAC3F,kEAAkE;gBAClEC,MAAMA,eAAAA,OAAQW,gCAAY,CAACG,SAAS;YACtC;QACF;QAEA,uHAAuH;QACvH,4IAA4I;QAC5I,IAAId,QAAQC,mBAAmBD,IAAI,KAAKW,gCAAY,CAACG,SAAS,EAAE;YAC9Db,mBAAmBD,IAAI,GAAGA;QAC5B;QAEA,qFAAqF;QACrF,OAAOC;IACT;IAEA,kDAAkD;IAClD,OAAOY,wBAAwB;QAC7BhB;QACAJ;QACAK;QACAJ;QACAK;QACAC,MACEA,QACA,8EAA8E;QAC7Ee,CAAAA,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACtBN,gCAAY,CAACO,IAAI,GACjBP,gCAAY,CAACG,SAAS,AAAD;IAC7B;AACF;AAEA;;;CAGC,GACD,SAASK,iCAAiC,KAMzC;IANyC,IAAA,EACxC1B,GAAG,EACHC,OAAO,EACPK,aAAa,EAGd,GANyC;IAOxC,MAAMqB,mBAAmB5B,uBAAuBC;IAChD,MAAMQ,qBAAqBF,cAAcM,GAAG,CAACe;IAC7C,IAAI,CAACnB,oBAAoB;QACvB,yCAAyC;QACzC;IACF;IAEA,MAAMoB,cAAc7B,uBAAuBC,KAAKC;IAChDK,cAAcuB,GAAG,CAACD,aAAapB;IAC/BF,cAAcwB,MAAM,CAACH;AACvB;AAKO,SAAS/B,uCAAuC,KAWtD;IAXsD,IAAA,EACrDK,OAAO,EACPG,IAAI,EACJE,aAAa,EACbN,GAAG,EACHO,IAAI,EACJwB,IAAI,EAKL,GAXsD;IAYrD,MAAM,OAAOC,UAAU,GAAGD;IAC1B,qGAAqG;IACrG,MAAMlB,mBAAmBmB,YACrBjC,uBAAuBC,KAAKC,WAC5BF,uBAAuBC;IAE3B,MAAMiC,gBAAgB;QACpBC,sBAAsB9B;QACtB2B,MAAMI,QAAQC,OAAO,CAACL;QACtBxB;QACA8B,cAAcC,KAAKC,GAAG;QACtBC,cAAcF,KAAKC,GAAG;QACtBE,KAAK5B;QACLE,QAAQ2B,4CAAwB,CAACC,KAAK;IACxC;IAEArC,cAAcuB,GAAG,CAAChB,kBAAkBoB;IAEpC,OAAOA;AACT;AAEA;;CAEC,GACD,SAASb,wBAAwB,KAahC;IAbgC,IAAA,EAC/BpB,GAAG,EACHO,IAAI,EACJH,IAAI,EACJH,OAAO,EACPI,OAAO,EACPC,aAAa,EAOd,GAbgC;IAc/B,MAAMO,mBAAmBd,uBAAuBC;IAEhD,uEAAuE;IACvE,6FAA6F;IAC7F,MAAM+B,OAAOa,8BAAa,CAACC,OAAO,CAAC,IACjCC,IAAAA,wCAAmB,EAAC9C,KAAKI,MAAMH,SAASI,SAASE,MAAMwC,IAAI,CACzD,CAACC;YACC,+FAA+F;YAC/F,wDAAwD;YACxD,kEAAkE;YAClE,MAAM,OAAOC,YAAY,GAAGD;YAC5B,IAAIC,aAAa;gBACfvB,iCAAiC;oBAAE1B;oBAAKC;oBAASK;gBAAc;YACjE;YAEA,OAAO0C;QACT;IAIJ,MAAMf,gBAAgB;QACpBC,sBAAsB9B;QACtB2B;QACAxB;QACA8B,cAAcC,KAAKC,GAAG;QACtBC,cAAc;QACdC,KAAK5B;QACLE,QAAQ2B,4CAAwB,CAACC,KAAK;IACxC;IAEArC,cAAcuB,GAAG,CAAChB,kBAAkBoB;IAEpC,OAAOA;AACT;AAEO,SAASnC,mBACdQ,aAAoD;IAEpD,KAAK,MAAM,CAAC4C,MAAMC,mBAAmB,IAAI7C,cAAe;QACtD,IACEU,4BAA4BmC,wBAC5BT,4CAAwB,CAACU,OAAO,EAChC;YACA9C,cAAcwB,MAAM,CAACoB;QACvB;IACF;AACF;AAEA,8FAA8F;AAC9F,2DAA2D;AAC3D,MAAMG,uBACJC,OAAOhC,QAAQC,GAAG,CAACgC,sCAAsC,IAAI;AAE/D,MAAMC,sBACJF,OAAOhC,QAAQC,GAAG,CAACkC,qCAAqC,IAAI;AAE9D,SAASzC,4BAA4B,KAIhB;IAJgB,IAAA,EACnCT,IAAI,EACJ8B,YAAY,EACZG,YAAY,EACO,GAJgB;IAKnC,gFAAgF;IAChF,IAAIF,KAAKC,GAAG,KAAK,AAACC,CAAAA,uBAAAA,eAAgBH,YAAW,IAAKgB,sBAAsB;QACtE,OAAOb,eACHE,4CAAwB,CAACgB,QAAQ,GACjChB,4CAAwB,CAACC,KAAK;IACpC;IAEA,sGAAsG;IACtG,4EAA4E;IAC5E,sDAAsD;IACtD,IAAIpC,SAAS,QAAQ;QACnB,IAAI+B,KAAKC,GAAG,KAAKF,eAAemB,qBAAqB;YACnD,OAAOd,4CAAwB,CAACiB,KAAK;QACvC;IACF;IAEA,iGAAiG;IACjG,IAAIpD,SAAS,QAAQ;QACnB,IAAI+B,KAAKC,GAAG,KAAKF,eAAemB,qBAAqB;YACnD,OAAOd,4CAAwB,CAACgB,QAAQ;QAC1C;IACF;IAEA,OAAOhB,4CAAwB,CAACU,OAAO;AACzC"}