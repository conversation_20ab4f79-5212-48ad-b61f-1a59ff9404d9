{"version": 3, "file": "findPhoneNumbers.js", "names": ["_findPhoneNumbers", "searchPhoneNumbers", "_searchPhoneNumbers", "normalizeArguments", "findPhoneNumbers", "_normalizeArguments", "arguments", "text", "options", "metadata", "_normalizeArguments2"], "sources": ["../../source/legacy/findPhoneNumbers.js"], "sourcesContent": ["// This is a legacy function.\r\n// Use `findNumbers()` instead.\r\n\r\nimport _findPhoneNumbers, { searchPhoneNumbers as _searchPhoneNumbers } from './findPhoneNumbersInitialImplementation.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _findPhoneNumbers(text, options, metadata)\r\n}\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport function searchPhoneNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _searchPhoneNumbers(text, options, metadata)\r\n}"], "mappings": "AAAA;AACA;;AAEA,OAAOA,iBAAiB,IAAIC,kBAAkB,IAAIC,mBAAmB,QAAQ,4CAA4C;AACzH,OAAOC,kBAAkB,MAAM,0BAA0B;AAEzD,eAAe,SAASC,gBAAgBA,CAAA,EACxC;EACC,IAAAC,mBAAA,GAAoCF,kBAAkB,CAACG,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC/B,OAAOT,iBAAiB,CAACO,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAClD;;AAEA;AACA;AACA;AACA,OAAO,SAASR,kBAAkBA,CAAA,EAClC;EACC,IAAAS,oBAAA,GAAoCP,kBAAkB,CAACG,SAAS,CAAC;IAAzDC,IAAI,GAAAG,oBAAA,CAAJH,IAAI;IAAEC,OAAO,GAAAE,oBAAA,CAAPF,OAAO;IAAEC,QAAQ,GAAAC,oBAAA,CAARD,QAAQ;EAC/B,OAAOP,mBAAmB,CAACK,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACpD", "ignoreList": []}