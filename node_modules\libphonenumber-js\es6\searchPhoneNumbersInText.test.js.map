{"version": 3, "file": "searchPhoneNumbersInText.test.js", "names": ["searchPhoneNumbersInText", "metadata", "type", "describe", "it", "NUMBERS", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "number", "value", "expect", "to", "equal", "shift", "_iterator2", "_step2", "expectedNumbers", "country", "nationalNumber", "startsAt", "endsAt", "_iterator3", "_step3", "expected", "length"], "sources": ["../source/searchPhoneNumbersInText.test.js"], "sourcesContent": ["import searchPhoneNumbersInText from './searchPhoneNumbersInText.js'\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\ndescribe('searchPhoneNumbersInText', () => {\r\n\tit('should find phone numbers (with default country)', () => {\r\n\t\tconst NUMBERS = ['+78005553535', '+12133734253']\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\texpect(number.number.number).to.equal(NUMBERS[0])\r\n\t\t\tNUMBERS.shift()\r\n\t\t}\r\n\t})\r\n\r\n\tit('should find phone numbers', () => {\r\n\t\tconst NUMBERS = ['+78005553535', '+12133734253']\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', metadata)) {\r\n\t\t\texpect(number.number.number).to.equal(NUMBERS[0])\r\n\t\t\tNUMBERS.shift()\r\n\t\t}\r\n\t})\r\n\r\n\tit('should find phone numbers in text', () => {\r\n\t\tconst expectedNumbers = [{\r\n\t\t\tcountry: 'RU',\r\n\t\t\tnationalNumber: '8005553535',\r\n\t\t\tstartsAt: 14,\r\n\t\t\tendsAt: 32\r\n\t\t}, {\r\n\t\t\tcountry: 'US',\r\n\t\t\tnationalNumber: '2133734253',\r\n\t\t\tstartsAt: 41,\r\n\t\t\tendsAt: 55\r\n\t\t}]\r\n\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\tconst expected = expectedNumbers.shift()\r\n\t\t\texpect(number.startsAt).to.equal(expected.startsAt)\r\n\t\t\texpect(number.endsAt).to.equal(expected.endsAt)\r\n\t\t\texpect(number.number.nationalNumber).to.equal(expected.nationalNumber)\r\n\t\t\texpect(number.number.country).to.equal(expected.country)\r\n\t\t}\r\n\r\n\t\texpect(expectedNumbers.length).to.equal(0)\r\n\t})\r\n})"], "mappings": ";;;AAAA,OAAOA,wBAAwB,MAAM,+BAA+B;AACpE,OAAOC,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAE/DC,QAAQ,CAAC,0BAA0B,EAAE,YAAM;EAC1CC,EAAE,CAAC,kDAAkD,EAAE,YAAM;IAC5D,IAAMC,OAAO,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;IAChD,SAAAC,SAAA,GAAAC,+BAAA,CAAqBP,wBAAwB,CAAC,qFAAqF,EAAE,IAAI,EAAEC,QAAQ,CAAC,GAAAO,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAE;MAAA,IAA3IC,MAAM,GAAAF,KAAA,CAAAG,KAAA;MAChBC,MAAM,CAACF,MAAM,CAACA,MAAM,CAACA,MAAM,CAAC,CAACG,EAAE,CAACC,KAAK,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC;MACjDA,OAAO,CAACU,KAAK,CAAC,CAAC;IAChB;EACD,CAAC,CAAC;EAEFX,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC,IAAMC,OAAO,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;IAChD,SAAAW,UAAA,GAAAT,+BAAA,CAAqBP,wBAAwB,CAAC,qFAAqF,EAAEC,QAAQ,CAAC,GAAAgB,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAAP,IAAA,GAAE;MAAA,IAArIC,MAAM,GAAAO,MAAA,CAAAN,KAAA;MAChBC,MAAM,CAACF,MAAM,CAACA,MAAM,CAACA,MAAM,CAAC,CAACG,EAAE,CAACC,KAAK,CAACT,OAAO,CAAC,CAAC,CAAC,CAAC;MACjDA,OAAO,CAACU,KAAK,CAAC,CAAC;IAChB;EACD,CAAC,CAAC;EAEFX,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7C,IAAMc,eAAe,GAAG,CAAC;MACxBC,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,YAAY;MAC5BC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;IACT,CAAC,EAAE;MACFH,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,YAAY;MAC5BC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;IACT,CAAC,CAAC;IAEF,SAAAC,UAAA,GAAAhB,+BAAA,CAAqBP,wBAAwB,CAAC,qFAAqF,EAAE,IAAI,EAAEC,QAAQ,CAAC,GAAAuB,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAAd,IAAA,GAAE;MAAA,IAA3IC,MAAM,GAAAc,MAAA,CAAAb,KAAA;MAChB,IAAMc,QAAQ,GAAGP,eAAe,CAACH,KAAK,CAAC,CAAC;MACxCH,MAAM,CAACF,MAAM,CAACW,QAAQ,CAAC,CAACR,EAAE,CAACC,KAAK,CAACW,QAAQ,CAACJ,QAAQ,CAAC;MACnDT,MAAM,CAACF,MAAM,CAACY,MAAM,CAAC,CAACT,EAAE,CAACC,KAAK,CAACW,QAAQ,CAACH,MAAM,CAAC;MAC/CV,MAAM,CAACF,MAAM,CAACA,MAAM,CAACU,cAAc,CAAC,CAACP,EAAE,CAACC,KAAK,CAACW,QAAQ,CAACL,cAAc,CAAC;MACtER,MAAM,CAACF,MAAM,CAACA,MAAM,CAACS,OAAO,CAAC,CAACN,EAAE,CAACC,KAAK,CAACW,QAAQ,CAACN,OAAO,CAAC;IACzD;IAEAP,MAAM,CAACM,eAAe,CAACQ,MAAM,CAAC,CAACb,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}