{"version": 3, "file": "constants.js", "names": ["MIN_LENGTH_FOR_NSN", "exports", "MAX_LENGTH_FOR_NSN", "MAX_LENGTH_COUNTRY_CODE", "VALID_DIGITS", "DASHES", "SLASHES", "DOTS", "WHITESPACE", "BRACKETS", "TILDES", "VALID_PUNCTUATION", "concat", "PLUS_CHARS"], "sources": ["../source/constants.js"], "sourcesContent": ["// The minimum length of the national significant number.\r\nexport const MIN_LENGTH_FOR_NSN = 2\r\n\r\n// The ITU says the maximum length should be 15,\r\n// but one can find longer numbers in Germany.\r\nexport const MAX_LENGTH_FOR_NSN = 17\r\n\r\n// The maximum length of the country calling code.\r\nexport const MAX_LENGTH_COUNTRY_CODE = 3\r\n\r\n// Digits accepted in phone numbers\r\n// (ascii, fullwidth, arabic-indic, and eastern arabic digits).\r\nexport const VALID_DIGITS = '0-9\\uFF10-\\uFF19\\u0660-\\u0669\\u06F0-\\u06F9'\r\n\r\n// `DASHES` will be right after the opening square bracket of the \"character class\"\r\nconst DASHES = '-\\u2010-\\u2015\\u2212\\u30FC\\uFF0D'\r\nconst SLASHES = '\\uFF0F/'\r\nconst DOTS = '\\uFF0E.'\r\nexport const WHITESPACE = ' \\u00A0\\u00AD\\u200B\\u2060\\u3000'\r\nconst BRACKETS = '()\\uFF08\\uFF09\\uFF3B\\uFF3D\\\\[\\\\]'\r\n// export const OPENING_BRACKETS = '(\\uFF08\\uFF3B\\\\\\['\r\nconst TILDES = '~\\u2053\\u223C\\uFF5E'\r\n\r\n// Regular expression of acceptable punctuation found in phone numbers. This\r\n// excludes punctuation found as a leading character only. This consists of dash\r\n// characters, white space characters, full stops, slashes, square brackets,\r\n// parentheses and tildes. Full-width variants are also present.\r\nexport const VALID_PUNCTUATION = `${DASHES}${SLASHES}${DOTS}${WHITESPACE}${BRACKETS}${TILDES}`\r\n\r\nexport const PLUS_CHARS = '+\\uFF0B'\r\n// const LEADING_PLUS_CHARS_PATTERN = new RegExp('^[' + PLUS_CHARS + ']+')"], "mappings": ";;;;;;AAAA;AACO,IAAMA,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAG,CAAC;;AAEnC;AACA;AACO,IAAME,kBAAkB,GAAAD,OAAA,CAAAC,kBAAA,GAAG,EAAE;;AAEpC;AACO,IAAMC,uBAAuB,GAAAF,OAAA,CAAAE,uBAAA,GAAG,CAAC;;AAExC;AACA;AACO,IAAMC,YAAY,GAAAH,OAAA,CAAAG,YAAA,GAAG,4CAA4C;;AAExE;AACA,IAAMC,MAAM,GAAG,kCAAkC;AACjD,IAAMC,OAAO,GAAG,SAAS;AACzB,IAAMC,IAAI,GAAG,SAAS;AACf,IAAMC,UAAU,GAAAP,OAAA,CAAAO,UAAA,GAAG,6BAAiC;AAC3D,IAAMC,QAAQ,GAAG,kCAAkC;AACnD;AACA,IAAMC,MAAM,GAAG,qBAAqB;;AAEpC;AACA;AACA;AACA;AACO,IAAMC,iBAAiB,GAAAV,OAAA,CAAAU,iBAAA,MAAAC,MAAA,CAAMP,MAAM,EAAAO,MAAA,CAAGN,OAAO,EAAAM,MAAA,CAAGL,IAAI,EAAAK,MAAA,CAAGJ,UAAU,EAAAI,MAAA,CAAGH,QAAQ,EAAAG,MAAA,CAAGF,MAAM,CAAE;AAEvF,IAAMG,UAAU,GAAAZ,OAAA,CAAAY,UAAA,GAAG,SAAS;AACnC", "ignoreList": []}