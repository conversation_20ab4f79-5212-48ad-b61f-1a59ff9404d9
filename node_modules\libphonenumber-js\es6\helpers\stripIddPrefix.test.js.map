{"version": 3, "file": "stripIddPrefix.test.js", "names": ["stripIddPrefix", "metadata", "type", "describe", "it", "expect", "to", "equal", "be", "undefined"], "sources": ["../../source/helpers/stripIddPrefix.test.js"], "sourcesContent": ["import stripIddPrefix from './stripIddPrefix.js'\r\n\r\nimport metadata from '../../metadata.min.json' with { type: 'json' }\r\n\r\ndescribe('stripIddPrefix', () => {\r\n\tit('should strip a valid IDD prefix', () => {\r\n\t\texpect(stripIddPrefix('01178005553535', 'US', '1', metadata)).to.equal('78005553535')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (no country calling code)', () => {\r\n\t\texpect(stripIddPrefix('011', 'US', '1', metadata)).to.equal('')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (valid country calling code)', () => {\r\n\t\texpect(stripIddPrefix('0117', 'US', '1', metadata)).to.equal('7')\r\n\t})\r\n\r\n\tit('should strip a valid IDD prefix (not a valid country calling code)', () => {\r\n\t\texpect(stripIddPrefix('0110', 'US', '1', metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAEhD,OAAOC,QAAQ,MAAM,yBAAyB,QAAQC,IAAI,EAAE,MAAM;AAElEC,QAAQ,CAAC,gBAAgB,EAAE,YAAM;EAChCC,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3CC,MAAM,CAACL,cAAc,CAAC,gBAAgB,EAAE,IAAI,EAAE,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,KAAK,CAAC,aAAa,CAAC;EACtF,CAAC,CAAC;EAEFH,EAAE,CAAC,2DAA2D,EAAE,YAAM;IACrEC,MAAM,CAACL,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;EAChE,CAAC,CAAC;EAEFH,EAAE,CAAC,8DAA8D,EAAE,YAAM;IACxEC,MAAM,CAACL,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;EAClE,CAAC,CAAC;EAEFH,EAAE,CAAC,oEAAoE,EAAE,YAAM;IAC9EC,MAAM,CAACL,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,EAAEC,QAAQ,CAAC,CAAC,CAACK,EAAE,CAACE,EAAE,CAACC,SAAS;EACpE,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}