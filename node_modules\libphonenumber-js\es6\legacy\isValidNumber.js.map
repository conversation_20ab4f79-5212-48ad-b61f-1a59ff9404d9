{"version": 3, "file": "isValidNumber.js", "names": ["_isValidNumber", "normalizeArguments", "isValidNumber", "_normalizeArguments", "arguments", "input", "options", "metadata", "phone"], "sources": ["../../source/legacy/isValidNumber.js"], "sourcesContent": ["import _isValidNumber from '../isValid.js'\r\nimport { normalizeArguments } from './getNumberType.js'\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function isValidNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isValidNumber(input, options, metadata)\r\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,eAAe;AAC1C,SAASC,kBAAkB,QAAQ,oBAAoB;;AAEvD;AACA,eAAe,SAASC,aAAaA,CAAA,EAAG;EACvC,IAAAC,mBAAA,GAAqCF,kBAAkB,CAACG,SAAS,CAAC;IAA1DC,KAAK,GAAAF,mBAAA,CAALE,KAAK;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAChC;EACA,IAAI,CAACF,KAAK,CAACG,KAAK,EAAE;IACjB,OAAO,KAAK;EACb;EACA,OAAOR,cAAc,CAACK,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAChD", "ignoreList": []}