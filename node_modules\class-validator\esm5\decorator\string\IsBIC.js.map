{"version": 3, "file": "IsBIC.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBIC.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,cAAc,MAAM,qBAAqB,CAAC;AAEjD,MAAM,CAAC,IAAM,MAAM,GAAG,OAAO,CAAC;AAE9B;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAC,iBAAqC;IACzD,OAAO,UAAU,CACf;QACE,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,KAAK,CAAC,KAAK,CAAC,EAAZ,CAAY;YAChD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,uCAAuC,EAApD,CAAoD,EAClE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBICValidator from 'validator/lib/isBIC';\n\nexport const IS_BIC = 'isBIC';\n\n/**\n * Check if a string is a BIC (Bank Identification Code) or SWIFT code.\n * If given value is not a string, then it returns false.\n */\nexport function isBIC(value: unknown): boolean {\n  return typeof value === 'string' && isBICValidator(value);\n}\n\n/**\n * Check if a string is a BIC (Bank Identification Code) or SWIFT code.\n * If given value is not a string, then it returns false.\n */\nexport function IsBIC(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BIC,\n      validator: {\n        validate: (value, args): boolean => isBIC(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a BIC or SWIFT code',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}