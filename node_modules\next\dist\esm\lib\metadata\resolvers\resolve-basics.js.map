{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-basics.ts"], "names": ["resolveAsArrayOrUndefined", "resolveAbsoluteUrlWithPathname", "resolveAlternateUrl", "url", "metadataBase", "metadataContext", "URL", "pathname", "resolveThemeColor", "themeColor", "themeColorDescriptors", "for<PERSON>ach", "descriptor", "push", "color", "media", "resolveUrlValuesOfObject", "obj", "result", "key", "value", "Object", "entries", "item", "index", "title", "resolveCanonicalUrl", "urlOrDescriptor", "resolveAlternates", "alternates", "context", "canonical", "languages", "types", "robotsKeys", "resolveRobotsValue", "robots", "values", "follow", "join", "resolveRobots", "basic", "googleBot", "VerificationKeys", "resolveVerification", "verification", "res", "other", "otherKey", "otherValue", "resolveAppleWebApp", "appWebApp", "capable", "startupImages", "startupImage", "map", "statusBarStyle", "resolveAppLinks", "appLinks", "resolveItunes", "itunes", "appId", "appArgument", "undefined", "resolveFacebook", "facebook", "admins"], "mappings": "AAeA,SAASA,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,8BAA8B,QAAQ,gBAAe;AAE9D,SAASC,oBACPC,GAAiB,EACjBC,YAAwB,EACxBC,eAAgC;IAEhC,0CAA0C;IAC1C,8DAA8D;IAC9D,IAAIF,eAAeG,KAAK;QACtBH,MAAM,IAAIG,IAAID,gBAAgBE,QAAQ,EAAEJ;IAC1C;IACA,OAAOF,+BAA+BE,KAAKC,cAAcC;AAC3D;AAEA,OAAO,MAAMG,oBAA2D,CACtEC;QAKAT;IAHA,IAAI,CAACS,YAAY,OAAO;IACxB,MAAMC,wBAAgD,EAAE;KAExDV,6BAAAA,0BAA0BS,gCAA1BT,2BAAuCW,OAAO,CAAC,CAACC;QAC9C,IAAI,OAAOA,eAAe,UACxBF,sBAAsBG,IAAI,CAAC;YAAEC,OAAOF;QAAW;aAC5C,IAAI,OAAOA,eAAe,UAC7BF,sBAAsBG,IAAI,CAAC;YACzBC,OAAOF,WAAWE,KAAK;YACvBC,OAAOH,WAAWG,KAAK;QACzB;IACJ;IAEA,OAAOL;AACT,EAAC;AAED,SAASM,yBACPC,GAMa,EACbb,YAA8C,EAC9CC,eAAgC;IAEhC,IAAI,CAACY,KAAK,OAAO;IAEjB,MAAMC,SAAoD,CAAC;IAC3D,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACL,KAAM;QAC9C,IAAI,OAAOG,UAAU,YAAYA,iBAAiBd,KAAK;YACrDY,MAAM,CAACC,IAAI,GAAG;gBACZ;oBACEhB,KAAKD,oBAAoBkB,OAAOhB,cAAcC;gBAChD;aACD;QACH,OAAO;YACLa,MAAM,CAACC,IAAI,GAAG,EAAE;YAChBC,yBAAAA,MAAOT,OAAO,CAAC,CAACY,MAAMC;gBACpB,MAAMrB,MAAMD,oBAAoBqB,KAAKpB,GAAG,EAAEC,cAAcC;gBACxDa,MAAM,CAACC,IAAI,CAACK,MAAM,GAAG;oBACnBrB;oBACAsB,OAAOF,KAAKE,KAAK;gBACnB;YACF;QACF;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,oBACPC,eAA0E,EAC1EvB,YAAwB,EACxBC,eAAgC;IAEhC,IAAI,CAACsB,iBAAiB,OAAO;IAE7B,MAAMxB,MACJ,OAAOwB,oBAAoB,YAAYA,2BAA2BrB,MAC9DqB,kBACAA,gBAAgBxB,GAAG;IAEzB,qEAAqE;IACrE,OAAO;QACLA,KAAKD,oBAAoBC,KAAKC,cAAcC;IAC9C;AACF;AAEA,OAAO,MAAMuB,oBAGT,CAACC,YAAYzB,cAAc0B;IAC7B,IAAI,CAACD,YAAY,OAAO;IAExB,MAAME,YAAYL,oBAChBG,WAAWE,SAAS,EACpB3B,cACA0B;IAEF,MAAME,YAAYhB,yBAChBa,WAAWG,SAAS,EACpB5B,cACA0B;IAEF,MAAMf,QAAQC,yBACZa,WAAWd,KAAK,EAChBX,cACA0B;IAEF,MAAMG,QAAQjB,yBACZa,WAAWI,KAAK,EAChB7B,cACA0B;IAGF,MAAMZ,SAAgC;QACpCa;QACAC;QACAjB;QACAkB;IACF;IAEA,OAAOf;AACT,EAAC;AAED,MAAMgB,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,qBAAoE,CACxEC;IAEA,IAAI,CAACA,QAAQ,OAAO;IACpB,IAAI,OAAOA,WAAW,UAAU,OAAOA;IAEvC,MAAMC,SAAmB,EAAE;IAE3B,IAAID,OAAOZ,KAAK,EAAEa,OAAOxB,IAAI,CAAC;SACzB,IAAI,OAAOuB,OAAOZ,KAAK,KAAK,WAAWa,OAAOxB,IAAI,CAAC;IAExD,IAAIuB,OAAOE,MAAM,EAAED,OAAOxB,IAAI,CAAC;SAC1B,IAAI,OAAOuB,OAAOE,MAAM,KAAK,WAAWD,OAAOxB,IAAI,CAAC;IAEzD,KAAK,MAAMM,OAAOe,WAAY;QAC5B,MAAMd,QAAQgB,MAAM,CAACjB,IAAI;QACzB,IAAI,OAAOC,UAAU,eAAeA,UAAU,OAAO;YACnDiB,OAAOxB,IAAI,CAAC,OAAOO,UAAU,YAAYD,MAAM,CAAC,EAAEA,IAAI,CAAC,EAAEC,MAAM,CAAC;QAClE;IACF;IAEA,OAAOiB,OAAOE,IAAI,CAAC;AACrB;AAEA,OAAO,MAAMC,gBAAyC,CAACJ;IACrD,IAAI,CAACA,QAAQ,OAAO;IACpB,OAAO;QACLK,OAAON,mBAAmBC;QAC1BM,WACE,OAAON,WAAW,WAAWD,mBAAmBC,OAAOM,SAAS,IAAI;IACxE;AACF,EAAC;AAED,MAAMC,mBAAmB;IAAC;IAAU;IAAS;IAAU;IAAM;CAAQ;AACrE,OAAO,MAAMC,sBAAqD,CAChEC;IAEA,IAAI,CAACA,cAAc,OAAO;IAC1B,MAAMC,MAA4B,CAAC;IAEnC,KAAK,MAAM3B,OAAOwB,iBAAkB;QAClC,MAAMvB,QAAQyB,YAAY,CAAC1B,IAAI;QAC/B,IAAIC,OAAO;YACT,IAAID,QAAQ,SAAS;gBACnB2B,IAAIC,KAAK,GAAG,CAAC;gBACb,IAAK,MAAMC,YAAYH,aAAaE,KAAK,CAAE;oBACzC,MAAME,aAAajD,0BACjB6C,aAAaE,KAAK,CAACC,SAAS;oBAE9B,IAAIC,YAAYH,IAAIC,KAAK,CAACC,SAAS,GAAGC;gBACxC;YACF,OAAOH,GAAG,CAAC3B,IAAI,GAAGnB,0BAA0BoB;QAC9C;IACF;IACA,OAAO0B;AACT,EAAC;AAED,OAAO,MAAMI,qBAAmD,CAACC;QAS3DnD;IARJ,IAAI,CAACmD,WAAW,OAAO;IACvB,IAAIA,cAAc,MAAM;QACtB,OAAO;YACLC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBF,UAAUG,YAAY,IACxCtD,6BAAAA,0BAA0BmD,UAAUG,YAAY,sBAAhDtD,2BAAmDuD,GAAG,CAAC,CAAChC,OACtD,OAAOA,SAAS,WAAW;YAAEpB,KAAKoB;QAAK,IAAIA,QAE7C;IAEJ,OAAO;QACL6B,SAAS,aAAaD,YAAY,CAAC,CAACA,UAAUC,OAAO,GAAG;QACxD3B,OAAO0B,UAAU1B,KAAK,IAAI;QAC1B6B,cAAcD;QACdG,gBAAgBL,UAAUK,cAAc,IAAI;IAC9C;AACF,EAAC;AAED,OAAO,MAAMC,kBAA6C,CAACC;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,IAAK,MAAMvC,OAAOuC,SAAU;QAC1B,iCAAiC;QACjCA,QAAQ,CAACvC,IAAI,GAAGnB,0BAA0B0D,QAAQ,CAACvC,IAAI;IACzD;IACA,OAAOuC;AACT,EAAC;AAED,OAAO,MAAMC,gBAGT,CAACC,QAAQxD,cAAc0B;IACzB,IAAI,CAAC8B,QAAQ,OAAO;IACpB,OAAO;QACLC,OAAOD,OAAOC,KAAK;QACnBC,aAAaF,OAAOE,WAAW,GAC3B5D,oBAAoB0D,OAAOE,WAAW,EAAE1D,cAAc0B,WACtDiC;IACN;AACF,EAAC;AAED,OAAO,MAAMC,kBAA6C,CAACC;IACzD,IAAI,CAACA,UAAU,OAAO;IACtB,OAAO;QACLJ,OAAOI,SAASJ,KAAK;QACrBK,QAAQlE,0BAA0BiE,SAASC,MAAM;IACnD;AACF,EAAC"}