{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Terminal/EditorLink.tsx"], "names": ["EditorLink", "file", "isSourceFile", "location", "open", "useOpenInEditor", "lineNumber", "line", "column", "div", "data-with-open-in-editor-link", "data-with-open-in-editor-link-source-file", "undefined", "data-with-open-in-editor-link-import-trace", "tabIndex", "role", "onClick", "title", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;;;gEAXE;iCACc;AAUzB,SAASA,WAAW,KAAiD;IAAjD,IAAA,EAAEC,IAAI,EAAEC,YAAY,EAAEC,QAAQ,EAAmB,GAAjD;QAGXA,gBACJA;IAHV,MAAMC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BJ;QACAK,YAAYH,CAAAA,iBAAAA,4BAAAA,SAAUI,IAAI,YAAdJ,iBAAkB;QAC9BK,QAAQL,CAAAA,mBAAAA,4BAAAA,SAAUK,MAAM,YAAhBL,mBAAoB;IAC9B;IAEA,qBACE,sBAACM;QACCC,+BAA6B;QAC7BC,6CACET,eAAe,OAAOU;QAExBC,8CACEX,eAAeU,YAAY;QAE7BE,UAAU;QACVC,MAAM;QACNC,SAASZ;QACTa,OAAO;;YAENhB;YACAE,WAAW,AAAC,MAAGA,SAASI,IAAI,GAAC,MAAGJ,SAASK,MAAM,GAAK;0BACrD,sBAACU;gBACCC,OAAM;gBACNC,SAAQ;gBACRC,MAAK;gBACLC,QAAO;gBACPC,aAAY;gBACZC,eAAc;gBACdC,gBAAe;;kCAEf,qBAACC;wBAAKC,GAAE;;kCACR,qBAACC;wBAASC,QAAO;;kCACjB,qBAACtB;wBAAKuB,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;wBAAKC,IAAG;;;;;;AAIzC"}