{"version": 3, "file": "getExampleNumber.test.js", "names": ["_examplesMobile", "_interopRequireDefault", "require", "_metadataMin", "_getExampleNumber", "e", "__esModule", "describe", "it", "phoneNumber", "getExampleNumber", "examples", "metadata", "expect", "nationalNumber", "to", "equal", "number", "countryCallingCode", "country", "be", "undefined"], "sources": ["../source/getExampleNumber.test.js"], "sourcesContent": ["import examples from '../examples.mobile.json' with { type: 'json' }\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\nimport getExampleNumber from './getExampleNumber.js'\r\n\r\ndescribe('getExampleNumber', () => {\r\n\tit('should get an example number', () => {\r\n\t\tconst phoneNumber = getExampleNumber('RU', examples, metadata)\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('9123456789')\r\n\t\texpect(phoneNumber.number).to.equal('+79123456789')\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('7')\r\n\t\texpect(phoneNumber.country).to.equal('RU')\r\n\t})\r\n\r\n\tit('should handle a non-existing country', () => {\r\n\t\texpect(getExampleNumber('XX', examples, metadata)).to.be.undefined\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,iBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAoD,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEpDE,QAAQ,CAAC,kBAAkB,EAAE,YAAM;EAClCC,EAAE,CAAC,8BAA8B,EAAE,YAAM;IACxC,IAAMC,WAAW,GAAG,IAAAC,4BAAgB,EAAC,IAAI,EAAEC,0BAAQ,EAAEC,uBAAQ,CAAC;IAC9DC,MAAM,CAACJ,WAAW,CAACK,cAAc,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACzDH,MAAM,CAACJ,WAAW,CAACQ,MAAM,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACnDH,MAAM,CAACJ,WAAW,CAACS,kBAAkB,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpDH,MAAM,CAACJ,WAAW,CAACU,OAAO,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAC3C,CAAC,CAAC;EAEFR,EAAE,CAAC,sCAAsC,EAAE,YAAM;IAChDK,MAAM,CAAC,IAAAH,4BAAgB,EAAC,IAAI,EAAEC,0BAAQ,EAAEC,uBAAQ,CAAC,CAAC,CAACG,EAAE,CAACK,EAAE,CAACC,SAAS;EACnE,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}