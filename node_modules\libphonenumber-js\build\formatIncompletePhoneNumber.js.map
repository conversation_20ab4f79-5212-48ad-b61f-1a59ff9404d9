{"version": 3, "file": "formatIncompletePhoneNumber.js", "names": ["_AsYouType", "_interopRequireDefault", "require", "e", "__esModule", "formatIncompletePhoneNumber", "value", "optionsOrDefaultCountry", "metadata", "undefined", "AsYouType", "input"], "sources": ["../source/formatIncompletePhoneNumber.js"], "sourcesContent": ["import AsYouType from './AsYouType.js'\r\n\r\n/**\r\n * Formats a (possibly incomplete) phone number.\r\n * The phone number can be either in E.164 format\r\n * or in a form of national number digits.\r\n * @param {string} value - A possibly incomplete phone number. Either in E.164 format or in a form of national number digits.\r\n * @param {string|object} [optionsOrDefaultCountry] - A two-letter (\"ISO 3166-1 alpha-2\") country code, or an object of shape `{ defaultCountry?: string, defaultCallingCode?: string }`.\r\n * @return {string} Formatted (possibly incomplete) phone number.\r\n */\r\nexport default function formatIncompletePhoneNumber(value, optionsOrDefaultCountry, metadata) {\r\n\tif (!metadata) {\r\n\t\tmetadata = optionsOrDefaultCountry\r\n\t\toptionsOrDefaultCountry = undefined\r\n\t}\r\n\treturn new AsYouType(optionsOrDefaultCountry, metadata).input(value)\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAsC,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASE,2BAA2BA,CAACC,KAAK,EAAEC,uBAAuB,EAAEC,QAAQ,EAAE;EAC7F,IAAI,CAACA,QAAQ,EAAE;IACdA,QAAQ,GAAGD,uBAAuB;IAClCA,uBAAuB,GAAGE,SAAS;EACpC;EACA,OAAO,IAAIC,qBAAS,CAACH,uBAAuB,EAAEC,QAAQ,CAAC,CAACG,KAAK,CAACL,KAAK,CAAC;AACrE", "ignoreList": []}