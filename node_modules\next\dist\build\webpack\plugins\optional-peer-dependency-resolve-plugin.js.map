{"version": 3, "sources": ["../../../../src/build/webpack/plugins/optional-peer-dependency-resolve-plugin.ts"], "names": ["OptionalPeerDependencyResolverPlugin", "pluginSymbol", "Symbol", "apply", "resolver", "target", "ensureH<PERSON>", "tapAsync", "request", "resolveContext", "callback", "stack", "delete", "Array", "from", "pop", "doResolve", "err", "result", "descriptionFileData", "peerDependenciesMeta", "isOptional", "optional", "path"], "mappings": ";;;;+BAIaA;;;eAAAA;;;AAFb,MAAMC,eAAeC,OAAO;AAErB,MAAMF;IACXG,MAAMC,QAAkB,EAAE;QACxB,MAAMC,SAASD,SAASE,UAAU,CAAC;QACnCD,OAAOE,QAAQ,CACb,wCACA,CAACC,SAASC,gBAAgBC;gBAMxB,mDAAmD;YACnDD;YANA,iEAAiE;YACjE,IAAI,AAACD,OAAe,CAACP,aAAa,EAAE;gBAClC,OAAOS;YACT;aAGAD,wBAAAA,eAAeE,KAAK,qBAApBF,sBAAsBG,MAAM,CAACC,MAAMC,IAAI,CAACL,eAAeE,KAAK,EAAEI,GAAG;YAEjEX,SAASY,SAAS,CAChBX,QACA,8DAA8D;YAC9D,iCAAiC;YACjC;gBAAE,GAAGG,OAAO;gBAAE,CAACP,aAAa,EAAE;YAAK,GACnC,MACAQ,gBACA,CAACQ,KAAKC;oBAGFV;gBAFF,IACE,CAACU,WACDV,4BAAAA,+BAAAA,QAASW,mBAAmB,qBAA5BX,6BAA8BY,oBAAoB,KAClDZ,QAAQA,OAAO,EACf;oBACA,MAAMY,uBAAuBZ,QAAQW,mBAAmB,CACrDC,oBAAoB;oBAEvB,MAAMC,aACJD,wBACAA,oBAAoB,CAACZ,QAAQA,OAAO,CAAC,IACrCY,oBAAoB,CAACZ,QAAQA,OAAO,CAAC,CAACc,QAAQ;oBAEhD,IAAID,YAAY;wBACd,OAAOX,SAAS,MAAM;4BACpBa,MAAM;wBACR;oBACF;gBACF;gBAEA,OAAOb,SAASO,KAAKC;YACvB;QAEJ;IAEJ;AACF"}