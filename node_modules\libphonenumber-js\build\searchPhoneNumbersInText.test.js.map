{"version": 3, "file": "searchPhoneNumbersInText.test.js", "names": ["_searchPhoneNumbersInText", "_interopRequireDefault", "require", "_metadataMin", "e", "__esModule", "_createForOfIteratorHelperLoose", "r", "t", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "o", "done", "value", "TypeError", "a", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "n", "describe", "it", "NUMBERS", "_iterator", "searchPhoneNumbersInText", "metadata", "_step", "number", "expect", "to", "equal", "shift", "_iterator2", "_step2", "expectedNumbers", "country", "nationalNumber", "startsAt", "endsAt", "_iterator3", "_step3", "expected"], "sources": ["../source/searchPhoneNumbersInText.test.js"], "sourcesContent": ["import searchPhoneNumbersInText from './searchPhoneNumbersInText.js'\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\ndescribe('searchPhoneNumbersInText', () => {\r\n\tit('should find phone numbers (with default country)', () => {\r\n\t\tconst NUMBERS = ['+78005553535', '+12133734253']\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\texpect(number.number.number).to.equal(NUMBERS[0])\r\n\t\t\tNUMBERS.shift()\r\n\t\t}\r\n\t})\r\n\r\n\tit('should find phone numbers', () => {\r\n\t\tconst NUMBERS = ['+78005553535', '+12133734253']\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', metadata)) {\r\n\t\t\texpect(number.number.number).to.equal(NUMBERS[0])\r\n\t\t\tNUMBERS.shift()\r\n\t\t}\r\n\t})\r\n\r\n\tit('should find phone numbers in text', () => {\r\n\t\tconst expectedNumbers = [{\r\n\t\t\tcountry: 'RU',\r\n\t\t\tnationalNumber: '8005553535',\r\n\t\t\tstartsAt: 14,\r\n\t\t\tendsAt: 32\r\n\t\t}, {\r\n\t\t\tcountry: 'US',\r\n\t\t\tnationalNumber: '2133734253',\r\n\t\t\tstartsAt: 41,\r\n\t\t\tendsAt: 55\r\n\t\t}]\r\n\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\tconst expected = expectedNumbers.shift()\r\n\t\t\texpect(number.startsAt).to.equal(expected.startsAt)\r\n\t\t\texpect(number.endsAt).to.equal(expected.endsAt)\r\n\t\t\texpect(number.number.nationalNumber).to.equal(expected.nationalNumber)\r\n\t\t\texpect(number.number.country).to.equal(expected.country)\r\n\t\t}\r\n\r\n\t\texpect(expectedNumbers.length).to.equal(0)\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,yBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,gCAAAC,CAAA,EAAAH,CAAA,QAAAI,CAAA,yBAAAC,MAAA,IAAAF,CAAA,CAAAE,MAAA,CAAAC,QAAA,KAAAH,CAAA,oBAAAC,CAAA,UAAAA,CAAA,GAAAA,CAAA,CAAAG,IAAA,CAAAJ,CAAA,GAAAK,IAAA,CAAAC,IAAA,CAAAL,CAAA,OAAAM,KAAA,CAAAC,OAAA,CAAAR,CAAA,MAAAC,CAAA,GAAAQ,2BAAA,CAAAT,CAAA,MAAAH,CAAA,IAAAG,CAAA,uBAAAA,CAAA,CAAAU,MAAA,IAAAT,CAAA,KAAAD,CAAA,GAAAC,CAAA,OAAAU,CAAA,kCAAAA,CAAA,IAAAX,CAAA,CAAAU,MAAA,KAAAE,IAAA,WAAAA,IAAA,MAAAC,KAAA,EAAAb,CAAA,CAAAW,CAAA,sBAAAG,SAAA;AAAA,SAAAL,4BAAAT,CAAA,EAAAe,CAAA,QAAAf,CAAA,2BAAAA,CAAA,SAAAgB,iBAAA,CAAAhB,CAAA,EAAAe,CAAA,OAAAd,CAAA,MAAAgB,QAAA,CAAAb,IAAA,CAAAJ,CAAA,EAAAkB,KAAA,6BAAAjB,CAAA,IAAAD,CAAA,CAAAmB,WAAA,KAAAlB,CAAA,GAAAD,CAAA,CAAAmB,WAAA,CAAAC,IAAA,aAAAnB,CAAA,cAAAA,CAAA,GAAAM,KAAA,CAAAc,IAAA,CAAArB,CAAA,oBAAAC,CAAA,+CAAAqB,IAAA,CAAArB,CAAA,IAAAe,iBAAA,CAAAhB,CAAA,EAAAe,CAAA;AAAA,SAAAC,kBAAAhB,CAAA,EAAAe,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAf,CAAA,CAAAU,MAAA,MAAAK,CAAA,GAAAf,CAAA,CAAAU,MAAA,YAAAb,CAAA,MAAA0B,CAAA,GAAAhB,KAAA,CAAAQ,CAAA,GAAAlB,CAAA,GAAAkB,CAAA,EAAAlB,CAAA,IAAA0B,CAAA,CAAA1B,CAAA,IAAAG,CAAA,CAAAH,CAAA,UAAA0B,CAAA;AAEjEC,QAAQ,CAAC,0BAA0B,EAAE,YAAM;EAC1CC,EAAE,CAAC,kDAAkD,EAAE,YAAM;IAC5D,IAAMC,OAAO,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;IAChD,SAAAC,SAAA,GAAA5B,+BAAA,CAAqB,IAAA6B,oCAAwB,EAAC,qFAAqF,EAAE,IAAI,EAAEC,uBAAQ,CAAC,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAf,IAAA,GAAE;MAAA,IAA3ImB,MAAM,GAAAD,KAAA,CAAAjB,KAAA;MAChBmB,MAAM,CAACD,MAAM,CAACA,MAAM,CAACA,MAAM,CAAC,CAACE,EAAE,CAACC,KAAK,CAACR,OAAO,CAAC,CAAC,CAAC,CAAC;MACjDA,OAAO,CAACS,KAAK,CAAC,CAAC;IAChB;EACD,CAAC,CAAC;EAEFV,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC,IAAMC,OAAO,GAAG,CAAC,cAAc,EAAE,cAAc,CAAC;IAChD,SAAAU,UAAA,GAAArC,+BAAA,CAAqB,IAAA6B,oCAAwB,EAAC,qFAAqF,EAAEC,uBAAQ,CAAC,GAAAQ,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAAxB,IAAA,GAAE;MAAA,IAArImB,MAAM,GAAAM,MAAA,CAAAxB,KAAA;MAChBmB,MAAM,CAACD,MAAM,CAACA,MAAM,CAACA,MAAM,CAAC,CAACE,EAAE,CAACC,KAAK,CAACR,OAAO,CAAC,CAAC,CAAC,CAAC;MACjDA,OAAO,CAACS,KAAK,CAAC,CAAC;IAChB;EACD,CAAC,CAAC;EAEFV,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7C,IAAMa,eAAe,GAAG,CAAC;MACxBC,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,YAAY;MAC5BC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;IACT,CAAC,EAAE;MACFH,OAAO,EAAE,IAAI;MACbC,cAAc,EAAE,YAAY;MAC5BC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;IACT,CAAC,CAAC;IAEF,SAAAC,UAAA,GAAA5C,+BAAA,CAAqB,IAAA6B,oCAAwB,EAAC,qFAAqF,EAAE,IAAI,EAAEC,uBAAQ,CAAC,GAAAe,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAA/B,IAAA,GAAE;MAAA,IAA3ImB,MAAM,GAAAa,MAAA,CAAA/B,KAAA;MAChB,IAAMgC,QAAQ,GAAGP,eAAe,CAACH,KAAK,CAAC,CAAC;MACxCH,MAAM,CAACD,MAAM,CAACU,QAAQ,CAAC,CAACR,EAAE,CAACC,KAAK,CAACW,QAAQ,CAACJ,QAAQ,CAAC;MACnDT,MAAM,CAACD,MAAM,CAACW,MAAM,CAAC,CAACT,EAAE,CAACC,KAAK,CAACW,QAAQ,CAACH,MAAM,CAAC;MAC/CV,MAAM,CAACD,MAAM,CAACA,MAAM,CAACS,cAAc,CAAC,CAACP,EAAE,CAACC,KAAK,CAACW,QAAQ,CAACL,cAAc,CAAC;MACtER,MAAM,CAACD,MAAM,CAACA,MAAM,CAACQ,OAAO,CAAC,CAACN,EAAE,CAACC,KAAK,CAACW,QAAQ,CAACN,OAAO,CAAC;IACzD;IAEAP,MAAM,CAACM,eAAe,CAAC5B,MAAM,CAAC,CAACuB,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC;EAC3C,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}