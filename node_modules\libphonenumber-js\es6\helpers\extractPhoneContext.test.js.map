{"version": 3, "file": "extractPhoneContext.test.js", "names": ["parsePhoneNumber_", "PhoneNumber", "metadata", "type", "parsePhoneNumber", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "apply", "describe", "it", "NZ_NUMBER", "expectPhoneNumbersToBeEqual", "nzFromPhoneContext", "brFromPhoneContext", "usFromPhoneContext", "expectToThrowForInvalidPhoneContext", "string", "expect", "to", "be", "undefined", "phoneNumber1", "phoneNumber2", "number", "ext"], "sources": ["../../source/helpers/extractPhoneContext.test.js"], "sourcesContent": ["import parsePhoneNumber_ from '../parsePhoneNumber.js'\r\nimport PhoneNumber from '../PhoneNumber.js'\r\nimport metadata from '../../metadata.min.json' with { type: 'json' }\r\n\r\nfunction parsePhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn parsePhoneNumber_.apply(this, parameters)\r\n}\r\n\r\ndescribe('extractPhoneContext', function() {\r\n\tit('should parse RFC 3966 phone number URIs', function() {\r\n\t\t// context    = \";phone-context=\" descriptor\r\n\t\t// descriptor = domainname / global-number-digits\r\n\r\n\t\tconst NZ_NUMBER = new PhoneNumber('64', '33316005', metadata)\r\n\r\n\t\t// Valid global-phone-digits\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+64'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+64;{this isn\\'t part of phone-context anymore!}'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\r\n\t\tconst nzFromPhoneContext = new PhoneNumber('64', '3033316005', metadata)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+64-3'),\r\n\t\t\tnzFromPhoneContext\r\n\t\t)\r\n\r\n\t\tconst brFromPhoneContext = new PhoneNumber('55', '5033316005', metadata)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+(555)'),\r\n\t\t\tbrFromPhoneContext\r\n\t\t)\r\n\r\n\t\tconst usFromPhoneContext = new PhoneNumber('1', '23033316005', metadata)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=+-1-2.3()'),\r\n\t\t\tusFromPhoneContext\r\n\t\t)\r\n\r\n\t\t// Valid domainname.\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=abc.nz', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=www.PHONE-numb3r.com', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=a', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=3phone.J.', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;phone-context=a--z', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\r\n\t\t// Should strip ISDN subaddress.\r\n\t\texpectPhoneNumbersToBeEqual(\r\n\t\t\tparsePhoneNumber('tel:033316005;isub=/@;phone-context=+64', 'NZ'),\r\n\t\t\tNZ_NUMBER\r\n\t\t)\r\n\r\n\t\t// // Should support incorrectly-written RFC 3966 phone numbers:\r\n\t\t// // the ones written without a `tel:` prefix.\r\n\t\t// expectPhoneNumbersToBeEqual(\r\n\t\t// \tparsePhoneNumber('033316005;phone-context=+64', 'NZ'),\r\n\t\t// \tNZ_NUMBER\r\n\t\t// )\r\n\r\n\t\t// Invalid descriptor.\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=+')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=64')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=++64')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=+abc')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=.')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=3phone')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=a-.nz')\r\n\t\texpectToThrowForInvalidPhoneContext('tel:033316005;phone-context=a{b}c')\r\n\t})\r\n})\r\n\r\nfunction expectToThrowForInvalidPhoneContext(string) {\r\n\texpect(parsePhoneNumber(string)).to.be.undefined\r\n}\r\n\r\nfunction expectPhoneNumbersToBeEqual(phoneNumber1, phoneNumber2) {\r\n\tif (!phoneNumber1 || !phoneNumber2) {\r\n\t\treturn false\r\n\t}\r\n\treturn phoneNumber1.number === phoneNumber2.number &&\r\n\t\tphoneNumber1.ext === phoneNumber2.ext\r\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,yBAAyB,QAAQC,IAAI,EAAE,MAAM;AAElE,SAASC,gBAAgBA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACtCF,UAAU,CAACG,IAAI,CAACT,QAAQ,CAAC;EACzB,OAAOF,iBAAiB,CAACY,KAAK,CAAC,IAAI,EAAEJ,UAAU,CAAC;AACjD;AAEAK,QAAQ,CAAC,qBAAqB,EAAE,YAAW;EAC1CC,EAAE,CAAC,yCAAyC,EAAE,YAAW;IACxD;IACA;;IAEA,IAAMC,SAAS,GAAG,IAAId,WAAW,CAAC,IAAI,EAAE,UAAU,EAAEC,QAAQ,CAAC;;IAE7D;IACAc,2BAA2B,CAC1BZ,gBAAgB,CAAC,iCAAiC,CAAC,EACnDW,SACD,CAAC;IAEDC,2BAA2B,CAC1BZ,gBAAgB,CAAC,8EAA8E,CAAC,EAChGW,SACD,CAAC;IAED,IAAME,kBAAkB,GAAG,IAAIhB,WAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,QAAQ,CAAC;IACxEc,2BAA2B,CAC1BZ,gBAAgB,CAAC,mCAAmC,CAAC,EACrDa,kBACD,CAAC;IAED,IAAMC,kBAAkB,GAAG,IAAIjB,WAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,QAAQ,CAAC;IACxEc,2BAA2B,CAC1BZ,gBAAgB,CAAC,oCAAoC,CAAC,EACtDc,kBACD,CAAC;IAED,IAAMC,kBAAkB,GAAG,IAAIlB,WAAW,CAAC,GAAG,EAAE,aAAa,EAAEC,QAAQ,CAAC;IACxEc,2BAA2B,CAC1BZ,gBAAgB,CAAC,uCAAuC,CAAC,EACzDe,kBACD,CAAC;;IAED;IACAH,2BAA2B,CAC1BZ,gBAAgB,CAAC,oCAAoC,EAAE,IAAI,CAAC,EAC5DW,SACD,CAAC;IACDC,2BAA2B,CAC1BZ,gBAAgB,CAAC,kDAAkD,EAAE,IAAI,CAAC,EAC1EW,SACD,CAAC;IACDC,2BAA2B,CAC1BZ,gBAAgB,CAAC,+BAA+B,EAAE,IAAI,CAAC,EACvDW,SACD,CAAC;IACDC,2BAA2B,CAC1BZ,gBAAgB,CAAC,uCAAuC,EAAE,IAAI,CAAC,EAC/DW,SACD,CAAC;IACDC,2BAA2B,CAC1BZ,gBAAgB,CAAC,kCAAkC,EAAE,IAAI,CAAC,EAC1DW,SACD,CAAC;;IAED;IACAC,2BAA2B,CAC1BZ,gBAAgB,CAAC,yCAAyC,EAAE,IAAI,CAAC,EACjEW,SACD,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAK,mCAAmC,CAAC,8BAA8B,CAAC;IACnEA,mCAAmC,CAAC,+BAA+B,CAAC;IACpEA,mCAAmC,CAAC,gCAAgC,CAAC;IACrEA,mCAAmC,CAAC,kCAAkC,CAAC;IACvEA,mCAAmC,CAAC,kCAAkC,CAAC;IACvEA,mCAAmC,CAAC,+BAA+B,CAAC;IACpEA,mCAAmC,CAAC,oCAAoC,CAAC;IACzEA,mCAAmC,CAAC,mCAAmC,CAAC;IACxEA,mCAAmC,CAAC,mCAAmC,CAAC;EACzE,CAAC,CAAC;AACH,CAAC,CAAC;AAEF,SAASA,mCAAmCA,CAACC,MAAM,EAAE;EACpDC,MAAM,CAAClB,gBAAgB,CAACiB,MAAM,CAAC,CAAC,CAACE,EAAE,CAACC,EAAE,CAACC,SAAS;AACjD;AAEA,SAAST,2BAA2BA,CAACU,YAAY,EAAEC,YAAY,EAAE;EAChE,IAAI,CAACD,YAAY,IAAI,CAACC,YAAY,EAAE;IACnC,OAAO,KAAK;EACb;EACA,OAAOD,YAAY,CAACE,MAAM,KAAKD,YAAY,CAACC,MAAM,IACjDF,YAAY,CAACG,GAAG,KAAKF,YAAY,CAACE,GAAG;AACvC", "ignoreList": []}