{"version": 3, "sources": ["../../../../../../src/build/webpack/plugins/terser-webpack-plugin/src/index.ts"], "names": ["path", "webpack", "ModuleFilenameHelpers", "sources", "pLimit", "Worker", "spans", "getEcmaVersion", "environment", "arrowFunction", "const", "destructuring", "forOf", "module", "bigIntLiteral", "dynamicImport", "buildError", "error", "file", "line", "Error", "message", "col", "stack", "split", "slice", "join", "debugMinify", "process", "env", "NEXT_DEBUG_MINIFY", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "options", "terserOptions", "parallel", "swcMinify", "optimize", "compiler", "compilation", "assets", "optimizeOptions", "cache", "SourceMapSource", "RawSource", "compilationSpan", "get", "terserSpan", "<PERSON><PERSON><PERSON><PERSON>", "setAttribute", "name", "traceAsyncFn", "numberOfAssetsForMinify", "assetsList", "Object", "keys", "assetsForMinify", "Promise", "all", "filter", "matchObject", "bind", "undefined", "test", "res", "getAsset", "console", "log", "info", "minimized", "map", "source", "eTag", "getLazyHashedEtag", "output", "getPromise", "JSON", "stringify", "toString", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputSource", "numberOfWorkers", "Math", "min", "availableNumberOfCores", "initializedWorker", "getWorker", "minify", "result", "require", "input", "inputSourceMap", "sourceMap", "content", "compress", "mangle", "comments", "__dirname", "numWorkers", "enableWorkerThreads", "getStdout", "pipe", "stdout", "getStderr", "stderr", "limit", "scheduledTasks", "asset", "push", "minifySpan", "sourceFromInputSource", "sourceAndMap", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "javascriptModule", "errors", "code", "storePromise", "newInfo", "updateAsset", "end", "apply", "ecma", "pluginName", "hooks", "thisCompilation", "tap", "getCache", "handleHashForChunk", "hash", "_chunk", "update", "JSModulesHooks", "javascript", "JavascriptModulesPlugin", "getCompilationHooks", "chunkHash", "chunk", "hasRuntime", "processAssets", "tapPromise", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_SIZE", "statsPrinter", "stats", "print", "for", "green", "formatFlag"], "mappings": "AAAA,YAAYA,UAAU,OAAM;AAC5B,SACEC,OAAO,EACPC,qBAAqB,EACrBC,OAAO,QACF,qCAAoC;AAC3C,OAAOC,YAAY,6BAA4B;AAC/C,SAASC,MAAM,QAAQ,iCAAgC;AACvD,SAASC,KAAK,QAAQ,yBAAwB;AAE9C,SAASC,eAAeC,WAAgB;IACtC,SAAS;IACT,IACEA,YAAYC,aAAa,IACzBD,YAAYE,KAAK,IACjBF,YAAYG,aAAa,IACzBH,YAAYI,KAAK,IACjBJ,YAAYK,MAAM,EAClB;QACA,OAAO;IACT;IAEA,UAAU;IACV,IAAIL,YAAYM,aAAa,IAAIN,YAAYO,aAAa,EAAE;QAC1D,OAAO;IACT;IAEA,OAAO;AACT;AAEA,SAASC,WAAWC,KAAU,EAAEC,IAAY;IAC1C,IAAID,MAAME,IAAI,EAAE;QACd,OAAO,IAAIC,MACT,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEH,KAAK,CAAC,EAAED,MAAME,IAAI,CAAC,CAAC,EAC5DF,MAAMK,GAAG,CACV,CAAC,EACAL,MAAMM,KAAK,GAAG,CAAC,EAAE,EAAEN,MAAMM,KAAK,CAACC,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAGC,IAAI,CAAC,MAAM,CAAC,GAAG,GACpE,CAAC;IAEN;IAEA,IAAIT,MAAMM,KAAK,EAAE;QACf,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,EAAE,EAAEJ,MAAMM,KAAK,CAAC,CAAC;IAC1E;IAEA,OAAO,IAAIH,MAAM,CAAC,EAAEF,KAAK,cAAc,EAAED,MAAMI,OAAO,CAAC,CAAC;AAC1D;AAEA,MAAMM,cAAcC,QAAQC,GAAG,CAACC,iBAAiB;AAEjD,OAAO,MAAMC;IAEXC,YAAYC,UAAe,CAAC,CAAC,CAAE;QAC7B,MAAM,EAAEC,gBAAgB,CAAC,CAAC,EAAEC,QAAQ,EAAEC,SAAS,EAAE,GAAGH;QAEpD,IAAI,CAACA,OAAO,GAAG;YACbG;YACAD;YACAD;QACF;IACF;IAEA,MAAMG,SACJC,QAAa,EACbC,WAAgB,EAChBC,MAAW,EACXC,eAAoB,EACpBC,KAAU,EACV,EAAEC,eAAe,EAAEC,SAAS,EAAO,EACnC;QACA,MAAMC,kBAAkBvC,MAAMwC,GAAG,CAACP,gBAAiBjC,MAAMwC,GAAG,CAACR;QAC7D,MAAMS,aAAaF,gBAAgBG,UAAU,CAC3C;QAEFD,WAAWE,YAAY,CAAC,mBAAmBV,YAAYW,IAAI;QAC3DH,WAAWE,YAAY,CAAC,aAAa,IAAI,CAAChB,OAAO,CAACG,SAAS;QAE3D,OAAOW,WAAWI,YAAY,CAAC;YAC7B,IAAIC,0BAA0B;YAC9B,MAAMC,aAAaC,OAAOC,IAAI,CAACf;YAE/B,MAAMgB,kBAAkB,MAAMC,QAAQC,GAAG,CACvCL,WACGM,MAAM,CAAC,CAACT;gBACP,IACE,CAAChD,sBAAsB0D,WAAW,CAACC,IAAI,CACrC,wCAAwC;gBACxCC,WACA;oBAAEC,MAAM;gBAAqB,GAC7Bb,OACF;oBACA,OAAO;gBACT;gBAEA,MAAMc,MAAMzB,YAAY0B,QAAQ,CAACf;gBACjC,IAAI,CAACc,KAAK;oBACRE,QAAQC,GAAG,CAACjB;oBACZ,OAAO;gBACT;gBAEA,MAAM,EAAEkB,IAAI,EAAE,GAAGJ;gBAEjB,qDAAqD;gBACrD,IAAII,KAAKC,SAAS,EAAE;oBAClB,OAAO;gBACT;gBAEA,OAAO;YACT,GACCC,GAAG,CAAC,OAAOpB;gBACV,MAAM,EAAEkB,IAAI,EAAEG,MAAM,EAAE,GAAGhC,YAAY0B,QAAQ,CAACf;gBAE9C,MAAMsB,OAAO9B,MAAM+B,iBAAiB,CAACF;gBACrC,MAAMG,SAAS,MAAMhC,MAAMiC,UAAU,CAACzB,MAAMsB;gBAE5C,IAAI,CAACE,QAAQ;oBACXtB,2BAA2B;gBAC7B;gBAEA,IAAIzB,eAAeA,gBAAgB,KAAK;oBACtCuC,QAAQC,GAAG,CACTS,KAAKC,SAAS,CAAC;wBACb3B;wBACAqB,QAAQA,OAAOA,MAAM,GAAGO,QAAQ;oBAClC,IACA;wBACEC,aAAaC;wBACbC,iBAAiBD;oBACnB;gBAEJ;gBACA,OAAO;oBAAE9B;oBAAMkB;oBAAMc,aAAaX;oBAAQG;oBAAQF;gBAAK;YACzD;YAGJ,MAAMW,kBAAkBC,KAAKC,GAAG,CAC9BjC,yBACAX,gBAAgB6C,sBAAsB;YAGxC,IAAIC;YAEJ,6CAA6C;YAC7C,MAAMC,YAAY;gBAChB,IAAI,IAAI,CAACvD,OAAO,CAACG,SAAS,EAAE;oBAC1B,OAAO;wBACLqD,QAAQ,OAAOxD;4BACb,MAAMyD,SAAS,MAAMC,QAAQ,mBAAmBF,MAAM,CACpDxD,QAAQ2D,KAAK,EACb;gCACE,GAAI3D,QAAQ4D,cAAc,GACtB;oCACEC,WAAW;wCACTC,SAASnB,KAAKC,SAAS,CAAC5C,QAAQ4D,cAAc;oCAChD;gCACF,IACA,CAAC,CAAC;gCACNG,UAAU;gCACVC,QAAQ;gCACRvB,QAAQ;oCACNwB,UAAU;gCACZ;4BACF;4BAGF,OAAOR;wBACT;oBACF;gBACF;gBAEA,IAAIH,mBAAmB;oBACrB,OAAOA;gBACT;gBAEAA,oBAAoB,IAAIlF,OAAOL,KAAK0B,IAAI,CAACyE,WAAW,gBAAgB;oBAClEC,YAAYjB;oBACZkB,qBAAqB;gBACvB;gBAEAd,kBAAkBe,SAAS,GAAGC,IAAI,CAAC3E,QAAQ4E,MAAM;gBACjDjB,kBAAkBkB,SAAS,GAAGF,IAAI,CAAC3E,QAAQ8E,MAAM;gBAEjD,OAAOnB;YACT;YAEA,MAAMoB,QAAQvG,OACZ,mEAAmE;YACnE,IAAI,CAAC6B,OAAO,CAACG,SAAS,GAClB4C,WACA5B,0BAA0B,IAC1B+B,kBACAH;YAEN,MAAM4B,iBAAiB,EAAE;YAEzB,KAAK,MAAMC,SAASrD,gBAAiB;gBACnCoD,eAAeE,IAAI,CACjBH,MAAM;oBACJ,MAAM,EAAEzD,IAAI,EAAEgC,WAAW,EAAEd,IAAI,EAAEI,IAAI,EAAE,GAAGqC;oBAC1C,IAAI,EAAEnC,MAAM,EAAE,GAAGmC;oBAEjB,MAAME,aAAahE,WAAWC,UAAU,CAAC;oBACzC+D,WAAW9D,YAAY,CAAC,QAAQC;oBAChC6D,WAAW9D,YAAY,CACrB,SACA,OAAOyB,WAAW,cAAc,SAAS;oBAG3C,OAAOqC,WAAW5D,YAAY,CAAC;wBAC7B,IAAI,CAACuB,QAAQ;4BACX,MAAM,EAAEH,QAAQyC,qBAAqB,EAAE1C,KAAKuB,cAAc,EAAE,GAC1DX,YAAY+B,YAAY;4BAE1B,MAAMrB,QAAQsB,OAAOC,QAAQ,CAACH,yBAC1BA,sBAAsBlC,QAAQ,KAC9BkC;4BAEJ,MAAM/E,UAAU;gCACdiB;gCACA0C;gCACAC;gCACA3D,eAAe;oCAAE,GAAG,IAAI,CAACD,OAAO,CAACC,aAAa;gCAAC;4BACjD;4BAEA,IAAI,OAAOD,QAAQC,aAAa,CAACrB,MAAM,KAAK,aAAa;gCACvD,IAAI,OAAOuD,KAAKgD,gBAAgB,KAAK,aAAa;oCAChDnF,QAAQC,aAAa,CAACrB,MAAM,GAAGuD,KAAKgD,gBAAgB;gCACtD,OAAO,IAAI,iBAAiBrD,IAAI,CAACb,OAAO;oCACtCjB,QAAQC,aAAa,CAACrB,MAAM,GAAG;gCACjC,OAAO,IAAI,iBAAiBkD,IAAI,CAACb,OAAO;oCACtCjB,QAAQC,aAAa,CAACrB,MAAM,GAAG;gCACjC;4BACF;4BAEA,IAAI;gCACF6D,SAAS,MAAMc,YAAYC,MAAM,CAACxD;4BACpC,EAAE,OAAOhB,OAAO;gCACdsB,YAAY8E,MAAM,CAACP,IAAI,CAAC9F,WAAWC,OAAOiC;gCAE1C;4BACF;4BAEA,IAAIwB,OAAOJ,GAAG,EAAE;gCACdI,OAAOH,MAAM,GAAG,IAAI5B,gBAClB+B,OAAO4C,IAAI,EACXpE,MACAwB,OAAOJ,GAAG,EACVsB,OACAC,gBACA;4BAEJ,OAAO;gCACLnB,OAAOH,MAAM,GAAG,IAAI3B,UAAU8B,OAAO4C,IAAI;4BAC3C;4BAEA,MAAM5E,MAAM6E,YAAY,CAACrE,MAAMsB,MAAM;gCACnCD,QAAQG,OAAOH,MAAM;4BACvB;wBACF;wBAEA,MAAMiD,UAAU;4BAAEnD,WAAW;wBAAK;wBAClC,MAAM,EAAEE,MAAM,EAAE,GAAGG;wBAEnBnC,YAAYkF,WAAW,CAACvE,MAAMqB,QAAQiD;oBACxC;gBACF;YAEJ;YAEA,MAAM/D,QAAQC,GAAG,CAACkD;YAElB,IAAIrB,mBAAmB;gBACrB,MAAMA,kBAAkBmC,GAAG;YAC7B;QACF;IACF;IAEAC,MAAMrF,QAAa,EAAE;YACoBA;QAAvC,MAAM,EAAEK,eAAe,EAAEC,SAAS,EAAE,GAAGN,CAAAA,6BAAAA,oBAAAA,SAAUrC,OAAO,qBAAjBqC,kBAAmBnC,OAAO,KAAIA;QACrE,MAAM,EAAEuE,MAAM,EAAE,GAAGpC,SAASL,OAAO;QAEnC,IAAI,OAAO,IAAI,CAACA,OAAO,CAACC,aAAa,CAAC0F,IAAI,KAAK,aAAa;YAC1D,IAAI,CAAC3F,OAAO,CAACC,aAAa,CAAC0F,IAAI,GAAGrH,eAAemE,OAAOlE,WAAW,IAAI,CAAC;QAC1E;QAEA,MAAMqH,aAAa,IAAI,CAAC7F,WAAW,CAACkB,IAAI;QACxC,MAAMoC,yBAAyB,IAAI,CAACrD,OAAO,CAACE,QAAQ;QAEpDG,SAASwF,KAAK,CAACC,eAAe,CAACC,GAAG,CAACH,YAAY,CAACtF;YAC9C,MAAMG,QAAQH,YAAY0F,QAAQ,CAAC;YAEnC,MAAMC,qBAAqB,CAACC,MAAWC;gBACrC,oCAAoC;gBACpCD,KAAKE,MAAM,CAAC;YACd;YAEA,MAAMC,iBACJrI,QAAQsI,UAAU,CAACC,uBAAuB,CAACC,mBAAmB,CAC5DlG;YAEJ+F,eAAeI,SAAS,CAACV,GAAG,CAACH,YAAY,CAACc,OAAOR;gBAC/C,IAAI,CAACQ,MAAMC,UAAU,IAAI;gBACzB,OAAOV,mBAAmBC,MAAMQ;YAClC;YAEApG,YAAYuF,KAAK,CAACe,aAAa,CAACC,UAAU,CACxC;gBACE5F,MAAM2E;gBACNkB,OAAO9I,QAAQ+I,WAAW,CAACC,kCAAkC;YAC/D,GACA,CAACzG,SACC,IAAI,CAACH,QAAQ,CACXC,UACAC,aACAC,QACA;oBACE8C;gBACF,GACA5C,OACA;oBAAEC;oBAAiBC;gBAAU;YAInCL,YAAYuF,KAAK,CAACoB,YAAY,CAAClB,GAAG,CAACH,YAAY,CAACsB;gBAC9CA,MAAMrB,KAAK,CAACsB,KAAK,CACdC,GAAG,CAAC,wBACJrB,GAAG,CACF,yBACA,CAAC3D,WAAgB,EAAEiF,KAAK,EAAEC,UAAU,EAAO,GACzC,wCAAwC;oBACxClF,YAAYiF,MAAMC,WAAW,gBAAgBzF;YAErD;QACF;IACF;AACF"}