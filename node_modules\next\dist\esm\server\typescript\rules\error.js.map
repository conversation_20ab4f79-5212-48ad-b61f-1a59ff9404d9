{"version": 3, "sources": ["../../../../src/server/typescript/rules/error.ts"], "names": ["NEXT_TS_ERRORS", "getTs", "errorEntry", "getSemanticDiagnostics", "source", "isClientEntry", "isErrorFile", "test", "fileName", "isGlobalErrorFile", "ts", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_ERROR_COMPONENT", "messageText", "start", "length", "text"], "mappings": "AAAA,8FAA8F;AAE9F,SAASA,cAAc,QAAQ,cAAa;AAC5C,SAASC,KAAK,QAAQ,WAAU;AAGhC,MAAMC,aAAa;IACjBC,wBACEC,MAA2B,EAC3BC,aAAsB;QAEtB,MAAMC,cAAc,oBAAoBC,IAAI,CAACH,OAAOI,QAAQ;QAC5D,MAAMC,oBAAoB,2BAA2BF,IAAI,CAACH,OAAOI,QAAQ;QAEzE,IAAI,CAACF,eAAe,CAACG,mBAAmB,OAAO,EAAE;QAEjD,MAAMC,KAAKT;QAEX,IAAI,CAACI,eAAe;YAClB,6CAA6C;YAC7C,OAAO;gBACL;oBACEM,MAAMP;oBACNQ,UAAUF,GAAGG,kBAAkB,CAACC,KAAK;oBACrCC,MAAMf,eAAegB,uBAAuB;oBAC5CC,aAAa,CAAC,mJAAmJ,CAAC;oBAClKC,OAAO;oBACPC,QAAQf,OAAOgB,IAAI,CAACD,MAAM;gBAC5B;aACD;QACH;QACA,OAAO,EAAE;IACX;AACF;AAEA,eAAejB,WAAU"}