{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "names": ["isResourceInPackages", "makeExternalHandler", "resolveExternal", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "path", "sep", "includes", "join", "replace", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "_optOutBundlingPackages", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "NODE_ESM_RESOLVE_OPTIONS", "nodeResolveOptions", "NODE_RESOLVE_OPTIONS", "baseEsmResolveOptions", "NODE_BASE_ESM_RESOLVE_OPTIONS", "baseResolveOptions", "NODE_BASE_RESOLVE_OPTIONS", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolveOptions", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "config", "optOutBundlingPackages", "optOutBundlingPackageRegex", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "isWebpackAppLayer", "test", "notExternalModules", "BARREL_OPTIMIZATION_PREFIX", "isWebpackServerOnlyLayer", "resolveNextExternal", "resolveResult", "undefined", "defaultOverrides", "isOptOutBundling", "Error", "externalType", "transpilePackages", "Map", "pkg", "pkgRes", "set", "dirname", "resolvedBundlingOptOutRes", "resolveBundlingOptOutPackages", "resolvedRes", "shouldBundlePages", "bundlePagesExternals", "shouldBeBundled", "isExternal", "normalizePathSep"], "mappings": ";;;;;;;;;;;;;;;;IA2BgBA,oBAAoB;eAApBA;;IA0GAC,mBAAmB;eAAnBA;;IAzFMC,eAAe;eAAfA;;;6BAzCW;2BACU;6DAC1B;+BAMV;uBACqD;kCAC3B;;;;;;AACjC,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,CAAC,EAAEF,SAAS,EAAEF,gBAAgB,EAAE,EAAEC,gBAAgB,CAAC;AAGrD,MAAMI,mBAAmB;AAElB,SAASV,qBACdW,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,IAAI,CAACD,cAAc,OAAO;IAC1B,OAAOA,aAAaE,IAAI,CAAC,CAACC,IACxBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMI,aAAI,CAACC,GAAG,IACxDT,SAASU,QAAQ,CACfF,aAAI,CAACC,GAAG,GACND,aAAI,CAACG,IAAI,CAAC,gBAAgBP,EAAEQ,OAAO,CAAC,OAAOJ,aAAI,CAACC,GAAG,KACnDD,aAAI,CAACC,GAAG;AAGpB;AAEO,eAAelB,gBACpBsB,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,uBAAiC,EACjCC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBC,uCAAwB,EACjDC,qBAA0BC,mCAAoB,EAC9CC,wBAA6BC,4CAA6B,EAC1DC,qBAA0BC,wCAAyB;IAEnD,MAAMC,eAAe,CAAC,CAAChB;IACvB,MAAMiB,oBAAoBjB,uBAAuB;IAEjD,IAAIkB,MAAqB;IACzB,IAAIC,QAAiB;IAErB,MAAMC,mBACJJ,gBAAgBb,iBAAiB;QAAC;QAAM;KAAM,GAAG;QAAC;KAAM;IAE1D,KAAK,MAAMkB,aAAaD,iBAAkB;QACxC,MAAME,iBAAiBD,YAAYb,oBAAoBE;QAEvD,MAAMa,UAAUlB,WAAWiB;QAE3B,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;YACD,CAACJ,KAAKC,MAAM,GAAG,MAAMI,QAAQtB,SAASC;QACzC,EAAE,OAAOsB,KAAK;YACZN,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACf,kBAAkBgB,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIX,iBAAiB;YACnB,OAAO;gBAAEmB,UAAUnB,gBAAgBY;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIX,kBAAkB;YACpB,IAAImB;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAAcvB,WAClBc,QAAQP,wBAAwBE;gBAEjC,CAACY,SAASC,UAAU,GAAG,MAAMC,YAAY7B,KAAKG;YACjD,EAAE,OAAOsB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,2EAA2E;YAC3E,wDAAwD;YACxD,IAAID,YAAYR,OAAOC,UAAUQ,WAAW;gBAC1CT,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEO,SAAS3C,oBAAoB,EAClCqD,MAAM,EACNC,sBAAsB,EACtBC,0BAA0B,EAC1BhC,GAAG,EAMJ;QAE2B8B;IAD1B,IAAIG;IACJ,MAAMf,oBAAoBY,EAAAA,uBAAAA,OAAOI,YAAY,qBAAnBJ,qBAAqBb,YAAY,MAAK;IAEhE,OAAO,eAAekB,gBACpBjC,OAAe,EACfC,OAAe,EACfiC,cAAsB,EACtBC,KAA8B,EAC9B/B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAMgC,UACJnC,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBE,aAAI,CAAC4C,KAAK,CAACC,UAAU,CAACrC,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBsC,QAAQC,QAAQ,KAAK,WAAW/C,aAAI,CAACgD,KAAK,CAACH,UAAU,CAACrC;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMyC,aAAaC,IAAAA,wBAAiB,EAACR;QAErC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,aAAaQ,IAAI,CAAC3C,UAAU;gBAC9B,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IAAIxB,mBAAmBmE,IAAI,CAAC3C,YAAY,CAACyC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAEzC,QAAQ,CAAC;YAC9B;YAEA,MAAM4C,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAAC3C,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQN,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIM,QAAQV,UAAU,CAACuD,qCAA0B,GAAG;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAM5C,iBAAiBgC,mBAAmB;QAE1C,4DAA4D;QAC5D,yFAAyF;QACzF,4DAA4D;QAC5D,IACEa,IAAAA,+BAAwB,EAACZ,UACzBlC,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDqD,IAAI,CAAC3C,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8C2C,IAAI,CAAC3C,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,8DAA8D2C,IAAI,CAChE3C,YAEF,4CAA4C2C,IAAI,CAAC3C,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,QAAQ,CAAC;YAC9B;YAEA,IACE,sEAAsE2C,IAAI,CACxE3C,YAEF,2CAA2C2C,IAAI,CAAC3C,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,QAAQ,CAAC;YAC5B;YAEA,OAAO+C,oBAAoB/C;QAC7B;QAEA,6FAA6F;QAC7F,MAAMgD,gBAAgB,MAAMzE,gBAC1BsB,KACA8B,OAAOI,YAAY,CAACjB,YAAY,EAChCf,SACAC,SACAC,gBACA2B,wBACAzB,YACAgC,UAAUY,sBAAsBE;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAczB,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAIvB,YAAY,oBAAoB;YAClCgD,cAAchC,GAAG,GAAGkC,6BAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAElC,GAAG,EAAEC,KAAK,EAAE,GAAG+B;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAAChC,KAAK;YACR;QACF;QAEA,MAAMmC,mBAAmBtB,2BAA2Bc,IAAI,CAAC3B;QACzD,0CAA0C;QAC1C,4FAA4F;QAC5F,IAAI,CAACmC,oBAAoBV,YAAY;YACnC;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACxC,kBAAkBgB,SAAS,CAACF,qBAAqB,CAACoB,SAAS;YAC9D,MAAM,IAAIiB,MACR,CAAC,cAAc,EAAEpD,QAAQ,2HAA2H,CAAC;QAEzJ;QAEA,MAAMqD,eAAepC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2C0B,IAAI,CAAC3B,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2B2B,IAAI,CAAC3B,QAChC,8BAA8B2B,IAAI,CAAC3B,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIW,OAAO2B,iBAAiB,IAAI,CAACxB,6BAA6B;YAC5DA,8BAA8B,IAAIyB;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAO7B,OAAO2B,iBAAiB,CAAE;gBAC1C,MAAMG,SAAS,MAAMlF,gBACnBsB,KACA8B,OAAOI,YAAY,CAACjB,YAAY,EAChCf,SACAyD,MAAM,iBACNvD,gBACA2B,wBACAzB,YACAgC,UAAUY,sBAAsBE;gBAElC,IAAIQ,OAAOzC,GAAG,EAAE;oBACdc,4BAA4B4B,GAAG,CAACF,KAAKhE,aAAI,CAACmE,OAAO,CAACF,OAAOzC,GAAG;gBAC9D;YACF;QACF;QAEA,MAAM4C,4BAA4BC,8BAA8B;YAC9DC,aAAa9C;YACbW;YACAG;YACAW;YACAY;YACAF;YACAnD;QACF;QACA,IAAI4D,2BAA2B;YAC7B,OAAOA;QACT;QAEA,2CAA2C;QAC3C;IACF;AACF;AAEA,SAASC,8BAA8B,EACrCC,WAAW,EACXnC,MAAM,EACNG,2BAA2B,EAC3BW,UAAU,EACVY,YAAY,EACZF,gBAAgB,EAChBnD,OAAO,EASR;IACC,IAAIjB,iBAAiB4D,IAAI,CAACmB,cAAc;QACtC,MAAMC,oBACJ,CAACtB,cACDd,OAAOI,YAAY,CAACiC,oBAAoB,IACxC,CAACb;QACH,MAAMc,kBACJF,qBACA1F,qBACEyF,aACAnC,OAAO2B,iBAAiB,EACxBxB;QAEJ,IAAI,CAACmC,iBAAiB;YACpB,OAAO,CAAC,EAAEZ,aAAa,CAAC,EAAErD,QAAQ,CAAC,CAAC,0CAA0C;;QAChF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS+C,oBAAoBxB,QAAgB;IAC3C,MAAM2C,aAAarF,gBAAgB8D,IAAI,CAACpB;IAExC,sFAAsF;IACtF,sGAAsG;IACtG,IAAI2C,YAAY;QACd,oGAAoG;QACpG,oCAAoC;QACpC,OAAO,CAAC,SAAS,EAAEC,IAAAA,kCAAgB,EACjC5C,SAAS3B,OAAO,CAAC,oBAAoB,cACrC,CAAC;IACL;AACF"}