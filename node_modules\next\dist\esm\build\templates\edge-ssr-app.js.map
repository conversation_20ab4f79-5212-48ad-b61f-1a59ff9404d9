{"version": 3, "sources": ["../../../src/build/templates/edge-ssr-app.ts"], "names": ["self", "adapter", "getRender", "IncrementalCache", "renderToHTMLOrFlight", "renderToHTML", "pageMod", "PAGE_TYPES", "setReferenceManifestsSingleton", "createServerModuleMap", "Document", "appMod", "errorMod", "error500Mod", "maybeJSONParse", "str", "JSON", "parse", "undefined", "buildManifest", "__BUILD_MANIFEST", "reactLoadableManifest", "__REACT_LOADABLE_MANIFEST", "rscManifest", "__RSC_MANIFEST", "rscServerManifest", "__RSC_SERVER_MANIFEST", "subresourceIntegrityManifest", "sriEnabled", "__SUBRESOURCE_INTEGRITY_MANIFEST", "nextFontManifest", "__NEXT_FONT_MANIFEST", "interceptionRouteRewrites", "__INTERCEPTION_ROUTE_REWRITE_MANIFEST", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "pageName", "render", "pagesType", "APP", "dev", "page", "isServerComponent", "serverActions", "config", "nextConfig", "buildId", "process", "env", "__NEXT_BUILD_ID", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "ComponentMod", "nH<PERSON><PERSON>", "opts", "handler"], "mappings": "IAwCoBA;AAxCpB,OAAO,2BAA0B;AACjC,SAASC,OAAO,QAAQ,2BAA0B;AAClD,SAASC,SAAS,QAAQ,iDAAgD;AAC1E,SAASC,gBAAgB,QAAQ,qCAAoC;AAErE,SAASC,wBAAwBC,YAAY,QAAQ,qCAAoC;AACzF,YAAYC,aAAa,eAAc;AAMvC,SAASC,UAAU,QAAQ,uBAAsB;AACjD,SAASC,8BAA8B,QAAQ,2CAA0C;AACzF,SAASC,qBAAqB,QAAQ,uCAAsC;AAG5E,0CAA0C;AAE1C,MAAMC,WAAyB;AAC/B,MAAMC,SAAS;AACf,MAAMC,WAAW;AACjB,MAAMC,cAAc;AAQpB,oBAAoB;AACpB,2BAA2B;AAC3B,aAAa;AACb,uBAAuB;AACvB,oBAAoB;AAEpB,MAAMC,iBAAiB,CAACC,MAAkBA,MAAMC,KAAKC,KAAK,CAACF,OAAOG;AAElE,MAAMC,gBAA+BnB,KAAKoB,gBAAgB;AAC1D,MAAMC,wBAAwBP,eAAed,KAAKsB,yBAAyB;AAC3E,MAAMC,eAAcvB,uBAAAA,KAAKwB,cAAc,qBAAnBxB,oBAAqB,CAAC,WAAW;AACrD,MAAMyB,oBAAoBX,eAAed,KAAK0B,qBAAqB;AACnE,MAAMC,+BAA+BC,aACjCd,eAAed,KAAK6B,gCAAgC,IACpDX;AACJ,MAAMY,mBAAmBhB,eAAed,KAAK+B,oBAAoB;AAEjE,MAAMC,4BACJlB,eAAed,KAAKiC,qCAAqC,KAAK,EAAE;AAElE,IAAIV,eAAeE,mBAAmB;IACpCjB,+BAA+B;QAC7B0B,yBAAyBX;QACzBY,uBAAuBV;QACvBW,iBAAiB3B,sBAAsB;YACrC0B,uBAAuBV;YACvBY,UAAU;QACZ;IACF;AACF;AAEA,MAAMC,SAASpC,UAAU;IACvBqC,WAAWhC,WAAWiC,GAAG;IACzBC;IACAC,MAAM;IACN/B;IACAL;IACAM;IACAC;IACAH;IACAS;IACAd;IACAgB;IACAa,yBAAyBS,oBAAoBpB,cAAc;IAC3DY,uBAAuBQ,oBAAoBlB,oBAAoB;IAC/DmB,eAAeD,oBAAoBC,gBAAgB1B;IACnDS;IACAkB,QAAQC;IACRC,SAASC,QAAQC,GAAG,CAACC,eAAe;IACpCpB;IACAqB;IACAnB;AACF;AAEA,OAAO,MAAMoB,eAAe9C,QAAO;AAEnC,eAAe,SAAS+C,SAASC,IAA4C;IAC3E,OAAOrD,QAAQ;QACb,GAAGqD,IAAI;QACPnD;QACAoD,SAASjB;IACX;AACF"}