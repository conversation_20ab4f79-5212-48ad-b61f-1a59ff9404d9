{"version": 3, "file": "AsYouTypeState.js", "names": ["AsYouTypeState", "_ref", "onCountryChange", "onCallingCodeChange", "_classCallCheck", "_createClass", "key", "value", "reset", "_ref2", "country", "callingCode", "international", "missingPlus", "IDDPrefix", "undefined", "digits", "resetNationalSignificantNumber", "initCountryAndCallingCode", "nationalSignificantNumber", "getNationalDigits", "nationalSignificantNumberMatchesInput", "nationalPrefix", "carrierCode", "complexPrefixBeforeNationalSignificantNumber", "update", "properties", "_i", "_Object$keys", "Object", "keys", "length", "setCountry", "setCallingCode", "startInternationalNumber", "appendDigits", "nextDigits", "appendNationalSignificantNumberDigits", "slice", "getDigitsWithoutInternationalPrefix", "default"], "sources": ["../source/AsYouTypeState.js"], "sourcesContent": ["// This \"state\" object simply holds the state of the \"AsYouType\" parser:\r\n//\r\n// * `country?: string`\r\n// * `callingCode?: string`\r\n// * `digits: string`\r\n// * `international: boolean`\r\n// * `missingPlus: boolean`\r\n// * `IDDPrefix?: string`\r\n// * `carrierCode?: string`\r\n// * `nationalPrefix?: string`\r\n// * `nationalSignificantNumber?: string`\r\n// * `nationalSignificantNumberMatchesInput: boolean`\r\n// * `complexPrefixBeforeNationalSignificantNumber?: string`\r\n//\r\n// `state.country` and `state.callingCode` aren't required to be in sync.\r\n// For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\r\n// So `state.country` and `state.callingCode` are totally independent.\r\n//\r\nexport default class AsYouTypeState {\r\n\tconstructor({ onCountryChange, onCallingCodeChange }) {\r\n\t\tthis.onCountryChange = onCountryChange\r\n\t\tthis.onCallingCodeChange = onCallingCodeChange\r\n\t}\r\n\r\n\treset({ country, callingCode }) {\r\n\t\tthis.international = false\r\n\t\tthis.missingPlus = false\r\n\t\tthis.IDDPrefix = undefined\r\n\t\tthis.callingCode = undefined\r\n\t\tthis.digits = ''\r\n\t\tthis.resetNationalSignificantNumber()\r\n\t\tthis.initCountryAndCallingCode(country, callingCode)\r\n\t}\r\n\r\n\tresetNationalSignificantNumber() {\r\n\t\tthis.nationalSignificantNumber = this.getNationalDigits()\r\n\t\tthis.nationalSignificantNumberMatchesInput = true\r\n\t\tthis.nationalPrefix = undefined\r\n\t\tthis.carrierCode = undefined\r\n\t\tthis.complexPrefixBeforeNationalSignificantNumber = undefined\r\n\t}\r\n\r\n\tupdate(properties) {\r\n\t\tfor (const key of Object.keys(properties)) {\r\n\t\t\tthis[key] = properties[key]\r\n\t\t}\r\n\t}\r\n\r\n\tinitCountryAndCallingCode(country, callingCode) {\r\n\t\tthis.setCountry(country)\r\n\t\tthis.setCallingCode(callingCode)\r\n\t}\r\n\r\n\tsetCountry(country) {\r\n\t\tthis.country = country\r\n\t\tthis.onCountryChange(country)\r\n\t}\r\n\r\n\tsetCallingCode(callingCode) {\r\n\t\tthis.callingCode = callingCode\r\n\t\tthis.onCallingCodeChange(callingCode, this.country)\r\n\t}\r\n\r\n\tstartInternationalNumber(country, callingCode) {\r\n\t\t// Prepend the `+` to parsed input.\r\n\t\tthis.international = true\r\n\t\t// If a default country was set then reset it\r\n\t\t// because an explicitly international phone\r\n\t\t// number is being entered.\r\n\t\tthis.initCountryAndCallingCode(country, callingCode)\r\n\t}\r\n\r\n\tappendDigits(nextDigits) {\r\n\t\tthis.digits += nextDigits\r\n\t}\r\n\r\n\tappendNationalSignificantNumberDigits(nextDigits) {\r\n\t\tthis.nationalSignificantNumber += nextDigits\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the part of `this.digits` that corresponds to the national number.\r\n\t * Basically, all digits that have been input by the user, except for the\r\n\t * international prefix and the country calling code part\r\n\t * (if the number is an international one).\r\n\t * @return {string}\r\n\t */\r\n\tgetNationalDigits() {\r\n\t\tif (this.international) {\r\n\t\t\treturn this.digits.slice(\r\n\t\t\t\t(this.IDDPrefix ? this.IDDPrefix.length : 0) +\r\n\t\t\t\t(this.callingCode ? this.callingCode.length : 0)\r\n\t\t\t)\r\n\t\t}\r\n\t\treturn this.digits\r\n\t}\r\n\r\n\tgetDigitsWithoutInternationalPrefix() {\r\n\t\tif (this.international) {\r\n\t\t\tif (this.IDDPrefix) {\r\n\t\t\t\treturn this.digits.slice(this.IDDPrefix.length)\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this.digits\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,IACqBA,cAAc;EAClC,SAAAA,eAAAC,IAAA,EAAsD;IAAA,IAAxCC,eAAe,GAAAD,IAAA,CAAfC,eAAe;MAAEC,mBAAmB,GAAAF,IAAA,CAAnBE,mBAAmB;IAAAC,eAAA,OAAAJ,cAAA;IACjD,IAAI,CAACE,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;EAC/C;EAAC,OAAAE,YAAA,CAAAL,cAAA;IAAAM,GAAA;IAAAC,KAAA,EAED,SAAAC,KAAKA,CAAAC,KAAA,EAA2B;MAAA,IAAxBC,OAAO,GAAAD,KAAA,CAAPC,OAAO;QAAEC,WAAW,GAAAF,KAAA,CAAXE,WAAW;MAC3B,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,SAAS,GAAGC,SAAS;MAC1B,IAAI,CAACJ,WAAW,GAAGI,SAAS;MAC5B,IAAI,CAACC,MAAM,GAAG,EAAE;MAChB,IAAI,CAACC,8BAA8B,CAAC,CAAC;MACrC,IAAI,CAACC,yBAAyB,CAACR,OAAO,EAAEC,WAAW,CAAC;IACrD;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAAU,8BAA8BA,CAAA,EAAG;MAChC,IAAI,CAACE,yBAAyB,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACzD,IAAI,CAACC,qCAAqC,GAAG,IAAI;MACjD,IAAI,CAACC,cAAc,GAAGP,SAAS;MAC/B,IAAI,CAACQ,WAAW,GAAGR,SAAS;MAC5B,IAAI,CAACS,4CAA4C,GAAGT,SAAS;IAC9D;EAAC;IAAAT,GAAA;IAAAC,KAAA,EAED,SAAAkB,MAAMA,CAACC,UAAU,EAAE;MAClB,SAAAC,EAAA,MAAAC,YAAA,GAAkBC,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,EAAAC,EAAA,GAAAC,YAAA,CAAAG,MAAA,EAAAJ,EAAA,IAAE;QAAtC,IAAMrB,GAAG,GAAAsB,YAAA,CAAAD,EAAA;QACb,IAAI,CAACrB,GAAG,CAAC,GAAGoB,UAAU,CAACpB,GAAG,CAAC;MAC5B;IACD;EAAC;IAAAA,GAAA;IAAAC,KAAA,EAED,SAAAW,yBAAyBA,CAACR,OAAO,EAAEC,WAAW,EAAE;MAC/C,IAAI,CAACqB,UAAU,CAACtB,OAAO,CAAC;MACxB,IAAI,CAACuB,cAAc,CAACtB,WAAW,CAAC;IACjC;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAAyB,UAAUA,CAACtB,OAAO,EAAE;MACnB,IAAI,CAACA,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACR,eAAe,CAACQ,OAAO,CAAC;IAC9B;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAA0B,cAAcA,CAACtB,WAAW,EAAE;MAC3B,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACR,mBAAmB,CAACQ,WAAW,EAAE,IAAI,CAACD,OAAO,CAAC;IACpD;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAED,SAAA2B,wBAAwBA,CAACxB,OAAO,EAAEC,WAAW,EAAE;MAC9C;MACA,IAAI,CAACC,aAAa,GAAG,IAAI;MACzB;MACA;MACA;MACA,IAAI,CAACM,yBAAyB,CAACR,OAAO,EAAEC,WAAW,CAAC;IACrD;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAA4B,YAAYA,CAACC,UAAU,EAAE;MACxB,IAAI,CAACpB,MAAM,IAAIoB,UAAU;IAC1B;EAAC;IAAA9B,GAAA;IAAAC,KAAA,EAED,SAAA8B,qCAAqCA,CAACD,UAAU,EAAE;MACjD,IAAI,CAACjB,yBAAyB,IAAIiB,UAAU;IAC7C;;IAEA;AACD;AACA;AACA;AACA;AACA;AACA;EANC;IAAA9B,GAAA;IAAAC,KAAA,EAOA,SAAAa,iBAAiBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACR,aAAa,EAAE;QACvB,OAAO,IAAI,CAACI,MAAM,CAACsB,KAAK,CACvB,CAAC,IAAI,CAACxB,SAAS,GAAG,IAAI,CAACA,SAAS,CAACiB,MAAM,GAAG,CAAC,KAC1C,IAAI,CAACpB,WAAW,GAAG,IAAI,CAACA,WAAW,CAACoB,MAAM,GAAG,CAAC,CAChD,CAAC;MACF;MACA,OAAO,IAAI,CAACf,MAAM;IACnB;EAAC;IAAAV,GAAA;IAAAC,KAAA,EAED,SAAAgC,mCAAmCA,CAAA,EAAG;MACrC,IAAI,IAAI,CAAC3B,aAAa,EAAE;QACvB,IAAI,IAAI,CAACE,SAAS,EAAE;UACnB,OAAO,IAAI,CAACE,MAAM,CAACsB,KAAK,CAAC,IAAI,CAACxB,SAAS,CAACiB,MAAM,CAAC;QAChD;MACD;MACA,OAAO,IAAI,CAACf,MAAM;IACnB;EAAC;AAAA;AAAA,SAtFmBhB,cAAc,IAAAwC,OAAA", "ignoreList": []}