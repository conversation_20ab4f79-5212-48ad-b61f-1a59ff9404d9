{"version": 3, "file": "isValidPhoneNumber.js", "names": ["normalizeArguments", "parsePhoneNumber", "isValidPhoneNumber", "_normalizeArguments", "arguments", "text", "options", "metadata", "_objectSpread", "extract", "phoneNumber", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../source/isValidPhoneNumber.js"], "sourcesContent": ["import normalizeArguments from './normalizeArguments.js'\r\nimport parsePhoneNumber from './parsePhoneNumber_.js'\r\n\r\nexport default function isValidPhoneNumber() {\r\n\tlet { text, options, metadata } = normalizeArguments(arguments)\r\n\toptions = {\r\n\t\t...options,\r\n\t\textract: false\r\n\t}\r\n\tconst phoneNumber = parsePhoneNumber(text, options, metadata)\r\n\treturn phoneNumber && phoneNumber.isValid() || false\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,gBAAgB,MAAM,wBAAwB;AAErD,eAAe,SAASC,kBAAkBA,CAAA,EAAG;EAC5C,IAAAC,mBAAA,GAAkCH,kBAAkB,CAACI,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC7BD,OAAO,GAAAE,aAAA,CAAAA,aAAA,KACHF,OAAO;IACVG,OAAO,EAAE;EAAK,EACd;EACD,IAAMC,WAAW,GAAGT,gBAAgB,CAACI,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC7D,OAAOG,WAAW,IAAIA,WAAW,CAACC,OAAO,CAAC,CAAC,IAAI,KAAK;AACrD", "ignoreList": []}