{"version": 3, "file": "ValidateNested.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/ValidateNested.ts"], "names": [], "mappings": ";;;AAEA,sEAAmE;AACnE,0EAAuE;AACvE,oEAAoE;AAEpE;;GAEG;AACH,SAAgB,cAAc,CAAC,iBAAqC;IAClE,MAAM,IAAI,GAAsB,EAAE,GAAG,iBAAiB,EAAE,CAAC;IACzD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;IACrD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,UAAU,GAAG,0DAA0D,CAAC;IAEvG,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,MAAM,IAAI,GAA2B;YACnC,IAAI,EAAE,iCAAe,CAAC,iBAAiB;YACvC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,iBAAiB,EAAE,IAAI;SACxB,CAAC;QACF,IAAA,oCAAkB,GAAE,CAAC,qBAAqB,CAAC,IAAI,uCAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;AACJ,CAAC;AAdD,wCAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { ValidationMetadataArgs } from '../../metadata/ValidationMetadataArgs';\nimport { ValidationTypes } from '../../validation/ValidationTypes';\nimport { ValidationMetadata } from '../../metadata/ValidationMetadata';\nimport { getMetadataStorage } from '../../metadata/MetadataStorage';\n\n/**\n * Objects / object arrays marked with this decorator will also be validated.\n */\nexport function ValidateNested(validationOptions?: ValidationOptions): PropertyDecorator {\n  const opts: ValidationOptions = { ...validationOptions };\n  const eachPrefix = opts.each ? 'each value in ' : '';\n  opts.message = opts.message || eachPrefix + 'nested property $property must be either object or array';\n\n  return function (object: object, propertyName: string): void {\n    const args: ValidationMetadataArgs = {\n      type: ValidationTypes.NESTED_VALIDATION,\n      target: object.constructor,\n      propertyName: propertyName,\n      validationOptions: opts,\n    };\n    getMetadataStorage().addValidationMetadata(new ValidationMetadata(args));\n  };\n}\n"]}