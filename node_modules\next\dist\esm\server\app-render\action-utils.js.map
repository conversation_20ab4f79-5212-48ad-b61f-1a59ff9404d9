{"version": 3, "sources": ["../../../src/server/app-render/action-utils.ts"], "names": ["normalizeAppPath", "pathHasPrefix", "removePathPrefix", "createServerModuleMap", "serverActionsManifest", "pageName", "Proxy", "get", "_", "id", "process", "env", "NEXT_RUNTIME", "workers", "normalizeWorkerPageName", "name", "chunks", "selectWorkerForForwarding", "actionId", "worker<PERSON>ame", "denormalizeWorkerPageName", "Object", "keys", "bundlePath"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,gBAAgB,QAAQ,mDAAkD;AAEnF,6EAA6E;AAC7E,8DAA8D;AAC9D,4EAA4E;AAC5E,8EAA8E;AAC9E,OAAO,SAASC,sBAAsB,EACpCC,qBAAqB,EACrBC,QAAQ,EAIT;IACC,OAAO,IAAIC,MACT,CAAC,GACD;QACEC,KAAK,CAACC,GAAGC;YACP,OAAO;gBACLA,IAAIL,qBAAqB,CACvBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,SAAS,OAChD,CAACH,GAAG,CAACI,OAAO,CAACC,wBAAwBT,UAAU;gBAChDU,MAAMN;gBACNO,QAAQ,EAAE;YACZ;QACF;IACF;AAEJ;AAEA;;;CAGC,GACD,OAAO,SAASC,0BACdC,QAAgB,EAChBb,QAAgB,EAChBD,qBAAqC;QAGnCA;IADF,MAAMS,WACJT,mCAAAA,qBAAqB,CACnBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,SAAS,OAChD,CAACM,SAAS,qBAFXd,iCAEaS,OAAO;IACtB,MAAMM,aAAaL,wBAAwBT;IAE3C,oCAAoC;IACpC,IAAI,CAACQ,SAAS;IAEd,6DAA6D;IAC7D,IAAIA,OAAO,CAACM,WAAW,EAAE;QACvB;IACF;IAEA,yEAAyE;IACzE,OAAOC,0BAA0BC,OAAOC,IAAI,CAACT,QAAQ,CAAC,EAAE;AAC1D;AAEA;;;CAGC,GACD,SAASC,wBAAwBT,QAAgB;IAC/C,IAAIJ,cAAcI,UAAU,QAAQ;QAClC,OAAOA;IACT;IAEA,OAAO,QAAQA;AACjB;AAEA;;CAEC,GACD,SAASe,0BAA0BG,UAAkB;IACnD,OAAOvB,iBAAiBE,iBAAiBqB,YAAY;AACvD"}