{"version": 3, "file": "extractExtension.js", "names": ["_createExtensionPattern", "_interopRequireDefault", "require", "e", "__esModule", "EXTN_PATTERN", "RegExp", "createExtensionPattern", "extractExtension", "number", "start", "search", "numberWithoutExtension", "slice", "matches", "match", "i", "length", "ext"], "sources": ["../../../source/helpers/extension/extractExtension.js"], "sourcesContent": ["import createExtensionPattern from './createExtensionPattern.js'\r\n\r\n// Regexp of all known extension prefixes used by different regions followed by\r\n// 1 or more valid digits, for use when parsing.\r\nconst EXTN_PATTERN = new RegExp('(?:' + createExtensionPattern() + ')$', 'i')\r\n\r\n// Strips any extension (as in, the part of the number dialled after the call is\r\n// connected, usually indicated with extn, ext, x or similar) from the end of\r\n// the number, and returns it.\r\nexport default function extractExtension(number) {\r\n\tconst start = number.search(EXTN_PATTERN)\r\n\tif (start < 0) {\r\n\t\treturn {}\r\n\t}\r\n\t// If we find a potential extension, and the number preceding this is a viable\r\n\t// number, we assume it is an extension.\r\n\tconst numberWithoutExtension = number.slice(0, start)\r\n\tconst matches = number.match(EXTN_PATTERN)\r\n\tlet i = 1\r\n\twhile (i < matches.length) {\r\n\t\tif (matches[i]) {\r\n\t\t\treturn {\r\n\t\t\t\tnumber: numberWithoutExtension,\r\n\t\t\t\text: matches[i]\r\n\t\t\t}\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,uBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAgE,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEhE;AACA;AACA,IAAME,YAAY,GAAG,IAAIC,MAAM,CAAC,KAAK,GAAG,IAAAC,kCAAsB,EAAC,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC;;AAE7E;AACA;AACA;AACe,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAChD,IAAMC,KAAK,GAAGD,MAAM,CAACE,MAAM,CAACN,YAAY,CAAC;EACzC,IAAIK,KAAK,GAAG,CAAC,EAAE;IACd,OAAO,CAAC,CAAC;EACV;EACA;EACA;EACA,IAAME,sBAAsB,GAAGH,MAAM,CAACI,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;EACrD,IAAMI,OAAO,GAAGL,MAAM,CAACM,KAAK,CAACV,YAAY,CAAC;EAC1C,IAAIW,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAE;IAC1B,IAAIH,OAAO,CAACE,CAAC,CAAC,EAAE;MACf,OAAO;QACNP,MAAM,EAAEG,sBAAsB;QAC9BM,GAAG,EAAEJ,OAAO,CAACE,CAAC;MACf,CAAC;IACF;IACAA,CAAC,EAAE;EACJ;AACD", "ignoreList": []}