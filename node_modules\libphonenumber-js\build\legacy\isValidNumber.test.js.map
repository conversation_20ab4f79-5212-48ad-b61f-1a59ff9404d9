{"version": 3, "file": "isValidNumber.test.js", "names": ["_metadataMin", "_interopRequireDefault", "require", "_isValidNumber2", "e", "__esModule", "isValidNumber", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "metadata", "_isValidNumber", "apply", "describe", "it", "expect", "to", "equal", "undefined", "country", "phone", "console", "log", "phoneNumberTypePatterns", "countries", "UZ", "thrower"], "sources": ["../../source/legacy/isValidNumber.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' with { type: 'json' }\r\nimport _isValidNumber from './isValidNumber.js'\r\n\r\nfunction isValidNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isValidNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidNumber', () => {\r\n\tit('should validate phone numbers', () => {\r\n\t\texpect(isValidNumber('******-373-4253')).to.equal(true)\r\n\t\texpect(isValidNumber('******-373')).to.equal(false)\r\n\r\n\t\texpect(isValidNumber('******-373-4253', undefined)).to.equal(true)\r\n\r\n\t\texpect(isValidNumber('(*************', 'US')).to.equal(true)\r\n\t\texpect(isValidNumber('(213) 37', 'US')).to.equal(false)\r\n\r\n\t\texpect(isValidNumber({ country: 'US', phone: '2133734253' })).to.equal(true)\r\n\r\n\t\t// No \"types\" info: should return `true`.\r\n\t\texpect(isValidNumber('+380972423740')).to.equal(true)\r\n\r\n\t\texpect(isValidNumber('0912345678', 'TW')).to.equal(true)\r\n\r\n\t\t// Moible numbers starting 07624* are Isle of Man\r\n\t\t// which has its own \"country code\" \"IM\"\r\n\t\t// which is in the \"GB\" \"country calling code\" zone.\r\n\t\t// So while this number is for \"IM\" it's still supposed to\r\n\t\t// be valid when passed \"GB\" as a default country.\r\n\t\texpect(isValidNumber('07624369230', 'GB')).to.equal(true)\r\n\t})\r\n\r\n\tit('should refine phone number validation in case extended regular expressions are set for a country', () => {\r\n\t\t// Germany general validation must pass\r\n\t\tconsole.log('--------------------------')\r\n\t\texpect(isValidNumber('961111111', 'UZ')).to.equal(true)\r\n\r\n\t\tconst phoneNumberTypePatterns = metadata.countries.UZ[11]\r\n\r\n\t\t// Different regular expressions for precise national number validation.\r\n\t\t// `types` index in compressed array is `9` for v1.\r\n\t\t// For v2 it's 10.\r\n\t\t// For v3 it's 11.\r\n\t\tmetadata.countries.UZ[11] =\r\n\t\t[\r\n\t\t\t[\"(?:6(?:1(?:22|3[124]|4[1-4]|5[123578]|64)|2(?:22|3[0-57-9]|41)|5(?:22|3[3-7]|5[024-8])|6\\\\d{2}|7(?:[23]\\\\d|7[69])|9(?:22|4[1-8]|6[135]))|7(?:0(?:5[4-9]|6[0146]|7[12456]|9[135-8])|1[12]\\\\d|2(?:22|3[1345789]|4[123579]|5[14])|3(?:2\\\\d|3[1578]|4[1-35-7]|5[1-57]|61)|4(?:2\\\\d|3[1-579]|7[1-79])|5(?:22|5[1-9]|6[1457])|6(?:22|3[12457]|4[13-8])|9(?:22|5[1-9])))\\\\d{5}\"],\r\n\t\t\t[\"6(?:1(?:2(?:98|2[01])|35[0-4]|50\\\\d|61[23]|7(?:[01][017]|4\\\\d|55|9[5-9]))|2(?:11\\\\d|2(?:[12]1|9[01379])|5(?:[126]\\\\d|3[0-4])|7\\\\d{2})|5(?:19[01]|2(?:27|9[26])|30\\\\d|59\\\\d|7\\\\d{2})|6(?:2(?:1[5-9]|2[0367]|38|41|52|60)|3[79]\\\\d|4(?:56|83)|7(?:[07]\\\\d|1[017]|3[07]|4[047]|5[057]|67|8[0178]|9[79])|9[0-3]\\\\d)|7(?:2(?:24|3[237]|4[5-9]|7[15-8])|5(?:7[12]|8[0589])|7(?:0\\\\d|[39][07])|9(?:0\\\\d|7[079]))|9(?:2(?:1[1267]|5\\\\d|3[01]|7[0-4])|5[67]\\\\d|6(?:2[0-26]|8\\\\d)|7\\\\d{2}))\\\\d{4}|7(?:0\\\\d{3}|1(?:13[01]|6(?:0[47]|1[67]|66)|71[3-69]|98\\\\d)|2(?:2(?:2[79]|95)|3(?:2[5-9]|6[0-6])|57\\\\d|7(?:0\\\\d|1[17]|2[27]|3[37]|44|5[057]|66|88))|3(?:2(?:1[0-6]|21|3[469]|7[159])|33\\\\d|5(?:0[0-4]|5[579]|9\\\\d)|7(?:[0-3579]\\\\d|4[0467]|6[67]|8[078])|9[4-6]\\\\d)|4(?:2(?:29|5[0257]|6[0-7]|7[1-57])|5(?:1[0-4]|8\\\\d|9[5-9])|7(?:0\\\\d|1[024589]|2[0127]|3[0137]|[46][07]|5[01]|7[5-9]|9[079])|9(?:7[015-9]|[89]\\\\d))|5(?:112|2(?:0\\\\d|2[29]|[49]4)|3[1568]\\\\d|52[6-9]|7(?:0[01578]|1[017]|[23]7|4[047]|[5-7]\\\\d|8[78]|9[079]))|6(?:2(?:2[1245]|4[2-4])|39\\\\d|41[179]|5(?:[349]\\\\d|5[0-2])|7(?:0[017]|[13]\\\\d|22|44|55|67|88))|9(?:22[128]|3(?:2[0-4]|7\\\\d)|57[05629]|7(?:2[05-9]|3[37]|4\\\\d|60|7[2579]|87|9[07])))\\\\d{4}|9[0-57-9]\\\\d{7}\"]\r\n\t\t]\r\n\r\n\t\t// Extended validation must not pass for an invalid phone number\r\n\t\texpect(isValidNumber('961111111', 'UZ')).to.equal(false)\r\n\r\n\t\t// Extended validation must pass for a valid phone number\r\n\t\texpect(isValidNumber('912345678', 'UZ')).to.equal(true)\r\n\r\n\t\tmetadata.countries.UZ[11] = phoneNumberTypePatterns\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// No metadata\r\n\t\tlet thrower = () => _isValidNumber('+78005553535')\r\n\t\texpect(thrower).to.throw('`metadata` argument not passed')\r\n\r\n\t\t// Non-phone-number characters in a phone number\r\n\t\texpect(isValidNumber('+499821958a')).to.equal(false)\r\n\t\texpect(isValidNumber('88005553535x', 'RU')).to.equal(false)\r\n\r\n\t\t// Doesn't have `types` regexps in default metadata.\r\n\t\texpect(isValidNumber({ country: 'UA', phone: '300000000' })).to.equal(true)\r\n\t\texpect(isValidNumber({ country: 'UA', phone: '200000000' })).to.equal(false)\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => isValidNumber(88005553535, 'RU')\r\n\t\texpect(thrower).to.throw(\r\n            'A phone number must either be a string or an object of shape { phone, [country] }.'\r\n        )\r\n\r\n\t\t// Long country phone code\r\n\t\texpect(isValidNumber('+3725555555')).to.equal(true)\r\n\r\n\t\t// Invalid country\r\n\t\tthrower = () => isValidNumber({ phone: '8005553535', country: 'RUS' })\r\n\t\texpect(thrower).to.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should accept phone number extensions', () => {\r\n\t\t// International\r\n\t\texpect(isValidNumber('+12133734253 ext. 123')).to.equal(true)\r\n\t\t// National\r\n\t\texpect(isValidNumber('88005553535 x123', 'RU')).to.equal(true)\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,eAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA+C,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE/C,SAASE,aAAaA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACnCF,UAAU,CAACG,IAAI,CAACC,uBAAQ,CAAC;EACzB,OAAOC,0BAAc,CAACC,KAAK,CAAC,IAAI,EAAEN,UAAU,CAAC;AAC9C;AAEAO,QAAQ,CAAC,eAAe,EAAE,YAAM;EAC/BC,EAAE,CAAC,+BAA+B,EAAE,YAAM;IACzCC,MAAM,CAACb,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACvDF,MAAM,CAACb,aAAa,CAAC,YAAY,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAEnDF,MAAM,CAACb,aAAa,CAAC,iBAAiB,EAAEgB,SAAS,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAElEF,MAAM,CAACb,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC5DF,MAAM,CAACb,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAEvDF,MAAM,CAACb,aAAa,CAAC;MAAEiB,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE5E;IACAF,MAAM,CAACb,aAAa,CAAC,eAAe,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAErDF,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAExD;IACA;IACA;IACA;IACA;IACAF,MAAM,CAACb,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAC1D,CAAC,CAAC;EAEFH,EAAE,CAAC,kGAAkG,EAAE,YAAM;IAC5G;IACAO,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCP,MAAM,CAACb,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAEvD,IAAMM,uBAAuB,GAAGb,uBAAQ,CAACc,SAAS,CAACC,EAAE,CAAC,EAAE,CAAC;;IAEzD;IACA;IACA;IACA;IACAf,uBAAQ,CAACc,SAAS,CAACC,EAAE,CAAC,EAAE,CAAC,GACzB,CACC,CAAC,yWAAyW,CAAC,EAC3W,CAAC,mqCAAmqC,CAAC,CACrqC;;IAED;IACAV,MAAM,CAACb,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;;IAExD;IACAF,MAAM,CAACb,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAEvDP,uBAAQ,CAACc,SAAS,CAACC,EAAE,CAAC,EAAE,CAAC,GAAGF,uBAAuB;EACpD,CAAC,CAAC;EAEFT,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC;IACA,IAAIY,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAAf,0BAAc,EAAC,cAAc,CAAC;IAAA;IAClDI,MAAM,CAACW,OAAO,CAAC,CAACV,EAAE,SAAM,CAAC,gCAAgC,CAAC;;IAE1D;IACAD,MAAM,CAACb,aAAa,CAAC,aAAa,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACpDF,MAAM,CAACb,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;;IAE3D;IACAF,MAAM,CAACb,aAAa,CAAC;MAAEiB,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAY,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3EF,MAAM,CAACb,aAAa,CAAC;MAAEiB,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAY,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;;IAE5E;IACAS,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASxB,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC;IAAA;IAChDa,MAAM,CAACW,OAAO,CAAC,CAACV,EAAE,SAAM,CACd,oFACJ,CAAC;;IAEP;IACAD,MAAM,CAACb,aAAa,CAAC,aAAa,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAEnD;IACAS,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASxB,aAAa,CAAC;QAAEkB,KAAK,EAAE,YAAY;QAAED,OAAO,EAAE;MAAM,CAAC,CAAC;IAAA;IACtEJ,MAAM,CAACW,OAAO,CAAC,CAACV,EAAE,SAAM,CAAC,iBAAiB,CAAC;EAC5C,CAAC,CAAC;EAEFF,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjD;IACAC,MAAM,CAACb,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7D;IACAF,MAAM,CAACb,aAAa,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAC/D,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}