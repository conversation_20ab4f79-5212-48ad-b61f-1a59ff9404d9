{"version": 3, "file": "IsHalfWidth.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsHalfWidth.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,oBAAoB,MAAM,2BAA2B,CAAC;AAE7D,MAAM,CAAC,MAAM,aAAa,GAAG,aAAa,CAAC;AAE3C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,iBAAqC;IAC/D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,aAAa;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YACtD,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,gDAAgD,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isHalfWidthValidator from 'validator/lib/isHalfWidth';\n\nexport const IS_HALF_WIDTH = 'isHalfWidth';\n\n/**\n * Checks if the string contains any half-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function isHalfWidth(value: unknown): boolean {\n  return typeof value === 'string' && isHalfWidthValidator(value);\n}\n\n/**\n * Checks if the string contains any half-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function IsHalfWidth(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_HALF_WIDTH,\n      validator: {\n        validate: (value, args): boolean => isHalfWidth(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain a half-width characters',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}