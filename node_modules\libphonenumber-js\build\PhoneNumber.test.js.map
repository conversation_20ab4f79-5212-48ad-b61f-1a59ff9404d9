{"version": 3, "file": "PhoneNumber.test.js", "names": ["_metadataMin", "_interopRequireDefault", "require", "_PhoneNumber", "e", "__esModule", "describe", "it", "phoneNumber", "PhoneNumber", "metadata", "setExt", "expect", "country", "to", "be", "undefined", "countryCallingCode", "equal", "nationalNumber", "formatNational", "number", "ext", "format", "formatExtension", "extension", "concat", "isEqual", "isNonGeographic", "getPossibleCountries", "deep", "indexOf", "length"], "sources": ["../source/PhoneNumber.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\nimport PhoneNumber from './PhoneNumber.js'\r\n\r\ndescribe('PhoneNumber', () => {\r\n\tit('should create a phone number via a public constructor', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('+78005553535', metadata)\r\n\t\tphoneNumber.setExt('1234')\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('7')\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('8005553535')\r\n\t\texpect(phoneNumber.formatNational()).to.equal('8 (800) 555-35-35 ext. 1234')\r\n\t})\r\n\r\n\tit('should validate constructor arguments (public constructor)', () => {\r\n\t\texpect(() => new PhoneNumber()).to.throw('argument is required')\r\n\t\texpect(() => new PhoneNumber(undefined, metadata)).to.throw('argument is required')\r\n\t\texpect(() => new PhoneNumber('7', metadata)).to.throw('must consist of a \"+\"')\r\n\t\texpect(() => new PhoneNumber('+7', metadata)).to.throw('too short')\r\n\t\texpect(() => new PhoneNumber('+7800')).to.throw('`metadata` argument not passed')\r\n\t\texpect(() => new PhoneNumber(1234567890)).to.throw('must be a string')\r\n\t\texpect(() => new PhoneNumber('+1', 1234567890)).to.throw('must be a string')\r\n\t})\r\n\r\n\tit('should validate constructor arguments (private constructor)', () => {\r\n\t\texpect(() => new PhoneNumber(undefined, '800', metadata)).to.throw('First argument is required')\r\n\t\texpect(() => new PhoneNumber('7', undefined, metadata)).to.throw('`nationalNumber` argument is required')\r\n\t\texpect(() => new PhoneNumber('7', '8005553535')).to.throw('`metadata` argument not passed')\r\n\t})\r\n\r\n\tit('should accept country code argument', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('RU', '8005553535', metadata)\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('7')\r\n\t\texpect(phoneNumber.country).to.equal('RU')\r\n\t\texpect(phoneNumber.number).to.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should format number with options', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('7', '8005553535', metadata)\r\n\t\tphoneNumber.ext = '123'\r\n\t\texpect(phoneNumber.format('NATIONAL', {\r\n\t\t\tformatExtension: (number, extension) => `${number} доб. ${extension}`\r\n\t\t})).to.equal('8 (800) 555-35-35 доб. 123')\r\n\t})\r\n\r\n\tit('should compare phone numbers', () => {\r\n\t\texpect(\r\n            new PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('RU', '8005553535', metadata))\r\n        ).to.equal(true)\r\n\t\texpect(\r\n            new PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('7', '8005553535', metadata))\r\n        ).to.equal(true)\r\n\t\texpect(\r\n            new PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('RU', '8005553536', metadata))\r\n        ).to.equal(false)\r\n\t})\r\n\r\n\tit('should tell if a number is non-geographic', () => {\r\n\t\texpect(new PhoneNumber('7', '8005553535', metadata).isNonGeographic()).to.equal(false)\r\n\t\texpect(new PhoneNumber('870', '773111632', metadata).isNonGeographic()).to.equal(true)\r\n\t})\r\n\r\n\tit('should allow setting extension', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '2133734253', metadata)\r\n\t\tphoneNumber.setExt('1234')\r\n\t\texpect(phoneNumber.ext).to.equal('1234')\r\n\t\texpect(phoneNumber.formatNational()).to.equal('(************* ext. 1234')\r\n\t})\r\n\r\n\tit('should return possible countries', () => {\r\n      // \"599\": [\r\n      //    \"CW\", //  \"possible_lengths\": [7, 8]\r\n      //    \"BQ\" //  \"possible_lengths\": [7]\r\n      // ]\r\n\r\n\t\tlet phoneNumber = new PhoneNumber('599', '123456', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.getPossibleCountries()).to.deep.equal([])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '1234567', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.getPossibleCountries()).to.deep.equal(['CW', 'BQ'])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '12345678', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.getPossibleCountries()).to.deep.equal(['CW'])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '123456789', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.getPossibleCountries()).to.deep.equal([])\r\n\t})\r\n\r\n\tit('should return possible countries in case of ambiguity', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '2223334444', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.getPossibleCountries().indexOf('US')).to.equal(0)\r\n\t\texpect(phoneNumber.getPossibleCountries().length).to.equal(25)\r\n\t})\r\n\r\n\t// it('should return empty possible countries when no national number has been input', () => {\r\n\t// \tconst phoneNumber = new PhoneNumber('1', '', metadata)\r\n\t// \texpect(phoneNumber.country).toBe.undefined\r\n\t// \tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t// })\r\n\r\n\tit('should return empty possible countries when not enough national number digits have been input', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '222', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.getPossibleCountries()).to.deep.equal([])\r\n\t})\r\n\r\n\tit('should return possible countries in case of no ambiguity', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('US', '2133734253', metadata)\r\n\t\texpect(phoneNumber.country).to.equal('US')\r\n\t\texpect(phoneNumber.getPossibleCountries()).to.deep.equal(['US'])\r\n\t})\r\n\r\n\tit('should return empty possible countries in case of an unknown calling code', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('777', '123', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.getPossibleCountries()).to.deep.equal([])\r\n\t})\r\n\r\n\t// it('should validate phone number length', () => {\r\n\t// \tconst phoneNumber = new PhoneNumber('RU', '800', metadata)\r\n\t// \texpect(phoneNumber.validateLength()).to.equal('TOO_SHORT')\r\n\t//\r\n\t// \tconst phoneNumberValid = new PhoneNumber('RU', '8005553535', metadata)\r\n\t// \texpect(phoneNumberValid.validateLength()).toBe.undefined\r\n\t// })\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAA0C,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE1CE,QAAQ,CAAC,aAAa,EAAE,YAAM;EAC7BC,EAAE,CAAC,uDAAuD,EAAE,YAAM;IACjE,IAAMC,WAAW,GAAG,IAAIC,uBAAW,CAAC,cAAc,EAAEC,uBAAQ,CAAC;IAC7DF,WAAW,CAACG,MAAM,CAAC,MAAM,CAAC;IAC1BC,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACC,EAAE,CAACC,SAAS;IAC3CJ,MAAM,CAACJ,WAAW,CAACS,kBAAkB,CAAC,CAACH,EAAE,CAACI,KAAK,CAAC,GAAG,CAAC;IACpDN,MAAM,CAACJ,WAAW,CAACW,cAAc,CAAC,CAACL,EAAE,CAACI,KAAK,CAAC,YAAY,CAAC;IACzDN,MAAM,CAACJ,WAAW,CAACY,cAAc,CAAC,CAAC,CAAC,CAACN,EAAE,CAACI,KAAK,CAAC,6BAA6B,CAAC;EAC7E,CAAC,CAAC;EAEFX,EAAE,CAAC,4DAA4D,EAAE,YAAM;IACtEK,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAAC,CAAC;IAAA,EAAC,CAACK,EAAE,SAAM,CAAC,sBAAsB,CAAC;IAChEF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAACO,SAAS,EAAEN,uBAAQ,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,sBAAsB,CAAC;IACnFF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAAC,GAAG,EAAEC,uBAAQ,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,uBAAuB,CAAC;IAC9EF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAAC,IAAI,EAAEC,uBAAQ,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,WAAW,CAAC;IACnEF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAAC,OAAO,CAAC;IAAA,EAAC,CAACK,EAAE,SAAM,CAAC,gCAAgC,CAAC;IACjFF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAAC,UAAU,CAAC;IAAA,EAAC,CAACK,EAAE,SAAM,CAAC,kBAAkB,CAAC;IACtEF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAAC,IAAI,EAAE,UAAU,CAAC;IAAA,EAAC,CAACK,EAAE,SAAM,CAAC,kBAAkB,CAAC;EAC7E,CAAC,CAAC;EAEFP,EAAE,CAAC,6DAA6D,EAAE,YAAM;IACvEK,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAACO,SAAS,EAAE,KAAK,EAAEN,uBAAQ,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,4BAA4B,CAAC;IAChGF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAAC,GAAG,EAAEO,SAAS,EAAEN,uBAAQ,CAAC;IAAA,EAAC,CAACI,EAAE,SAAM,CAAC,uCAAuC,CAAC;IACzGF,MAAM,CAAC;MAAA,OAAM,IAAIH,uBAAW,CAAC,GAAG,EAAE,YAAY,CAAC;IAAA,EAAC,CAACK,EAAE,SAAM,CAAC,gCAAgC,CAAC;EAC5F,CAAC,CAAC;EAEFP,EAAE,CAAC,qCAAqC,EAAE,YAAM;IAC/C,IAAMC,WAAW,GAAG,IAAIC,uBAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,uBAAQ,CAAC;IACjEE,MAAM,CAACJ,WAAW,CAACS,kBAAkB,CAAC,CAACH,EAAE,CAACI,KAAK,CAAC,GAAG,CAAC;IACpDN,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACI,KAAK,CAAC,IAAI,CAAC;IAC1CN,MAAM,CAACJ,WAAW,CAACa,MAAM,CAAC,CAACP,EAAE,CAACI,KAAK,CAAC,cAAc,CAAC;EACpD,CAAC,CAAC;EAEFX,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7C,IAAMC,WAAW,GAAG,IAAIC,uBAAW,CAAC,GAAG,EAAE,YAAY,EAAEC,uBAAQ,CAAC;IAChEF,WAAW,CAACc,GAAG,GAAG,KAAK;IACvBV,MAAM,CAACJ,WAAW,CAACe,MAAM,CAAC,UAAU,EAAE;MACrCC,eAAe,EAAE,SAAjBA,eAAeA,CAAGH,MAAM,EAAEI,SAAS;QAAA,UAAAC,MAAA,CAAQL,MAAM,2BAAAK,MAAA,CAASD,SAAS;MAAA;IACpE,CAAC,CAAC,CAAC,CAACX,EAAE,CAACI,KAAK,CAAC,4BAA4B,CAAC;EAC3C,CAAC,CAAC;EAEFX,EAAE,CAAC,8BAA8B,EAAE,YAAM;IACxCK,MAAM,CACI,IAAIH,uBAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,uBAAQ,CAAC,CAACiB,OAAO,CAAC,IAAIlB,uBAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,uBAAQ,CAAC,CACvG,CAAC,CAACI,EAAE,CAACI,KAAK,CAAC,IAAI,CAAC;IACtBN,MAAM,CACI,IAAIH,uBAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,uBAAQ,CAAC,CAACiB,OAAO,CAAC,IAAIlB,uBAAW,CAAC,GAAG,EAAE,YAAY,EAAEC,uBAAQ,CAAC,CACtG,CAAC,CAACI,EAAE,CAACI,KAAK,CAAC,IAAI,CAAC;IACtBN,MAAM,CACI,IAAIH,uBAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,uBAAQ,CAAC,CAACiB,OAAO,CAAC,IAAIlB,uBAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,uBAAQ,CAAC,CACvG,CAAC,CAACI,EAAE,CAACI,KAAK,CAAC,KAAK,CAAC;EACxB,CAAC,CAAC;EAEFX,EAAE,CAAC,2CAA2C,EAAE,YAAM;IACrDK,MAAM,CAAC,IAAIH,uBAAW,CAAC,GAAG,EAAE,YAAY,EAAEC,uBAAQ,CAAC,CAACkB,eAAe,CAAC,CAAC,CAAC,CAACd,EAAE,CAACI,KAAK,CAAC,KAAK,CAAC;IACtFN,MAAM,CAAC,IAAIH,uBAAW,CAAC,KAAK,EAAE,WAAW,EAAEC,uBAAQ,CAAC,CAACkB,eAAe,CAAC,CAAC,CAAC,CAACd,EAAE,CAACI,KAAK,CAAC,IAAI,CAAC;EACvF,CAAC,CAAC;EAEFX,EAAE,CAAC,gCAAgC,EAAE,YAAM;IAC1C,IAAMC,WAAW,GAAG,IAAIC,uBAAW,CAAC,GAAG,EAAE,YAAY,EAAEC,uBAAQ,CAAC;IAChEF,WAAW,CAACG,MAAM,CAAC,MAAM,CAAC;IAC1BC,MAAM,CAACJ,WAAW,CAACc,GAAG,CAAC,CAACR,EAAE,CAACI,KAAK,CAAC,MAAM,CAAC;IACxCN,MAAM,CAACJ,WAAW,CAACY,cAAc,CAAC,CAAC,CAAC,CAACN,EAAE,CAACI,KAAK,CAAC,0BAA0B,CAAC;EAC1E,CAAC,CAAC;EAEFX,EAAE,CAAC,kCAAkC,EAAE,YAAM;IACxC;IACA;IACA;IACA;;IAEJ,IAAIC,WAAW,GAAG,IAAIC,uBAAW,CAAC,KAAK,EAAE,QAAQ,EAAEC,uBAAQ,CAAC;IAC5DE,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACC,EAAE,CAACC,SAAS;IAC3CJ,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAAC,CAACf,EAAE,CAACgB,IAAI,CAACZ,KAAK,CAAC,EAAE,CAAC;IAE5DV,WAAW,GAAG,IAAIC,uBAAW,CAAC,KAAK,EAAE,SAAS,EAAEC,uBAAQ,CAAC;IACzDE,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACC,EAAE,CAACC,SAAS;IAC3CJ,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAAC,CAACf,EAAE,CAACgB,IAAI,CAACZ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAEtEV,WAAW,GAAG,IAAIC,uBAAW,CAAC,KAAK,EAAE,UAAU,EAAEC,uBAAQ,CAAC;IAC1DE,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACC,EAAE,CAACC,SAAS;IAC3CJ,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAAC,CAACf,EAAE,CAACgB,IAAI,CAACZ,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;IAEhEV,WAAW,GAAG,IAAIC,uBAAW,CAAC,KAAK,EAAE,WAAW,EAAEC,uBAAQ,CAAC;IAC3DE,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACC,EAAE,CAACC,SAAS;IAC3CJ,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAAC,CAACf,EAAE,CAACgB,IAAI,CAACZ,KAAK,CAAC,EAAE,CAAC;EAC7D,CAAC,CAAC;EAEFX,EAAE,CAAC,uDAAuD,EAAE,YAAM;IACjE,IAAMC,WAAW,GAAG,IAAIC,uBAAW,CAAC,GAAG,EAAE,YAAY,EAAEC,uBAAQ,CAAC;IAChEE,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACC,EAAE,CAACC,SAAS;IAC3CJ,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAACE,OAAO,CAAC,IAAI,CAAC,CAAC,CAACjB,EAAE,CAACI,KAAK,CAAC,CAAC,CAAC;IACpEN,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAACG,MAAM,CAAC,CAAClB,EAAE,CAACI,KAAK,CAAC,EAAE,CAAC;EAC/D,CAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA;;EAEAX,EAAE,CAAC,+FAA+F,EAAE,YAAM;IACzG,IAAMC,WAAW,GAAG,IAAIC,uBAAW,CAAC,GAAG,EAAE,KAAK,EAAEC,uBAAQ,CAAC;IACzDE,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACC,EAAE,CAACC,SAAS;IAC3CJ,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAAC,CAACf,EAAE,CAACgB,IAAI,CAACZ,KAAK,CAAC,EAAE,CAAC;EAC7D,CAAC,CAAC;EAEFX,EAAE,CAAC,0DAA0D,EAAE,YAAM;IACpE,IAAMC,WAAW,GAAG,IAAIC,uBAAW,CAAC,IAAI,EAAE,YAAY,EAAEC,uBAAQ,CAAC;IACjEE,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACI,KAAK,CAAC,IAAI,CAAC;IAC1CN,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAAC,CAACf,EAAE,CAACgB,IAAI,CAACZ,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;EACjE,CAAC,CAAC;EAEFX,EAAE,CAAC,2EAA2E,EAAE,YAAM;IACrF,IAAMC,WAAW,GAAG,IAAIC,uBAAW,CAAC,KAAK,EAAE,KAAK,EAAEC,uBAAQ,CAAC;IAC3DE,MAAM,CAACJ,WAAW,CAACK,OAAO,CAAC,CAACC,EAAE,CAACC,EAAE,CAACC,SAAS;IAC3CJ,MAAM,CAACJ,WAAW,CAACqB,oBAAoB,CAAC,CAAC,CAAC,CAACf,EAAE,CAACgB,IAAI,CAACZ,KAAK,CAAC,EAAE,CAAC;EAC7D,CAAC,CAAC;;EAEF;EACA;EACA;EACA;EACA;EACA;EACA;AACD,CAAC,CAAC", "ignoreList": []}