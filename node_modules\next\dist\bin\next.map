{"version": 3, "sources": ["../../src/bin/next.ts"], "names": ["semver", "lt", "process", "versions", "node", "env", "__NEXT_REQUIRED_NODE_VERSION", "console", "error", "exit", "performance", "mark", "dependency", "require", "resolve", "err", "warn", "MyRootCommand", "Command", "createCommand", "name", "cmd", "addOption", "Option", "hideHelp", "hook", "thisCommand", "cmdName", "defaultEnv", "standardEnv", "NODE_ENV", "isNotStandard", "includes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NON_STANDARD_NODE_ENV", "NEXT_RUNTIME", "getOptionValue", "program", "description", "configureHelp", "formatHelp", "helper", "formatCliHelpOutput", "subcommandTerm", "usage", "helpCommand", "helpOption", "version", "__NEXT_VERSION", "command", "argument", "italic", "option", "choices", "default", "action", "directory", "options", "then", "mod", "nextBuild", "isDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "myParseInt", "_optionValueSources", "portSource", "port", "nextDev", "hidden", "nextExport", "addHelpText", "cyan", "nextInfo", "nextLint", "nextStart", "bold", "addArgument", "Argument", "conflicts", "arg", "nextTelemetry", "parse", "argv"], "mappings": ";;;;;QAEO;2BAEmC;qBAErB;+DACF;4BACgB;qCACC;2BACE;uBACX;;;;;;AAE3B,IACEA,eAAM,CAACC,EAAE,CAACC,QAAQC,QAAQ,CAACC,IAAI,EAAEF,QAAQG,GAAG,CAACC,4BAA4B,GACzE;IACAC,QAAQC,KAAK,CACX,CAAC,sBAAsB,EAAEN,QAAQC,QAAQ,CAACC,IAAI,CAAC,mCAAmC,EAAEF,QAAQG,GAAG,CAACC,4BAA4B,CAAC,aAAa,CAAC;IAE7IJ,QAAQO,IAAI,CAAC;AACf;AAEA,+DAA+D;AAC/DC,YAAYC,IAAI,CAAC;AAEjB,KAAK,MAAMC,cAAc;IAAC;IAAS;CAAY,CAAE;IAC/C,IAAI;QACF,yEAAyE;QACzEC,QAAQC,OAAO,CAACF;IAClB,EAAE,OAAOG,KAAK;QACZR,QAAQS,IAAI,CACV,CAAC,YAAY,EAAEJ,WAAW,4HAA4H,EAAEA,WAAW,CAAC,CAAC;IAEzK;AACF;AAEA,MAAMK,sBAAsBC,kBAAO;IACjCC,cAAcC,IAAY,EAAE;QAC1B,MAAMC,MAAM,IAAIH,kBAAO,CAACE;QAExBC,IAAIC,SAAS,CAAC,IAAIC,iBAAM,CAAC,aAAaC,QAAQ;QAE9CH,IAAII,IAAI,CAAC,aAAa,CAACC;YACrB,MAAMC,UAAUD,YAAYN,IAAI;YAChC,MAAMQ,aAAaD,YAAY,QAAQ,gBAAgB;YACvD,MAAME,cAAc;gBAAC;gBAAc;gBAAe;aAAO;YAEzD,IAAI3B,QAAQG,GAAG,CAACyB,QAAQ,EAAE;gBACxB,MAAMC,gBAAgB,CAACF,YAAYG,QAAQ,CAAC9B,QAAQG,GAAG,CAACyB,QAAQ;gBAChE,MAAMG,qBACJ/B,QAAQG,GAAG,CAACyB,QAAQ,KAAK,gBACrB;oBAAC;oBAAS;iBAAQ,GAClB5B,QAAQG,GAAG,CAACyB,QAAQ,KAAK,eACzB;oBAAC;iBAAM,GACP,EAAE;gBAER,IAAIC,iBAAiBE,mBAAmBD,QAAQ,CAACL,UAAU;oBACzDX,IAAAA,SAAI,EAACkB,gCAAqB;gBAC5B;YACF;YAEEhC,QAAQG,GAAG,CAASyB,QAAQ,GAAG5B,QAAQG,GAAG,CAACyB,QAAQ,IAAIF;YACvD1B,QAAQG,GAAG,CAAS8B,YAAY,GAAG;YAErC,IAAIT,YAAYU,cAAc,CAAC,eAAe,MAAM;gBAClD7B,QAAQC,KAAK,CACX,CAAC,uGAAuG,EAAEmB,QAAQ,CAAC;gBAErHzB,QAAQO,IAAI,CAAC;YACf;QACF;QAEA,OAAOY;IACT;AACF;AAEA,MAAMgB,UAAU,IAAIpB;AAEpBoB,QACGjB,IAAI,CAAC,QACLkB,WAAW,CACV,mFAEDC,aAAa,CAAC;IACbC,YAAY,CAACnB,KAAKoB,SAAWC,IAAAA,wCAAmB,EAACrB,KAAKoB;IACtDE,gBAAgB,CAACtB,MAAQ,CAAC,EAAEA,IAAID,IAAI,GAAG,CAAC,EAAEC,IAAIuB,KAAK,GAAG,CAAC;AACzD,GACCC,WAAW,CAAC,OACZC,UAAU,CAAC,cAAc,0BACzBC,OAAO,CACN,CAAC,SAAS,EAAE7C,QAAQG,GAAG,CAAC2C,cAAc,CAAC,CAAC,EACxC,iBACA;AAGJX,QACGY,OAAO,CAAC,SACRX,WAAW,CACV,gHAEDY,QAAQ,CACP,eACA,CAAC,+CAA+C,EAAEC,IAAAA,kBAAM,EACtD,oEACA,CAAC,EAEJC,MAAM,CAAC,eAAe,wCAEtBA,MAAM,CAAC,aAAa,qBACpBA,MAAM,CAAC,iBAAiB,sBACxBA,MAAM,CAAC,aAAa,2CACpBA,MAAM,CAAC,2BAA2B,kCAClC9B,SAAS,CAAC,IAAIC,iBAAM,CAAC,wBAAwBC,QAAQ,IACrDF,SAAS,CACR,IAAIC,iBAAM,CACR,oCACA,oCAEC8B,OAAO,CAAC;IAAC;IAAW;CAAW,EAC/BC,OAAO,CAAC,YAEZF,MAAM,CACL,qCACA,kEAEDG,MAAM,CAAC,CAACC,WAAWC,UAClB,yEAAyE;IACzE,8BAA8B;IAC9B,MAAM,CAAC,wBAAwBC,IAAI,CAAC,CAACC,MACnCA,IAAIC,SAAS,CAACH,SAASD,WAAWE,IAAI,CAAC,IAAMxD,QAAQO,IAAI,CAAC,MAG7DmC,KAAK,CAAC;AAETP,QACGY,OAAO,CAAC,OAAO;IAAEY,WAAW;AAAK,GACjCvB,WAAW,CACV,0FAEDY,QAAQ,CACP,eACA,CAAC,+CAA+C,EAAEC,IAAAA,kBAAM,EACtD,oEACA,CAAC,EAEJC,MAAM,CAAC,WAAW,mDAClB9B,SAAS,CACR,IAAIC,iBAAM,CACR,qBACA,4DAECuC,SAAS,CAACC,iBAAU,EACpBT,OAAO,CAAC,MACRjD,GAAG,CAAC,SAER+C,MAAM,CACL,6BACA,4EAEDA,MAAM,CACL,wBACA,yEAEDA,MAAM,CAAC,oCAAoC,6BAC3CA,MAAM,CACL,qCACA,qCAEDA,MAAM,CACL,mCACA,+CAEDA,MAAM,CACL,2CACA,0FAEDG,MAAM,CAAC,CAACC,WAAWC,SAAS,EAAEO,mBAAmB,EAAE;IAClD,MAAMC,aAAaD,oBAAoBE,IAAI;IAC3C,MAAM,CAAC,sBAAsBR,IAAI,CAAC,CAACC,MACjCA,IAAIQ,OAAO,CAACV,SAASQ,YAAYT;AAErC,GACCZ,KAAK,CAAC;AAETP,QACGY,OAAO,CAAC,UAAU;IAAEmB,QAAQ;AAAK,GACjCb,MAAM,CAAC,IAAM,MAAM,CAAC,yBAAyBG,IAAI,CAAC,CAACC,MAAQA,IAAIU,UAAU,KACzEvB,UAAU,CAAC;AAEdT,QACGY,OAAO,CAAC,QACRX,WAAW,CACV,8FAEDgC,WAAW,CACV,SACA,CAAC,cAAc,EAAEC,IAAAA,gBAAI,EAAC,kDAAkD,CAAC,EAE1EnB,MAAM,CAAC,aAAa,kDACpBG,MAAM,CAAC,CAACE,UACP,MAAM,CAAC,uBAAuBC,IAAI,CAAC,CAACC,MAAQA,IAAIa,QAAQ,CAACf;AAG7DpB,QACGY,OAAO,CAAC,QACRX,WAAW,CACV,mOAEDY,QAAQ,CACP,eACA,CAAC,mDAAmD,EAAEC,IAAAA,kBAAM,EAC1D,oEACA,CAAC,EAEJC,MAAM,CACL,wBACA,qDAEDA,MAAM,CAAC,sBAAsB,0CAC7B9B,SAAS,CACR,IAAIC,iBAAM,CACR,oBACA,uCACA+B,OAAO,CAAC;IAAC;IAAO;IAAQ;IAAQ;IAAQ;IAAO;IAAQ;IAAQ;CAAO,GAEzEF,MAAM,CACL,0BACA,6EAEDA,MAAM,CACL,yCACA,8DAEDA,MAAM,CACL,YACA,2EAEDA,MAAM,CACL,6BACA,iDAEDA,MAAM,CAAC,SAAS,qCAChBA,MAAM,CACL,wBACA,4EAEDA,MAAM,CAAC,wBAAwB,6BAC/BA,MAAM,CAAC,eAAe,wCACtBA,MAAM,CAAC,WAAW,wBAClB9B,SAAS,CACR,IAAIC,iBAAM,CACR,gCACA,0EAECuC,SAAS,CAACC,iBAAU,EACpBT,OAAO,CAAC,CAAC,IAEbF,MAAM,CACL,mCACA,sCAEDA,MAAM,CAAC,0BAA0B,kCACjCA,MAAM,CACL,sBACA,oDAED9B,SAAS,CACR,IAAIC,iBAAM,CACR,uDACA,gEACA8B,OAAO,CAAC;IAAC;IAAS;IAAO;CAAO,GAEnCD,MAAM,CAAC,cAAc,qBACrBA,MAAM,CAAC,qCAAqC,iCAC5C9B,SAAS,CACR,IAAIC,iBAAM,CACR,qCACA,uEACA+B,OAAO,CAAC,aAEXF,MAAM,CACL,gCACA,wDAEDG,MAAM,CAAC,CAACC,WAAWC,UAClB,MAAM,CAAC,uBAAuBC,IAAI,CAAC,CAACC,MAClCA,IAAIc,QAAQ,CAAChB,SAASD,aAGzBZ,KAAK,CAAC;AAETP,QACGY,OAAO,CAAC,SACRX,WAAW,CACV,kGAEDY,QAAQ,CACP,eACA,CAAC,+CAA+C,EAAEC,IAAAA,kBAAM,EACtD,oEACA,CAAC,EAEJ7B,SAAS,CACR,IAAIC,iBAAM,CACR,qBACA,4DAECuC,SAAS,CAACC,iBAAU,EACpBT,OAAO,CAAC,MACRjD,GAAG,CAAC,SAER+C,MAAM,CACL,6BACA,4EAED9B,SAAS,CACR,IAAIC,iBAAM,CACR,yCACA,2FACAuC,SAAS,CAACC,iBAAU,GAEvBR,MAAM,CAAC,CAACC,WAAWC,UAClB,MAAM,CAAC,wBAAwBC,IAAI,CAAC,CAACC,MACnCA,IAAIe,SAAS,CAACjB,SAASD,aAG1BZ,KAAK,CAAC;AAETP,QACGY,OAAO,CAAC,aACRX,WAAW,CACV,CAAC,yCAAyC,EAAEqC,IAAAA,gBAAI,EAC9C,wBACA,sBAAsB,CAAC,EAE1BC,WAAW,CAAC,IAAIC,mBAAQ,CAAC,SAASxB,OAAO,CAAC;IAAC;IAAW;IAAU;CAAS,GACzEiB,WAAW,CAAC,SAAS,CAAC,cAAc,EAAEC,IAAAA,gBAAI,EAAC,gCAAgC,CAAC,EAC5EjD,SAAS,CACR,IAAIC,iBAAM,CAAC,YAAY,CAAC,sCAAsC,CAAC,EAAEuD,SAAS,CACxE,YAGH1B,MAAM,CAAC,aAAa,CAAC,uCAAuC,CAAC,EAC7DG,MAAM,CAAC,CAACwB,KAAKtB,UACZ,MAAM,CAAC,4BAA4BC,IAAI,CAAC,CAACC,MACvCA,IAAIqB,aAAa,CAACvB,SAASsB,OAG9BnC,KAAK,CAAC;AAETP,QAAQ4C,KAAK,CAAC/E,QAAQgF,IAAI"}