{"version": 3, "sources": ["../../src/lib/generate-interception-routes-rewrites.ts"], "names": ["pathToRegexp", "NEXT_URL", "INTERCEPTION_ROUTE_MARKERS", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "toPathToRegexpPath", "path", "replace", "_", "capture", "startsWith", "slice", "voidParamsBeforeInterceptionMarker", "newPath", "foundInterceptionMarker", "segment", "split", "find", "marker", "push", "join", "generateInterceptionRoutesRewrites", "appPaths", "basePath", "rewrites", "appPath", "interceptingRoute", "interceptedRoute", "normalizedInterceptingRoute", "normalizedInterceptedRoute", "normalizedAppPath", "interceptingRouteRegex", "toString", "source", "destination", "has", "type", "key", "value", "isInterceptionRouteRewrite", "route"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oCAAmC;AAChE,SAASC,QAAQ,QAAQ,0CAAyC;AAClE,SACEC,0BAA0B,EAC1BC,mCAAmC,EACnCC,0BAA0B,QACrB,+CAA8C;AAGrD,iIAAiI;AACjI,SAASC,mBAAmBC,IAAY;IACtC,OAAOA,KAAKC,OAAO,CAAC,uBAAuB,CAACC,GAAGC;QAC7C,4EAA4E;QAC5E,IAAIA,QAAQC,UAAU,CAAC,QAAQ;YAC7B,OAAO,CAAC,CAAC,EAAED,QAAQE,KAAK,CAAC,GAAG,CAAC,CAAC;QAChC;QACA,OAAO,MAAMF;IACf;AACF;AAEA,gFAAgF;AAChF,yEAAyE;AACzE,2EAA2E;AAC3E,0EAA0E;AAC1E,SAASG,mCAAmCN,IAAY;IACtD,IAAIO,UAAU,EAAE;IAEhB,IAAIC,0BAA0B;IAC9B,KAAK,MAAMC,WAAWT,KAAKU,KAAK,CAAC,KAAM;QACrC,IACEd,2BAA2Be,IAAI,CAAC,CAACC,SAAWH,QAAQL,UAAU,CAACQ,UAC/D;YACAJ,0BAA0B;QAC5B;QAEA,IAAIC,QAAQL,UAAU,CAAC,QAAQ,CAACI,yBAAyB;YACvDD,QAAQM,IAAI,CAAC;QACf,OAAO;YACLN,QAAQM,IAAI,CAACJ;QACf;IACF;IAEA,OAAOF,QAAQO,IAAI,CAAC;AACtB;AAEA,OAAO,SAASC,mCACdC,QAAkB,EAClBC,WAAW,EAAE;IAEb,MAAMC,WAAsB,EAAE;IAE9B,KAAK,MAAMC,WAAWH,SAAU;QAC9B,IAAIlB,2BAA2BqB,UAAU;YACvC,MAAM,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAE,GAC3CxB,oCAAoCsB;YAEtC,MAAMG,8BAA8B,CAAC,EACnCF,sBAAsB,MAAMrB,mBAAmBqB,qBAAqB,GACrE,MAAM,CAAC;YAER,MAAMG,6BAA6BxB,mBAAmBsB;YACtD,MAAMG,oBAAoBlB,mCACxBP,mBAAmBoB;YAGrB,qEAAqE;YACrE,4DAA4D;YAC5D,4CAA4C;YAC5C,IAAIM,yBAAyB/B,aAAa4B,6BACvCI,QAAQ,GACRrB,KAAK,CAAC,GAAG,CAAC;YAEba,SAASL,IAAI,CAAC;gBACZc,QAAQ,CAAC,EAAEV,SAAS,EAAEM,2BAA2B,CAAC;gBAClDK,aAAa,CAAC,EAAEX,SAAS,EAAEO,kBAAkB,CAAC;gBAC9CK,KAAK;oBACH;wBACEC,MAAM;wBACNC,KAAKpC;wBACLqC,OAAOP;oBACT;iBACD;YACH;QACF;IACF;IAEA,OAAOP;AACT;AAEA,OAAO,SAASe,2BAA2BC,KAAc;QAEhDA,aAAAA;IADP,0HAA0H;IAC1H,OAAOA,EAAAA,aAAAA,MAAML,GAAG,sBAATK,cAAAA,UAAW,CAAC,EAAE,qBAAdA,YAAgBH,GAAG,MAAKpC;AACjC"}