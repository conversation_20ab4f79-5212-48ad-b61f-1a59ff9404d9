/*
 React
 react-server-dom-webpack-client.node.unbundled.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var n=require("util"),q=require("react-dom"),r={stream:!0};function t(a,b){var d=a[b[0]];if(a=d[b[2]])d=a.name;else{a=d["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');d=b[2]}return{specifier:a.specifier,name:d,async:4===b.length}}var v=new Map;
function w(a){var b=v.get(a.specifier);if(b)return"fulfilled"===b.status?null:b;var d=import(a.specifier);a.async&&(d=d.then(function(c){return c.default}));d.then(function(c){var e=d;e.status="fulfilled";e.value=c},function(c){var e=d;e.status="rejected";e.reason=c});v.set(a.specifier,d);return d}
function x(a,b,d){if(null!==a)for(var c=1;c<b.length;c+=2){var e=d,g=y.current;if(g){var f=g.preinitScript,l=a.prefix+b[c];var k=a.crossOrigin;k="string"===typeof k?"use-credentials"===k?k:"":void 0;f.call(g,l,{crossOrigin:k,nonce:e})}}}var y=q.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,A=Symbol.for("react.element"),B=Symbol.for("react.lazy"),aa=Symbol.for("react.postpone"),C=Symbol.iterator;
function ba(a){if(null===a||"object"!==typeof a)return null;a=C&&a[C]||a["@@iterator"];return"function"===typeof a?a:null}var ca=Array.isArray,D=Object.getPrototypeOf,da=Object.prototype,F=new WeakMap;function ea(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function fa(a,b,d,c){function e(k,h){if(null===h)return null;if("object"===typeof h){if("function"===typeof h.then){null===l&&(l=new FormData);f++;var u=g++;h.then(function(p){p=JSON.stringify(p,e);var z=l;z.append(b+u,p);f--;0===f&&d(z)},function(p){c(p)});return"$@"+u.toString(16)}if(ca(h))return h;if(h instanceof FormData){null===l&&(l=new FormData);var E=l;k=g++;var m=b+k+"_";h.forEach(function(p,z){E.append(m+z,p)});return"$K"+k.toString(16)}if(h instanceof Map)return h=JSON.stringify(Array.from(h),
e),null===l&&(l=new FormData),k=g++,l.append(b+k,h),"$Q"+k.toString(16);if(h instanceof Set)return h=JSON.stringify(Array.from(h),e),null===l&&(l=new FormData),k=g++,l.append(b+k,h),"$W"+k.toString(16);if(ba(h))return Array.from(h);k=D(h);if(k!==da&&(null===k||null!==D(k)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return h}if("string"===typeof h){if("Z"===h[h.length-1]&&this[k]instanceof Date)return"$D"+h;
h="$"===h[0]?"$"+h:h;return h}if("boolean"===typeof h)return h;if("number"===typeof h)return ea(h);if("undefined"===typeof h)return"$undefined";if("function"===typeof h){h=F.get(h);if(void 0!==h)return h=JSON.stringify(h,e),null===l&&(l=new FormData),k=g++,l.set(b+k,h),"$F"+k.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof h){k=h.description;if(Symbol.for(k)!==h)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(h.description+") cannot be found among global symbols."));return"$S"+k}if("bigint"===typeof h)return"$n"+h.toString(10);throw Error("Type "+typeof h+" is not supported as an argument to a Server Function.");}var g=1,f=0,l=null;a=JSON.stringify(a,e);null===l?d(a):(l.set(b+"0",a),0===f&&d(l))}var G=new WeakMap;
function ha(a){var b,d,c=new Promise(function(e,g){b=e;d=g});fa(a,"",function(e){if("string"===typeof e){var g=new FormData;g.append("0",e);e=g}c.status="fulfilled";c.value=e;b(e)},function(e){c.status="rejected";c.reason=e;d(e)});return c}
function ia(a){var b=F.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var d=null;if(null!==b.bound){d=G.get(b);d||(d=ha(b),G.set(b,d));if("rejected"===d.status)throw d.reason;if("fulfilled"!==d.status)throw d;b=d.value;var c=new FormData;b.forEach(function(e,g){c.append("$ACTION_"+a+":"+g,e)});d=c;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:d}}
function H(a,b){var d=F.get(this);if(!d)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(d.id!==a)return!1;var c=d.bound;if(null===c)return 0===b;switch(c.status){case "fulfilled":return c.value.length===b;case "pending":throw c;case "rejected":throw c.reason;default:throw"string"!==typeof c.status&&(c.status="pending",c.then(function(e){c.status="fulfilled";c.value=e},function(e){c.status="rejected";c.reason=e})),c;}}
function I(a,b,d){Object.defineProperties(a,{$$FORM_ACTION:{value:void 0===d?ia:function(){var c=F.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var e=c.bound;null===e&&(e=Promise.resolve([]));return d(c.id,e)}},$$IS_SIGNATURE_EQUAL:{value:H},bind:{value:J}});F.set(a,b)}var ja=Function.prototype.bind,ka=Array.prototype.slice;
function J(){var a=ja.apply(this,arguments),b=F.get(this);if(b){var d=ka.call(arguments,1),c=null;c=null!==b.bound?Promise.resolve(b.bound).then(function(e){return e.concat(d)}):Promise.resolve(d);Object.defineProperties(a,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:H},bind:{value:J}});F.set(a,{id:b.id,bound:c})}return a}function la(a,b,d){function c(){var e=Array.prototype.slice.call(arguments);return b(a,e)}I(c,{id:a,bound:null},d);return c}
function K(a,b,d,c){this.status=a;this.value=b;this.reason=d;this._response=c}K.prototype=Object.create(Promise.prototype);K.prototype.then=function(a,b){switch(this.status){case "resolved_model":L(this);break;case "resolved_module":M(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function ma(a){switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function N(a,b){for(var d=0;d<a.length;d++)(0,a[d])(b)}function O(a,b,d){switch(a.status){case "fulfilled":N(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=d;break;case "rejected":d&&N(d,a.reason)}}
function P(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.reason;a.status="rejected";a.reason=b;null!==d&&N(d,b)}}function Q(a,b){if("pending"===a.status||"blocked"===a.status){var d=a.value,c=a.reason;a.status="resolved_module";a.value=b;null!==d&&(M(a),O(a,d,c))}}var R=null,S=null;
function L(a){var b=R,d=S;R=a;S=null;var c=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var e=JSON.parse(c,a._response._fromJSON);if(null!==S&&0<S.deps)S.value=e,a.status="blocked",a.value=null,a.reason=null;else{var g=a.value;a.status="fulfilled";a.value=e;null!==g&&N(g,e)}}catch(f){a.status="rejected",a.reason=f}finally{R=b,S=d}}
function M(a){try{var b=a.value,d=v.get(b.specifier);if("fulfilled"===d.status)var c=d.value;else throw d.reason;var e="*"===b.name?c:""===b.name?c.default:c[b.name];a.status="fulfilled";a.value=e}catch(g){a.status="rejected",a.reason=g}}function T(a,b){a._chunks.forEach(function(d){"pending"===d.status&&P(d,b)})}function U(a,b){var d=a._chunks,c=d.get(b);c||(c=new K("pending",null,null,a),d.set(b,c));return c}
function na(a,b,d,c){if(S){var e=S;c||e.deps++}else e=S={deps:c?0:1,value:null};return function(g){b[d]=g;e.deps--;0===e.deps&&"blocked"===a.status&&(g=a.value,a.status="fulfilled",a.value=e.value,null!==g&&N(g,e.value))}}function oa(a){return function(b){return P(a,b)}}
function pa(a,b){function d(){var e=Array.prototype.slice.call(arguments),g=b.bound;return g?"fulfilled"===g.status?c(b.id,g.value.concat(e)):Promise.resolve(g).then(function(f){return c(b.id,f.concat(e))}):c(b.id,e)}var c=a._callServer;I(d,b,a._encodeFormAction);return d}function V(a,b){a=U(a,b);switch(a.status){case "resolved_model":L(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function qa(a,b,d,c){if("$"===c[0]){if("$"===c)return A;switch(c[1]){case "$":return c.slice(1);case "L":return b=parseInt(c.slice(2),16),a=U(a,b),{$$typeof:B,_payload:a,_init:ma};case "@":if(2===c.length)return new Promise(function(){});b=parseInt(c.slice(2),16);return U(a,b);case "S":return Symbol.for(c.slice(2));case "F":return b=parseInt(c.slice(2),16),b=V(a,b),pa(a,b);case "Q":return b=parseInt(c.slice(2),16),a=V(a,b),new Map(a);case "W":return b=parseInt(c.slice(2),16),a=V(a,b),new Set(a);case "I":return Infinity;
case "-":return"$-0"===c?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(c.slice(2)));case "n":return BigInt(c.slice(2));default:c=parseInt(c.slice(1),16);a=U(a,c);switch(a.status){case "resolved_model":L(a);break;case "resolved_module":M(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":return c=R,a.then(na(c,b,d,"cyclic"===a.status),oa(c)),null;default:throw a.reason;}}}return c}
function ra(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function sa(a,b,d,c,e){var g=new Map;a={_bundlerConfig:a,_moduleLoading:b,_callServer:void 0!==d?d:ra,_encodeFormAction:c,_nonce:e,_chunks:g,_stringDecoder:new n.TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};a._fromJSON=ta(a);return a}function W(a,b,d){a._chunks.set(b,new K("fulfilled",d,null,a))}
function ua(a,b,d){var c=a._chunks,e=c.get(b);d=JSON.parse(d,a._fromJSON);var g=t(a._bundlerConfig,d);x(a._moduleLoading,d[1],a._nonce);if(d=w(g)){if(e){var f=e;f.status="blocked"}else f=new K("blocked",null,null,a),c.set(b,f);d.then(function(){return Q(f,g)},function(l){return P(f,l)})}else e?Q(e,g):c.set(b,new K("resolved_module",g,null,a))}
function X(a,b){for(var d=a.length,c=b.length,e=0;e<d;e++)c+=a[e].byteLength;c=new Uint8Array(c);for(var g=e=0;g<d;g++){var f=a[g];c.set(f,e);e+=f.byteLength}c.set(b,e);return c}function Y(a,b,d,c,e,g){d=0===d.length&&0===c.byteOffset%g?c:X(d,c);e=new e(d.buffer,d.byteOffset,d.byteLength/g);W(a,b,e)}
function va(a,b,d,c,e){switch(d){case 65:W(a,b,X(c,e).buffer);return;case 67:Y(a,b,c,e,Int8Array,1);return;case 99:W(a,b,0===c.length?e:X(c,e));return;case 85:Y(a,b,c,e,Uint8ClampedArray,1);return;case 83:Y(a,b,c,e,Int16Array,2);return;case 115:Y(a,b,c,e,Uint16Array,2);return;case 76:Y(a,b,c,e,Int32Array,4);return;case 108:Y(a,b,c,e,Uint32Array,4);return;case 70:Y(a,b,c,e,Float32Array,4);return;case 100:Y(a,b,c,e,Float64Array,8);return;case 78:Y(a,b,c,e,BigInt64Array,8);return;case 109:Y(a,b,c,e,
BigUint64Array,8);return;case 86:Y(a,b,c,e,DataView,1);return}for(var g=a._stringDecoder,f="",l=0;l<c.length;l++)f+=g.decode(c[l],r);f+=g.decode(e);switch(d){case 73:ua(a,b,f);break;case 72:b=f[0];f=f.slice(1);a=JSON.parse(f,a._fromJSON);if(f=y.current)switch(b){case "D":f.prefetchDNS(a);break;case "C":"string"===typeof a?f.preconnect(a):f.preconnect(a[0],a[1]);break;case "L":b=a[0];d=a[1];3===a.length?f.preload(b,d,a[2]):f.preload(b,d);break;case "m":"string"===typeof a?f.preloadModule(a):f.preloadModule(a[0],
a[1]);break;case "S":"string"===typeof a?f.preinitStyle(a):f.preinitStyle(a[0],0===a[1]?void 0:a[1],3===a.length?a[2]:void 0);break;case "X":"string"===typeof a?f.preinitScript(a):f.preinitScript(a[0],a[1]);break;case "M":"string"===typeof a?f.preinitModuleScript(a):f.preinitModuleScript(a[0],a[1])}break;case 69:d=JSON.parse(f).digest;f=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
f.stack="Error: "+f.message;f.digest=d;d=a._chunks;(c=d.get(b))?P(c,f):d.set(b,new K("rejected",null,f,a));break;case 84:a._chunks.set(b,new K("fulfilled",f,null,a));break;case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");case 80:f=Error("A Server Component was postponed. The reason is omitted in production builds to avoid leaking sensitive details.");
f.$$typeof=aa;f.stack="Error: "+f.message;d=a._chunks;(c=d.get(b))?P(c,f):d.set(b,new K("rejected",null,f,a));break;default:c=a._chunks,(d=c.get(b))?"pending"===d.status&&(a=d.value,b=d.reason,d.status="resolved_model",d.value=f,null!==a&&(L(d),O(d,a,b))):c.set(b,new K("resolved_model",f,null,a))}}function ta(a){return function(b,d){return"string"===typeof d?qa(a,this,b,d):"object"===typeof d&&null!==d?(b=d[0]===A?{$$typeof:A,type:d[1],key:d[2],ref:null,props:d[3],_owner:null}:d,b):d}}
function Z(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
exports.createFromNodeStream=function(a,b,d){var c=sa(b.moduleMap,b.moduleLoading,Z,d?d.encodeFormAction:void 0,d&&"string"===typeof d.nonce?d.nonce:void 0);a.on("data",function(e){for(var g=0,f=c._rowState,l=c._rowID,k=c._rowTag,h=c._rowLength,u=c._buffer,E=e.length;g<E;){var m=-1;switch(f){case 0:m=e[g++];58===m?f=1:l=l<<4|(96<m?m-87:m-48);continue;case 1:f=e[g];84===f||65===f||67===f||99===f||85===f||83===f||115===f||76===f||108===f||70===f||100===f||78===f||109===f||86===f?(k=f,f=2,g++):64<f&&
91>f?(k=f,f=3,g++):(k=0,f=3);continue;case 2:m=e[g++];44===m?f=4:h=h<<4|(96<m?m-87:m-48);continue;case 3:m=e.indexOf(10,g);break;case 4:m=g+h,m>e.length&&(m=-1)}var p=e.byteOffset+g;if(-1<m)h=new Uint8Array(e.buffer,p,m-g),va(c,l,k,u,h),g=m,3===f&&g++,h=l=k=f=0,u.length=0;else{e=new Uint8Array(e.buffer,p,e.byteLength-g);u.push(e);h-=e.byteLength;break}}c._rowState=f;c._rowID=l;c._rowTag=k;c._rowLength=h});a.on("error",function(e){T(c,e)});a.on("end",function(){T(c,Error("Connection closed."))});return U(c,
0)};exports.createServerReference=function(a){return la(a,Z)};

//# sourceMappingURL=react-server-dom-webpack-client.node.unbundled.production.min.js.map
