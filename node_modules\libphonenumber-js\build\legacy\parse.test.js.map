{"version": 3, "file": "parse.test.js", "names": ["_metadataMin", "_interopRequireDefault", "require", "_parse", "_metadata", "e", "__esModule", "parseNumber", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "metadata", "_parseNumber", "apply", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "describe", "it", "expect", "to", "deep", "equal", "defaultCountry", "country", "phone", "extended", "countryCallingCode", "carrierCode", "undefined", "ext", "valid", "possible", "thrower", "defaultCallingCode", "be", "v2"], "sources": ["../../source/legacy/parse.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' with { type: 'json' }\r\nimport _parseNumber from './parse.js'\r\nimport Metadata from '../metadata.js'\r\n\r\nfunction parseNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _parseNumber.apply(this, parameters)\r\n}\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\ndescribe('parse', () => {\r\n\tit('should not parse invalid phone numbers', () => {\r\n\t\t// Too short.\r\n\t\texpect(parseNumber('+7 (800) 55-35-35')).to.deep.equal({})\r\n\t\t// Too long.\r\n\t\texpect(parseNumber('+7 (800) 55-35-35-55')).to.deep.equal({})\r\n\r\n\t\texpect(parseNumber('+7 (800) 55-35-35', 'US')).to.deep.equal({})\r\n\t\texpect(parseNumber('(800) 55 35 35', { defaultCountry: 'RU' })).to.deep.equal({})\r\n\t\texpect(parseNumber('****** 215 5230', 'US')).to.deep.equal({})\r\n\r\n\t\texpect(parseNumber('911231231', 'BE')).to.deep.equal({})\r\n\t})\r\n\r\n\tit('should parse valid phone numbers', () => {\r\n\t\t// Instant loans\r\n\t\t// https://www.youtube.com/watch?v=6e1pMrYH5jI\r\n\t\t//\r\n\t\t// Restrict to RU\r\n\t\texpect(parseNumber('Phone: 8 (800) 555 35 35.', 'RU')).to.deep.equal({ country: 'RU', phone: '8005553535' })\r\n\t\t// International format\r\n\t\texpect(parseNumber('Phone: +7 (800) 555-35-35.')).to.deep.equal({ country: 'RU', phone: '8005553535' })\r\n\t\t// // Restrict to US, but not a US country phone code supplied\r\n\t\t// parseNumber('+7 (800) 555-35-35', 'US').should.deep.equal({})\r\n\t\t// Restrict to RU\r\n\t\texpect(parseNumber('(800) 555 35 35', 'RU')).to.deep.equal({ country: 'RU', phone: '8005553535' })\r\n\t\t// Default to RU\r\n\t\texpect(parseNumber('8 (800) 555 35 35', { defaultCountry: 'RU' })).to.deep.equal({ country: 'RU', phone: '8005553535' })\r\n\r\n\t\t// Gangster partyline\r\n\t\texpect(parseNumber('******-373-4253')).to.deep.equal({ country: 'US', phone: '2133734253' })\r\n\r\n\t\t// Switzerland (just in case)\r\n\t\texpect(parseNumber('044 668 18 00', 'CH')).to.deep.equal({ country: 'CH', phone: '446681800' })\r\n\r\n\t\t// China, Beijing\r\n\t\texpect(parseNumber('010-852644821', 'CN')).to.deep.equal({ country: 'CN', phone: '10852644821' })\r\n\r\n\t\t// France\r\n\t\texpect(parseNumber('+33169454850')).to.deep.equal({ country: 'FR', phone: '169454850' })\r\n\r\n\t\t// UK (Jersey)\r\n\t\texpect(parseNumber('+44 7700 300000')).to.deep.equal({ country: 'JE', phone: '7700300000' })\r\n\r\n\t\t// KZ\r\n\t\texpect(parseNumber('****** 211 1111')).to.deep.equal({ country: 'KZ', phone: '7022111111' })\r\n\r\n\t\t// Brazil\r\n\t\texpect(parseNumber('11987654321', 'BR')).to.deep.equal({ country: 'BR', phone: '11987654321' })\r\n\r\n\t\t// Long country phone code.\r\n\t\texpect(parseNumber('+212659777777')).to.deep.equal({ country: 'MA', phone: '659777777' })\r\n\r\n\t\t// No country could be derived.\r\n\t\t// parseNumber('+212569887076').should.deep.equal({ countryPhoneCode: '212', phone: '569887076' })\r\n\r\n\t\t// GB. Moible numbers starting 07624* are Isle of Man.\r\n\t\texpect(parseNumber('07624369230', 'GB')).to.deep.equal({ country: 'IM', phone: '7624369230' })\r\n\t})\r\n\r\n\tit('should parse possible numbers', () => {\r\n\t\t// Invalid phone number for a given country.\r\n\t\texpect(parseNumber('1112223344', 'RU', { extended: true })).to.deep.equal({\r\n\t\t\tcountry            : 'RU',\r\n\t\t\tcountryCallingCode : '7',\r\n\t\t\tphone              : '1112223344',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\r\n\t\t// International phone number.\r\n\t\t// Several countries with the same country phone code.\r\n\t\texpect(parseNumber('+71112223344')).to.deep.equal({})\r\n\t\texpect(parseNumber('+71112223344', { extended: true })).to.deep.equal({\r\n\t\t\tcountry            : undefined,\r\n\t\t\tcountryCallingCode : '7',\r\n\t\t\tphone              : '1112223344',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\r\n\t\t// International phone number.\r\n\t\t// Single country with the given country phone code.\r\n\t\texpect(parseNumber('+33011222333', { extended: true })).to.deep.equal({\r\n\t\t\tcountry            : 'FR',\r\n\t\t\tcountryCallingCode : '33',\r\n\t\t\tphone              : '011222333',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\r\n\t\t// Too short.\r\n\t\t// Won't strip national prefix `8` because otherwise the number would be too short.\r\n\t\texpect(parseNumber('+7 (800) 55-35-35', { extended: true })).to.deep.equal({\r\n\t\t\tcountry            : undefined,\r\n\t\t\tcountryCallingCode : '7',\r\n\t\t\tphone              : '800553535',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : false\r\n\t\t})\r\n\r\n\t\t// Too long.\r\n\t\texpect(parseNumber('****** 37342530', { extended: true })).to.deep.equal({\r\n\t\t\tcountry            : undefined,\r\n\t\t\tcountryCallingCode : '1',\r\n\t\t\tphone              : '21337342530',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : false,\r\n\t\t\tpossible           : false\r\n\t\t})\r\n\r\n\t\t// No national number to be parsed.\r\n\t\texpect(parseNumber('+996', { extended: true })).to.deep.equal({\r\n\t\t\t// countryCallingCode : '996'\r\n\t\t})\r\n\r\n\t\t// Valid number.\r\n\t\texpect(parseNumber('+78005553535', { extended: true })).to.deep.equal({\r\n\t\t\tcountry            : 'RU',\r\n\t\t\tcountryCallingCode : '7',\r\n\t\t\tphone              : '8005553535',\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : true,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/211\r\n\t\texpect(parseNumber('+966', { extended: true })).to.deep.equal({})\r\n\t\texpect(parseNumber('+9664', { extended: true })).to.deep.equal({})\r\n\t\texpect(parseNumber('+96645', { extended: true })).to.deep.equal({\r\n\t\t\tcarrierCode        : undefined,\r\n\t\t\tphone              : '45',\r\n\t\t\text                : undefined,\r\n\t\t\tcountry            : 'SA',\r\n\t\t\tcountryCallingCode : '966',\r\n\t\t\tpossible           : false,\r\n\t\t\tvalid              : false\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse non-European digits', () => {\r\n\t\texpect(parseNumber('+١٢١٢٢٣٢٣٢٣٢')).to.deep.equal({ country: 'US', phone: '2122323232' })\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\tlet thrower\r\n\r\n\t\t// No input\r\n\t\texpect(parseNumber('')).to.deep.equal({})\r\n\r\n\t\t// No country phone code\r\n\t\texpect(parseNumber('+')).to.deep.equal({})\r\n\r\n\t\t// No country at all (non international number and no explicit country code)\r\n\t\texpect(parseNumber('123')).to.deep.equal({})\r\n\r\n\t\t// No country metadata for this `require` country code\r\n\t\tthrower = () => parseNumber('123', 'ZZ')\r\n\t\texpect(thrower).to.throw('Unknown country')\r\n\r\n\t\t// No country metadata for this `default` country code\r\n\t\tthrower = () => parseNumber('123', { defaultCountry: 'ZZ' })\r\n\t\texpect(thrower).to.throw('Unknown country')\r\n\r\n\t\t// Invalid country phone code\r\n\t\texpect(parseNumber('+210')).to.deep.equal({})\r\n\r\n\t\t// Invalid country phone code (extended parsing mode)\r\n\t\texpect(parseNumber('+210', { extended: true })).to.deep.equal({})\r\n\r\n\t\t// Too short of a number.\r\n\t\texpect(parseNumber('1', 'US', { extended: true })).to.deep.equal({})\r\n\r\n\t\t// Too long of a number.\r\n\t\texpect(parseNumber('1111111111111111111', 'RU', { extended: true })).to.deep.equal({})\r\n\r\n\t\t// Not a number.\r\n\t\texpect(parseNumber('abcdefg', 'US', { extended: true })).to.deep.equal({})\r\n\r\n\t\t// Country phone code beginning with a '0'\r\n\t\texpect(parseNumber('+0123')).to.deep.equal({})\r\n\r\n\t\t// Barbados NANPA phone number\r\n\t\texpect(parseNumber('+12460000000')).to.deep.equal({ country: 'BB', phone: '2460000000' })\r\n\r\n\t\t// // A case when country (restricted to) is not equal\r\n\t\t// // to the one parsed out of an international number.\r\n\t\t// parseNumber('******-373-4253', 'RU').should.deep.equal({})\r\n\r\n\t\t// National (significant) number too short\r\n\t\texpect(parseNumber('2', 'US')).to.deep.equal({})\r\n\r\n\t\t// National (significant) number too long\r\n\t\texpect(parseNumber('222222222222222222', 'US')).to.deep.equal({})\r\n\r\n\t\t// No `national_prefix_for_parsing`\r\n\t\texpect(parseNumber('41111', 'AC')).to.deep.equal({ country: 'AC', phone: '41111'})\r\n\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/235\r\n\t\t// `matchesEntirely()` bug fix.\r\n\t\texpect(parseNumber('+4915784846111‬')).to.deep.equal({ country: 'DE', phone: '15784846111' })\r\n\r\n\t\t// No metadata\r\n\t\tthrower = () => _parseNumber('')\r\n\t\texpect(thrower).to.throw('`metadata` argument not passed')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => parseNumber(2141111111, 'US')\r\n\t\texpect(thrower).to.throw('A text for parsing must be a string.')\r\n\r\n\t\t// Input string too long.\r\n\t\texpect(\r\n            parseNumber('8005553535                                                                                                                                                                                                                                                 ', 'RU')\r\n        ).to.deep.equal({})\r\n\t})\r\n\r\n\tit('should parse phone number extensions', () => {\r\n\t\t// \"ext\"\r\n\t\texpect(parseNumber('2134567890 ext 123', 'US')).to.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2134567890',\r\n\t\t\text     : '123'\r\n\t\t})\r\n\r\n\t\t// \"ext.\"\r\n\t\texpect(parseNumber('+12134567890 ext. 12345', 'US')).to.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2134567890',\r\n\t\t\text     : '12345'\r\n\t\t})\r\n\r\n\t\t// \"доб.\"\r\n\t\texpect(parseNumber('+78005553535 доб. 1234', 'RU')).to.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\text     : '1234'\r\n\t\t})\r\n\r\n\t\t// \"#\"\r\n\t\texpect(parseNumber('+12134567890#1234')).to.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2134567890',\r\n\t\t\text     : '1234'\r\n\t\t})\r\n\r\n\t\t// \"x\"\r\n\t\texpect(parseNumber('+78005553535 x1234')).to.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\text     : '1234'\r\n\t\t})\r\n\r\n\t\t// Not a valid extension\r\n\t\texpect(parseNumber('2134567890 ext. abc', 'US')).to.deep.equal({\r\n\t\t\tcountry : 'US',\r\n\t\t\tphone   : '2134567890'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse RFC 3966 phone numbers', () => {\r\n\t\texpect(parseNumber('tel:+78005553535;ext=123')).to.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\text     : '123'\r\n\t\t})\r\n\r\n\t\t// Should parse \"visual separators\".\r\n\t\texpect(parseNumber('tel:+7(800)555-35.35;ext=123')).to.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535',\r\n\t\t\text     : '123'\r\n\t\t})\r\n\r\n\t\t// Invalid number.\r\n\t\texpect(parseNumber('tel:+7x8005553535;ext=123')).to.deep.equal({})\r\n\t})\r\n\r\n\tit('should parse invalid international numbers even if they are invalid', () => {\r\n\t\texpect(parseNumber('+7(8)8005553535', 'RU')).to.deep.equal({\r\n\t\t\tcountry : 'RU',\r\n\t\t\tphone   : '8005553535'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse carrier codes', () => {\r\n\t\texpect(parseNumber('0 15 21 5555-5555', 'BR', { extended: true })).to.deep.equal({\r\n\t\t\tcountry            : 'BR',\r\n\t\t\tcountryCallingCode : '55',\r\n\t\t\tphone              : '2155555555',\r\n\t\t\tcarrierCode        : '15',\r\n\t\t\text                : undefined,\r\n\t\t\tvalid              : true,\r\n\t\t\tpossible           : true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse IDD prefixes', () => {\r\n\t\texpect(parseNumber('011 61 2 3456 7890', 'US')).to.deep.equal({\r\n\t\t\tphone   : '234567890',\r\n\t\t\tcountry : 'AU'\r\n\t\t})\r\n\r\n\t\texpect(parseNumber('011 61 2 3456 7890', 'FR')).to.deep.equal({})\r\n\r\n\t\texpect(parseNumber('00 61 2 3456 7890', 'US')).to.deep.equal({})\r\n\r\n\t\texpect(parseNumber('810 61 2 3456 7890', 'RU')).to.deep.equal({\r\n\t\t\tphone   : '234567890',\r\n\t\t\tcountry : 'AU'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should work with v2 API', () => {\r\n\t\tparseNumber('+99989160151539')\r\n\t})\r\n\r\n\tit('should work with Argentina numbers', () => {\r\n\t\t// The same mobile number is written differently\r\n\t\t// in different formats in Argentina:\r\n\t\t// `9` gets prepended in international format.\r\n\t\texpect(parseNumber('+54 9 3435 55 1212')).to.deep.equal({\r\n\t\t\tcountry: 'AR',\r\n\t\t\tphone: '93435551212'\r\n\t\t})\r\n\t\texpect(parseNumber('0343 15-555-1212', 'AR')).to.deep.equal({\r\n\t\t\tcountry: 'AR',\r\n\t\t\tphone: '93435551212'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should work with Mexico numbers', () => {\r\n\t\t// Fixed line.\r\n\t\texpect(parseNumber('+52 ************')).to.deep.equal({\r\n\t\t\tcountry: 'MX',\r\n\t\t\tphone: '4499780001'\r\n\t\t})\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t//\r\n\t\t// parseNumber('01 (449)978-0001', 'MX').should.deep.equal({\r\n\t\t// \tcountry: 'MX',\r\n\t\t// \tphone: '4499780001'\r\n\t\t// })\r\n\t\texpect(parseNumber('(449)978-0001', 'MX')).to.deep.equal({\r\n\t\t\tcountry: 'MX',\r\n\t\t\tphone: '4499780001'\r\n\t\t})\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t//\r\n\t\t// // Mobile.\r\n\t\t// // `1` is prepended before area code to mobile numbers in international format.\r\n\t\t// parseNumber('+52 1 33 1234-5678', 'MX').should.deep.equal({\r\n\t\t// \tcountry: 'MX',\r\n\t\t// \tphone: '3312345678'\r\n\t\t// })\r\n\t\texpect(parseNumber('+52 33 1234-5678', 'MX')).to.deep.equal({\r\n\t\t\tcountry: 'MX',\r\n\t\t\tphone: '3312345678'\r\n\t\t})\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t//\r\n\t\t// parseNumber('044 (33) 1234-5678', 'MX').should.deep.equal({\r\n\t\t// \tcountry: 'MX',\r\n\t\t// \tphone: '3312345678'\r\n\t\t// })\r\n\t\t// parseNumber('045 33 1234-5678', 'MX').should.deep.equal({\r\n\t\t// \tcountry: 'MX',\r\n\t\t// \tphone: '3312345678'\r\n\t\t// })\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers', () => {\r\n\t\texpect(parseNumber('+870773111632')).to.deep.equal(USE_NON_GEOGRAPHIC_COUNTRY_CODE ?\r\n        {\r\n            country: '001',\r\n            phone: '773111632'\r\n        } :\r\n        {})\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers (default country code)', () => {\r\n\t\texpect(parseNumber('773111632', { defaultCallingCode: '870' })).to.deep.equal(USE_NON_GEOGRAPHIC_COUNTRY_CODE ?\r\n        {\r\n            country: '001',\r\n            phone: '773111632'\r\n        } :\r\n        {})\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers (extended)', () => {\r\n\t\texpect(parseNumber('+870773111632', { extended: true })).to.deep.equal({\r\n\t\t\tcountry: USE_NON_GEOGRAPHIC_COUNTRY_CODE ? '001' : undefined,\r\n\t\t\tcountryCallingCode: '870',\r\n\t\t\tphone: '773111632',\r\n\t\t\tcarrierCode: undefined,\r\n\t\t\text: undefined,\r\n\t\t\tpossible: true,\r\n\t\t\tvalid: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers (default country code) (extended)', () => {\r\n\t\texpect(parseNumber('773111632', { defaultCallingCode: '870', extended: true })).to.deep.equal({\r\n\t\t\tcountry: USE_NON_GEOGRAPHIC_COUNTRY_CODE ? '001' : undefined,\r\n\t\t\tcountryCallingCode: '870',\r\n\t\t\tphone: '773111632',\r\n\t\t\tcarrierCode: undefined,\r\n\t\t\text: undefined,\r\n\t\t\tpossible: true,\r\n\t\t\tvalid: true\r\n\t\t})\r\n\t})\r\n\r\n\tit('shouldn\\'t crash when invalid `defaultCallingCode` is passed', () => {\r\n\t\texpect(() => parseNumber('773111632', { defaultCallingCode: '999' })).to.throw('Unknown calling code')\r\n\t})\r\n\r\n\tit('shouldn\\'t set `country` when there\\'s no `defaultCountry` and `defaultCallingCode` is not of a \"non-geographic entity\"', () => {\r\n\t\texpect(parseNumber('88005553535', { defaultCallingCode: '7' })).to.deep.equal({\r\n\t\t\tcountry: 'RU',\r\n\t\t\tphone: '8005553535'\r\n\t\t})\r\n\t})\r\n\r\n\tit('should correctly parse numbers starting with the same digit as the national prefix', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/373\r\n\t\t// `BY`'s `national_prefix` is `8`.\r\n\t\texpect(parseNumber('+37582004910060')).to.deep.equal({\r\n\t\t\tcountry: 'BY',\r\n\t\t\tphone: '82004910060'\r\n\t\t});\r\n\t})\r\n\r\n\tit('should autocorrect numbers without a leading +', () => {\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/376\r\n\t\texpect(parseNumber('375447521111', 'BY')).to.deep.equal({\r\n\t\t\tcountry: 'BY',\r\n\t\t\tphone: '447521111'\r\n\t\t});\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/316\r\n\t\texpect(parseNumber('33612902554', 'FR')).to.deep.equal({\r\n\t\t\tcountry: 'FR',\r\n\t\t\tphone: '612902554'\r\n\t\t});\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/375\r\n\t\texpect(parseNumber('61438331999', 'AU')).to.deep.equal({\r\n\t\t\tcountry: 'AU',\r\n\t\t\tphone: '438331999'\r\n\t\t});\r\n\t\t// A case when `49` is a country calling code of a number without a leading `+`.\r\n\t\texpect(parseNumber('4930123456', 'DE')).to.deep.equal({\r\n\t\t\tcountry: 'DE',\r\n\t\t\tphone: '30123456'\r\n\t\t});\r\n\t\t// A case when `49` is a valid area code.\r\n\t\texpect(parseNumber('4951234567890', 'DE')).to.deep.equal({\r\n\t\t\tcountry: 'DE',\r\n\t\t\tphone: '4951234567890'\r\n\t\t});\r\n\t})\r\n\r\n\tit('should parse extensions (long extensions with explicitl abels)', () => {\r\n\t\t// Test lower and upper limits of extension lengths for each type of label.\r\n\r\n\t\t// Firstly, when in RFC format: PhoneNumberUtil.extLimitAfterExplicitLabel\r\n\t\texpect(parseNumber('33316005 ext 0', 'NZ').ext).to.equal('0')\r\n\t\texpect(parseNumber('33316005 ext 01234567890123456789', 'NZ').ext).to.equal('01234567890123456789')\r\n\t\t// Extension too long.\r\n\t\texpect(parseNumber('33316005 ext 012345678901234567890', 'NZ').ext).to.be.undefined\r\n\r\n\t\t// Explicit extension label.\r\n\t\texpect(parseNumber('03 3316005ext:1', 'NZ').ext).to.equal('1')\r\n\t\texpect(parseNumber('03 3316005 xtn:12345678901234567890', 'NZ').ext).to.equal('12345678901234567890')\r\n\t\texpect(parseNumber('03 3316005 extension\\t12345678901234567890', 'NZ').ext).to.equal('12345678901234567890')\r\n\t\texpect(parseNumber('03 3316005 xtensio:12345678901234567890', 'NZ').ext).to.equal('12345678901234567890')\r\n\t\texpect(parseNumber('03 3316005 xtensión, 12345678901234567890#', 'NZ').ext).to.equal('12345678901234567890')\r\n\t\texpect(parseNumber('03 3316005extension.12345678901234567890', 'NZ').ext).to.equal('12345678901234567890')\r\n\t\texpect(parseNumber('03 3316005 доб:12345678901234567890', 'NZ').ext).to.equal('12345678901234567890')\r\n\r\n\t\t// Extension too long.\r\n\t\texpect(parseNumber('03 3316005 extension 123456789012345678901', 'NZ').ext).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse extensions (long extensions with auto dialling labels)', () => {\r\n\t\texpect(parseNumber('+12679000000,,123456789012345#').ext).to.equal('123456789012345')\r\n\t\texpect(parseNumber('+12679000000;123456789012345#').ext).to.equal('123456789012345')\r\n\t\texpect(parseNumber('+442034000000,,123456789#').ext).to.equal('123456789')\r\n\t\t// Extension too long.\r\n\t\texpect(parseNumber('+12679000000,,1234567890123456#').ext).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse extensions (short extensions with ambiguous characters)', () => {\r\n\t\texpect(parseNumber('03 3316005 x 123456789', 'NZ').ext).to.equal('123456789')\r\n\t\texpect(parseNumber('03 3316005 x. 123456789', 'NZ').ext).to.equal('123456789')\r\n\t\texpect(parseNumber('03 3316005 #123456789#', 'NZ').ext).to.equal('123456789')\r\n\t\texpect(parseNumber('03 3316005 ~ 123456789', 'NZ').ext).to.equal('123456789')\r\n\t\t// Extension too long.\r\n\t\texpect(parseNumber('03 3316005 ~ 1234567890', 'NZ').ext).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse extensions (short extensions when not sure of label)', () => {\r\n\t\texpect(parseNumber('+1123-456-7890 666666#', { v2: true }).ext).to.equal('666666')\r\n\t\texpect(parseNumber('+11234567890-6#', { v2: true }).ext).to.equal('6')\r\n\t\t// Extension too long.\r\n\t\texpect(() => parseNumber('+1123-456-7890 7777777#', { v2: true })).to.throw('NOT_A_NUMBER')\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAqC,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAErC,SAASE,WAAWA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACjCF,UAAU,CAACG,IAAI,CAACC,uBAAQ,CAAC;EACzB,OAAOC,iBAAY,CAACC,KAAK,CAAC,IAAI,EAAEN,UAAU,CAAC;AAC5C;AAEA,IAAMO,+BAA+B,GAAG,KAAK;AAE7CC,QAAQ,CAAC,OAAO,EAAE,YAAM;EACvBC,EAAE,CAAC,wCAAwC,EAAE,YAAM;IAClD;IACAC,MAAM,CAACd,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1D;IACAH,MAAM,CAACd,WAAW,CAAC,sBAAsB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE7DH,MAAM,CAACd,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChEH,MAAM,CAACd,WAAW,CAAC,gBAAgB,EAAE;MAAEkB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjFH,MAAM,CAACd,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE9DH,MAAM,CAACd,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACzD,CAAC,CAAC;EAEFJ,EAAE,CAAC,kCAAkC,EAAE,YAAM;IAC5C;IACA;IACA;IACA;IACAC,MAAM,CAACd,WAAW,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;IAC5G;IACAN,MAAM,CAACd,WAAW,CAAC,4BAA4B,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;IACvG;IACA;IACA;IACAN,MAAM,CAACd,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;IAClG;IACAN,MAAM,CAACd,WAAW,CAAC,mBAAmB,EAAE;MAAEkB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;;IAExH;IACAN,MAAM,CAACd,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;;IAE5F;IACAN,MAAM,CAACd,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAY,CAAC,CAAC;;IAE/F;IACAN,MAAM,CAACd,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAc,CAAC,CAAC;;IAEjG;IACAN,MAAM,CAACd,WAAW,CAAC,cAAc,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAY,CAAC,CAAC;;IAExF;IACAN,MAAM,CAACd,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;;IAE5F;IACAN,MAAM,CAACd,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;;IAE5F;IACAN,MAAM,CAACd,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAc,CAAC,CAAC;;IAE/F;IACAN,MAAM,CAACd,WAAW,CAAC,eAAe,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAY,CAAC,CAAC;;IAEzF;IACA;;IAEA;IACAN,MAAM,CAACd,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;EAC/F,CAAC,CAAC;EAEFP,EAAE,CAAC,+BAA+B,EAAE,YAAM;IACzC;IACAC,MAAM,CAACd,WAAW,CAAC,YAAY,EAAE,IAAI,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACzEE,OAAO,EAAc,IAAI;MACzBG,kBAAkB,EAAG,GAAG;MACxBF,KAAK,EAAgB,YAAY;MACjCG,WAAW,EAAUC,SAAS;MAC9BC,GAAG,EAAkBD,SAAS;MAC9BE,KAAK,EAAgB,KAAK;MAC1BC,QAAQ,EAAa;IACtB,CAAC,CAAC;;IAEF;IACA;IACAb,MAAM,CAACd,WAAW,CAAC,cAAc,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACrDH,MAAM,CAACd,WAAW,CAAC,cAAc,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACrEE,OAAO,EAAcK,SAAS;MAC9BF,kBAAkB,EAAG,GAAG;MACxBF,KAAK,EAAgB,YAAY;MACjCG,WAAW,EAAUC,SAAS;MAC9BC,GAAG,EAAkBD,SAAS;MAC9BE,KAAK,EAAgB,KAAK;MAC1BC,QAAQ,EAAa;IACtB,CAAC,CAAC;;IAEF;IACA;IACAb,MAAM,CAACd,WAAW,CAAC,cAAc,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACrEE,OAAO,EAAc,IAAI;MACzBG,kBAAkB,EAAG,IAAI;MACzBF,KAAK,EAAgB,WAAW;MAChCG,WAAW,EAAUC,SAAS;MAC9BC,GAAG,EAAkBD,SAAS;MAC9BE,KAAK,EAAgB,KAAK;MAC1BC,QAAQ,EAAa;IACtB,CAAC,CAAC;;IAEF;IACA;IACAb,MAAM,CAACd,WAAW,CAAC,mBAAmB,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC1EE,OAAO,EAAcK,SAAS;MAC9BF,kBAAkB,EAAG,GAAG;MACxBF,KAAK,EAAgB,WAAW;MAChCG,WAAW,EAAUC,SAAS;MAC9BC,GAAG,EAAkBD,SAAS;MAC9BE,KAAK,EAAgB,KAAK;MAC1BC,QAAQ,EAAa;IACtB,CAAC,CAAC;;IAEF;IACAb,MAAM,CAACd,WAAW,CAAC,iBAAiB,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACxEE,OAAO,EAAcK,SAAS;MAC9BF,kBAAkB,EAAG,GAAG;MACxBF,KAAK,EAAgB,aAAa;MAClCG,WAAW,EAAUC,SAAS;MAC9BC,GAAG,EAAkBD,SAAS;MAC9BE,KAAK,EAAgB,KAAK;MAC1BC,QAAQ,EAAa;IACtB,CAAC,CAAC;;IAEF;IACAb,MAAM,CAACd,WAAW,CAAC,MAAM,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC7D;IAAA,CACA,CAAC;;IAEF;IACAH,MAAM,CAACd,WAAW,CAAC,cAAc,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACrEE,OAAO,EAAc,IAAI;MACzBG,kBAAkB,EAAG,GAAG;MACxBF,KAAK,EAAgB,YAAY;MACjCG,WAAW,EAAUC,SAAS;MAC9BC,GAAG,EAAkBD,SAAS;MAC9BE,KAAK,EAAgB,IAAI;MACzBC,QAAQ,EAAa;IACtB,CAAC,CAAC;;IAEF;IACAb,MAAM,CAACd,WAAW,CAAC,MAAM,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjEH,MAAM,CAACd,WAAW,CAAC,OAAO,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAClEH,MAAM,CAACd,WAAW,CAAC,QAAQ,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC/DM,WAAW,EAAUC,SAAS;MAC9BJ,KAAK,EAAgB,IAAI;MACzBK,GAAG,EAAkBD,SAAS;MAC9BL,OAAO,EAAc,IAAI;MACzBG,kBAAkB,EAAG,KAAK;MAC1BK,QAAQ,EAAa,KAAK;MAC1BD,KAAK,EAAgB;IACtB,CAAC,CAAC;EACH,CAAC,CAAC;EAEFb,EAAE,CAAC,kCAAkC,EAAE,YAAM;IAC5CC,MAAM,CAACd,WAAW,CAAC,cAAc,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;EAC1F,CAAC,CAAC;EAEFP,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC,IAAIe,OAAO;;IAEX;IACAd,MAAM,CAACd,WAAW,CAAC,EAAE,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEzC;IACAH,MAAM,CAACd,WAAW,CAAC,GAAG,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE1C;IACAH,MAAM,CAACd,WAAW,CAAC,KAAK,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE5C;IACAW,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS5B,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;IAAA;IACxCc,MAAM,CAACc,OAAO,CAAC,CAACb,EAAE,SAAM,CAAC,iBAAiB,CAAC;;IAE3C;IACAa,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS5B,WAAW,CAAC,KAAK,EAAE;QAAEkB,cAAc,EAAE;MAAK,CAAC,CAAC;IAAA;IAC5DJ,MAAM,CAACc,OAAO,CAAC,CAACb,EAAE,SAAM,CAAC,iBAAiB,CAAC;;IAE3C;IACAD,MAAM,CAACd,WAAW,CAAC,MAAM,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE7C;IACAH,MAAM,CAACd,WAAW,CAAC,MAAM,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEjE;IACAH,MAAM,CAACd,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEpE;IACAH,MAAM,CAACd,WAAW,CAAC,qBAAqB,EAAE,IAAI,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEtF;IACAH,MAAM,CAACd,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE1E;IACAH,MAAM,CAACd,WAAW,CAAC,OAAO,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAE9C;IACAH,MAAM,CAACd,WAAW,CAAC,cAAc,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC;;IAEzF;IACA;IACA;;IAEA;IACAN,MAAM,CAACd,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEhD;IACAH,MAAM,CAACd,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;;IAEjE;IACAH,MAAM,CAACd,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAO,CAAC,CAAC;;IAElF;IACA;IACAN,MAAM,CAACd,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAAEE,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAc,CAAC,CAAC;;IAE7F;IACAQ,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAAnB,iBAAY,EAAC,EAAE,CAAC;IAAA;IAChCK,MAAM,CAACc,OAAO,CAAC,CAACb,EAAE,SAAM,CAAC,gCAAgC,CAAC;;IAE1D;IACAa,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS5B,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC;IAAA;IAC7Cc,MAAM,CAACc,OAAO,CAAC,CAACb,EAAE,SAAM,CAAC,sCAAsC,CAAC;;IAEhE;IACAD,MAAM,CACId,WAAW,CAAC,6PAA6P,EAAE,IAAI,CACnR,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC;EAEFJ,EAAE,CAAC,sCAAsC,EAAE,YAAM;IAChD;IACAC,MAAM,CAACd,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC7DE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtBK,GAAG,EAAO;IACX,CAAC,CAAC;;IAEF;IACAX,MAAM,CAACd,WAAW,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAClEE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtBK,GAAG,EAAO;IACX,CAAC,CAAC;;IAEF;IACAX,MAAM,CAACd,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACjEE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtBK,GAAG,EAAO;IACX,CAAC,CAAC;;IAEF;IACAX,MAAM,CAACd,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACtDE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtBK,GAAG,EAAO;IACX,CAAC,CAAC;;IAEF;IACAX,MAAM,CAACd,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACvDE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtBK,GAAG,EAAO;IACX,CAAC,CAAC;;IAEF;IACAX,MAAM,CAACd,WAAW,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC9DE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFP,EAAE,CAAC,qCAAqC,EAAE,YAAM;IAC/CC,MAAM,CAACd,WAAW,CAAC,0BAA0B,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC7DE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtBK,GAAG,EAAO;IACX,CAAC,CAAC;;IAEF;IACAX,MAAM,CAACd,WAAW,CAAC,8BAA8B,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACjEE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK,YAAY;MACtBK,GAAG,EAAO;IACX,CAAC,CAAC;;IAEF;IACAX,MAAM,CAACd,WAAW,CAAC,2BAA2B,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACnE,CAAC,CAAC;EAEFJ,EAAE,CAAC,qEAAqE,EAAE,YAAM;IAC/EC,MAAM,CAACd,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC1DE,OAAO,EAAG,IAAI;MACdC,KAAK,EAAK;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFP,EAAE,CAAC,4BAA4B,EAAE,YAAM;IACtCC,MAAM,CAACd,WAAW,CAAC,mBAAmB,EAAE,IAAI,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAChFE,OAAO,EAAc,IAAI;MACzBG,kBAAkB,EAAG,IAAI;MACzBF,KAAK,EAAgB,YAAY;MACjCG,WAAW,EAAU,IAAI;MACzBE,GAAG,EAAkBD,SAAS;MAC9BE,KAAK,EAAgB,IAAI;MACzBC,QAAQ,EAAa;IACtB,CAAC,CAAC;EACH,CAAC,CAAC;EAEFd,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrCC,MAAM,CAACd,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC7DG,KAAK,EAAK,WAAW;MACrBD,OAAO,EAAG;IACX,CAAC,CAAC;IAEFL,MAAM,CAACd,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEjEH,MAAM,CAACd,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEhEH,MAAM,CAACd,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC7DG,KAAK,EAAK,WAAW;MACrBD,OAAO,EAAG;IACX,CAAC,CAAC;EACH,CAAC,CAAC;EAEFN,EAAE,CAAC,yBAAyB,EAAE,YAAM;IACnCb,WAAW,CAAC,iBAAiB,CAAC;EAC/B,CAAC,CAAC;EAEFa,EAAE,CAAC,oCAAoC,EAAE,YAAM;IAC9C;IACA;IACA;IACAC,MAAM,CAACd,WAAW,CAAC,oBAAoB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACvDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;IACFN,MAAM,CAACd,WAAW,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC3DE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC,CAAC;EAEFP,EAAE,CAAC,iCAAiC,EAAE,YAAM;IAC3C;IACAC,MAAM,CAACd,WAAW,CAAC,kBAAkB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACrDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACAN,MAAM,CAACd,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACxDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAN,MAAM,CAACd,WAAW,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC3DE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD,CAAC,CAAC;EAEFP,EAAE,CAAC,0DAA0D,EAAE,YAAM;IACpEC,MAAM,CAACd,WAAW,CAAC,eAAe,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAACN,+BAA+B,GAC5E;MACIQ,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACX,CAAC,GACD,CAAC,CAAC,CAAC;EACV,CAAC,CAAC;EAEFP,EAAE,CAAC,iFAAiF,EAAE,YAAM;IAC3FC,MAAM,CAACd,WAAW,CAAC,WAAW,EAAE;MAAE6B,kBAAkB,EAAE;IAAM,CAAC,CAAC,CAAC,CAACd,EAAE,CAACC,IAAI,CAACC,KAAK,CAACN,+BAA+B,GACvG;MACIQ,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACX,CAAC,GACD,CAAC,CAAC,CAAC;EACV,CAAC,CAAC;EAEFP,EAAE,CAAC,qEAAqE,EAAE,YAAM;IAC/EC,MAAM,CAACd,WAAW,CAAC,eAAe,EAAE;MAAEqB,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACtEE,OAAO,EAAER,+BAA+B,GAAG,KAAK,GAAGa,SAAS;MAC5DF,kBAAkB,EAAE,KAAK;MACzBF,KAAK,EAAE,WAAW;MAClBG,WAAW,EAAEC,SAAS;MACtBC,GAAG,EAAED,SAAS;MACdG,QAAQ,EAAE,IAAI;MACdD,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC,CAAC;EAEFb,EAAE,CAAC,4FAA4F,EAAE,YAAM;IACtGC,MAAM,CAACd,WAAW,CAAC,WAAW,EAAE;MAAE6B,kBAAkB,EAAE,KAAK;MAAER,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC7FE,OAAO,EAAER,+BAA+B,GAAG,KAAK,GAAGa,SAAS;MAC5DF,kBAAkB,EAAE,KAAK;MACzBF,KAAK,EAAE,WAAW;MAClBG,WAAW,EAAEC,SAAS;MACtBC,GAAG,EAAED,SAAS;MACdG,QAAQ,EAAE,IAAI;MACdD,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC,CAAC;EAEFb,EAAE,CAAC,8DAA8D,EAAE,YAAM;IACxEC,MAAM,CAAC;MAAA,OAAMd,WAAW,CAAC,WAAW,EAAE;QAAE6B,kBAAkB,EAAE;MAAM,CAAC,CAAC;IAAA,EAAC,CAACd,EAAE,SAAM,CAAC,sBAAsB,CAAC;EACvG,CAAC,CAAC;EAEFF,EAAE,CAAC,yHAAyH,EAAE,YAAM;IACnIC,MAAM,CAACd,WAAW,CAAC,aAAa,EAAE;MAAE6B,kBAAkB,EAAE;IAAI,CAAC,CAAC,CAAC,CAACd,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MAC7EE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC,CAAC;EAEFP,EAAE,CAAC,oFAAoF,EAAE,YAAM;IAC9F;IACA;IACAC,MAAM,CAACd,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACpDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC,CAAC;EAEFP,EAAE,CAAC,gDAAgD,EAAE,YAAM;IAC1D;IACAC,MAAM,CAACd,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACvDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;IACF;IACAN,MAAM,CAACd,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACtDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;IACF;IACAN,MAAM,CAACd,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACtDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;IACF;IACAN,MAAM,CAACd,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACrDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;IACF;IACAN,MAAM,CAACd,WAAW,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAACe,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC;MACxDE,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE;IACR,CAAC,CAAC;EACH,CAAC,CAAC;EAEFP,EAAE,CAAC,gEAAgE,EAAE,YAAM;IAC1E;;IAEA;IACAC,MAAM,CAACd,WAAW,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;IAC7DH,MAAM,CAACd,WAAW,CAAC,mCAAmC,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,sBAAsB,CAAC;IACnG;IACAH,MAAM,CAACd,WAAW,CAAC,oCAAoC,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACe,EAAE,CAACN,SAAS;;IAEnF;IACAV,MAAM,CAACd,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;IAC9DH,MAAM,CAACd,WAAW,CAAC,qCAAqC,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,sBAAsB,CAAC;IACrGH,MAAM,CAACd,WAAW,CAAC,4CAA4C,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,sBAAsB,CAAC;IAC5GH,MAAM,CAACd,WAAW,CAAC,yCAAyC,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,sBAAsB,CAAC;IACzGH,MAAM,CAACd,WAAW,CAAC,4CAA4C,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,sBAAsB,CAAC;IAC5GH,MAAM,CAACd,WAAW,CAAC,0CAA0C,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,sBAAsB,CAAC;IAC1GH,MAAM,CAACd,WAAW,CAAC,qCAAqC,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,sBAAsB,CAAC;;IAErG;IACAH,MAAM,CAACd,WAAW,CAAC,4CAA4C,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACe,EAAE,CAACN,SAAS;EAC5F,CAAC,CAAC;EAEFX,EAAE,CAAC,qEAAqE,EAAE,YAAM;IAC/EC,MAAM,CAACd,WAAW,CAAC,gCAAgC,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,iBAAiB,CAAC;IACrFH,MAAM,CAACd,WAAW,CAAC,+BAA+B,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,iBAAiB,CAAC;IACpFH,MAAM,CAACd,WAAW,CAAC,2BAA2B,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,WAAW,CAAC;IAC1E;IACAH,MAAM,CAACd,WAAW,CAAC,iCAAiC,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACe,EAAE,CAACN,SAAS;EAC3E,CAAC,CAAC;EAEFX,EAAE,CAAC,sEAAsE,EAAE,YAAM;IAChFC,MAAM,CAACd,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,WAAW,CAAC;IAC7EH,MAAM,CAACd,WAAW,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,WAAW,CAAC;IAC9EH,MAAM,CAACd,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,WAAW,CAAC;IAC7EH,MAAM,CAACd,WAAW,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,WAAW,CAAC;IAC7E;IACAH,MAAM,CAACd,WAAW,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAACyB,GAAG,CAAC,CAACV,EAAE,CAACe,EAAE,CAACN,SAAS;EACzE,CAAC,CAAC;EAEFX,EAAE,CAAC,mEAAmE,EAAE,YAAM;IAC7EC,MAAM,CAACd,WAAW,CAAC,wBAAwB,EAAE;MAAE+B,EAAE,EAAE;IAAK,CAAC,CAAC,CAACN,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,QAAQ,CAAC;IAClFH,MAAM,CAACd,WAAW,CAAC,iBAAiB,EAAE;MAAE+B,EAAE,EAAE;IAAK,CAAC,CAAC,CAACN,GAAG,CAAC,CAACV,EAAE,CAACE,KAAK,CAAC,GAAG,CAAC;IACtE;IACAH,MAAM,CAAC;MAAA,OAAMd,WAAW,CAAC,yBAAyB,EAAE;QAAE+B,EAAE,EAAE;MAAK,CAAC,CAAC;IAAA,EAAC,CAAChB,EAAE,SAAM,CAAC,cAAc,CAAC;EAC5F,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}