{"version": 3, "file": "parsePhoneNumber_.js", "names": ["parsePhoneNumberWithError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSupportedCountry", "parsePhoneNumber", "text", "options", "metadata", "defaultCountry", "_objectSpread", "undefined", "error"], "sources": ["../source/parsePhoneNumber_.js"], "sourcesContent": ["import parsePhoneNumberWithError from './parsePhoneNumberWithError_.js'\r\nimport ParseError from './ParseError.js'\r\nimport { isSupportedCountry } from './metadata.js'\r\n\r\nexport default function parsePhoneNumber(text, options, metadata) {\r\n\t// Validate `defaultCountry`.\r\n\tif (options && options.defaultCountry && !isSupportedCountry(options.defaultCountry, metadata)) {\r\n\t\toptions = {\r\n\t\t\t...options,\r\n\t\t\tdefaultCountry: undefined\r\n\t\t}\r\n\t}\r\n\t// Parse phone number.\r\n\ttry {\r\n\t\treturn parsePhoneNumberWithError(text, options, metadata)\r\n\t} catch (error) {\r\n\t\t/* istanbul ignore else */\r\n\t\tif (error instanceof ParseError) {\r\n\t\t\t//\r\n\t\t} else {\r\n\t\t\tthrow error\r\n\t\t}\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;AAAA,OAAOA,yBAAyB,MAAM,iCAAiC;AACvE,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,kBAAkB,QAAQ,eAAe;AAElD,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACjE;EACA,IAAID,OAAO,IAAIA,OAAO,CAACE,cAAc,IAAI,CAACL,kBAAkB,CAACG,OAAO,CAACE,cAAc,EAAED,QAAQ,CAAC,EAAE;IAC/FD,OAAO,GAAAG,aAAA,CAAAA,aAAA,KACHH,OAAO;MACVE,cAAc,EAAEE;IAAS,EACzB;EACF;EACA;EACA,IAAI;IACH,OAAOT,yBAAyB,CAACI,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC1D,CAAC,CAAC,OAAOI,KAAK,EAAE;IACf;IACA,IAAIA,KAAK,YAAYT,UAAU,EAAE;MAChC;IAAA,CACA,MAAM;MACN,MAAMS,KAAK;IACZ;EACD;AACD", "ignoreList": []}