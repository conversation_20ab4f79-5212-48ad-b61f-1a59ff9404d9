{"version": 3, "file": "ValidatePromise.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/ValidatePromise.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AAEpE;;GAEG;AACH,MAAM,UAAU,eAAe,CAAC,iBAAqC;IACnE,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAM,IAAI,GAA2B;YACnC,IAAI,EAAE,eAAe,CAAC,kBAAkB;YACxC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,iBAAiB,EAAE,iBAAiB;SACrC,CAAC;QACF,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { ValidationMetadataArgs } from '../../metadata/ValidationMetadataArgs';\nimport { ValidationTypes } from '../../validation/ValidationTypes';\nimport { ValidationMetadata } from '../../metadata/ValidationMetadata';\nimport { getMetadataStorage } from '../../metadata/MetadataStorage';\n\n/**\n * Resolve promise before validation\n */\nexport function ValidatePromise(validationOptions?: ValidationOptions): PropertyDecorator {\n  return function (object: object, propertyName: string): void {\n    const args: ValidationMetadataArgs = {\n      type: ValidationTypes.PROMISE_VALIDATION,\n      target: object.constructor,\n      propertyName: propertyName,\n      validationOptions: validationOptions,\n    };\n    getMetadataStorage().addValidationMetadata(new ValidationMetadata(args));\n  };\n}\n"]}