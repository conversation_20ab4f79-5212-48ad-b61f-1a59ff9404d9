{"version": 3, "sources": ["../../../src/server/lib/incremental-cache-server.ts"], "names": ["createIpcServer", "IncrementalCache", "initializeResult", "initialize", "constructorArgs", "incrementalCache", "ipcPort", "ipcValidationKey", "revalidateTag", "args", "get", "set", "lock", "unlock"], "mappings": "AAAA,SAASA,eAAe,QAAQ,eAAc;AAC9C,SAASC,gBAAgB,QAAQ,sBAAqB;AAEtD,IAAIC;AAOJ,OAAO,eAAeC,WACpB,GAAGC,eAA+D;IAElE,MAAMC,mBAAmB,IAAIJ,oBAAoBG;IAEjD,MAAM,EAAEE,OAAO,EAAEC,gBAAgB,EAAE,GAAG,MAAMP,gBAAgB;QAC1D,MAAMQ,eACJ,GAAGC,IAAmD;YAEtD,OAAOJ,iBAAiBG,aAAa,IAAIC;QAC3C;QAEA,MAAMC,KAAI,GAAGD,IAAyC;YACpD,OAAOJ,iBAAiBK,GAAG,IAAID;QACjC;QAEA,MAAME,KAAI,GAAGF,IAAyC;YACpD,OAAOJ,iBAAiBM,GAAG,IAAIF;QACjC;QAEA,MAAMG,MAAK,GAAGH,IAA0C;YACtD,OAAOJ,iBAAiBO,IAAI,IAAIH;QAClC;QAEA,MAAMI,QAAO,GAAGJ,IAA4C;YAC1D,OAAOJ,iBAAiBQ,MAAM,IAAIJ;QACpC;IACF;IAEA,OAAO;QACLH;QACAC;IACF;AACF"}