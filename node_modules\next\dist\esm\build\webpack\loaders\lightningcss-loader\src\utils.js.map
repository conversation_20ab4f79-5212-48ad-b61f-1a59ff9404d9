{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/lightningcss-loader/src/utils.ts"], "names": ["targetsCache", "version", "major", "minor", "patch", "parseVersion", "v", "split", "reduce", "acc", "val", "parsed", "parseInt", "isNaN", "push", "browserslistToTargets", "targets", "value", "name", "parsedVersion", "versionDigit", "getTargets", "opts", "cache", "key", "result"], "mappings": "AAAA,IAAIA,eAAoC,CAAC;AAEzC;;;;CAIC,GACD,SAASC,QAAQC,KAAa,EAAEC,QAAQ,CAAC,EAAEC,QAAQ,CAAC;IAClD,OAAO,AAACF,SAAS,KAAOC,SAAS,IAAKC;AACxC;AAEA,SAASC,aAAaC,CAAS;IAC7B,OAAOA,EAAEC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,KAAKC;QAC/B,IAAI,CAACD,KAAK;YACR,OAAO;QACT;QAEA,MAAME,SAASC,SAASF,KAAK;QAC7B,IAAIG,MAAMF,SAAS;YACjB,OAAO;QACT;QACAF,IAAIK,IAAI,CAACH;QACT,OAAOF;IACT,GAAG,EAAE;AACP;AAEA,SAASM,sBAAsBC,OAAsB;IACnD,OAAOA,QAAQR,MAAM,CAAC,CAACC,KAAKQ;QAC1B,MAAM,CAACC,MAAMZ,EAAE,GAAGW,MAAMV,KAAK,CAAC;QAC9B,MAAMY,gBAAgBd,aAAaC;QAEnC,IAAI,CAACa,eAAe;YAClB,OAAOV;QACT;QACA,MAAMW,eAAenB,QACnBkB,aAAa,CAAC,EAAE,EAChBA,aAAa,CAAC,EAAE,EAChBA,aAAa,CAAC,EAAE;QAGlB,IACED,SAAS,YACTA,SAAS,YACTA,SAAS,WACTA,SAAS,QACTA,SAAS,WACTA,SAAS,WACT;YACA,OAAOT;QACT;QAEA,IAAIA,GAAG,CAACS,KAAK,IAAI,QAAQE,eAAeX,GAAG,CAACS,KAAK,EAAE;YACjDT,GAAG,CAACS,KAAK,GAAGE;QACd;QAEA,OAAOX;IACT,GAAG,CAAC;AACN;AAEA,OAAO,MAAMY,aAAa,CAACC;IACzB,MAAMC,QAAQvB,YAAY,CAACsB,KAAKE,GAAG,CAAC;IACpC,IAAID,OAAO;QACT,OAAOA;IACT;IAEA,MAAME,SAASV,sBAAsBO,KAAKN,OAAO,IAAI,EAAE;IACvD,OAAQhB,YAAY,CAACsB,KAAKE,GAAG,CAAC,GAAGC;AACnC,EAAC"}