{"version": 3, "file": "isValidNumberForRegion_.js", "names": ["_isValid", "_interopRequireDefault", "require", "e", "__esModule", "isValidNumberForRegion", "input", "country", "options", "metadata", "isValidNumber"], "sources": ["../../source/legacy/isValidNumberForRegion_.js"], "sourcesContent": ["import isValidNumber from '../isValid.js'\r\n\r\n/**\r\n * Checks if a given phone number is valid within a given region.\r\n * Is just an alias for `phoneNumber.isValid() && phoneNumber.country === country`.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n */\r\nexport default function isValidNumberForRegion(input, country, options, metadata) {\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\treturn input.country === country && isValidNumber(input, options, metadata)\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAyC,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEzC;AACA;AACA;AACA;AACA;AACe,SAASE,sBAAsBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACjF;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,OAAOF,KAAK,CAACC,OAAO,KAAKA,OAAO,IAAI,IAAAG,mBAAa,EAACJ,KAAK,EAAEE,OAAO,EAAEC,QAAQ,CAAC;AAC5E", "ignoreList": []}