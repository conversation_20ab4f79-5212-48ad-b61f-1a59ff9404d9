{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-patch-reducer.ts"], "names": ["createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "applyFlightData", "handleMutable", "createEmptyCacheNode", "handleSegmentMismatch", "serverPatchReducer", "state", "action", "serverResponse", "flightData", "overrideCanonicalUrl", "mutable", "preserveCustomHistoryState", "pushRef", "pendingPush", "currentTree", "tree", "currentCache", "cache", "flightDataPath", "flightSegmentPath", "slice", "treePatch", "newTree", "canonicalUrl", "canonicalUrlOverrideHref", "undefined", "patchedTree"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AAEjD,SAASC,oBAAoB,QAAQ,mBAAkB;AACvD,SAASC,qBAAqB,QAAQ,6BAA4B;AAElE,OAAO,SAASC,mBACdC,KAA2B,EAC3BC,MAAyB;IAEzB,MAAM,EAAEC,cAAc,EAAE,GAAGD;IAC3B,MAAM,CAACE,YAAYC,qBAAqB,GAAGF;IAE3C,MAAMG,UAAmB,CAAC;IAE1BA,QAAQC,0BAA0B,GAAG;IAErC,4DAA4D;IAC5D,IAAI,OAAOH,eAAe,UAAU;QAClC,OAAOT,kBACLM,OACAK,SACAF,YACAH,MAAMO,OAAO,CAACC,WAAW;IAE7B;IAEA,IAAIC,cAAcT,MAAMU,IAAI;IAC5B,IAAIC,eAAeX,MAAMY,KAAK;IAE9B,KAAK,MAAMC,kBAAkBV,WAAY;QACvC,mFAAmF;QACnF,MAAMW,oBAAoBD,eAAeE,KAAK,CAAC,GAAG,CAAC;QAEnD,MAAM,CAACC,UAAU,GAAGH,eAAeE,KAAK,CAAC,CAAC,GAAG,CAAC;QAC9C,MAAME,UAAUzB,4BACd,sBAAsB;QACtB;YAAC;eAAOsB;SAAkB,EAC1BL,aACAO,WACAhB,MAAMkB,YAAY;QAGpB,IAAID,YAAY,MAAM;YACpB,OAAOnB,sBAAsBE,OAAOC,QAAQe;QAC9C;QAEA,IAAIvB,4BAA4BgB,aAAaQ,UAAU;YACrD,OAAOvB,kBACLM,OACAK,SACAL,MAAMkB,YAAY,EAClBlB,MAAMO,OAAO,CAACC,WAAW;QAE7B;QAEA,MAAMW,2BAA2Bf,uBAC7Bb,kBAAkBa,wBAClBgB;QAEJ,IAAID,0BAA0B;YAC5Bd,QAAQa,YAAY,GAAGC;QACzB;QAEA,MAAMP,QAAmBf;QACzBF,gBAAgBgB,cAAcC,OAAOC;QAErCR,QAAQgB,WAAW,GAAGJ;QACtBZ,QAAQO,KAAK,GAAGA;QAEhBD,eAAeC;QACfH,cAAcQ;IAChB;IAEA,OAAOrB,cAAcI,OAAOK;AAC9B"}