{"version": 3, "file": "RegExpCache.js", "names": ["L<PERSON><PERSON><PERSON>", "RegExpCache", "size", "_classCallCheck", "cache", "_createClass", "key", "value", "getPatternForRegExp", "pattern", "regExp", "get", "RegExp", "put", "default"], "sources": ["../../source/findNumbers/RegExpCache.js"], "sourcesContent": ["import LRUCache from './LRUCache.js'\r\n\r\n// A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\r\n// countries being used for the same doc with ~10 patterns for each country. Some pages will have\r\n// a lot more countries in use, but typically fewer numbers for each so expanding the cache for\r\n// that use-case won't have a lot of benefit.\r\nexport default class RegExpCache {\r\n\tconstructor(size) {\r\n\t\tthis.cache = new LRUCache(size)\r\n\t}\r\n\r\n\tgetPatternForRegExp(pattern) {\r\n\t\tlet regExp = this.cache.get(pattern)\r\n\t\tif (!regExp) {\r\n\t\t\tregExp = new RegExp('^' + pattern)\r\n\t\t\tthis.cache.put(pattern, regExp)\r\n\t\t}\r\n\t\treturn regExp\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AAAA,IACqBC,WAAW;EAC/B,SAAAA,YAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,WAAA;IACjB,IAAI,CAACG,KAAK,GAAG,IAAIJ,QAAQ,CAACE,IAAI,CAAC;EAChC;EAAC,OAAAG,YAAA,CAAAJ,WAAA;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAAC,mBAAmBA,CAACC,OAAO,EAAE;MAC5B,IAAIC,MAAM,GAAG,IAAI,CAACN,KAAK,CAACO,GAAG,CAACF,OAAO,CAAC;MACpC,IAAI,CAACC,MAAM,EAAE;QACZA,MAAM,GAAG,IAAIE,MAAM,CAAC,GAAG,GAAGH,OAAO,CAAC;QAClC,IAAI,CAACL,KAAK,CAACS,GAAG,CAACJ,OAAO,EAAEC,MAAM,CAAC;MAChC;MACA,OAAOA,MAAM;IACd;EAAC;AAAA;AAAA,SAZmBT,WAAW,IAAAa,OAAA", "ignoreList": []}