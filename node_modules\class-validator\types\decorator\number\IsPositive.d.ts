import { ValidationOptions } from '../ValidationOptions';
export declare const IS_POSITIVE = "isPositive";
/**
 * Checks if the value is a positive number greater than zero.
 */
export declare function isPositive(value: unknown): boolean;
/**
 * Checks if the value is a positive number greater than zero.
 */
export declare function IsPositive(validationOptions?: ValidationOptions): PropertyDecorator;
