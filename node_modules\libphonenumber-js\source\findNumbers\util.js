/** Returns a regular expression quantifier with an upper and lower limit. */
export function limit(lower, upper)
{
	if ((lower < 0) || (upper <= 0) || (upper < lower)) {
		throw new TypeError()
	}
	return `{${lower},${upper}}`
}

/**
 * Trims away any characters after the first match of {@code pattern} in {@code candidate},
 * returning the trimmed version.
 */
export function trimAfterFirstMatch(regexp, string)
{
	const index = string.search(regexp)

	if (index >= 0) {
		return string.slice(0, index)
	}

	return string
}

export function startsWith(string, substring)
{
	return string.indexOf(substring) === 0
}

export function endsWith(string, substring)
{
	return string.indexOf(substring, string.length - substring.length) === string.length - substring.length
}
