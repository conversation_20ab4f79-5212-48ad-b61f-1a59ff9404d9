{"version": 3, "file": "parseDigits.test.js", "names": ["_parseDigits", "_interopRequireDefault", "require", "e", "__esModule", "describe", "it", "expect", "parseDigits", "to", "equal"], "sources": ["../../source/helpers/parseDigits.test.js"], "sourcesContent": ["import parseDigits from './parseDigits.js'\r\n\r\ndescribe('parseDigits', () => {\r\n\tit('should parse digits', () => {\r\n\t\texpect(parseDigits('+٤٤٢٣٢٣٢٣٤')).to.equal('442323234')\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE1CE,QAAQ,CAAC,aAAa,EAAE,YAAM;EAC7BC,EAAE,CAAC,qBAAqB,EAAE,YAAM;IAC/BC,MAAM,CAAC,IAAAC,uBAAW,EAAC,YAAY,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EACxD,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}