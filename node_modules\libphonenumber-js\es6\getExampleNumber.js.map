{"version": 3, "file": "getExampleNumber.js", "names": ["PhoneNumber", "getExampleNumber", "country", "examples", "metadata"], "sources": ["../source/getExampleNumber.js"], "sourcesContent": ["import PhoneNumber from './PhoneNumber.js'\r\n\r\nexport default function getExampleNumber(country, examples, metadata) {\r\n\tif (examples[country]) {\r\n\t\treturn new PhoneNumber(country, examples[country], metadata)\r\n\t}\r\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAE1C,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACrE,IAAID,QAAQ,CAACD,OAAO,CAAC,EAAE;IACtB,OAAO,IAAIF,WAAW,CAACE,OAAO,EAAEC,QAAQ,CAACD,OAAO,CAAC,EAAEE,QAAQ,CAAC;EAC7D;AACD", "ignoreList": []}