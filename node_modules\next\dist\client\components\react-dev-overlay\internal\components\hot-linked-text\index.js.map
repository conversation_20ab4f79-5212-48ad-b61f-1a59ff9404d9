{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/hot-linked-text/index.tsx"], "names": ["HotlinkedText", "linkRegex", "splitRegexp", "RegExp", "MAGIC_IDENTIFIER_REGEX", "source", "props", "text", "matcher", "wordsAndWhitespaces", "split", "map", "word", "index", "test", "link", "exec", "href", "React", "Fragment", "a", "target", "rel", "decodedWord", "decodeMagicIdentifier", "i", "e"], "mappings": ";;;;+BAUaA;;;eAAAA;;;;;gEAVK;iCAIX;AAEP,MAAMC,YAAY;AAElB,MAAMC,cAAc,IAAIC,OAAO,AAAC,MAAGC,uCAAsB,CAACC,MAAM,GAAC;AAE1D,MAAML,gBAGR,SAASA,cAAcM,KAAK;IAC/B,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE,GAAGF;IAE1B,MAAMG,sBAAsBF,KAAKG,KAAK,CAACR;IAEvC,qBACE;kBACGO,oBAAoBE,GAAG,CAAC,CAACC,MAAMC;YAC9B,IAAIZ,UAAUa,IAAI,CAACF,OAAO;gBACxB,MAAMG,OAAOd,UAAUe,IAAI,CAACJ;gBAC5B,MAAMK,OAAOF,IAAI,CAAC,EAAE;gBACpB,mFAAmF;gBACnF,IAAI,OAAOP,YAAY,cAAc,CAACA,QAAQS,OAAO;oBACnD,OAAOL;gBACT;gBACA,qBACE,qBAACM,cAAK,CAACC,QAAQ;8BACb,cAAA,qBAACC;wBAAEH,MAAMA;wBAAMI,QAAO;wBAASC,KAAI;kCAChCV;;mBAFgB,AAAC,UAAOC;YAMjC;YACA,IAAI;gBACF,MAAMU,cAAcC,IAAAA,sCAAqB,EAACZ;gBAC1C,IAAIW,gBAAgBX,MAAM;oBACxB,qBACE,sBAACa;;4BACE;4BACAF;4BACA;;uBAHK,AAAC,WAAQV;gBAMrB;YACF,EAAE,OAAOa,GAAG;gBACV,qBACE,sBAACD;;wBACE;wBACAb;wBAAK;wBAAoB,KAAKc;wBAAE;wBAAE;;mBAF7B,AAAC,WAAQb;YAKrB;YACA,qBAAO,qBAACK,cAAK,CAACC,QAAQ;0BAAwBP;eAAlB,AAAC,UAAOC;QACtC;;AAGN"}