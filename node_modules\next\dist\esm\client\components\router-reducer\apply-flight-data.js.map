{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-flight-data.ts"], "names": ["fillLazyItemsTillLeafWithHead", "fillCacheWithNewSubTreeData", "applyFlightData", "existingCache", "cache", "flightDataPath", "prefetchEntry", "treePatch", "cacheNodeSeedData", "head", "slice", "length", "rsc", "loading", "prefetchRsc", "parallelRoutes", "Map"], "mappings": "AAEA,SAASA,6BAA6B,QAAQ,wCAAuC;AACrF,SAASC,2BAA2B,QAAQ,qCAAoC;AAGhF,OAAO,SAASC,gBACdC,aAAwB,EACxBC,KAAgB,EAChBC,cAA8B,EAC9BC,aAAkC;IAElC,0DAA0D;IAC1D,MAAM,CAACC,WAAWC,mBAAmBC,KAAK,GAAGJ,eAAeK,KAAK,CAAC,CAAC;IAEnE,8FAA8F;IAC9F,IAAIF,sBAAsB,MAAM;QAC9B,OAAO;IACT;IAEA,IAAIH,eAAeM,MAAM,KAAK,GAAG;QAC/B,MAAMC,MAAMJ,iBAAiB,CAAC,EAAE;QAChC,MAAMK,UAAUL,iBAAiB,CAAC,EAAE;QACpCJ,MAAMS,OAAO,GAAGA;QAChBT,MAAMQ,GAAG,GAAGA;QACZ,kEAAkE;QAClE,oEAAoE;QACpE,2DAA2D;QAC3D,kEAAkE;QAClE,+BAA+B;QAC/BR,MAAMU,WAAW,GAAG;QACpBd,8BACEI,OACAD,eACAI,WACAC,mBACAC,MACAH;IAEJ,OAAO;QACL,2CAA2C;QAC3CF,MAAMQ,GAAG,GAAGT,cAAcS,GAAG;QAC7B,oEAAoE;QACpE,kEAAkE;QAClE,2BAA2B;QAC3BR,MAAMU,WAAW,GAAGX,cAAcW,WAAW;QAC7CV,MAAMW,cAAc,GAAG,IAAIC,IAAIb,cAAcY,cAAc;QAC3DX,MAAMS,OAAO,GAAGV,cAAcU,OAAO;QACrC,4DAA4D;QAC5DZ,4BACEG,OACAD,eACAE,gBACAC;IAEJ;IAEA,OAAO;AACT"}