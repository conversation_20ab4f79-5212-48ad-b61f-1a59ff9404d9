{"version": 3, "file": "getNumberType.test.js", "names": ["_getNumberType", "_interopRequireDefault", "require", "_metadataMin", "_metadata", "e", "__esModule", "describe", "it", "expect", "getNumberType", "nationalNumber", "country", "v2", "oldMetadata", "to", "equal", "be", "undefined"], "sources": ["../../source/helpers/getNumberType.test.js"], "sourcesContent": ["import getNumberType from './getNumberType.js'\r\n\r\nimport oldMetadata from '../../test/metadata/1.0.0/metadata.min.json' with { type: 'json' }\r\n\r\nimport Metadata from '../metadata.js'\r\n\r\ndescribe('getNumberType', function() {\r\n\tit('should get number type when using old metadata', function() {\r\n\t\texpect(getNumberType(\r\n\t\t\t{\r\n\t\t\t\tnationalNumber: '2133734253',\r\n\t\t\t\tcountry: 'US'\r\n\t\t\t},\r\n\t\t\t{ v2: true },\r\n\t\t\toldMetadata\r\n\t\t)).to.equal('FIXED_LINE_OR_MOBILE')\r\n\t})\r\n\r\n\tit('should return `undefined` when the phone number is a malformed one', function() {\r\n\t\texpect(getNumberType(\r\n\t\t\t{},\r\n\t\t\t{ v2: true },\r\n\t\t\toldMetadata\r\n\t\t)).to.be.undefined\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAEA,IAAAE,SAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAqC,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAErCE,QAAQ,CAAC,eAAe,EAAE,YAAW;EACpCC,EAAE,CAAC,gDAAgD,EAAE,YAAW;IAC/DC,MAAM,CAAC,IAAAC,yBAAa,EACnB;MACCC,cAAc,EAAE,YAAY;MAC5BC,OAAO,EAAE;IACV,CAAC,EACD;MAAEC,EAAE,EAAE;IAAK,CAAC,EACZC,uBACD,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,sBAAsB,CAAC;EACpC,CAAC,CAAC;EAEFR,EAAE,CAAC,oEAAoE,EAAE,YAAW;IACnFC,MAAM,CAAC,IAAAC,yBAAa,EACnB,CAAC,CAAC,EACF;MAAEG,EAAE,EAAE;IAAK,CAAC,EACZC,uBACD,CAAC,CAAC,CAACC,EAAE,CAACE,EAAE,CAACC,SAAS;EACnB,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}