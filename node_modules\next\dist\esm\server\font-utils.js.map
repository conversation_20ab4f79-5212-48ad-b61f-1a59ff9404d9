{"version": 3, "sources": ["../../src/server/font-utils.ts"], "names": ["Log", "GOOGLE_FONT_PROVIDER", "DEFAULT_SERIF_FONT", "DEFAULT_SANS_SERIF_FONT", "capsizeFontsMetrics", "require", "CHROME_UA", "IE_UA", "isGoogleFont", "url", "startsWith", "getFontForUA", "UA", "res", "fetch", "headers", "text", "getFontDefinitionFromNetwork", "result", "e", "warn", "parseGoogleFontName", "css", "regex", "matches", "matchAll", "fontNames", "Set", "font", "fontFamily", "replace", "add", "formatName", "str", "word", "index", "toLowerCase", "toUpperCase", "formatOverrideValue", "val", "Math", "abs", "toFixed", "calculateOverrideValues", "fontName", "font<PERSON>ey", "fontMetrics", "category", "ascent", "descent", "lineGap", "unitsPerEm", "fallbackFont", "name", "calculateSizeAdjustValues", "xWidthAvg", "mainFontAvgWidth", "fallback<PERSON>ontName", "fallbackFontMetrics", "fallbackFontAvgWidth", "sizeAdjust", "calculateOverrideCSS", "trim", "calculateSizeAdjustCSS", "getFontOverrideCss", "useSizeAdjust", "calcFn", "fontCss", "reduce", "cssStr", "console", "log"], "mappings": "AAAA,YAAYA,SAAS,sBAAqB;AAC1C,SACEC,oBAAoB,EACpBC,kBAAkB,EAClBC,uBAAuB,QAClB,0BAAyB;AAChC,MAAMC,sBAAsBC,QAAQ;AAEpC,MAAMC,YACJ;AACF,MAAMC,QAAQ;AASd,SAASC,aAAaC,GAAW;IAC/B,OAAOA,IAAIC,UAAU,CAACT;AACxB;AAEA,eAAeU,aAAaF,GAAW,EAAEG,EAAU;IACjD,MAAMC,MAAM,MAAMC,MAAML,KAAK;QAAEM,SAAS;YAAE,cAAcH;QAAG;IAAE;IAC7D,OAAO,MAAMC,IAAIG,IAAI;AACvB;AAEA,OAAO,eAAeC,6BACpBR,GAAW;IAEX,IAAIS,SAAS;IACb;;;GAGC,GACD,IAAI;QACF,IAAIV,aAAaC,MAAM;YACrBS,UAAU,MAAMP,aAAaF,KAAKF;QACpC;QACAW,UAAU,MAAMP,aAAaF,KAAKH;IACpC,EAAE,OAAOa,GAAG;QACVnB,IAAIoB,IAAI,CACN,CAAC,sCAAsC,EAAEX,IAAI,+BAA+B,CAAC;QAE/E,OAAO;IACT;IAEA,OAAOS;AACT;AAEA,SAASG,oBAAoBC,GAAW;IACtC,MAAMC,QAAQ;IACd,MAAMC,UAAUF,IAAIG,QAAQ,CAACF;IAC7B,MAAMG,YAAY,IAAIC;IAEtB,KAAK,IAAIC,QAAQJ,QAAS;QACxB,MAAMK,aAAaD,IAAI,CAAC,EAAE,CAACE,OAAO,CAAC,gBAAgB;QACnDJ,UAAUK,GAAG,CAACF;IAChB;IAEA,OAAO;WAAIH;KAAU;AACvB;AAEA,SAASM,WAAWC,GAAW;IAC7B,OAAOA,IACJH,OAAO,CAAC,uBAAuB,SAAUI,IAAI,EAAEC,KAAK;QACnD,OAAOA,UAAU,IAAID,KAAKE,WAAW,KAAKF,KAAKG,WAAW;IAC5D,GACCP,OAAO,CAAC,QAAQ;AACrB;AAEA,SAASQ,oBAAoBC,GAAW;IACtC,OAAOC,KAAKC,GAAG,CAACF,MAAM,KAAKG,OAAO,CAAC;AACrC;AAEA,OAAO,SAASC,wBAAwBC,QAAgB;IACtD,MAAMC,UAAUb,WAAWY;IAC3B,MAAME,cAAc1C,mBAAmB,CAACyC,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAE,GAAGL;IACzD,MAAMM,eACJL,aAAa,UAAU7C,qBAAqBC;IAC9C6C,SAASV,oBAAoBU,SAASG;IACtCF,UAAUX,oBAAoBW,UAAUE;IACxCD,UAAUZ,oBAAoBY,UAAUC;IAExC,OAAO;QACLH;QACAC;QACAC;QACAE,cAAcA,aAAaC,IAAI;IACjC;AACF;AAEA,OAAO,SAASC,0BAA0BV,QAAgB;IACxD,MAAMC,UAAUb,WAAWY;IAC3B,MAAME,cAAc1C,mBAAmB,CAACyC,QAAQ;IAChD,IAAI,EAAEE,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,EAAEI,SAAS,EAAE,GAC/DT;IACF,MAAMU,mBAAmBD,YAAYJ;IACrC,MAAMC,eACJL,aAAa,UAAU7C,qBAAqBC;IAC9C,MAAMsD,mBAAmBzB,WAAWoB,aAAaC,IAAI;IACrD,MAAMK,sBAAsBtD,mBAAmB,CAACqD,iBAAiB;IACjE,MAAME,uBACJD,oBAAoBH,SAAS,GAAGG,oBAAoBP,UAAU;IAChE,IAAIS,aAAaL,YAAYC,mBAAmBG,uBAAuB;IAEvEX,SAASV,oBAAoBU,SAAUG,CAAAA,aAAaS,UAAS;IAC7DX,UAAUX,oBAAoBW,UAAWE,CAAAA,aAAaS,UAAS;IAC/DV,UAAUZ,oBAAoBY,UAAWC,CAAAA,aAAaS,UAAS;IAE/D,OAAO;QACLZ;QACAC;QACAC;QACAE,cAAcA,aAAaC,IAAI;QAC/BO,YAAYtB,oBAAoBsB;IAClC;AACF;AAEA,SAASC,qBAAqBjC,IAAY;IACxC,MAAMgB,WAAWhB,KAAKkC,IAAI;IAE1B,MAAM,EAAEd,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEE,YAAY,EAAE,GAC9CT,wBAAwBC;IAE1B,OAAO,CAAC;;oBAEU,EAAEA,SAAS;uBACR,EAAEI,OAAO;wBACR,EAAEC,QAAQ;yBACT,EAAEC,QAAQ;kBACjB,EAAEE,aAAa;;EAE/B,CAAC;AACH;AAEA,SAASW,uBAAuBnC,IAAY;IAC1C,MAAMgB,WAAWhB,KAAKkC,IAAI;IAE1B,MAAM,EAAEd,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEE,YAAY,EAAEQ,UAAU,EAAE,GAC1DN,0BAA0BV;IAE5B,OAAO,CAAC;;oBAEU,EAAEA,SAAS;uBACR,EAAEI,OAAO;wBACR,EAAEC,QAAQ;yBACT,EAAEC,QAAQ;mBAChB,EAAEU,WAAW;kBACd,EAAER,aAAa;;EAE/B,CAAC;AACH;AAEA,OAAO,SAASY,mBACdvD,GAAW,EACXa,GAAW,EACX2C,gBAAgB,KAAK;IAErB,IAAI,CAACzD,aAAaC,MAAM;QACtB,OAAO;IACT;IAEA,MAAMyD,SAASD,gBAAgBF,yBAAyBF;IAExD,IAAI;QACF,MAAMnC,YAAYL,oBAAoBC;QAEtC,MAAM6C,UAAUzC,UAAU0C,MAAM,CAAC,CAACC,QAAQzB;YACxCyB,UAAUH,OAAOtB;YACjB,OAAOyB;QACT,GAAG;QAEH,OAAOF;IACT,EAAE,OAAOhD,GAAG;QACVmD,QAAQC,GAAG,CAAC,yCAAyCpD;QACrD,OAAO;IACT;AACF"}