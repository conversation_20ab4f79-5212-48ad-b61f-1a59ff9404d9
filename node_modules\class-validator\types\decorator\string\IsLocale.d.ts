import { ValidationOptions } from '../ValidationOptions';
export declare const IS_LOCALE = "isLocale";
/**
 * Check if the string is a locale.
 * If given value is not a string, then it returns false.
 */
export declare function isLocale(value: unknown): boolean;
/**
 * Check if the string is a locale.
 * If given value is not a string, then it returns false.
 */
export declare function IsLocale(validationOptions?: ValidationOptions): PropertyDecorator;
