{"version": 3, "file": "findPhoneNumbersInText.js", "names": ["PhoneNumberMatcher", "normalizeArguments", "findPhoneNumbersInText", "_normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "_objectSpread", "v2", "results", "hasNext", "push", "next"], "sources": ["../source/findPhoneNumbersInText.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function findPhoneNumbersInText() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, { ...options, v2: true }, metadata)\r\n\tconst results = []\r\n\twhile (matcher.hasNext()) {\r\n\t\tresults.push(matcher.next())\r\n\t}\r\n\treturn results\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,kBAAkB,MAAM,yBAAyB;AAExD,eAAe,SAASC,sBAAsBA,CAAA,EAAG;EAChD,IAAAC,mBAAA,GAAoCF,kBAAkB,CAACG,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC/B,IAAMC,OAAO,GAAG,IAAIR,kBAAkB,CAACK,IAAI,EAAAI,aAAA,CAAAA,aAAA,KAAOH,OAAO;IAAEI,EAAE,EAAE;EAAI,IAAIH,QAAQ,CAAC;EAChF,IAAMI,OAAO,GAAG,EAAE;EAClB,OAAOH,OAAO,CAACI,OAAO,CAAC,CAAC,EAAE;IACzBD,OAAO,CAACE,IAAI,CAACL,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC;EAC7B;EACA,OAAOH,OAAO;AACf", "ignoreList": []}