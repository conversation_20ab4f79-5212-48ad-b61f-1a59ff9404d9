{"version": 3, "file": "stripIddPrefix.js", "names": ["_metadata", "_interopRequireDefault", "require", "_constants", "e", "__esModule", "CAPTURING_DIGIT_PATTERN", "RegExp", "VALID_DIGITS", "stripIddPrefix", "number", "country", "callingCode", "metadata", "countryMetadata", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "IDDPrefixPattern", "IDDPrefix", "search", "slice", "match", "length", "matchedGroups"], "sources": ["../../source/helpers/stripIddPrefix.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport { VALID_DIGITS } from '../constants.js'\r\n\r\nconst CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])')\r\n\r\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\r\n\tif (!country) {\r\n\t\treturn\r\n\t}\r\n\t// Check if the number is IDD-prefixed.\r\n\tconst countryMetadata = new Metadata(metadata)\r\n\tcountryMetadata.selectNumberingPlan(country, callingCode)\r\n\tconst IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix())\r\n\tif (number.search(IDDPrefixPattern) !== 0) {\r\n\t\treturn\r\n\t}\r\n\t// Strip IDD prefix.\r\n\tnumber = number.slice(number.match(IDDPrefixPattern)[0].length)\r\n\t// If there're any digits after an IDD prefix,\r\n\t// then those digits are a country calling code.\r\n\t// Since no country code starts with a `0`,\r\n\t// the code below validates that the next digit (if present) is not `0`.\r\n\tconst matchedGroups = number.match(CAPTURING_DIGIT_PATTERN)\r\n\tif (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\r\n\t\tif (matchedGroups[1] === '0') {\r\n\t\t\treturn\r\n\t\t}\r\n\t}\r\n\treturn number\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAA8C,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE9C,IAAME,uBAAuB,GAAG,IAAIC,MAAM,CAAC,IAAI,GAAGC,uBAAY,GAAG,IAAI,CAAC;AAEvD,SAASC,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EAC9E,IAAI,CAACF,OAAO,EAAE;IACb;EACD;EACA;EACA,IAAMG,eAAe,GAAG,IAAIC,oBAAQ,CAACF,QAAQ,CAAC;EAC9CC,eAAe,CAACE,mBAAmB,CAACL,OAAO,EAAEC,WAAW,CAAC;EACzD,IAAMK,gBAAgB,GAAG,IAAIV,MAAM,CAACO,eAAe,CAACI,SAAS,CAAC,CAAC,CAAC;EAChE,IAAIR,MAAM,CAACS,MAAM,CAACF,gBAAgB,CAAC,KAAK,CAAC,EAAE;IAC1C;EACD;EACA;EACAP,MAAM,GAAGA,MAAM,CAACU,KAAK,CAACV,MAAM,CAACW,KAAK,CAACJ,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAACK,MAAM,CAAC;EAC/D;EACA;EACA;EACA;EACA,IAAMC,aAAa,GAAGb,MAAM,CAACW,KAAK,CAACf,uBAAuB,CAAC;EAC3D,IAAIiB,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,aAAa,CAAC,CAAC,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;IAC7E,IAAIC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC7B;IACD;EACD;EACA,OAAOb,MAAM;AACd", "ignoreList": []}