{"version": 3, "file": "mergeArrays.js", "names": ["mergeArrays", "a", "b", "merged", "slice", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "element", "value", "indexOf", "push", "sort"], "sources": ["../../source/helpers/mergeArrays.js"], "sourcesContent": ["/**\r\n * Merges two arrays.\r\n * @param  {*} a\r\n * @param  {*} b\r\n * @return {*}\r\n */\r\nexport default function mergeArrays(a, b) {\r\n\tconst merged = a.slice()\r\n\r\n\tfor (const element of b) {\r\n\t\tif (a.indexOf(element) < 0) {\r\n\t\t\tmerged.push(element)\r\n\t\t}\r\n\t}\r\n\r\n\treturn merged.sort((a, b) => a - b)\r\n\r\n\t// ES6 version, requires Set polyfill.\r\n\t// let merged = new Set(a)\r\n\t// for (const element of b) {\r\n\t// \tmerged.add(i)\r\n\t// }\r\n\t// return Array.from(merged).sort((a, b) => a - b)\r\n}"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzC,IAAMC,MAAM,GAAGF,CAAC,CAACG,KAAK,CAAC,CAAC;EAExB,SAAAC,SAAA,GAAAC,+BAAA,CAAsBJ,CAAC,GAAAK,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAE;IAAA,IAAdC,OAAO,GAAAF,KAAA,CAAAG,KAAA;IACjB,IAAIT,CAAC,CAACU,OAAO,CAACF,OAAO,CAAC,GAAG,CAAC,EAAE;MAC3BN,MAAM,CAACS,IAAI,CAACH,OAAO,CAAC;IACrB;EACD;EAEA,OAAON,MAAM,CAACU,IAAI,CAAC,UAACZ,CAAC,EAAEC,CAAC;IAAA,OAAKD,CAAC,GAAGC,CAAC;EAAA,EAAC;;EAEnC;EACA;EACA;EACA;EACA;EACA;AACD", "ignoreList": []}