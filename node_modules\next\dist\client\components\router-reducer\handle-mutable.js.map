{"version": 3, "sources": ["../../../../src/client/components/router-reducer/handle-mutable.ts"], "names": ["handleMutable", "isNotUndefined", "value", "state", "mutable", "shouldScroll", "nextUrl", "patchedTree", "changedPath", "computeChangedPath", "tree", "canonicalUrl", "buildId", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "apply", "scrollableSegments", "onlyHashChange", "hashFragment", "split", "decodeURIComponent", "slice", "segmentPaths", "cache", "prefetchCache"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;oCAXmB;AAOnC,SAASC,eAAkBC,KAAQ;IACjC,OAAO,OAAOA,UAAU;AAC1B;AAEO,SAASF,cACdG,KAA2B,EAC3BC,OAAgB;QAoDRA;QAjDaA;IADrB,0DAA0D;IAC1D,MAAMC,eAAeD,CAAAA,wBAAAA,QAAQC,YAAY,YAApBD,wBAAwB;IAE7C,IAAIE,UAAUH,MAAMG,OAAO;IAE3B,IAAIL,eAAeG,QAAQG,WAAW,GAAG;QACvC,sEAAsE;QACtE,MAAMC,cAAcC,IAAAA,sCAAkB,EAACN,MAAMO,IAAI,EAAEN,QAAQG,WAAW;QACtE,IAAIC,aAAa;YACf,qDAAqD;YACrDF,UAAUE;QACZ,OAAO,IAAI,CAACF,SAAS;YACnB,6HAA6H;YAC7HA,UAAUH,MAAMQ,YAAY;QAC9B;IACA,0EAA0E;IAC5E;QA6CQP;IA3CR,OAAO;QACLQ,SAAST,MAAMS,OAAO;QACtB,YAAY;QACZD,cAAcV,eAAeG,QAAQO,YAAY,IAC7CP,QAAQO,YAAY,KAAKR,MAAMQ,YAAY,GACzCR,MAAMQ,YAAY,GAClBP,QAAQO,YAAY,GACtBR,MAAMQ,YAAY;QACtBE,SAAS;YACPC,aAAab,eAAeG,QAAQU,WAAW,IAC3CV,QAAQU,WAAW,GACnBX,MAAMU,OAAO,CAACC,WAAW;YAC7BC,eAAed,eAAeG,QAAQW,aAAa,IAC/CX,QAAQW,aAAa,GACrBZ,MAAMU,OAAO,CAACE,aAAa;YAC/BC,4BAA4Bf,eAC1BG,QAAQY,0BAA0B,IAEhCZ,QAAQY,0BAA0B,GAClCb,MAAMU,OAAO,CAACG,0BAA0B;QAC9C;QACA,kEAAkE;QAClEC,mBAAmB;YACjBC,OAAOb,eACHJ,eAAeG,2BAAAA,QAASe,kBAAkB,IACxC,OACAhB,MAAMc,iBAAiB,CAACC,KAAK,GAE/B;YACJE,gBACE,CAAC,CAAChB,QAAQiB,YAAY,IACtBlB,MAAMQ,YAAY,CAACW,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,OACjClB,wBAAAA,QAAQO,YAAY,qBAApBP,sBAAsBkB,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;YAC1CD,cAAchB,eAEV,oCAAoC;YACpCD,QAAQiB,YAAY,IAAIjB,QAAQiB,YAAY,KAAK,KAE/CE,mBAAmBnB,QAAQiB,YAAY,CAACG,KAAK,CAAC,MAC9CrB,MAAMc,iBAAiB,CAACI,YAAY,GAEtC;YACJI,cAAcpB,eACVD,CAAAA,8BAAAA,2BAAAA,QAASe,kBAAkB,YAA3Bf,8BAA+BD,MAAMc,iBAAiB,CAACQ,YAAY,GAEnE,EAAE;QACR;QACA,eAAe;QACfC,OAAOtB,QAAQsB,KAAK,GAAGtB,QAAQsB,KAAK,GAAGvB,MAAMuB,KAAK;QAClDC,eAAevB,QAAQuB,aAAa,GAChCvB,QAAQuB,aAAa,GACrBxB,MAAMwB,aAAa;QACvB,8BAA8B;QAC9BjB,MAAMT,eAAeG,QAAQG,WAAW,IACpCH,QAAQG,WAAW,GACnBJ,MAAMO,IAAI;QACdJ;IACF;AACF"}