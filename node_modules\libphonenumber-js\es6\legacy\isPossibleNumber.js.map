{"version": 3, "file": "isPossibleNumber.js", "names": ["normalizeArguments", "_isPossibleNumber", "isPossibleNumber", "_normalizeArguments", "arguments", "input", "options", "metadata", "phone", "v2"], "sources": ["../../source/legacy/isPossibleNumber.js"], "sourcesContent": ["import { normalizeArguments } from './getNumberType.js'\r\nimport _isPossibleNumber from '../isPossible.js'\r\n\r\n/**\r\n * Checks if a given phone number is possible.\r\n * Which means it only checks phone number length\r\n * and doesn't test any regular expressions.\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isPossibleNumber('+78005553535', metadata)\r\n * isPossibleNumber('8005553535', 'RU', metadata)\r\n * isPossibleNumber('88005553535', 'RU', metadata)\r\n * isPossibleNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\r\nexport default function isPossibleNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone && !(options && options.v2)) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isPossibleNumber(input, options, metadata)\r\n}"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,oBAAoB;AACvD,OAAOC,iBAAiB,MAAM,kBAAkB;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAAA,EAAG;EAC1C,IAAAC,mBAAA,GAAqCH,kBAAkB,CAACI,SAAS,CAAC;IAA1DC,KAAK,GAAAF,mBAAA,CAALE,KAAK;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAChC;EACA,IAAI,CAACF,KAAK,CAACG,KAAK,IAAI,EAAEF,OAAO,IAAIA,OAAO,CAACG,EAAE,CAAC,EAAE;IAC7C,OAAO,KAAK;EACb;EACA,OAAOR,iBAAiB,CAACI,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AACnD", "ignoreList": []}