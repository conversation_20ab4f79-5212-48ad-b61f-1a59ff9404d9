{"version": 3, "file": "PhoneNumberMatcher.test.js", "names": ["PhoneNumberMatcher", "metadata", "type", "test", "text", "defaultCountry", "expectedNumbers", "nationalNumber", "matcher", "v2", "hasNext", "number", "next", "phoneNumber", "shift", "startsAt", "undefined", "expect", "to", "equal", "endsAt", "country", "length", "describe", "it", "leniency", "max<PERSON>ries"], "sources": ["../source/PhoneNumberMatcher.test.js"], "sourcesContent": ["import PhoneNumberMatcher from './PhoneNumberMatcher.js'\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\nfunction test(text, defaultCountry, expectedNumbers) {\r\n\tif (typeof expectedNumbers === 'string') {\r\n\t\texpectedNumbers = [{\r\n\t\t\tnationalNumber: expectedNumbers\r\n\t\t}]\r\n\t}\r\n\tconst matcher = new PhoneNumberMatcher(text, { defaultCountry, v2: true }, metadata)\r\n\twhile (matcher.hasNext()) {\r\n\t\tconst number = matcher.next()\r\n\t\tconst phoneNumber = expectedNumbers.shift()\r\n\t\tif (phoneNumber.startsAt !== undefined) {\r\n\t\t\texpect(number.startsAt).to.equal(phoneNumber.startsAt)\r\n\t\t}\r\n\t\tif (phoneNumber.endsAt !== undefined) {\r\n\t\t\texpect(number.endsAt).to.equal(phoneNumber.endsAt)\r\n\t\t}\r\n\t\texpect(number.number.country).to.equal(phoneNumber.country || defaultCountry)\r\n\t\texpect(number.number.nationalNumber).to.equal(phoneNumber.nationalNumber)\r\n\t}\r\n\texpect(expectedNumbers.length).to.equal(0)\r\n}\r\n\r\ndescribe('PhoneNumberMatcher', () => {\r\n\tit('should find phone numbers', () => {\r\n\t\ttest(\r\n\t\t\t'The number is +7 (800) 555-35-35 and not (************* as written in the document.',\r\n\t\t\t'US',\r\n\t\t\t[{\r\n\t\t\t\tcountry: 'RU',\r\n\t\t\t\tnationalNumber: '8005553535',\r\n\t\t\t\tstartsAt: 14,\r\n\t\t\t\tendsAt: 32\r\n\t\t\t}, {\r\n\t\t\t\tcountry: 'US',\r\n\t\t\t\tnationalNumber: '2133734253',\r\n\t\t\t\tstartsAt: 41,\r\n\t\t\t\tendsAt: 55\r\n\t\t\t}]\r\n\t\t)\r\n\t})\r\n\r\n\tit('should find phone numbers from Mexico', () => {\r\n\t\t// Test parsing fixed-line numbers of Mexico.\r\n\t\ttest('+52 (449)978-0001', 'MX', '4499780001')\r\n\t\ttest('01 (449)978-0001', 'MX', '4499780001')\r\n\t\ttest('(449)978-0001', 'MX', '4499780001')\r\n\r\n\t\t// \"Dialling tokens 01, 02, 044, 045 and 1 are removed as they are\r\n\t\t//  no longer valid since August 2019.\"\r\n\t\t// // Test parsing mobile numbers of Mexico.\r\n\t\t// test('+52 1 33 1234-5678', 'MX', '3312345678')\r\n\t\t// test('044 (33) 1234-5678', 'MX', '3312345678')\r\n\t\t// test('045 33 1234-5678', 'MX', '3312345678')\r\n\t})\r\n\r\n\tit('should find phone numbers from Argentina', () => {\r\n\t\t// Test parsing mobile numbers of Argentina.\r\n\t\ttest('+54 9 ************', 'AR', '93435551212')\r\n\t\ttest('0343 15-555-1212', 'AR', '93435551212')\r\n\r\n\t\ttest('+54 9 3715 65 4320', 'AR', '93715654320')\r\n\t\ttest('03715 15 65 4320', 'AR', '93715654320')\r\n\r\n\t\t// Test parsing fixed-line numbers of Argentina.\r\n\t\ttest('+54 11 3797 0000', 'AR', '1137970000')\r\n\t\ttest('011 3797 0000', 'AR', '1137970000')\r\n\r\n\t\ttest('+54 3715 65 4321', 'AR', '3715654321')\r\n\t\ttest('03715 65 4321', 'AR', '3715654321')\r\n\r\n\t\ttest('+54 23 1234 0000', 'AR', '2312340000')\r\n\t\ttest('023 1234 0000', 'AR', '2312340000')\r\n\t})\r\n\r\n\tit('should only support the supported leniency values', function() {\r\n\t\texpect(() => new PhoneNumberMatcher('+54 23 1234 0000', { leniency: 'STRICT_GROUPING', v2: true }, metadata)).to.throw('Supported values: \"POSSIBLE\", \"VALID\".')\r\n\t})\r\n\r\n\tit('should validate input parameters', function() {\r\n\t\t// expect(() => new PhoneNumberMatcher('+54 23 1234 0000', { v2: true }, metadata)).to.throw('`leniency` is required')\r\n\t\texpect(() => new PhoneNumberMatcher('+54 23 1234 0000', { leniency: '???', v2: true }, metadata)).to.throw('Unknown leniency: \"???\"')\r\n\t\texpect(() => new PhoneNumberMatcher('+54 23 1234 0000', { maxTries: -1, v2: true }, metadata)).to.throw('`maxTries` must be `>= 0`')\r\n\t})\r\n\r\n\tit('should throw when calling `.next()` and there\\'s no next match', function() {\r\n\t\tconst matcher = new PhoneNumberMatcher('+54 23 1234 0000', { v2: true }, metadata)\r\n\t\tmatcher.next()\r\n\t\texpect(() => matcher.next()).to.throw('No next element')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAE/D,SAASC,IAAIA,CAACC,IAAI,EAAEC,cAAc,EAAEC,eAAe,EAAE;EACpD,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;IACxCA,eAAe,GAAG,CAAC;MAClBC,cAAc,EAAED;IACjB,CAAC,CAAC;EACH;EACA,IAAME,OAAO,GAAG,IAAIR,kBAAkB,CAACI,IAAI,EAAE;IAAEC,cAAc,EAAdA,cAAc;IAAEI,EAAE,EAAE;EAAK,CAAC,EAAER,QAAQ,CAAC;EACpF,OAAOO,OAAO,CAACE,OAAO,CAAC,CAAC,EAAE;IACzB,IAAMC,MAAM,GAAGH,OAAO,CAACI,IAAI,CAAC,CAAC;IAC7B,IAAMC,WAAW,GAAGP,eAAe,CAACQ,KAAK,CAAC,CAAC;IAC3C,IAAID,WAAW,CAACE,QAAQ,KAAKC,SAAS,EAAE;MACvCC,MAAM,CAACN,MAAM,CAACI,QAAQ,CAAC,CAACG,EAAE,CAACC,KAAK,CAACN,WAAW,CAACE,QAAQ,CAAC;IACvD;IACA,IAAIF,WAAW,CAACO,MAAM,KAAKJ,SAAS,EAAE;MACrCC,MAAM,CAACN,MAAM,CAACS,MAAM,CAAC,CAACF,EAAE,CAACC,KAAK,CAACN,WAAW,CAACO,MAAM,CAAC;IACnD;IACAH,MAAM,CAACN,MAAM,CAACA,MAAM,CAACU,OAAO,CAAC,CAACH,EAAE,CAACC,KAAK,CAACN,WAAW,CAACQ,OAAO,IAAIhB,cAAc,CAAC;IAC7EY,MAAM,CAACN,MAAM,CAACA,MAAM,CAACJ,cAAc,CAAC,CAACW,EAAE,CAACC,KAAK,CAACN,WAAW,CAACN,cAAc,CAAC;EAC1E;EACAU,MAAM,CAACX,eAAe,CAACgB,MAAM,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC;AAC3C;AAEAI,QAAQ,CAAC,oBAAoB,EAAE,YAAM;EACpCC,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrCrB,IAAI,CACH,qFAAqF,EACrF,IAAI,EACJ,CAAC;MACAkB,OAAO,EAAE,IAAI;MACbd,cAAc,EAAE,YAAY;MAC5BQ,QAAQ,EAAE,EAAE;MACZK,MAAM,EAAE;IACT,CAAC,EAAE;MACFC,OAAO,EAAE,IAAI;MACbd,cAAc,EAAE,YAAY;MAC5BQ,QAAQ,EAAE,EAAE;MACZK,MAAM,EAAE;IACT,CAAC,CACF,CAAC;EACF,CAAC,CAAC;EAEFI,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjD;IACArB,IAAI,CAAC,mBAAmB,EAAE,IAAI,EAAE,YAAY,CAAC;IAC7CA,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,YAAY,CAAC;IAC5CA,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC;;IAEzC;IACA;IACA;IACA;IACA;IACA;EACD,CAAC,CAAC;EAEFqB,EAAE,CAAC,0CAA0C,EAAE,YAAM;IACpD;IACArB,IAAI,CAAC,oBAAoB,EAAE,IAAI,EAAE,aAAa,CAAC;IAC/CA,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,aAAa,CAAC;IAE7CA,IAAI,CAAC,oBAAoB,EAAE,IAAI,EAAE,aAAa,CAAC;IAC/CA,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,aAAa,CAAC;;IAE7C;IACAA,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,YAAY,CAAC;IAC5CA,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC;IAEzCA,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,YAAY,CAAC;IAC5CA,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC;IAEzCA,IAAI,CAAC,kBAAkB,EAAE,IAAI,EAAE,YAAY,CAAC;IAC5CA,IAAI,CAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC;EAC1C,CAAC,CAAC;EAEFqB,EAAE,CAAC,mDAAmD,EAAE,YAAW;IAClEP,MAAM,CAAC;MAAA,OAAM,IAAIjB,kBAAkB,CAAC,kBAAkB,EAAE;QAAEyB,QAAQ,EAAE,iBAAiB;QAAEhB,EAAE,EAAE;MAAK,CAAC,EAAER,QAAQ,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,wCAAwC,CAAC;EACjK,CAAC,CAAC;EAEFM,EAAE,CAAC,kCAAkC,EAAE,YAAW;IACjD;IACAP,MAAM,CAAC;MAAA,OAAM,IAAIjB,kBAAkB,CAAC,kBAAkB,EAAE;QAAEyB,QAAQ,EAAE,KAAK;QAAEhB,EAAE,EAAE;MAAK,CAAC,EAAER,QAAQ,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,yBAAyB,CAAC;IACrID,MAAM,CAAC;MAAA,OAAM,IAAIjB,kBAAkB,CAAC,kBAAkB,EAAE;QAAE0B,QAAQ,EAAE,CAAC,CAAC;QAAEjB,EAAE,EAAE;MAAK,CAAC,EAAER,QAAQ,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,2BAA2B,CAAC;EACrI,CAAC,CAAC;EAEFM,EAAE,CAAC,gEAAgE,EAAE,YAAW;IAC/E,IAAMhB,OAAO,GAAG,IAAIR,kBAAkB,CAAC,kBAAkB,EAAE;MAAES,EAAE,EAAE;IAAK,CAAC,EAAER,QAAQ,CAAC;IAClFO,OAAO,CAACI,IAAI,CAAC,CAAC;IACdK,MAAM,CAAC;MAAA,OAAMT,OAAO,CAACI,IAAI,CAAC,CAAC;IAAA,EAAC,CAACM,EAAE,SAAM,CAAC,iBAAiB,CAAC;EACzD,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}