{"version": 3, "sources": ["../../src/client/router.ts"], "names": ["React", "Router", "RouterContext", "isError", "singletonRouter", "router", "readyCallbacks", "ready", "callback", "window", "push", "url<PERSON><PERSON><PERSON><PERSON>ields", "routerEvents", "core<PERSON><PERSON><PERSON><PERSON><PERSON>s", "Object", "defineProperty", "get", "events", "getRouter", "message", "Error", "for<PERSON>ach", "field", "args", "event", "on", "eventField", "char<PERSON>t", "toUpperCase", "substring", "_singletonRouter", "err", "console", "error", "stack", "default", "with<PERSON><PERSON><PERSON>", "useRouter", "useContext", "createRouter", "cb", "makePublicRouterInstance", "scopedRouter", "instance", "property", "assign", "Array", "isArray"], "mappings": "AAAA,iBAAiB,GACjB,OAAOA,WAAW,QAAO;AACzB,OAAOC,YAAY,8BAA6B;AAEhD,SAASC,aAAa,QAAQ,8CAA6C;AAC3E,OAAOC,aAAa,kBAAiB;AAQrC,SAASF,MAAM,GAAE;AAMjB,MAAMG,kBAAuC;IAC3CC,QAAQ;IACRC,gBAAgB,EAAE;IAClBC,OAAMC,QAAoB;QACxB,IAAI,IAAI,CAACH,MAAM,EAAE,OAAOG;QACxB,IAAI,OAAOC,WAAW,aAAa;YACjC,IAAI,CAACH,cAAc,CAACI,IAAI,CAACF;QAC3B;IACF;AACF;AAEA,4EAA4E;AAC5E,MAAMG,oBAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAMC,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,iGAAiG;AACjGC,OAAOC,cAAc,CAACX,iBAAiB,UAAU;IAC/CY;QACE,OAAOf,OAAOgB,MAAM;IACtB;AACF;AAEA,SAASC;IACP,IAAI,CAACd,gBAAgBC,MAAM,EAAE;QAC3B,MAAMc,UACJ,gCACA;QACF,MAAM,IAAIC,MAAMD;IAClB;IACA,OAAOf,gBAAgBC,MAAM;AAC/B;AAEAM,kBAAkBU,OAAO,CAAC,CAACC;IACzB,sEAAsE;IACtE,6CAA6C;IAC7C,kEAAkE;IAClE,0BAA0B;IAC1BR,OAAOC,cAAc,CAACX,iBAAiBkB,OAAO;QAC5CN;YACE,MAAMX,SAASa;YACf,OAAOb,MAAM,CAACiB,MAAM;QACtB;IACF;AACF;AAEAT,iBAAiBQ,OAAO,CAAC,CAACC;IAEtBlB,eAAuB,CAACkB,MAAM,GAAG;yCAAIC;YAAAA;;QACrC,MAAMlB,SAASa;QACf,OAAOb,MAAM,CAACiB,MAAM,IAAIC;IAC1B;AACF;AAEAX,aAAaS,OAAO,CAAC,CAACG;IACpBpB,gBAAgBG,KAAK,CAAC;QACpBN,OAAOgB,MAAM,CAACQ,EAAE,CAACD,OAAO;6CAAID;gBAAAA;;YAC1B,MAAMG,aAAa,AAAC,OAAIF,MAAMG,MAAM,CAAC,GAAGC,WAAW,KAAKJ,MAAMK,SAAS,CACrE;YAEF,MAAMC,mBAAmB1B;YACzB,IAAI0B,gBAAgB,CAACJ,WAAW,EAAE;gBAChC,IAAI;oBACFI,gBAAgB,CAACJ,WAAW,IAAIH;gBAClC,EAAE,OAAOQ,KAAK;oBACZC,QAAQC,KAAK,CAAC,AAAC,0CAAuCP;oBACtDM,QAAQC,KAAK,CACX9B,QAAQ4B,OAAO,AAAGA,IAAIZ,OAAO,GAAC,OAAIY,IAAIG,KAAK,GAAKH,MAAM;gBAE1D;YACF;QACF;IACF;AACF;AAEA,yDAAyD;AACzD,eAAe3B,gBAAkC;AAEjD,8BAA8B;AAC9B,SAAS+B,WAAWC,UAAU,QAAQ,gBAAe;AAErD;;;;;CAKC,GACD,OAAO,SAASC;IACd,MAAMhC,SAASL,MAAMsC,UAAU,CAACpC;IAChC,IAAI,CAACG,QAAQ;QACX,MAAM,IAAIe,MACR;IAEJ;IAEA,OAAOf;AACT;AAEA;;;;;CAKC,GACD,OAAO,SAASkC;IACd,IAAA,IAAA,OAAA,UAAA,QAAA,AAAGhB,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAGA,KAAH,QAAA,SAAA,CAAA,KAA6C;;IAE7CnB,gBAAgBC,MAAM,GAAG,IAAIJ,UAAUsB;IACvCnB,gBAAgBE,cAAc,CAACe,OAAO,CAAC,CAACmB,KAAOA;IAC/CpC,gBAAgBE,cAAc,GAAG,EAAE;IAEnC,OAAOF,gBAAgBC,MAAM;AAC/B;AAEA;;;CAGC,GACD,OAAO,SAASoC,yBAAyBpC,MAAc;IACrD,MAAMqC,eAAerC;IACrB,MAAMsC,WAAW,CAAC;IAElB,KAAK,MAAMC,YAAYjC,kBAAmB;QACxC,IAAI,OAAO+B,YAAY,CAACE,SAAS,KAAK,UAAU;YAC9CD,QAAQ,CAACC,SAAS,GAAG9B,OAAO+B,MAAM,CAChCC,MAAMC,OAAO,CAACL,YAAY,CAACE,SAAS,IAAI,EAAE,GAAG,CAAC,GAC9CF,YAAY,CAACE,SAAS,EACtB,mCAAmC;;YACrC;QACF;QAEAD,QAAQ,CAACC,SAAS,GAAGF,YAAY,CAACE,SAAS;IAC7C;IAEA,iGAAiG;IACjGD,SAAS1B,MAAM,GAAGhB,OAAOgB,MAAM;IAE/BJ,iBAAiBQ,OAAO,CAAC,CAACC;QACxBqB,QAAQ,CAACrB,MAAM,GAAG;6CAAIC;gBAAAA;;YACpB,OAAOmB,YAAY,CAACpB,MAAM,IAAIC;QAChC;IACF;IAEA,OAAOoB;AACT"}