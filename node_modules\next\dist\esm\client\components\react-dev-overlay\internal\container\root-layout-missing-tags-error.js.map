{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/root-layout-missing-tags-error.tsx"], "names": ["React", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "Overlay", "VersionStalenessInfo", "HotlinkedText", "RootLayoutMissingTagsError", "missingTags", "versionInfo", "noop", "useCallback", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "className", "h3", "id", "p", "text", "map", "tagName", "join"], "mappings": ";AAAA,YAAYA,WAAW,QAAO;AAE9B,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,uBAAsB;AAC1E,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,aAAa,QAAQ,gCAA+B;AAO7D,OAAO,MAAMC,6BACX,SAASA,2BAA2B,KAA4B;IAA5B,IAAA,EAAEC,WAAW,EAAEC,WAAW,EAAE,GAA5B;IAClC,MAAMC,OAAOV,MAAMW,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,qBACE,KAACP;kBACC,cAAA,KAACH;YACCW,MAAK;YACLC,mBAAgB;YAChBC,oBAAiB;YACjBC,SAASL;sBAET,cAAA,KAACR;0BACC,cAAA,MAACC;oBAAaa,WAAU;;sCACtB,KAACC;4BAAGC,IAAG;sCAAiC;;wBAGvCT,4BAAc,KAACJ;4BAAsB,GAAGI,WAAW;6BAAO;sCAC3D,KAACU;4BACCD,IAAG;4BACHF,WAAU;sCAEV,cAAA,KAACV;gCACCc,MAAM,AAAC,wDAAqDZ,YACzDa,GAAG,CAAC,CAACC,UAAY,AAAC,MAAGA,UAAQ,KAC7BC,IAAI,CACH,QACA;;;;;;;;AAQpB,EAAC"}