{"version": 3, "file": "AsYouTypeFormatter.util.js", "names": ["DIGIT_PLACEHOLDER", "exports", "DIGIT_PLACEHOLDER_MATCHER", "RegExp", "countOccurences", "symbol", "string", "count", "_iterator", "_createForOfIteratorHelperLoose", "split", "_step", "done", "character", "value", "repeat", "times", "result", "cutAndStripNonPairedParens", "cutBeforeIndex", "stripNonPairedParens", "slice", "closeNonPairedParens", "template", "cut_before", "retained_template", "opening_braces", "closing_braces", "dangling_braces", "length", "i", "push", "pop", "start", "cleared_string", "_i", "_dangling_braces", "index", "populateTemplateWithDigits", "position", "digits", "_iterator2", "_step2", "digit", "search", "replace"], "sources": ["../source/AsYouTypeFormatter.util.js"], "sourcesContent": ["// Should be the same as `DIGIT_PLACEHOLDER` in `libphonenumber-metadata-generator`.\r\nexport const DIGIT_PLACEHOLDER = 'x' // '\\u2008' (punctuation space)\r\nconst DIGIT_PLACEHOLDER_MATCHER = new RegExp(DIGIT_PLACEHOLDER)\r\n\r\n// Counts all occurences of a symbol in a string.\r\n// Unicode-unsafe (because using `.split()`).\r\nexport function countOccurences(symbol, string) {\r\n\tlet count = 0\r\n\t// Using `.split('')` to iterate through a string here\r\n\t// to avoid requiring `Symbol.iterator` polyfill.\r\n\t// `.split('')` is generally not safe for Unicode,\r\n\t// but in this particular case for counting brackets it is safe.\r\n\t// for (const character of string)\r\n\tfor (const character of string.split('')) {\r\n\t\tif (character === symbol) {\r\n\t\t\tcount++\r\n\t\t}\r\n\t}\r\n\treturn count\r\n}\r\n\r\n// Repeats a string (or a symbol) N times.\r\n// http://stackoverflow.com/questions/202605/repeat-string-javascript\r\nexport function repeat(string, times) {\r\n\tif (times < 1) {\r\n\t\treturn ''\r\n\t}\r\n\tlet result = ''\r\n\twhile (times > 1) {\r\n\t\tif (times & 1) {\r\n\t\t\tresult += string\r\n\t\t}\r\n\t\ttimes >>= 1\r\n\t\tstring += string\r\n\t}\r\n\treturn result + string\r\n}\r\n\r\nexport function cutAndStripNonPairedParens(string, cutBeforeIndex) {\r\n\tif (string[cutBeforeIndex] === ')') {\r\n\t\tcutBeforeIndex++\r\n\t}\r\n\treturn stripNonPairedParens(string.slice(0, cutBeforeIndex))\r\n}\r\n\r\nexport function closeNonPairedParens(template, cut_before) {\r\n\tconst retained_template = template.slice(0, cut_before)\r\n\tconst opening_braces = countOccurences('(', retained_template)\r\n\tconst closing_braces = countOccurences(')', retained_template)\r\n\tlet dangling_braces = opening_braces - closing_braces\r\n\twhile (dangling_braces > 0 && cut_before < template.length) {\r\n\t\tif (template[cut_before] === ')') {\r\n\t\t\tdangling_braces--\r\n\t\t}\r\n\t\tcut_before++\r\n\t}\r\n\treturn template.slice(0, cut_before)\r\n}\r\n\r\nexport function stripNonPairedParens(string) {\r\n\tconst dangling_braces =[]\r\n\tlet i = 0\r\n\twhile (i < string.length) {\r\n\t\tif (string[i] === '(') {\r\n\t\t\tdangling_braces.push(i)\r\n\t\t}\r\n\t\telse if (string[i] === ')') {\r\n\t\t\tdangling_braces.pop()\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\tlet start = 0\r\n\tlet cleared_string = ''\r\n\tdangling_braces.push(string.length)\r\n\tfor (const index of dangling_braces) {\r\n\t\tcleared_string += string.slice(start, index)\r\n\t\tstart = index + 1\r\n\t}\r\n\treturn cleared_string\r\n}\r\n\r\nexport function populateTemplateWithDigits(template, position, digits) {\r\n\t// Using `.split('')` to iterate through a string here\r\n\t// to avoid requiring `Symbol.iterator` polyfill.\r\n\t// `.split('')` is generally not safe for Unicode,\r\n\t// but in this particular case for `digits` it is safe.\r\n\t// for (const digit of digits)\r\n\tfor (const digit of digits.split('')) {\r\n\t\t// If there is room for more digits in current `template`,\r\n\t\t// then set the next digit in the `template`,\r\n\t\t// and return the formatted digits so far.\r\n\t\t// If more digits are entered than the current format could handle.\r\n\t\tif (template.slice(position + 1).search(DIGIT_PLACEHOLDER_MATCHER) < 0) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tposition = template.search(DIGIT_PLACEHOLDER_MATCHER)\r\n\t\ttemplate = template.replace(DIGIT_PLACEHOLDER_MATCHER, digit)\r\n\t}\r\n\treturn [template, position]\r\n}"], "mappings": ";;;;;;;;;;;;;;;AAAA;AACO,IAAMA,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,GAAG,GAAG,EAAC;AACrC,IAAME,yBAAyB,GAAG,IAAIC,MAAM,CAACH,iBAAiB,CAAC;;AAE/D;AACA;AACO,SAASI,eAAeA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/C,IAAIC,KAAK,GAAG,CAAC;EACb;EACA;EACA;EACA;EACA;EACA,SAAAC,SAAA,GAAAC,+BAAA,CAAwBH,MAAM,CAACI,KAAK,CAAC,EAAE,CAAC,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAI,IAAA,GAAE;IAAA,IAA/BC,SAAS,GAAAF,KAAA,CAAAG,KAAA;IACnB,IAAID,SAAS,KAAKR,MAAM,EAAE;MACzBE,KAAK,EAAE;IACR;EACD;EACA,OAAOA,KAAK;AACb;;AAEA;AACA;AACO,SAASQ,MAAMA,CAACT,MAAM,EAAEU,KAAK,EAAE;EACrC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACd,OAAO,EAAE;EACV;EACA,IAAIC,MAAM,GAAG,EAAE;EACf,OAAOD,KAAK,GAAG,CAAC,EAAE;IACjB,IAAIA,KAAK,GAAG,CAAC,EAAE;MACdC,MAAM,IAAIX,MAAM;IACjB;IACAU,KAAK,KAAK,CAAC;IACXV,MAAM,IAAIA,MAAM;EACjB;EACA,OAAOW,MAAM,GAAGX,MAAM;AACvB;AAEO,SAASY,0BAA0BA,CAACZ,MAAM,EAAEa,cAAc,EAAE;EAClE,IAAIb,MAAM,CAACa,cAAc,CAAC,KAAK,GAAG,EAAE;IACnCA,cAAc,EAAE;EACjB;EACA,OAAOC,oBAAoB,CAACd,MAAM,CAACe,KAAK,CAAC,CAAC,EAAEF,cAAc,CAAC,CAAC;AAC7D;AAEO,SAASG,oBAAoBA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC1D,IAAMC,iBAAiB,GAAGF,QAAQ,CAACF,KAAK,CAAC,CAAC,EAAEG,UAAU,CAAC;EACvD,IAAME,cAAc,GAAGtB,eAAe,CAAC,GAAG,EAAEqB,iBAAiB,CAAC;EAC9D,IAAME,cAAc,GAAGvB,eAAe,CAAC,GAAG,EAAEqB,iBAAiB,CAAC;EAC9D,IAAIG,eAAe,GAAGF,cAAc,GAAGC,cAAc;EACrD,OAAOC,eAAe,GAAG,CAAC,IAAIJ,UAAU,GAAGD,QAAQ,CAACM,MAAM,EAAE;IAC3D,IAAIN,QAAQ,CAACC,UAAU,CAAC,KAAK,GAAG,EAAE;MACjCI,eAAe,EAAE;IAClB;IACAJ,UAAU,EAAE;EACb;EACA,OAAOD,QAAQ,CAACF,KAAK,CAAC,CAAC,EAAEG,UAAU,CAAC;AACrC;AAEO,SAASJ,oBAAoBA,CAACd,MAAM,EAAE;EAC5C,IAAMsB,eAAe,GAAE,EAAE;EACzB,IAAIE,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGxB,MAAM,CAACuB,MAAM,EAAE;IACzB,IAAIvB,MAAM,CAACwB,CAAC,CAAC,KAAK,GAAG,EAAE;MACtBF,eAAe,CAACG,IAAI,CAACD,CAAC,CAAC;IACxB,CAAC,MACI,IAAIxB,MAAM,CAACwB,CAAC,CAAC,KAAK,GAAG,EAAE;MAC3BF,eAAe,CAACI,GAAG,CAAC,CAAC;IACtB;IACAF,CAAC,EAAE;EACJ;EACA,IAAIG,KAAK,GAAG,CAAC;EACb,IAAIC,cAAc,GAAG,EAAE;EACvBN,eAAe,CAACG,IAAI,CAACzB,MAAM,CAACuB,MAAM,CAAC;EACnC,SAAAM,EAAA,MAAAC,gBAAA,GAAoBR,eAAe,EAAAO,EAAA,GAAAC,gBAAA,CAAAP,MAAA,EAAAM,EAAA,IAAE;IAAhC,IAAME,KAAK,GAAAD,gBAAA,CAAAD,EAAA;IACfD,cAAc,IAAI5B,MAAM,CAACe,KAAK,CAACY,KAAK,EAAEI,KAAK,CAAC;IAC5CJ,KAAK,GAAGI,KAAK,GAAG,CAAC;EAClB;EACA,OAAOH,cAAc;AACtB;AAEO,SAASI,0BAA0BA,CAACf,QAAQ,EAAEgB,QAAQ,EAAEC,MAAM,EAAE;EACtE;EACA;EACA;EACA;EACA;EACA,SAAAC,UAAA,GAAAhC,+BAAA,CAAoB+B,MAAM,CAAC9B,KAAK,CAAC,EAAE,CAAC,GAAAgC,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAA7B,IAAA,GAAE;IAAA,IAA3B+B,KAAK,GAAAD,MAAA,CAAA5B,KAAA;IACf;IACA;IACA;IACA;IACA,IAAIS,QAAQ,CAACF,KAAK,CAACkB,QAAQ,GAAG,CAAC,CAAC,CAACK,MAAM,CAAC1C,yBAAyB,CAAC,GAAG,CAAC,EAAE;MACvE;IACD;IACAqC,QAAQ,GAAGhB,QAAQ,CAACqB,MAAM,CAAC1C,yBAAyB,CAAC;IACrDqB,QAAQ,GAAGA,QAAQ,CAACsB,OAAO,CAAC3C,yBAAyB,EAAEyC,KAAK,CAAC;EAC9D;EACA,OAAO,CAACpB,QAAQ,EAAEgB,QAAQ,CAAC;AAC5B", "ignoreList": []}