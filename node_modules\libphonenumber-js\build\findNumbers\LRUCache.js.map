{"version": 3, "file": "LRUCache.js", "names": ["Node", "_createClass", "key", "value", "next", "arguments", "length", "undefined", "prev", "_classCallCheck", "L<PERSON><PERSON><PERSON>", "exports", "limit", "size", "head", "tail", "cache", "put", "ensureLimit", "node", "get", "remove", "console", "log", "concat", "clear"], "sources": ["../../source/findNumbers/LRUCache.js"], "sourcesContent": ["// https://medium.com/dsinjs/implementing-lru-cache-in-javascript-94ba6755cda9\r\n\r\nclass Node {\r\n  constructor(key, value, next = null, prev = null) {\r\n    this.key = key;\r\n    this.value = value;\r\n    this.next = next;\r\n    this.prev = prev;\r\n  }\r\n}\r\n\r\nexport default class LRUCache {\r\n  //set default limit of 10 if limit is not passed.\r\n  constructor(limit = 10) {\r\n    this.size = 0;\r\n    this.limit = limit;\r\n    this.head = null;\r\n    this.tail = null;\r\n    this.cache = {};\r\n  }\r\n\r\n  // Write Node to head of LinkedList\r\n  // update cache with Node key and Node reference\r\n  put(key, value){\r\n    this.ensureLimit();\r\n\r\n    if(!this.head){\r\n      this.head = this.tail = new Node(key, value);\r\n    }else{\r\n      const node = new Node(key, value, this.head);\r\n      this.head.prev = node;\r\n      this.head = node;\r\n    }\r\n\r\n    //Update the cache map\r\n    this.cache[key] = this.head;\r\n    this.size++;\r\n  }\r\n\r\n  // Read from cache map and make that node as new Head of LinkedList\r\n  get(key){\r\n    if(this.cache[key]){\r\n      const value = this.cache[key].value;\r\n\r\n      // node removed from it's position and cache\r\n      this.remove(key)\r\n      // write node again to the head of LinkedList to make it most recently used\r\n      this.put(key, value);\r\n\r\n      return value;\r\n    }\r\n\r\n    console.log(`Item not available in cache for key ${key}`);\r\n  }\r\n\r\n  ensureLimit(){\r\n    if(this.size === this.limit){\r\n      this.remove(this.tail.key)\r\n    }\r\n  }\r\n\r\n  remove(key){\r\n    const node = this.cache[key];\r\n\r\n    if(node.prev !== null){\r\n      node.prev.next = node.next;\r\n    }else{\r\n      this.head = node.next;\r\n    }\r\n\r\n    if(node.next !== null){\r\n      node.next.prev = node.prev;\r\n    }else{\r\n      this.tail = node.prev\r\n    }\r\n\r\n    delete this.cache[key];\r\n    this.size--;\r\n  }\r\n\r\n  clear() {\r\n    this.head = null;\r\n    this.tail = null;\r\n    this.size = 0;\r\n    this.cache = {};\r\n  }\r\n\r\n  // // Invokes the callback function with every node of the chain and the index of the node.\r\n  // forEach(fn) {\r\n  //   let node = this.head;\r\n  //   let counter = 0;\r\n  //   while (node) {\r\n  //     fn(node, counter);\r\n  //     node = node.next;\r\n  //     counter++;\r\n  //   }\r\n  // }\r\n\r\n  // // To iterate over LRU with a 'for...of' loop\r\n  // *[Symbol.iterator]() {\r\n  //   let node = this.head;\r\n  //   while (node) {\r\n  //     yield node;\r\n  //     node = node.next;\r\n  //   }\r\n  // }\r\n}"], "mappings": ";;;;;;;;;;;;AAAA;AAAA,IAEMA,IAAI,gBAAAC,YAAA,CACR,SAAAD,KAAYE,GAAG,EAAEC,KAAK,EAA4B;EAAA,IAA1BC,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAA,IAAEG,IAAI,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAAAI,eAAA,OAAAT,IAAA;EAC9C,IAAI,CAACE,GAAG,GAAGA,GAAG;EACd,IAAI,CAACC,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACI,IAAI,GAAGA,IAAI;AAClB,CAAC;AAAA,IAGkBE,QAAQ,GAAAC,OAAA;EAC3B;EACA,SAAAD,SAAA,EAAwB;IAAA,IAAZE,KAAK,GAAAP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IAAAI,eAAA,OAAAC,QAAA;IACpB,IAAI,CAACG,IAAI,GAAG,CAAC;IACb,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;EACjB;;EAEA;EACA;EAAA,OAAAf,YAAA,CAAAS,QAAA;IAAAR,GAAA;IAAAC,KAAA,EACA,SAAAc,GAAGA,CAACf,GAAG,EAAEC,KAAK,EAAC;MACb,IAAI,CAACe,WAAW,CAAC,CAAC;MAElB,IAAG,CAAC,IAAI,CAACJ,IAAI,EAAC;QACZ,IAAI,CAACA,IAAI,GAAG,IAAI,CAACC,IAAI,GAAG,IAAIf,IAAI,CAACE,GAAG,EAAEC,KAAK,CAAC;MAC9C,CAAC,MAAI;QACH,IAAMgB,IAAI,GAAG,IAAInB,IAAI,CAACE,GAAG,EAAEC,KAAK,EAAE,IAAI,CAACW,IAAI,CAAC;QAC5C,IAAI,CAACA,IAAI,CAACN,IAAI,GAAGW,IAAI;QACrB,IAAI,CAACL,IAAI,GAAGK,IAAI;MAClB;;MAEA;MACA,IAAI,CAACH,KAAK,CAACd,GAAG,CAAC,GAAG,IAAI,CAACY,IAAI;MAC3B,IAAI,CAACD,IAAI,EAAE;IACb;;IAEA;EAAA;IAAAX,GAAA;IAAAC,KAAA,EACA,SAAAiB,GAAGA,CAAClB,GAAG,EAAC;MACN,IAAG,IAAI,CAACc,KAAK,CAACd,GAAG,CAAC,EAAC;QACjB,IAAMC,KAAK,GAAG,IAAI,CAACa,KAAK,CAACd,GAAG,CAAC,CAACC,KAAK;;QAEnC;QACA,IAAI,CAACkB,MAAM,CAACnB,GAAG,CAAC;QAChB;QACA,IAAI,CAACe,GAAG,CAACf,GAAG,EAAEC,KAAK,CAAC;QAEpB,OAAOA,KAAK;MACd;MAEAmB,OAAO,CAACC,GAAG,wCAAAC,MAAA,CAAwCtB,GAAG,CAAE,CAAC;IAC3D;EAAC;IAAAA,GAAA;IAAAC,KAAA,EAED,SAAAe,WAAWA,CAAA,EAAE;MACX,IAAG,IAAI,CAACL,IAAI,KAAK,IAAI,CAACD,KAAK,EAAC;QAC1B,IAAI,CAACS,MAAM,CAAC,IAAI,CAACN,IAAI,CAACb,GAAG,CAAC;MAC5B;IACF;EAAC;IAAAA,GAAA;IAAAC,KAAA,EAED,SAAAkB,MAAMA,CAACnB,GAAG,EAAC;MACT,IAAMiB,IAAI,GAAG,IAAI,CAACH,KAAK,CAACd,GAAG,CAAC;MAE5B,IAAGiB,IAAI,CAACX,IAAI,KAAK,IAAI,EAAC;QACpBW,IAAI,CAACX,IAAI,CAACJ,IAAI,GAAGe,IAAI,CAACf,IAAI;MAC5B,CAAC,MAAI;QACH,IAAI,CAACU,IAAI,GAAGK,IAAI,CAACf,IAAI;MACvB;MAEA,IAAGe,IAAI,CAACf,IAAI,KAAK,IAAI,EAAC;QACpBe,IAAI,CAACf,IAAI,CAACI,IAAI,GAAGW,IAAI,CAACX,IAAI;MAC5B,CAAC,MAAI;QACH,IAAI,CAACO,IAAI,GAAGI,IAAI,CAACX,IAAI;MACvB;MAEA,OAAO,IAAI,CAACQ,KAAK,CAACd,GAAG,CAAC;MACtB,IAAI,CAACW,IAAI,EAAE;IACb;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAED,SAAAsB,KAAKA,CAAA,EAAG;MACN,IAAI,CAACX,IAAI,GAAG,IAAI;MAChB,IAAI,CAACC,IAAI,GAAG,IAAI;MAChB,IAAI,CAACF,IAAI,GAAG,CAAC;MACb,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;IACjB;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;AAAA", "ignoreList": []}