{"version": 3, "sources": ["../../../src/client/components/layout-router.tsx"], "names": ["React", "useContext", "use", "startTransition", "Suspense", "useDeferredValue", "ReactDOM", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "fetchServerResponse", "unresolvedThenable", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "handleSmoothScroll", "RedirectBoundary", "NotFoundBoundary", "getSegmentValue", "createRouterCache<PERSON>ey", "hasInterceptionRouteInCurrentTree", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "hasOwnProperty", "subTree", "undefined", "slice", "findDOMNode", "instance", "window", "process", "env", "NODE_ENV", "originalConsoleError", "console", "error", "messages", "includes", "rectProperties", "shouldSkipElement", "element", "getComputedStyle", "position", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "Error", "InnerLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "url", "childNodes", "tree", "cache<PERSON>ey", "buildId", "changeByServerResponse", "fullTree", "childNode", "get", "newLazyCacheNode", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "lazyDataResolved", "loading", "set", "resolvedPrefetchRsc", "resolvedRsc", "then", "refetchTree", "includeNextUrl", "URL", "location", "origin", "nextUrl", "serverResponse", "setTimeout", "previousTree", "subtree", "Provider", "value", "LoadingBoundary", "hasLoading", "loadingStyles", "loadingScripts", "fallback", "OuterLayoutRouter", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "notFoundStyles", "childNodesForParallelRouter", "treeSegment", "currentChildSegmentValue", "preservedSegments", "map", "preservedSegment", "preservedSegmentValue", "errorComponent", "Boolean", "isActive"], "mappings": "AAAA;;AAcA,OAAOA,SACLC,UAAU,EACVC,GAAG,EACHC,eAAe,EACfC,QAAQ,EACRC,gBAAgB,QACX,QAAO;AACd,OAAOC,cAAc,YAAW;AAChC,SACEC,mBAAmB,EACnBC,yBAAyB,EACzBC,eAAe,QACV,qDAAoD;AAC3D,SAASC,mBAAmB,QAAQ,yCAAwC;AAC5E,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,kBAAkB,QAAQ,qDAAoD;AACvF,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,gBAAgB,QAAQ,uBAAsB;AACvD,SAASC,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,oBAAoB,QAAQ,2CAA0C;AAC/E,SAASC,iCAAiC,QAAQ,mEAAkE;AAEpH;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIb,aAAaS,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACK,cAAc,CAACH,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMG,UAAUR,eACdS,WACAP,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBI,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLN,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBS,KAAK,CAAC,IACxBR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,4FAA4F;AAC5F;;CAEC,GACD,SAASS,YACPC,QAAoD;IAEpD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAC1C,wDAAwD;IACxD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,uBAAuBC,QAAQC,KAAK;QAC1C,IAAI;YACFD,QAAQC,KAAK,GAAG;iDAAIC;oBAAAA;;gBAClB,4DAA4D;gBAC5D,IAAI,CAACA,QAAQ,CAAC,EAAE,CAACC,QAAQ,CAAC,6CAA6C;oBACrEJ,wBAAwBG;gBAC1B;YACF;YACA,OAAOlC,SAASyB,WAAW,CAACC;QAC9B,SAAU;YACRM,QAAQC,KAAK,GAAGF;QAClB;IACF;IACA,OAAO/B,SAASyB,WAAW,CAACC;AAC9B;AAEA,MAAMU,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACH,QAAQ,CAACI,iBAAiBD,SAASE,QAAQ,GAAG;QACpE,IAAIZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CE,QAAQS,IAAI,CACV,4FACAH;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMI,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOP,eAAeQ,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBR,OAAoB,EAAES,cAAsB;IAC1E,MAAML,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,yBAAxBC,2BACA,8FAA8F;IAC9FA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmC7D,MAAM8D,SAAS;IAoGtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;;aAhHAN,wBAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAC9C,MAAM,KAAK,KAC1C,CAACyC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYrB,KAAK,CAAC,CAAC3B,SAASoD,QAC1B9D,aAAaU,SAASmD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMpB,eAAeW,kBAAkBX,YAAY;gBAEnD,IAAIA,cAAc;oBAChBoB,UAAUrB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACoB,SAAS;oBACZA,UAAU7C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE6C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMnC,kBAAkBiC,SAAU;oBACtE,uGAAuG;oBACvG,IAAIA,QAAQG,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAH,UAAUA,QAAQG,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7EZ,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBX,YAAY,GAAG;gBACjCW,kBAAkBK,YAAY,GAAG,EAAE;gBAEnC1D,mBACE;oBACE,uEAAuE;oBACvE,IAAI0C,cAAc;wBACdoB,QAAwBI,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAcxB,SAASyB,eAAe;oBAC5C,MAAM7B,iBAAiB4B,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAI/B,uBAAuBwB,SAAwBvB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7H4B,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAAChC,uBAAuBwB,SAAwBvB,iBAAiB;wBAEjEuB,QAAwBI,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBnB,kBAAkBmB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxEnB,kBAAkBmB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BV,QAAQW,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BjB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMmB,UAAUxF,WAAWO;IAC3B,IAAI,CAACiF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,qBACE,KAAC7B;QACCU,aAAaA;QACbJ,mBAAmBsB,QAAQtB,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAASqB,kBAAkB,KAiB1B;IAjB0B,IAAA,EACzBC,iBAAiB,EACjBC,GAAG,EACHC,UAAU,EACVvB,WAAW,EACXwB,IAAI,EACJ,oDAAoD;IACpD,YAAY;IACZC,QAAQ,EAST,GAjB0B;IAkBzB,MAAMP,UAAUxF,WAAWO;IAC3B,IAAI,CAACiF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAM,EAAEO,OAAO,EAAEC,sBAAsB,EAAEH,MAAMI,QAAQ,EAAE,GAAGV;IAE5D,yDAAyD;IACzD,IAAIW,YAAYN,WAAWO,GAAG,CAACL;IAE/B,2EAA2E;IAC3E,sBAAsB;IACtB,IAAII,cAAcvE,WAAW;QAC3B,MAAMyE,mBAAkC;YACtCC,UAAU;YACVC,KAAK;YACLC,aAAa;YACbC,MAAM;YACNC,cAAc;YACdC,gBAAgB,IAAIC;YACpBC,kBAAkB;YAClBC,SAAS;QACX;QAEA;;KAEC,GACDX,YAAYE;QACZR,WAAWkB,GAAG,CAAChB,UAAUM;IAC3B;IAEA,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMW,sBACJb,UAAUK,WAAW,KAAK,OAAOL,UAAUK,WAAW,GAAGL,UAAUI,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,EAAE;IACF,qEAAqE;IACrE,0EAA0E;IAC1E,gBAAgB;IAChB,MAAMA,MAAWnG,iBAAiB+F,UAAUI,GAAG,EAAES;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMC,cACJ,OAAOV,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIW,IAAI,KAAK,aAC3DjH,IAAIsG,OACJA;IAEN,IAAI,CAACU,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,8CAA8C;QAC9C,IAAIX,WAAWH,UAAUG,QAAQ;QACjC,IAAIA,aAAa,MAAM;YACrB;;OAEC,GACD,sBAAsB;YACtB,MAAMa,cAAchG,eAAe;gBAAC;mBAAOmD;aAAY,EAAE4B;YACzD,MAAMkB,iBAAiBlG,kCAAkCgF;YACzDC,UAAUG,QAAQ,GAAGA,WAAW7F,oBAC9B,IAAI4G,IAAIzB,KAAK0B,SAASC,MAAM,GAC5BJ,aACAC,iBAAiB5B,QAAQgC,OAAO,GAAG,MACnCxB;YAEFG,UAAUU,gBAAgB,GAAG;QAC/B;QAEA;;KAEC,GACD,8DAA8D;QAC9D,MAAMY,iBAAiBxH,IAAIqG;QAE3B,IAAI,CAACH,UAAUU,gBAAgB,EAAE;YAC/B,wGAAwG;YACxGa,WAAW;gBACTxH,gBAAgB;oBACd+F,uBAAuB;wBACrB0B,cAAczB;wBACduB;oBACF;gBACF;YACF;YAEA,uHAAuH;YACvH,yBAAyB;YACzBtB,UAAUU,gBAAgB,GAAG;QAC/B;QACA,yGAAyG;QACzG,iIAAiI;QACjI5G,IAAIS;IACN;IAEA,yEAAyE;IACzE,MAAMkH,UACJ,4EAA4E;kBAC5E,KAACtH,oBAAoBuH,QAAQ;QAC3BC,OAAO;YACLhC,MAAMA,IAAI,CAAC,EAAE,CAACH,kBAAkB;YAChCE,YAAYM,UAAUQ,cAAc;YACpC,kDAAkD;YAClDf,KAAKA;YACLkB,SAASX,UAAUW,OAAO;QAC5B;kBAECG;;IAGL,iFAAiF;IACjF,OAAOW;AACT;AAEA;;;CAGC,GACD,SAASG,gBAAgB,KAYxB;IAZwB,IAAA,EACvB1D,QAAQ,EACR2D,UAAU,EACVlB,OAAO,EACPmB,aAAa,EACbC,cAAc,EAOf,GAZwB;IAavB,oGAAoG;IACpG,yFAAyF;IACzF,IAAIF,YAAY;QACd,qBACE,KAAC7H;YACCgI,wBACE;;oBACGF;oBACAC;oBACApB;;;sBAIJzC;;IAGP;IAEA,qBAAO;kBAAGA;;AACZ;AAEA;;;CAGC,GACD,eAAe,SAAS+D,kBAAkB,KAsBzC;IAtByC,IAAA,EACxCzC,iBAAiB,EACjBrB,WAAW,EACXhC,KAAK,EACL+F,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,cAAc,EAYf,GAtByC;IAuBxC,MAAMnD,UAAUxF,WAAWM;IAC3B,IAAI,CAACkF,SAAS;QACZ,MAAM,IAAIC,MAAM;IAClB;IAEA,MAAM,EAAEI,UAAU,EAAEC,IAAI,EAAEF,GAAG,EAAEkB,OAAO,EAAE,GAAGtB;IAE3C,4CAA4C;IAC5C,IAAIoD,8BAA8B/C,WAAWO,GAAG,CAACT;IACjD,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACiD,6BAA6B;QAChCA,8BAA8B,IAAIhC;QAClCf,WAAWkB,GAAG,CAACpB,mBAAmBiD;IACpC;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMC,cAAc/C,IAAI,CAAC,EAAE,CAACH,kBAAkB,CAAC,EAAE;IAEjD,gIAAgI;IAChI,MAAMmD,2BAA2B9H,gBAAgB6H;IAEjD;;GAEC,GACD,+DAA+D;IAC/D,MAAME,oBAA+B;QAACF;KAAY;IAElD,qBACE;kBACGE,kBAAkBC,GAAG,CAAC,CAACC;YACtB,MAAMC,wBAAwBlI,gBAAgBiI;YAC9C,MAAMlD,WAAW9E,qBAAqBgI;YAEtC,OACE;;;;;;;;UAQA,iBACA,MAACzI,gBAAgBqH,QAAQ;gBAEvBC,qBACE,KAACvC;oBAAsBjB,aAAaA;8BAClC,cAAA,KAAC3D;wBACCwI,gBAAgB7G;wBAChB+F,aAAaA;wBACbC,cAAcA;kCAEd,cAAA,KAACP;4BACCC,YAAYoB,QAAQtC;4BACpBA,OAAO,EAAEA,2BAAAA,OAAS,CAAC,EAAE;4BACrBmB,aAAa,EAAEnB,2BAAAA,OAAS,CAAC,EAAE;4BAC3BoB,cAAc,EAAEpB,2BAAAA,OAAS,CAAC,EAAE;sCAE5B,cAAA,KAAC/F;gCACC2H,UAAUA;gCACVC,gBAAgBA;0CAEhB,cAAA,KAAC7H;8CACC,cAAA,KAAC4E;wCACCC,mBAAmBA;wCACnBC,KAAKA;wCACLE,MAAMA;wCACND,YAAY+C;wCACZtE,aAAaA;wCACbyB,UAAUA;wCACVsD,UACEP,6BAA6BI;;;;;;;;oBAU5CX;oBACAC;oBACAC;;eAvCIxH,qBAAqBgI,kBAAkB;QA0ClD;;AAGN"}