{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Overlay/body-locker.ts"], "names": ["lock", "unlock", "previousBodyPaddingRight", "previousBodyOverflowSetting", "activeLocks", "setTimeout", "scrollBarGap", "window", "innerWidth", "document", "documentElement", "clientWidth", "body", "style", "paddingRight", "overflow", "undefined"], "mappings": ";;;;;;;;;;;;;;;IAKgBA,IAAI;eAAJA;;IAmBAC,MAAM;eAANA;;;AAxBhB,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,cAAc;AAEX,SAASJ;IACdK,WAAW;QACT,IAAID,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAME,eACJC,OAAOC,UAAU,GAAGC,SAASC,eAAe,CAACC,WAAW;QAE1D,IAAIL,eAAe,GAAG;YACpBJ,2BAA2BO,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY;YAC3DL,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAG,AAAC,KAAER,eAAa;QACrD;QAEAH,8BAA8BM,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ;QAC1DN,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG;IACjC;AACF;AAEO,SAASd;IACdI,WAAW;QACT,IAAID,gBAAgB,KAAK,EAAEA,gBAAgB,GAAG;YAC5C;QACF;QAEA,IAAIF,6BAA6Bc,WAAW;YAC1CP,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAGZ;YACnCA,2BAA2Bc;QAC7B;QAEA,IAAIb,gCAAgCa,WAAW;YAC7CP,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAGZ;YAC/BA,8BAA8Ba;QAChC;IACF;AACF"}