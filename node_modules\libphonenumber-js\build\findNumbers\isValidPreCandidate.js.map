{"version": 3, "file": "isValidPreCandidate.js", "names": ["SLASH_SEPARATED_DATES", "TIME_STAMPS", "TIME_STAMPS_SUFFIX_LEADING", "isValidPreCandidate", "candidate", "offset", "text", "test", "followingText", "slice", "length"], "sources": ["../../source/findNumbers/isValidPreCandidate.js"], "sourcesContent": ["// Matches strings that look like dates using \"/\" as a separator.\r\n// Examples: 3/10/2011, 31/10/96 or 08/31/95.\r\nconst SLASH_SEPARATED_DATES = /(?:(?:[0-3]?\\d\\/[01]?\\d)|(?:[01]?\\d\\/[0-3]?\\d))\\/(?:[12]\\d)?\\d{2}/\r\n\r\n// Matches timestamps.\r\n// Examples: \"2012-01-02 08:00\".\r\n// Note that the reg-ex does not include the\r\n// trailing \":\\d\\d\" -- that is covered by TIME_STAMPS_SUFFIX.\r\nconst TIME_STAMPS = /[12]\\d{3}[-/]?[01]\\d[-/]?[0-3]\\d +[0-2]\\d$/\r\nconst TIME_STAMPS_SUFFIX_LEADING = /^:[0-5]\\d/\r\n\r\nexport default function isValidPreCandidate(candidate, offset, text)\r\n{\r\n\t// Skip a match that is more likely to be a date.\r\n\tif (SLASH_SEPARATED_DATES.test(candidate)) {\r\n\t\treturn false\r\n\t}\r\n\r\n\t// Skip potential time-stamps.\r\n\tif (TIME_STAMPS.test(candidate))\r\n\t{\r\n\t\tconst followingText = text.slice(offset + candidate.length)\r\n\t\tif (TIME_STAMPS_SUFFIX_LEADING.test(followingText)) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t}\r\n\r\n\treturn true\r\n}"], "mappings": ";;;;;;AAAA;AACA;AACA,IAAMA,qBAAqB,GAAG,mEAAmE;;AAEjG;AACA;AACA;AACA;AACA,IAAMC,WAAW,GAAG,4CAA4C;AAChE,IAAMC,0BAA0B,GAAG,WAAW;AAE/B,SAASC,mBAAmBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EACnE;EACC;EACA,IAAIN,qBAAqB,CAACO,IAAI,CAACH,SAAS,CAAC,EAAE;IAC1C,OAAO,KAAK;EACb;;EAEA;EACA,IAAIH,WAAW,CAACM,IAAI,CAACH,SAAS,CAAC,EAC/B;IACC,IAAMI,aAAa,GAAGF,IAAI,CAACG,KAAK,CAACJ,MAAM,GAAGD,SAAS,CAACM,MAAM,CAAC;IAC3D,IAAIR,0BAA0B,CAACK,IAAI,CAACC,aAAa,CAAC,EAAE;MACnD,OAAO,KAAK;IACb;EACD;EAEA,OAAO,IAAI;AACZ", "ignoreList": []}