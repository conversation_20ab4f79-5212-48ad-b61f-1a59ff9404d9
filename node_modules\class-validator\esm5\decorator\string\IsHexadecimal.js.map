{"version": 3, "file": "IsHexadecimal.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsHexadecimal.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,sBAAsB,MAAM,6BAA6B,CAAC;AAEjE,MAAM,CAAC,IAAM,cAAc,GAAG,eAAe,CAAC;AAE9C;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,KAAc;IAC1C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,sBAAsB,CAAC,KAAK,CAAC,CAAC;AACpE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,aAAa,CAAC,iBAAqC;IACjE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,cAAc;QACpB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,aAAa,CAAC,KAAK,CAAC,EAApB,CAAoB;YACxD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,wCAAwC,EAArD,CAAqD,EACnE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isHexadecimalValidator from 'validator/lib/isHexadecimal';\n\nexport const IS_HEXADECIMAL = 'isHexadecimal';\n\n/**\n * Checks if the string is a hexadecimal number.\n * If given value is not a string, then it returns false.\n */\nexport function isHexadecimal(value: unknown): boolean {\n  return typeof value === 'string' && isHexadecimalValidator(value);\n}\n\n/**\n * Checks if the string is a hexadecimal number.\n * If given value is not a string, then it returns false.\n */\nexport function IsHexadecimal(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_HEXADECIMAL,\n      validator: {\n        validate: (value, args): boolean => isHexadecimal(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a hexadecimal number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}