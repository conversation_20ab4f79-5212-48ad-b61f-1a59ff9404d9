{"version": 3, "file": "RFC3966.test.js", "names": ["_RFC", "require", "describe", "it", "expect", "formatRFC3966", "number", "to", "equal", "ext", "parseRFC3966", "deep"], "sources": ["../../source/helpers/RFC3966.test.js"], "sourcesContent": ["import { parseRFC3966, formatRFC3966 } from './RFC3966.js'\r\n\r\ndescribe('RFC3966', () => {\r\n\tit('should format', () => {\r\n\t\texpect(() => formatRFC3966({ number: '123' })).to.throw('expects \"number\" to be in E.164 format')\r\n\t\texpect(formatRFC3966({})).to.equal('')\r\n\t\texpect(formatRFC3966({ number: '+78005553535' })).to.equal('tel:+78005553535')\r\n\t\texpect(formatRFC3966({ number: '+78005553535', ext: '123' })).to.equal('tel:+78005553535;ext=123')\r\n\t})\r\n\r\n\tit('should parse', () => {\r\n\t\texpect(parseRFC3966('tel:+78005553535')).to.deep.equal({\r\n\t\t\tnumber : '+78005553535'\r\n\t\t})\r\n\r\n\t\texpect(parseRFC3966('tel:+78005553535;ext=123')).to.deep.equal({\r\n\t\t\tnumber : '+78005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// With `phone-context`\r\n\t\texpect(parseRFC3966('tel:8005553535;ext=123;phone-context=+7')).to.deep.equal({\r\n\t\t\tnumber : '+78005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// \"Domain contexts\" are ignored\r\n\t\texpect(parseRFC3966('tel:8005553535;ext=123;phone-context=www.leningrad.spb.ru')).to.deep.equal({\r\n\t\t\tnumber : '8005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// Not a viable phone number.\r\n\t\texpect(parseRFC3966('tel:3')).to.deep.equal({})\r\n\t})\r\n})\r\n"], "mappings": ";;AAAA,IAAAA,IAAA,GAAAC,OAAA;AAEAC,QAAQ,CAAC,SAAS,EAAE,YAAM;EACzBC,EAAE,CAAC,eAAe,EAAE,YAAM;IACzBC,MAAM,CAAC;MAAA,OAAM,IAAAC,kBAAa,EAAC;QAAEC,MAAM,EAAE;MAAM,CAAC,CAAC;IAAA,EAAC,CAACC,EAAE,SAAM,CAAC,wCAAwC,CAAC;IACjGH,MAAM,CAAC,IAAAC,kBAAa,EAAC,CAAC,CAAC,CAAC,CAAC,CAACE,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACtCJ,MAAM,CAAC,IAAAC,kBAAa,EAAC;MAAEC,MAAM,EAAE;IAAe,CAAC,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IAC9EJ,MAAM,CAAC,IAAAC,kBAAa,EAAC;MAAEC,MAAM,EAAE,cAAc;MAAEG,GAAG,EAAE;IAAM,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,0BAA0B,CAAC;EACnG,CAAC,CAAC;EAEFL,EAAE,CAAC,cAAc,EAAE,YAAM;IACxBC,MAAM,CAAC,IAAAM,iBAAY,EAAC,kBAAkB,CAAC,CAAC,CAACH,EAAE,CAACI,IAAI,CAACH,KAAK,CAAC;MACtDF,MAAM,EAAG;IACV,CAAC,CAAC;IAEFF,MAAM,CAAC,IAAAM,iBAAY,EAAC,0BAA0B,CAAC,CAAC,CAACH,EAAE,CAACI,IAAI,CAACH,KAAK,CAAC;MAC9DF,MAAM,EAAG,cAAc;MACvBG,GAAG,EAAM;IACV,CAAC,CAAC;;IAEF;IACAL,MAAM,CAAC,IAAAM,iBAAY,EAAC,yCAAyC,CAAC,CAAC,CAACH,EAAE,CAACI,IAAI,CAACH,KAAK,CAAC;MAC7EF,MAAM,EAAG,cAAc;MACvBG,GAAG,EAAM;IACV,CAAC,CAAC;;IAEF;IACAL,MAAM,CAAC,IAAAM,iBAAY,EAAC,2DAA2D,CAAC,CAAC,CAACH,EAAE,CAACI,IAAI,CAACH,KAAK,CAAC;MAC/FF,MAAM,EAAG,YAAY;MACrBG,GAAG,EAAM;IACV,CAAC,CAAC;;IAEF;IACAL,MAAM,CAAC,IAAAM,iBAAY,EAAC,OAAO,CAAC,CAAC,CAACH,EAAE,CAACI,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}