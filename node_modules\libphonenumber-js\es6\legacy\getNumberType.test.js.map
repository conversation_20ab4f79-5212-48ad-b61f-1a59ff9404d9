{"version": 3, "file": "getNumberType.test.js", "names": ["metadata", "type", "<PERSON><PERSON><PERSON>", "_getNumberType", "getNumberType", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "apply", "describe", "it", "expect", "to", "equal", "thrower", "phone", "country", "something", "_typeof"], "sources": ["../../source/legacy/getNumberType.test.js"], "sourcesContent": ["import metadata from '../../metadata.max.json' with { type: 'json' }\r\nimport Metadata from '../metadata.js'\r\nimport _getNumberType from './getNumberType.js'\r\n\r\nfunction getNumberType(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _getNumberType.apply(this, parameters)\r\n}\r\n\r\ndescribe('getNumberType', () => {\r\n\tit('should infer phone number type MOBILE', () => {\r\n\t\texpect(getNumberType('9150000000', 'RU')).to.equal('MOBILE')\r\n\t\texpect(getNumberType('7912345678', 'GB')).to.equal('MOBILE')\r\n\t\texpect(getNumberType('51234567', 'EE')).to.equal('MOBILE')\r\n\t})\r\n\r\n\tit('should infer phone number types', () =>  {\r\n\t\texpect(getNumberType('88005553535', 'RU')).to.equal('TOLL_FREE')\r\n\t\texpect(getNumberType('8005553535', 'RU')).to.equal('TOLL_FREE')\r\n\t\texpect(getNumberType('4957777777', 'RU')).to.equal('FIXED_LINE')\r\n\t\texpect(getNumberType('8030000000', 'RU')).to.equal('PREMIUM_RATE')\r\n\r\n\t\texpect(getNumberType('2133734253', 'US')).to.equal('FIXED_LINE_OR_MOBILE')\r\n\t\texpect(getNumberType('5002345678', 'US')).to.equal('PERSONAL_NUMBER')\r\n\t})\r\n\r\n\tit('should work when no country is passed', () => {\r\n\t\texpect(getNumberType('+79150000000')).to.equal('MOBILE')\r\n\t})\r\n\r\n\tit('should return FIXED_LINE_OR_MOBILE when there is ambiguity', () => {\r\n\t\t// (no such country in the metadata, therefore no unit test for this `if`)\r\n\t})\r\n\r\n\tit('should work in edge cases', function() {\r\n\t\tlet thrower\r\n\r\n\t\t// // No metadata\r\n\t\t// thrower = () => _getNumberType({ phone: '+78005553535' })\r\n\t\t// thrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\t// Parsed phone number\r\n\t\texpect(getNumberType({ phone: '8005553535', country: 'RU' })).to.equal('TOLL_FREE')\r\n\r\n\t\t// Invalid phone number\r\n\t\texpect(type(getNumberType('123', 'RU'))).to.equal('undefined')\r\n\r\n\t\t// Invalid country\r\n\t\tthrower = () => getNumberType({ phone: '8005553535', country: 'RUS' })\r\n\t\texpect(thrower).to.throw('Unknown country')\r\n\r\n\t\t// Numerical `value`\r\n\t\tthrower = () => getNumberType(89150000000, 'RU')\r\n\t\texpect(thrower).to.throw(\r\n            'A phone number must either be a string or an object of shape { phone, [country] }.'\r\n        )\r\n\r\n\t\t// When `options` argument is passed.\r\n\t\texpect(getNumberType('8005553535', 'RU', {})).to.equal('TOLL_FREE')\r\n\t\texpect(getNumberType('+78005553535', {})).to.equal('TOLL_FREE')\r\n\t\texpect(getNumberType({ phone: '8005553535', country: 'RU' }, {})).to.equal('TOLL_FREE')\r\n\t})\r\n})\r\n\r\nfunction type(something) {\r\n\treturn typeof something\r\n}"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,yBAAyB,QAAQC,IAAI,EAAE,MAAM;AAClE,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,cAAc,MAAM,oBAAoB;AAE/C,SAASC,aAAaA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACnCF,UAAU,CAACG,IAAI,CAACX,QAAQ,CAAC;EACzB,OAAOG,cAAc,CAACS,KAAK,CAAC,IAAI,EAAEJ,UAAU,CAAC;AAC9C;AAEAK,QAAQ,CAAC,eAAe,EAAE,YAAM;EAC/BC,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjDC,MAAM,CAACX,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC5DF,MAAM,CAACX,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;IAC5DF,MAAM,CAACX,aAAa,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;EAC3D,CAAC,CAAC;EAEFH,EAAE,CAAC,iCAAiC,EAAE,YAAO;IAC5CC,MAAM,CAACX,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAChEF,MAAM,CAACX,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC/DF,MAAM,CAACX,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IAChEF,MAAM,CAACX,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAElEF,MAAM,CAACX,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,sBAAsB,CAAC;IAC1EF,MAAM,CAACX,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;EACtE,CAAC,CAAC;EAEFH,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjDC,MAAM,CAACX,aAAa,CAAC,cAAc,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,QAAQ,CAAC;EACzD,CAAC,CAAC;EAEFH,EAAE,CAAC,4DAA4D,EAAE,YAAM;IACtE;EAAA,CACA,CAAC;EAEFA,EAAE,CAAC,2BAA2B,EAAE,YAAW;IAC1C,IAAII,OAAO;;IAEX;IACA;IACA;;IAEA;IACAH,MAAM,CAACX,aAAa,CAAC;MAAEe,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;;IAEnF;IACAF,MAAM,CAACd,IAAI,CAACG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;;IAE9D;IACAC,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASd,aAAa,CAAC;QAAEe,KAAK,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAM,CAAC,CAAC;IAAA;IACtEL,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CAAC,iBAAiB,CAAC;;IAE3C;IACAE,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAASd,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC;IAAA;IAChDW,MAAM,CAACG,OAAO,CAAC,CAACF,EAAE,SAAM,CACd,oFACJ,CAAC;;IAEP;IACAD,MAAM,CAACX,aAAa,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACnEF,MAAM,CAACX,aAAa,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAACY,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAC/DF,MAAM,CAACX,aAAa,CAAC;MAAEe,KAAK,EAAE,YAAY;MAAEC,OAAO,EAAE;IAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EACxF,CAAC,CAAC;AACH,CAAC,CAAC;AAEF,SAAShB,IAAIA,CAACoB,SAAS,EAAE;EACxB,OAAAC,OAAA,CAAcD,SAAS;AACxB", "ignoreList": []}