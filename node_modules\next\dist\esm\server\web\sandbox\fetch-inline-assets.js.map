{"version": 3, "sources": ["../../../../src/server/web/sandbox/fetch-inline-assets.ts"], "names": ["createReadStream", "promises", "fs", "requestToBodyStream", "resolve", "fetchInlineAsset", "options", "inputString", "String", "input", "startsWith", "name", "replace", "asset", "assets", "find", "x", "filePath", "distDir", "fileIsReadable", "access", "then", "readStream", "context", "Response", "Uint8Array"], "mappings": "AACA,SAASA,gBAAgB,EAAEC,YAAYC,EAAE,QAAQ,KAAI;AACrD,SAASC,mBAAmB,QAAQ,qBAAoB;AACxD,SAASC,OAAO,QAAQ,OAAM;AAE9B;;;;CAIC,GACD,OAAO,eAAeC,iBAAiBC,OAKtC;IACC,MAAMC,cAAcC,OAAOF,QAAQG,KAAK;IACxC,IAAI,CAACF,YAAYG,UAAU,CAAC,UAAU;QACpC;IACF;IAEA,MAAMC,OAAOJ,YAAYK,OAAO,CAAC,SAAS;IAC1C,MAAMC,QAAQP,QAAQQ,MAAM,GACxBR,QAAQQ,MAAM,CAACC,IAAI,CAAC,CAACC,IAAMA,EAAEL,IAAI,KAAKA,QACtC;QACEA;QACAM,UAAUN;IACZ;IACJ,IAAI,CAACE,OAAO;QACV;IACF;IAEA,MAAMI,WAAWb,QAAQE,QAAQY,OAAO,EAAEL,MAAMI,QAAQ;IACxD,MAAME,iBAAiB,MAAMjB,GAAGkB,MAAM,CAACH,UAAUI,IAAI,CACnD,IAAM,MACN,IAAM;IAGR,IAAIF,gBAAgB;QAClB,MAAMG,aAAatB,iBAAiBiB;QACpC,OAAO,IAAIX,QAAQiB,OAAO,CAACC,QAAQ,CACjCrB,oBAAoBG,QAAQiB,OAAO,EAAEE,YAAYH;IAErD;AACF"}