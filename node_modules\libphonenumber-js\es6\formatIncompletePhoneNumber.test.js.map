{"version": 3, "file": "formatIncompletePhoneNumber.test.js", "names": ["formatIncompletePhoneNumber", "metadata", "type", "describe", "it", "result", "expect", "to", "equal", "defaultCountry", "defaultCallingCode"], "sources": ["../source/formatIncompletePhoneNumber.test.js"], "sourcesContent": ["import formatIncompletePhoneNumber from './formatIncompletePhoneNumber.js'\r\n\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\ndescribe('formatIncompletePhoneNumber', () => {\r\n\tit('should format parsed input value', () => {\r\n\t\tlet result\r\n\r\n\t\t// National input.\r\n\t\texpect(formatIncompletePhoneNumber('*********', 'RU', metadata)).to.equal('8 (800) 555-35')\r\n\r\n\t\t// International input, no country.\r\n\t\texpect(formatIncompletePhoneNumber('+780055535', null, metadata)).to.equal('****** 555 35')\r\n\r\n\t\t// International input, no country argument.\r\n\t\texpect(formatIncompletePhoneNumber('+780055535', metadata)).to.equal('****** 555 35')\r\n\r\n\t\t// International input, with country.\r\n\t\texpect(formatIncompletePhoneNumber('+780055535', 'RU', metadata)).to.equal('****** 555 35')\r\n\t})\r\n\r\n\tit('should support an object argument', () => {\r\n\t\texpect(\r\n            formatIncompletePhoneNumber('*********', { defaultCountry: 'RU' }, metadata)\r\n        ).to.equal('8 (800) 555-35')\r\n\t\texpect(\r\n            formatIncompletePhoneNumber('*********', { defaultCallingCode: '7' }, metadata)\r\n        ).to.equal('8 (800) 555-35')\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,2BAA2B,MAAM,kCAAkC;AAE1E,OAAOC,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAE/DC,QAAQ,CAAC,6BAA6B,EAAE,YAAM;EAC7CC,EAAE,CAAC,kCAAkC,EAAE,YAAM;IAC5C,IAAIC,MAAM;;IAEV;IACAC,MAAM,CAACN,2BAA2B,CAAC,WAAW,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;;IAE3F;IACAF,MAAM,CAACN,2BAA2B,CAAC,YAAY,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;;IAE3F;IACAF,MAAM,CAACN,2BAA2B,CAAC,YAAY,EAAEC,QAAQ,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;;IAErF;IACAF,MAAM,CAACN,2BAA2B,CAAC,YAAY,EAAE,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,eAAe,CAAC;EAC5F,CAAC,CAAC;EAEFJ,EAAE,CAAC,mCAAmC,EAAE,YAAM;IAC7CE,MAAM,CACIN,2BAA2B,CAAC,WAAW,EAAE;MAAES,cAAc,EAAE;IAAK,CAAC,EAAER,QAAQ,CAC/E,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAClCF,MAAM,CACIN,2BAA2B,CAAC,WAAW,EAAE;MAAEU,kBAAkB,EAAE;IAAI,CAAC,EAAET,QAAQ,CAClF,CAAC,CAACM,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;EACnC,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}