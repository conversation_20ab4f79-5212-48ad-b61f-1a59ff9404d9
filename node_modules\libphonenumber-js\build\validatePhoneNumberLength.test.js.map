{"version": 3, "file": "validatePhoneNumberLength.test.js", "names": ["_validatePhoneNumberLength2", "_interopRequireDefault", "require", "_metadataMin", "e", "__esModule", "validatePhoneNumberLength", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "metadata", "_validatePhoneNumberLength", "apply", "describe", "it", "expect", "to", "equal", "be", "undefined"], "sources": ["../source/validatePhoneNumberLength.test.js"], "sourcesContent": ["import _validatePhoneNumberLength from './validatePhoneNumberLength.js'\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\nfunction validatePhoneNumberLength(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _validatePhoneNumberLength.apply(this, parameters)\r\n}\r\n\r\ndescribe('validatePhoneNumberLength', () => {\r\n\tit('should detect whether a phone number length is valid', () => {\r\n\t\t// Not a phone number.\r\n\t\texpect(validatePhoneNumberLength('+')).to.equal('NOT_A_NUMBER')\r\n\t\texpect(validatePhoneNumberLength('abcde')).to.equal('NOT_A_NUMBER')\r\n\r\n\t\t// No country supplied for a national number.\r\n\t\texpect(validatePhoneNumberLength('123')).to.equal('INVALID_COUNTRY')\r\n\r\n\t\t// Too short while the number is not considered \"viable\"\r\n\t\t// by Google's `libphonenumber`.\r\n\t\texpect(validatePhoneNumberLength('2', 'US')).to.equal('TOO_SHORT')\r\n\t\texpect(validatePhoneNumberLength('+1', 'US')).to.equal('TOO_SHORT')\r\n\t\texpect(validatePhoneNumberLength('+12', 'US')).to.equal('TOO_SHORT')\r\n\r\n\t\t// Test national (significant) number length.\r\n\t\texpect(validatePhoneNumberLength('444 1 44', 'TR')).to.equal('TOO_SHORT')\r\n\t\texpect(validatePhoneNumberLength('444 1 444', 'TR')).to.be.undefined\r\n\t\texpect(validatePhoneNumberLength('444 1 4444', 'TR')).to.equal('INVALID_LENGTH')\r\n\t\texpect(validatePhoneNumberLength('444 1 4444444444', 'TR')).to.equal('TOO_LONG')\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,2BAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEjE,SAASE,yBAAyBA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC/CF,UAAU,CAACG,IAAI,CAACC,uBAAQ,CAAC;EACzB,OAAOC,sCAA0B,CAACC,KAAK,CAAC,IAAI,EAAEN,UAAU,CAAC;AAC1D;AAEAO,QAAQ,CAAC,2BAA2B,EAAE,YAAM;EAC3CC,EAAE,CAAC,sDAAsD,EAAE,YAAM;IAChE;IACAC,MAAM,CAACb,yBAAyB,CAAC,GAAG,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IAC/DF,MAAM,CAACb,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;;IAEnE;IACAF,MAAM,CAACb,yBAAyB,CAAC,KAAK,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,iBAAiB,CAAC;;IAEpE;IACA;IACAF,MAAM,CAACb,yBAAyB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IAClEF,MAAM,CAACb,yBAAyB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACnEF,MAAM,CAACb,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;;IAEpE;IACAF,MAAM,CAACb,yBAAyB,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;IACzEF,MAAM,CAACb,yBAAyB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACE,EAAE,CAACC,SAAS;IACpEJ,MAAM,CAACb,yBAAyB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAChFF,MAAM,CAACb,yBAAyB,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,UAAU,CAAC;EACjF,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}