{"version": 3, "file": "parsePhoneNumberWithError.js", "names": ["_parsePhoneNumberWithError_", "_interopRequireDefault", "require", "_normalizeArguments2", "e", "__esModule", "parsePhoneNumberWithError", "_normalizeArguments", "normalizeArguments", "arguments", "text", "options", "metadata", "parsePhoneNumberWithError_"], "sources": ["../source/parsePhoneNumberWithError.js"], "sourcesContent": ["import parsePhoneNumberWithError_ from './parsePhoneNumberWithError_.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function parsePhoneNumberWithError() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn parsePhoneNumberWithError_(text, options, metadata)\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,2BAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAwD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEzC,SAASE,yBAAyBA,CAAA,EAAG;EACnD,IAAAC,mBAAA,GAAoC,IAAAC,+BAAkB,EAACC,SAAS,CAAC;IAAzDC,IAAI,GAAAH,mBAAA,CAAJG,IAAI;IAAEC,OAAO,GAAAJ,mBAAA,CAAPI,OAAO;IAAEC,QAAQ,GAAAL,mBAAA,CAARK,QAAQ;EAC/B,OAAO,IAAAC,sCAA0B,EAACH,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAC3D", "ignoreList": []}