{"version": 3, "file": "isObject.js", "names": ["objectConstructor", "constructor", "isObject", "object", "undefined"], "sources": ["../../source/helpers/isObject.js"], "sourcesContent": ["const objectConstructor = {}.constructor;\r\n\r\nexport default function isObject(object) {\r\n  return object !== undefined && object !== null && object.constructor === objectConstructor;\r\n}\r\n"], "mappings": ";;;;;;AAAA,IAAMA,iBAAiB,GAAG,CAAC,CAAC,CAACC,WAAW;AAEzB,SAASC,QAAQA,CAACC,MAAM,EAAE;EACvC,OAAOA,MAAM,KAAKC,SAAS,IAAID,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACF,WAAW,KAAKD,iBAAiB;AAC5F", "ignoreList": []}