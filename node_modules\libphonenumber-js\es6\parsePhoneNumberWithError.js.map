{"version": 3, "file": "parsePhoneNumberWithError.js", "names": ["parsePhoneNumberWithError_", "normalizeArguments", "parsePhoneNumberWithError", "_normalizeArguments", "arguments", "text", "options", "metadata"], "sources": ["../source/parsePhoneNumberWithError.js"], "sourcesContent": ["import parsePhoneNumberWithError_ from './parsePhoneNumberWithError_.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function parsePhoneNumberWithError() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn parsePhoneNumberWithError_(text, options, metadata)\r\n}"], "mappings": "AAAA,OAAOA,0BAA0B,MAAM,iCAAiC;AACxE,OAAOC,kBAAkB,MAAM,yBAAyB;AAExD,eAAe,SAASC,yBAAyBA,CAAA,EAAG;EACnD,IAAAC,mBAAA,GAAoCF,kBAAkB,CAACG,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC/B,OAAOP,0BAA0B,CAACK,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAC3D", "ignoreList": []}