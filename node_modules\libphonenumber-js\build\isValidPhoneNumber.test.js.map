{"version": 3, "file": "isValidPhoneNumber.test.js", "names": ["_isValidPhoneNumber2", "_interopRequireDefault", "require", "_metadataMin", "e", "__esModule", "isValidPhoneNumber", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "metadata", "_isValidPhoneNumber", "apply", "describe", "it", "expect", "to", "equal", "defaultCountry"], "sources": ["../source/isValidPhoneNumber.test.js"], "sourcesContent": ["import _isValidPhoneNumber from './isValidPhoneNumber.js'\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\n\r\nfunction isValidPhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isValidPhoneNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidPhoneNumber', () => {\r\n\tit('should detect whether a phone number is valid', () => {\r\n\t\texpect(isValidPhoneNumber('8 (800) 555 35 35', 'RU')).to.equal(true)\r\n\t\texpect(isValidPhoneNumber('8 (800) 555 35 35 0', 'RU')).to.equal(false)\r\n\t\texpect(isValidPhoneNumber('Call: 8 (800) 555 35 35', 'RU')).to.equal(false)\r\n\t\texpect(isValidPhoneNumber('8 (800) 555 35 35', { defaultCountry: 'RU' })).to.equal(true)\r\n\t\texpect(isValidPhoneNumber('+7 (800) 555 35 35')).to.equal(true)\r\n\t\texpect(isValidPhoneNumber('**** (800) 555 35 35')).to.equal(false)\r\n\t\texpect(isValidPhoneNumber(' +7 (800) 555 35 35')).to.equal(false)\r\n\t\texpect(isValidPhoneNumber(' ')).to.equal(false)\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,oBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEjE,SAASE,kBAAkBA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACxCF,UAAU,CAACG,IAAI,CAACC,uBAAQ,CAAC;EACzB,OAAOC,+BAAmB,CAACC,KAAK,CAAC,IAAI,EAAEN,UAAU,CAAC;AACnD;AAEAO,QAAQ,CAAC,oBAAoB,EAAE,YAAM;EACpCC,EAAE,CAAC,+CAA+C,EAAE,YAAM;IACzDC,MAAM,CAACb,kBAAkB,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACpEF,MAAM,CAACb,kBAAkB,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACvEF,MAAM,CAACb,kBAAkB,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAC3EF,MAAM,CAACb,kBAAkB,CAAC,mBAAmB,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACxFF,MAAM,CAACb,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC/DF,MAAM,CAACb,kBAAkB,CAAC,sBAAsB,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAClEF,MAAM,CAACb,kBAAkB,CAAC,qBAAqB,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACjEF,MAAM,CAACb,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EAChD,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}