{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/icons/FrameworkIcon.tsx"], "names": ["FrameworkIcon", "framework", "svg", "data-nextjs-call-stack-framework-icon", "xmlns", "width", "height", "viewBox", "fill", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "path", "d", "mask", "id", "maskUnits", "x", "y", "circle", "cx", "cy", "r", "g", "rect", "defs", "linearGradient", "x1", "y1", "x2", "y2", "gradientUnits", "stop", "stopColor", "offset", "stopOpacity"], "mappings": ";AAEA,OAAO,SAASA,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,IAAIA,cAAc,SAAS;QACzB,qBACE,MAACC;YACCC,yCAAsC;YACtCC,OAAM;YACNC,OAAM;YACNC,QAAO;YACPC,SAAQ;YACRC,MAAK;YACLC,gBAAe;YACfC,QAAO;YACPC,eAAc;YACdC,gBAAe;YACfC,aAAY;;8BAEZ,KAACC;oBACCC,GAAE;oBACFP,MAAK;;8BAEP,KAACM;oBACCC,GAAE;oBACFP,MAAK;;;;IAIb;IAEA,qBACE,MAACN;QACCC,yCAAsC;QACtCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;;0BAEL,KAACQ;gBACCC,IAAG;gBACHC,WAAU;gBACVC,GAAE;gBACFC,GAAE;gBACFf,OAAM;gBACNC,QAAO;0BAEP,cAAA,KAACe;oBAAOC,IAAG;oBAAKC,IAAG;oBAAKC,GAAE;oBAAKhB,MAAK;;;0BAEtC,MAACiB;gBAAET,MAAK;;kCACN,KAACK;wBACCC,IAAG;wBACHC,IAAG;wBACHC,GAAE;wBACFhB,MAAK;wBACLE,QAAO;wBACPG,aAAY;;kCAEd,KAACC;wBACCC,GAAE;wBACFP,MAAK;;kCAEP,KAACkB;wBACCP,GAAE;wBACFC,GAAE;wBACFf,OAAM;wBACNC,QAAO;wBACPE,MAAK;;;;0BAGT,MAACmB;;kCACC,MAACC;wBACCX,IAAG;wBACHY,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,KAACC;gCAAKC,WAAU;;0CAChB,KAACD;gCAAKE,QAAO;gCAAID,WAAU;gCAAQE,aAAY;;;;kCAEjD,MAACT;wBACCX,IAAG;wBACHY,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,KAACC;gCAAKC,WAAU;;0CAChB,KAACD;gCAAKE,QAAO;gCAAID,WAAU;gCAAQE,aAAY;;;;;;;;AAKzD"}