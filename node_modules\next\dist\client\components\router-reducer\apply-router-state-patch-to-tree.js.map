{"version": 3, "sources": ["../../../../src/client/components/router-reducer/apply-router-state-patch-to-tree.ts"], "names": ["applyRouterStatePatchToTree", "applyPatch", "initialTree", "patchTree", "flightSegmentPath", "initialSegment", "initialParallelRoutes", "patchSegment", "patchParallelRoutes", "DEFAULT_SEGMENT_KEY", "matchSegment", "newParallelRoutes", "key", "isInPatchTreeParallelRoutes", "tree", "flightRouterState", "treePatch", "path", "segment", "parallelRoutes", "url", "refetch", "isRootLayout", "length", "addRefreshMarkerToActiveParallelSegments", "currentSegment", "parallelRouteKey", "lastSegment", "parallelRoutePatch", "slice"], "mappings": ";;;;+BA8EgBA;;;eAAAA;;;yBA1EoB;+BACP;iDAC4B;AAEzD;;CAEC,GACD,SAASC,WACPC,WAA8B,EAC9BC,SAA4B,EAC5BC,iBAAoC;IAEpC,MAAM,CAACC,gBAAgBC,sBAAsB,GAAGJ;IAChD,MAAM,CAACK,cAAcC,oBAAoB,GAAGL;IAE5C,kGAAkG;IAClG,iFAAiF;IACjF,IACEI,iBAAiBE,4BAAmB,IACpCJ,mBAAmBI,4BAAmB,EACtC;QACA,OAAOP;IACT;IAEA,IAAIQ,IAAAA,2BAAY,EAACL,gBAAgBE,eAAe;QAC9C,MAAMI,oBAA0C,CAAC;QACjD,IAAK,MAAMC,OAAON,sBAAuB;YACvC,MAAMO,8BACJ,OAAOL,mBAAmB,CAACI,IAAI,KAAK;YACtC,IAAIC,6BAA6B;gBAC/BF,iBAAiB,CAACC,IAAI,GAAGX,WACvBK,qBAAqB,CAACM,IAAI,EAC1BJ,mBAAmB,CAACI,IAAI,EACxBR;YAEJ,OAAO;gBACLO,iBAAiB,CAACC,IAAI,GAAGN,qBAAqB,CAACM,IAAI;YACrD;QACF;QAEA,IAAK,MAAMA,OAAOJ,oBAAqB;YACrC,IAAIG,iBAAiB,CAACC,IAAI,EAAE;gBAC1B;YACF;YAEAD,iBAAiB,CAACC,IAAI,GAAGJ,mBAAmB,CAACI,IAAI;QACnD;QAEA,MAAME,OAA0B;YAACT;YAAgBM;SAAkB;QAEnE,8BAA8B;QAC9B,IAAIT,WAAW,CAAC,EAAE,EAAE;YAClBY,IAAI,CAAC,EAAE,GAAGZ,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBY,IAAI,CAAC,EAAE,GAAGZ,WAAW,CAAC,EAAE;QAC1B;QAEA,IAAIA,WAAW,CAAC,EAAE,EAAE;YAClBY,IAAI,CAAC,EAAE,GAAGZ,WAAW,CAAC,EAAE;QAC1B;QAEA,OAAOY;IACT;IAEA,OAAOX;AACT;AAOO,SAASH,4BACdI,iBAAoC,EACpCW,iBAAoC,EACpCC,SAA4B,EAC5BC,IAAY;IAEZ,MAAM,CAACC,SAASC,gBAAgBC,KAAKC,SAASC,aAAa,GACzDP;IAEF,eAAe;IACf,IAAIX,kBAAkBmB,MAAM,KAAK,GAAG;QAClC,MAAMT,OAA0Bb,WAC9Bc,mBACAC,WACAZ;QAGFoB,IAAAA,yEAAwC,EAACV,MAAMG;QAE/C,OAAOH;IACT;IAEA,MAAM,CAACW,gBAAgBC,iBAAiB,GAAGtB;IAE3C,iGAAiG;IACjG,IAAI,CAACM,IAAAA,2BAAY,EAACe,gBAAgBP,UAAU;QAC1C,OAAO;IACT;IAEA,MAAMS,cAAcvB,kBAAkBmB,MAAM,KAAK;IAEjD,IAAIK;IACJ,IAAID,aAAa;QACfC,qBAAqB3B,WACnBkB,cAAc,CAACO,iBAAiB,EAChCV,WACAZ;IAEJ,OAAO;QACLwB,qBAAqB5B,4BACnBI,kBAAkByB,KAAK,CAAC,IACxBV,cAAc,CAACO,iBAAiB,EAChCV,WACAC;QAGF,IAAIW,uBAAuB,MAAM;YAC/B,OAAO;QACT;IACF;IAEA,MAAMd,OAA0B;QAC9BV,iBAAiB,CAAC,EAAE;QACpB;YACE,GAAGe,cAAc;YACjB,CAACO,iBAAiB,EAAEE;QACtB;QACAR;QACAC;KACD;IAED,qCAAqC;IACrC,IAAIC,cAAc;QAChBR,IAAI,CAAC,EAAE,GAAG;IACZ;IAEAU,IAAAA,yEAAwC,EAACV,MAAMG;IAE/C,OAAOH;AACT"}