{"version": 3, "file": "isValidNumber.js", "names": ["_isValid", "_interopRequireDefault", "require", "_getNumberType", "e", "__esModule", "isValidNumber", "_normalizeArguments", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "_isValidNumber"], "sources": ["../../source/legacy/isValidNumber.js"], "sourcesContent": ["import _isValidNumber from '../isValid.js'\r\nimport { normalizeArguments } from './getNumberType.js'\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function isValidNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isValidNumber(input, options, metadata)\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AAAuD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAEvD;AACe,SAASE,aAAaA,CAAA,EAAG;EACvC,IAAAC,mBAAA,GAAqC,IAAAC,iCAAkB,EAACC,SAAS,CAAC;IAA1DC,KAAK,GAAAH,mBAAA,CAALG,KAAK;IAAEC,OAAO,GAAAJ,mBAAA,CAAPI,OAAO;IAAEC,QAAQ,GAAAL,mBAAA,CAARK,QAAQ;EAChC;EACA,IAAI,CAACF,KAAK,CAACG,KAAK,EAAE;IACjB,OAAO,KAAK;EACb;EACA,OAAO,IAAAC,mBAAc,EAACJ,KAAK,EAAEC,OAAO,EAAEC,QAAQ,CAAC;AAChD", "ignoreList": []}