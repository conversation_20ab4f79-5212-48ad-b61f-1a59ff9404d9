{"version": 3, "file": "AsYouTypeFormatter.util.test.js", "names": ["_AsYouTypeFormatterUtil", "require", "describe", "it", "expect", "closeNonPairedParens", "to", "equal", "stripNonPairedParens", "repeat"], "sources": ["../source/AsYouTypeFormatter.util.test.js"], "sourcesContent": ["import { closeNonPairedParens, stripNonPairedParens, repeat } from './AsYouTypeFormatter.util.js'\r\n\r\ndescribe('closeNonPairedParens', () => {\r\n\tit('should close non-paired braces', () => {\r\n\t\texpect(closeNonPairedParens('(000) 123-45 (9  )', 15)).to.equal('(000) 123-45 (9  )')\r\n\t})\r\n})\r\n\r\ndescribe('stripNonPairedParens', () => {\r\n\tit('should strip non-paired braces', () => {\r\n\t\texpect(stripNonPairedParens('(000) 123-45 (9')).to.equal('(000) 123-45 9')\r\n\t\texpect(stripNonPairedParens('(000) 123-45 (9)')).to.equal('(000) 123-45 (9)')\r\n\t})\r\n})\r\n\r\ndescribe('repeat', () => {\r\n\tit('should repeat string N times', () => {\r\n\t\texpect(repeat('a', 0)).to.equal('')\r\n\t\texpect(repeat('a', 3)).to.equal('aaa')\r\n\t\texpect(repeat('a', 4)).to.equal('aaaa')\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,uBAAA,GAAAC,OAAA;AAEAC,QAAQ,CAAC,sBAAsB,EAAE,YAAM;EACtCC,EAAE,CAAC,gCAAgC,EAAE,YAAM;IAC1CC,MAAM,CAAC,IAAAC,4CAAoB,EAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,oBAAoB,CAAC;EACtF,CAAC,CAAC;AACH,CAAC,CAAC;AAEFL,QAAQ,CAAC,sBAAsB,EAAE,YAAM;EACtCC,EAAE,CAAC,gCAAgC,EAAE,YAAM;IAC1CC,MAAM,CAAC,IAAAI,4CAAoB,EAAC,iBAAiB,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC1EH,MAAM,CAAC,IAAAI,4CAAoB,EAAC,kBAAkB,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EAC9E,CAAC,CAAC;AACH,CAAC,CAAC;AAEFL,QAAQ,CAAC,QAAQ,EAAE,YAAM;EACxBC,EAAE,CAAC,8BAA8B,EAAE,YAAM;IACxCC,MAAM,CAAC,IAAAK,8BAAM,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC;IACnCH,MAAM,CAAC,IAAAK,8BAAM,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IACtCH,MAAM,CAAC,IAAAK,8BAAM,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;EACxC,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}