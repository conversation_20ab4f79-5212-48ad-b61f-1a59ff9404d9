{"version": 3, "sources": ["../../../../src/client/components/router-reducer/should-hard-navigate.ts"], "names": ["matchSegment", "shouldHardNavigate", "flightSegmentPath", "flightRouterState", "segment", "parallelRoutes", "currentSegment", "parallelRouteKey", "Array", "isArray", "lastSegment", "length", "slice"], "mappings": "AAKA,SAASA,YAAY,QAAQ,oBAAmB;AAEhD,6FAA6F;AAC7F,OAAO,SAASC,mBACdC,iBAAiC,EACjCC,iBAAoC;IAEpC,MAAM,CAACC,SAASC,eAAe,GAAGF;IAClC,2CAA2C;IAC3C,MAAM,CAACG,gBAAgBC,iBAAiB,GAAGL;IAK3C,yDAAyD;IACzD,IAAI,CAACF,aAAaM,gBAAgBF,UAAU;QAC1C,kGAAkG;QAClG,IAAII,MAAMC,OAAO,CAACH,iBAAiB;YACjC,OAAO;QACT;QAEA,sEAAsE;QACtE,OAAO;IACT;IACA,MAAMI,cAAcR,kBAAkBS,MAAM,IAAI;IAEhD,IAAID,aAAa;QACf,OAAO;IACT;IAEA,OAAOT,mBACLC,kBAAkBU,KAAK,CAAC,IACxBP,cAAc,CAACE,iBAAiB;AAEpC"}