{"version": 3, "file": "findNumbers.js", "names": ["_PhoneNumberMatcher", "_interopRequireDefault", "require", "_normalizeArguments2", "e", "__esModule", "findNumbers", "_normalizeArguments", "normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "PhoneNumberMatcher", "results", "hasNext", "push", "next"], "sources": ["../../source/legacy/findNumbers.js"], "sourcesContent": ["import PhoneNumberMatcher from '../PhoneNumberMatcher.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findNumbers() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, options, metadata)\r\n\tconst results = []\r\n\twhile (matcher.hasNext()) {\r\n\t\tresults.push(matcher.next())\r\n\t}\r\n\treturn results\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,mBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAyD,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE1C,SAASE,WAAWA,CAAA,EAAG;EACrC,IAAAC,mBAAA,GAAoC,IAAAC,+BAAkB,EAACC,SAAS,CAAC;IAAzDC,IAAI,GAAAH,mBAAA,CAAJG,IAAI;IAAEC,OAAO,GAAAJ,mBAAA,CAAPI,OAAO;IAAEC,QAAQ,GAAAL,mBAAA,CAARK,QAAQ;EAC/B,IAAMC,OAAO,GAAG,IAAIC,8BAAkB,CAACJ,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC/D,IAAMG,OAAO,GAAG,EAAE;EAClB,OAAOF,OAAO,CAACG,OAAO,CAAC,CAAC,EAAE;IACzBD,OAAO,CAACE,IAAI,CAACJ,OAAO,CAACK,IAAI,CAAC,CAAC,CAAC;EAC7B;EACA,OAAOH,OAAO;AACf", "ignoreList": []}