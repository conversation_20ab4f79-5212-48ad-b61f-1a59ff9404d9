{"version": 3, "file": "semver-compare.js", "names": ["a", "b", "split", "pa", "pb", "i", "na", "Number", "nb", "isNaN"], "sources": ["../../source/tools/semver-compare.js"], "sourcesContent": ["// Copy-pasted from:\r\n// https://github.com/substack/semver-compare/blob/master/index.js\r\n//\r\n// Inlining this function because some users reported issues with\r\n// importing from `semver-compare` in a browser with ES6 \"native\" modules.\r\n//\r\n// Fixes `semver-compare` not being able to compare versions with alpha/beta/etc \"tags\".\r\n// https://github.com/catamphetamine/libphonenumber-js/issues/381\r\nexport default function(a, b) {\r\n    a = a.split('-')\r\n    b = b.split('-')\r\n    var pa = a[0].split('.')\r\n    var pb = b[0].split('.')\r\n    for (var i = 0; i < 3; i++) {\r\n        var na = Number(pa[i])\r\n        var nb = Number(pb[i])\r\n        if (na > nb) return 1\r\n        if (nb > na) return -1\r\n        if (!isNaN(na) && isNaN(nb)) return 1\r\n        if (isNaN(na) && !isNaN(nb)) return -1\r\n    }\r\n    if (a[1] && b[1]) {\r\n        return a[1] > b[1] ? 1 : (a[1] < b[1] ? -1 : 0)\r\n    }\r\n    return !a[1] && b[1] ? 1 : (a[1] && !b[1] ? -1 : 0)\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC1BD,CAAC,GAAGA,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;EAChBD,CAAC,GAAGA,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EAChB,IAAIC,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;EACxB,IAAIE,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EACxB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxB,IAAIC,EAAE,GAAGC,MAAM,CAACJ,EAAE,CAACE,CAAC,CAAC,CAAC;IACtB,IAAIG,EAAE,GAAGD,MAAM,CAACH,EAAE,CAACC,CAAC,CAAC,CAAC;IACtB,IAAIC,EAAE,GAAGE,EAAE,EAAE,OAAO,CAAC;IACrB,IAAIA,EAAE,GAAGF,EAAE,EAAE,OAAO,CAAC,CAAC;IACtB,IAAI,CAACG,KAAK,CAACH,EAAE,CAAC,IAAIG,KAAK,CAACD,EAAE,CAAC,EAAE,OAAO,CAAC;IACrC,IAAIC,KAAK,CAACH,EAAE,CAAC,IAAI,CAACG,KAAK,CAACD,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;EAC1C;EACA,IAAIR,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAID,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAE;EACnD;EACA,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAID,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAE;AACvD", "ignoreList": []}