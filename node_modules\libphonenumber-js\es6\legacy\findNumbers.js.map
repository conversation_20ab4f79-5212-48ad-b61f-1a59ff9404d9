{"version": 3, "file": "findNumbers.js", "names": ["PhoneNumberMatcher", "normalizeArguments", "findNumbers", "_normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "results", "hasNext", "push", "next"], "sources": ["../../source/legacy/findNumbers.js"], "sourcesContent": ["import PhoneNumberMatcher from '../PhoneNumberMatcher.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function findNumbers() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\tconst matcher = new PhoneNumberMatcher(text, options, metadata)\r\n\tconst results = []\r\n\twhile (matcher.hasNext()) {\r\n\t\tresults.push(matcher.next())\r\n\t}\r\n\treturn results\r\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,0BAA0B;AACzD,OAAOC,kBAAkB,MAAM,0BAA0B;AAEzD,eAAe,SAASC,WAAWA,CAAA,EAAG;EACrC,IAAAC,mBAAA,GAAoCF,kBAAkB,CAACG,SAAS,CAAC;IAAzDC,IAAI,GAAAF,mBAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,mBAAA,CAAPG,OAAO;IAAEC,QAAQ,GAAAJ,mBAAA,CAARI,QAAQ;EAC/B,IAAMC,OAAO,GAAG,IAAIR,kBAAkB,CAACK,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC/D,IAAME,OAAO,GAAG,EAAE;EAClB,OAAOD,OAAO,CAACE,OAAO,CAAC,CAAC,EAAE;IACzBD,OAAO,CAACE,IAAI,CAACH,OAAO,CAACI,IAAI,CAAC,CAAC,CAAC;EAC7B;EACA,OAAOH,OAAO;AACf", "ignoreList": []}