{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/client.ts"], "names": ["ReactDevOverlay", "getErrorByType", "getServerError", "onBeforeRefresh", "onBuildError", "onBuildOk", "onRefresh", "onVersionInfo", "register", "unregister", "patchConsoleError", "isRegistered", "stackTraceLimit", "undefined", "onUnhandledError", "ev", "error", "Error", "stack", "message", "match", "hydrationErrorState", "warning", "details", "e", "componentStackFrames", "componentStack", "parseComponentStack", "name", "Bus", "emit", "type", "ACTION_UNHANDLED_ERROR", "reason", "frames", "parseStack", "onUnhandledRejection", "ACTION_UNHANDLED_REJECTION", "limit", "window", "addEventListener", "removeEventListener", "ACTION_BUILD_OK", "ACTION_BUILD_ERROR", "ACTION_REFRESH", "ACTION_BEFORE_REFRESH", "versionInfo", "ACTION_VERSION_INFO"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IA2IoBA,eAAe;eAAfA,wBAAe;;IAF1BC,cAAc;eAAdA,8BAAc;;IACdC,cAAc;eAAdA,+BAAc;;IATPC,eAAe;eAAfA;;IARAC,YAAY;eAAZA;;IAJAC,SAAS;eAATA;;IAQAC,SAAS;eAATA;;IAQAC,aAAa;eAAbA;;IAjDAC,QAAQ;eAARA;;IAgBAC,UAAU;eAAVA;;;;;+DApGK;4BACM;qCACS;oCAI7B;wBASA;gCA0HwB;iCACA;0EACY;AAzH3C,oEAAoE;AACpEC,IAAAA,qCAAiB;AAEjB,IAAIC,eAAe;AACnB,IAAIC,kBAAsCC;AAE1C,SAASC,iBAAiBC,EAAc;IACtC,MAAMC,QAAQD,sBAAAA,GAAIC,KAAK;IACvB,IAAI,CAACA,SAAS,CAAEA,CAAAA,iBAAiBC,KAAI,KAAM,OAAOD,MAAME,KAAK,KAAK,UAAU;QAC1E,8DAA8D;QAC9D;IACF;IAEA,IACEF,MAAMG,OAAO,CAACC,KAAK,CAAC,sDACpB;QACA,IAAIC,uCAAmB,CAACC,OAAO,EAAE;YAI7BN,MAAcO,OAAO,GAAG;gBACxB,GAAG,AAACP,MAAcO,OAAO;gBACzB,wEAAwE;gBACxE,GAAGF,uCAAmB;YACxB;QACF;QACAL,MAAMG,OAAO,IAAK;IACpB;IAEA,MAAMK,IAAIR;IACV,MAAMS,uBACJ,OAAOJ,uCAAmB,CAACK,cAAc,KAAK,WAC1CC,IAAAA,wCAAmB,EAACN,uCAAmB,CAACK,cAAc,IACtDb;IAEN,mGAAmG;IACnG,wFAAwF;IACxF,IAAIW,EAAEI,IAAI,KAAK,sBAAsBJ,EAAEI,IAAI,KAAK,uBAAuB;QACrEC,KAAIC,IAAI,CAAC;YACPC,MAAMC,8BAAsB;YAC5BC,QAAQjB;YACRkB,QAAQC,IAAAA,sBAAU,EAACX,EAAEN,KAAK;YAC1BO;QACF;IACF;AACF;AAEA,SAASW,qBAAqBrB,EAAyB;IACrD,MAAMkB,SAASlB,sBAAAA,GAAIkB,MAAM;IACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBhB,KAAI,KACxB,OAAOgB,OAAOf,KAAK,KAAK,UACxB;QACA,8DAA8D;QAC9D;IACF;IAEA,MAAMM,IAAIS;IACVJ,KAAIC,IAAI,CAAC;QACPC,MAAMM,kCAA0B;QAChCJ,QAAQA;QACRC,QAAQC,IAAAA,sBAAU,EAACX,EAAEN,KAAK;IAC5B;AACF;AAEO,SAASV;IACd,IAAIG,cAAc;QAChB;IACF;IACAA,eAAe;IAEf,IAAI;QACF,MAAM2B,QAAQrB,MAAML,eAAe;QACnCK,MAAML,eAAe,GAAG;QACxBA,kBAAkB0B;IACpB,EAAE,UAAM,CAAC;IAETC,OAAOC,gBAAgB,CAAC,SAAS1B;IACjCyB,OAAOC,gBAAgB,CAAC,sBAAsBJ;AAChD;AAEO,SAAS3B;IACd,IAAI,CAACE,cAAc;QACjB;IACF;IACAA,eAAe;IAEf,IAAIC,oBAAoBC,WAAW;QACjC,IAAI;YACFI,MAAML,eAAe,GAAGA;QAC1B,EAAE,UAAM,CAAC;QACTA,kBAAkBC;IACpB;IAEA0B,OAAOE,mBAAmB,CAAC,SAAS3B;IACpCyB,OAAOE,mBAAmB,CAAC,sBAAsBL;AACnD;AAEO,SAAS/B;IACdwB,KAAIC,IAAI,CAAC;QAAEC,MAAMW,uBAAe;IAAC;AACnC;AAEO,SAAStC,aAAae,OAAe;IAC1CU,KAAIC,IAAI,CAAC;QAAEC,MAAMY,0BAAkB;QAAExB;IAAQ;AAC/C;AAEO,SAASb;IACduB,KAAIC,IAAI,CAAC;QAAEC,MAAMa,sBAAc;IAAC;AAClC;AAEO,SAASzC;IACd0B,KAAIC,IAAI,CAAC;QAAEC,MAAMc,6BAAqB;IAAC;AACzC;AAEO,SAAStC,cAAcuC,WAAwB;IACpDjB,KAAIC,IAAI,CAAC;QAAEC,MAAMgB,2BAAmB;QAAED;IAAY;AACpD"}