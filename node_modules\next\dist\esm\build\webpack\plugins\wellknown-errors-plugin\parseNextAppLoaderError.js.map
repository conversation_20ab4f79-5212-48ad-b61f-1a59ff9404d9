{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNextAppLoaderError.ts"], "names": ["relative", "SimpleWebpackError", "getNextAppLoaderError", "err", "module", "compiler", "loaders", "loader", "includes", "file", "context", "buildInfo", "route", "absolutePagePath", "message"], "mappings": "AACA,SAASA,QAAQ,QAAQ,OAAM;AAC/B,SAASC,kBAAkB,QAAQ,uBAAsB;AAEzD,OAAO,SAASC,sBACdC,GAAU,EACVC,MAAW,EACXC,QAA0B;IAE1B,IAAI;QACF,IAAI,CAACD,OAAOE,OAAO,CAAC,EAAE,CAACC,MAAM,CAACC,QAAQ,CAAC,uBAAuB;YAC5D,OAAO;QACT;QAEA,MAAMC,OAAOT,SACXK,SAASK,OAAO,EAChBN,OAAOO,SAAS,CAACC,KAAK,CAACC,gBAAgB;QAGzC,OAAO,IAAIZ,mBAAmBQ,MAAMN,IAAIW,OAAO;IACjD,EAAE,OAAM;QACN,OAAO;IACT;AACF"}