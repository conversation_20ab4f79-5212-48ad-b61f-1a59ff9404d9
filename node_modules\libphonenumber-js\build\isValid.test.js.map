{"version": 3, "file": "isValid.test.js", "names": ["_metadataMin", "_interopRequireDefault", "require", "_isValid", "_parsePhoneNumber", "e", "__esModule", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "isValidNumber", "v2", "_len", "parameters", "Array", "_key", "undefined", "parsePhoneNumber", "extract", "metadata", "_isValidNumber", "describe", "it", "expect", "to", "equal", "defaultCountry", "country", "phone", "phoneNumberTypePatterns", "countries", "UZ", "thrower"], "sources": ["../source/isValid.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' with { type: 'json' }\r\nimport _isValidNumber from './isValid.js'\r\nimport parsePhoneNumber from './parsePhoneNumber.js'\r\n\r\nfunction isValidNumber(...parameters) {\r\n\tlet v2\r\n\tif (parameters.length < 1) {\r\n\t\t// `input` parameter.\r\n\t\tparameters.push(undefined)\r\n\t} else {\r\n\t\t// Convert string `input` to a `PhoneNumber` instance.\r\n\t\tif (typeof parameters[0] === 'string') {\r\n\t\t\tv2 = true\r\n\t\t\tparameters[0] = parsePhoneNumber(parameters[0], {\r\n\t\t\t\t...parameters[1],\r\n\t\t\t\textract: false\r\n\t\t\t}, metadata)\r\n\t\t}\r\n\t}\r\n\tif (parameters.length < 2) {\r\n\t\t// `options` parameter.\r\n\t\tparameters.push(undefined)\r\n\t}\r\n\t// Set `v2` flag.\r\n\tparameters[1] = {\r\n\t\tv2,\r\n\t\t...parameters[1]\r\n\t}\r\n\t// Add `metadata` parameter.\r\n\tparameters.push(metadata)\r\n\t// Call the function.\r\n\treturn _isValidNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('validate', () => {\r\n\tit('should validate phone numbers', () => {\r\n\t\texpect(isValidNumber('******-373-4253')).to.equal(true)\r\n\t\texpect(isValidNumber('******-373')).to.equal(false)\r\n\r\n\t\texpect(isValidNumber('******-373-4253', undefined)).to.equal(true)\r\n\r\n\t\texpect(isValidNumber('(*************', { defaultCountry: 'US' })).to.equal(true)\r\n\t\texpect(isValidNumber('(213) 37', { defaultCountry: 'US' })).to.equal(false)\r\n\r\n\t\texpect(isValidNumber({ country: 'US', phone: '2133734253' })).to.equal(true)\r\n\r\n\t\t// No \"types\" info: should return `true`.\r\n\t\texpect(isValidNumber('+380972423740')).to.equal(true)\r\n\r\n\t\texpect(isValidNumber('0912345678', { defaultCountry: 'TW' })).to.equal(true)\r\n\r\n\t\t// Moible numbers starting 07624* are Isle of Man\r\n\t\t// which has its own \"country code\" \"IM\"\r\n\t\t// which is in the \"GB\" \"country calling code\" zone.\r\n\t\t// So while this number is for \"IM\" it's still supposed to\r\n\t\t// be valid when passed \"GB\" as a default country.\r\n\t\texpect(isValidNumber('07624369230', { defaultCountry: 'GB' })).to.equal(true)\r\n\t})\r\n\r\n\tit('should refine phone number validation in case extended regular expressions are set for a country', () => {\r\n\t\t// Germany general validation must pass\r\n\t\texpect(isValidNumber('961111111', { defaultCountry: 'UZ' })).to.equal(true)\r\n\r\n\t\tconst phoneNumberTypePatterns = metadata.countries.UZ[11]\r\n\r\n\t\t// Different regular expressions for precise national number validation.\r\n\t\t// `types` index in compressed array is `9` for v1.\r\n\t\t// For v2 it's 10.\r\n\t\t// For v3 it's 11.\r\n\t\tmetadata.countries.UZ[11] =\r\n\t\t[\r\n\t\t\t[\"(?:6(?:1(?:22|3[124]|4[1-4]|5[123578]|64)|2(?:22|3[0-57-9]|41)|5(?:22|3[3-7]|5[024-8])|6\\\\d{2}|7(?:[23]\\\\d|7[69])|9(?:22|4[1-8]|6[135]))|7(?:0(?:5[4-9]|6[0146]|7[12456]|9[135-8])|1[12]\\\\d|2(?:22|3[1345789]|4[123579]|5[14])|3(?:2\\\\d|3[1578]|4[1-35-7]|5[1-57]|61)|4(?:2\\\\d|3[1-579]|7[1-79])|5(?:22|5[1-9]|6[1457])|6(?:22|3[12457]|4[13-8])|9(?:22|5[1-9])))\\\\d{5}\"],\r\n\t\t\t[\"6(?:1(?:2(?:98|2[01])|35[0-4]|50\\\\d|61[23]|7(?:[01][017]|4\\\\d|55|9[5-9]))|2(?:11\\\\d|2(?:[12]1|9[01379])|5(?:[126]\\\\d|3[0-4])|7\\\\d{2})|5(?:19[01]|2(?:27|9[26])|30\\\\d|59\\\\d|7\\\\d{2})|6(?:2(?:1[5-9]|2[0367]|38|41|52|60)|3[79]\\\\d|4(?:56|83)|7(?:[07]\\\\d|1[017]|3[07]|4[047]|5[057]|67|8[0178]|9[79])|9[0-3]\\\\d)|7(?:2(?:24|3[237]|4[5-9]|7[15-8])|5(?:7[12]|8[0589])|7(?:0\\\\d|[39][07])|9(?:0\\\\d|7[079]))|9(?:2(?:1[1267]|5\\\\d|3[01]|7[0-4])|5[67]\\\\d|6(?:2[0-26]|8\\\\d)|7\\\\d{2}))\\\\d{4}|7(?:0\\\\d{3}|1(?:13[01]|6(?:0[47]|1[67]|66)|71[3-69]|98\\\\d)|2(?:2(?:2[79]|95)|3(?:2[5-9]|6[0-6])|57\\\\d|7(?:0\\\\d|1[17]|2[27]|3[37]|44|5[057]|66|88))|3(?:2(?:1[0-6]|21|3[469]|7[159])|33\\\\d|5(?:0[0-4]|5[579]|9\\\\d)|7(?:[0-3579]\\\\d|4[0467]|6[67]|8[078])|9[4-6]\\\\d)|4(?:2(?:29|5[0257]|6[0-7]|7[1-57])|5(?:1[0-4]|8\\\\d|9[5-9])|7(?:0\\\\d|1[024589]|2[0127]|3[0137]|[46][07]|5[01]|7[5-9]|9[079])|9(?:7[015-9]|[89]\\\\d))|5(?:112|2(?:0\\\\d|2[29]|[49]4)|3[1568]\\\\d|52[6-9]|7(?:0[01578]|1[017]|[23]7|4[047]|[5-7]\\\\d|8[78]|9[079]))|6(?:2(?:2[1245]|4[2-4])|39\\\\d|41[179]|5(?:[349]\\\\d|5[0-2])|7(?:0[017]|[13]\\\\d|22|44|55|67|88))|9(?:22[128]|3(?:2[0-4]|7\\\\d)|57[05629]|7(?:2[05-9]|3[37]|4\\\\d|60|7[2579]|87|9[07])))\\\\d{4}|9[0-57-9]\\\\d{7}\"]\r\n\t\t]\r\n\r\n\t\t// Extended validation must not pass for an invalid phone number\r\n\t\texpect(isValidNumber('961111111', { defaultCountry: 'UZ' })).to.equal(false)\r\n\r\n\t\t// Extended validation must pass for a valid phone number\r\n\t\texpect(isValidNumber('912345678', { defaultCountry: 'UZ' })).to.equal(true)\r\n\r\n\t\tmetadata.countries.UZ[11] = phoneNumberTypePatterns\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// No metadata\r\n\t\tlet thrower = () => _isValidNumber('+78005553535')\r\n\t\texpect(thrower).to.throw('`metadata` argument not passed')\r\n\r\n\t\t// // Non-phone-number characters in a phone number\r\n\t\t// isValidNumber('+499821958a').should.equal(false)\r\n\t\t// isValidNumber('88005553535x', { defaultCountry: 'RU' }).should.equal(false)\r\n\r\n\t\t// Doesn't have `types` regexps in default metadata.\r\n\t\texpect(isValidNumber({ country: 'UA', phone: '300000000' })).to.equal(true)\r\n\t\texpect(isValidNumber({ country: 'UA', phone: '200000000' })).to.equal(false)\r\n\r\n\t\t// // Numerical `value`\r\n\t\t// thrower = () => isValidNumber(88005553535, { defaultCountry: 'RU' })\r\n\t\t// thrower.should.throw('A phone number must either be a string or an object of shape { phone, [country] }.')\r\n\r\n\t\t// Long country phone code\r\n\t\texpect(isValidNumber('+3725555555')).to.equal(true)\r\n\r\n\t\t// // Invalid country\r\n\t\t// thrower = () => isValidNumber({ phone: '8005553535', country: 'RUS' })\r\n\t\t// thrower.should.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should accept phone number extensions', () => {\r\n\t\t// International\r\n\t\texpect(isValidNumber('+12133734253 ext. 123')).to.equal(true)\r\n\t\t// National\r\n\t\texpect(isValidNumber('88005553535 x123', { defaultCountry: 'RU' })).to.equal(true)\r\n\t})\r\n\r\n\tit('should validate non-geographic toll-free phone numbers', () => {\r\n\t\texpect(isValidNumber('+80074454123')).to.equal(true)\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,iBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAoD,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,QAAAC,CAAA,sCAAAD,OAAA,wBAAAE,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAF,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAC,MAAA,IAAAD,CAAA,CAAAG,WAAA,KAAAF,MAAA,IAAAD,CAAA,KAAAC,MAAA,CAAAG,SAAA,qBAAAJ,CAAA,KAAAD,OAAA,CAAAC,CAAA;AAAA,SAAAK,QAAAR,CAAA,EAAAS,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAZ,CAAA,OAAAW,MAAA,CAAAE,qBAAA,QAAAV,CAAA,GAAAQ,MAAA,CAAAE,qBAAA,CAAAb,CAAA,GAAAS,CAAA,KAAAN,CAAA,GAAAA,CAAA,CAAAW,MAAA,WAAAL,CAAA,WAAAE,MAAA,CAAAI,wBAAA,CAAAf,CAAA,EAAAS,CAAA,EAAAO,UAAA,OAAAN,CAAA,CAAAO,IAAA,CAAAC,KAAA,CAAAR,CAAA,EAAAP,CAAA,YAAAO,CAAA;AAAA,SAAAS,cAAAnB,CAAA,aAAAS,CAAA,MAAAA,CAAA,GAAAW,SAAA,CAAAC,MAAA,EAAAZ,CAAA,UAAAC,CAAA,WAAAU,SAAA,CAAAX,CAAA,IAAAW,SAAA,CAAAX,CAAA,QAAAA,CAAA,OAAAD,OAAA,CAAAG,MAAA,CAAAD,CAAA,OAAAY,OAAA,WAAAb,CAAA,IAAAc,eAAA,CAAAvB,CAAA,EAAAS,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAa,yBAAA,GAAAb,MAAA,CAAAc,gBAAA,CAAAzB,CAAA,EAAAW,MAAA,CAAAa,yBAAA,CAAAd,CAAA,KAAAF,OAAA,CAAAG,MAAA,CAAAD,CAAA,GAAAY,OAAA,WAAAb,CAAA,IAAAE,MAAA,CAAAe,cAAA,CAAA1B,CAAA,EAAAS,CAAA,EAAAE,MAAA,CAAAI,wBAAA,CAAAL,CAAA,EAAAD,CAAA,iBAAAT,CAAA;AAAA,SAAAuB,gBAAAvB,CAAA,EAAAS,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAkB,cAAA,CAAAlB,CAAA,MAAAT,CAAA,GAAAW,MAAA,CAAAe,cAAA,CAAA1B,CAAA,EAAAS,CAAA,IAAAmB,KAAA,EAAAlB,CAAA,EAAAM,UAAA,MAAAa,YAAA,MAAAC,QAAA,UAAA9B,CAAA,CAAAS,CAAA,IAAAC,CAAA,EAAAV,CAAA;AAAA,SAAA2B,eAAAjB,CAAA,QAAAqB,CAAA,GAAAC,YAAA,CAAAtB,CAAA,gCAAAR,OAAA,CAAA6B,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAtB,CAAA,EAAAD,CAAA,oBAAAP,OAAA,CAAAQ,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAV,CAAA,GAAAU,CAAA,CAAAN,MAAA,CAAA6B,WAAA,kBAAAjC,CAAA,QAAA+B,CAAA,GAAA/B,CAAA,CAAAkC,IAAA,CAAAxB,CAAA,EAAAD,CAAA,gCAAAP,OAAA,CAAA6B,CAAA,UAAAA,CAAA,YAAAI,SAAA,yEAAA1B,CAAA,GAAA2B,MAAA,GAAAC,MAAA,EAAA3B,CAAA;AAEpD,SAAS4B,aAAaA,CAAA,EAAgB;EACrC,IAAIC,EAAE;EAAA,SAAAC,IAAA,GAAApB,SAAA,CAAAC,MAAA,EADmBoB,UAAU,OAAAC,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAvB,SAAA,CAAAuB,IAAA;EAAA;EAEnC,IAAIF,UAAU,CAACpB,MAAM,GAAG,CAAC,EAAE;IAC1B;IACAoB,UAAU,CAACxB,IAAI,CAAC2B,SAAS,CAAC;EAC3B,CAAC,MAAM;IACN;IACA,IAAI,OAAOH,UAAU,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACtCF,EAAE,GAAG,IAAI;MACTE,UAAU,CAAC,CAAC,CAAC,GAAG,IAAAI,4BAAgB,EAACJ,UAAU,CAAC,CAAC,CAAC,EAAAtB,aAAA,CAAAA,aAAA,KAC1CsB,UAAU,CAAC,CAAC,CAAC;QAChBK,OAAO,EAAE;MAAK,IACZC,uBAAQ,CAAC;IACb;EACD;EACA,IAAIN,UAAU,CAACpB,MAAM,GAAG,CAAC,EAAE;IAC1B;IACAoB,UAAU,CAACxB,IAAI,CAAC2B,SAAS,CAAC;EAC3B;EACA;EACAH,UAAU,CAAC,CAAC,CAAC,GAAAtB,aAAA;IACZoB,EAAE,EAAFA;EAAE,GACCE,UAAU,CAAC,CAAC,CAAC,CAChB;EACD;EACAA,UAAU,CAACxB,IAAI,CAAC8B,uBAAQ,CAAC;EACzB;EACA,OAAOC,mBAAc,CAAC9B,KAAK,CAAC,IAAI,EAAEuB,UAAU,CAAC;AAC9C;AAEAQ,QAAQ,CAAC,UAAU,EAAE,YAAM;EAC1BC,EAAE,CAAC,+BAA+B,EAAE,YAAM;IACzCC,MAAM,CAACb,aAAa,CAAC,iBAAiB,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IACvDF,MAAM,CAACb,aAAa,CAAC,YAAY,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAEnDF,MAAM,CAACb,aAAa,CAAC,iBAAiB,EAAEM,SAAS,CAAC,CAAC,CAACQ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAElEF,MAAM,CAACb,aAAa,CAAC,gBAAgB,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAChFF,MAAM,CAACb,aAAa,CAAC,UAAU,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;IAE3EF,MAAM,CAACb,aAAa,CAAC;MAAEiB,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAa,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE5E;IACAF,MAAM,CAACb,aAAa,CAAC,eAAe,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAErDF,MAAM,CAACb,aAAa,CAAC,YAAY,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE5E;IACA;IACA;IACA;IACA;IACAF,MAAM,CAACb,aAAa,CAAC,aAAa,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAC9E,CAAC,CAAC;EAEFH,EAAE,CAAC,kGAAkG,EAAE,YAAM;IAC5G;IACAC,MAAM,CAACb,aAAa,CAAC,WAAW,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAE3E,IAAMI,uBAAuB,GAAGV,uBAAQ,CAACW,SAAS,CAACC,EAAE,CAAC,EAAE,CAAC;;IAEzD;IACA;IACA;IACA;IACAZ,uBAAQ,CAACW,SAAS,CAACC,EAAE,CAAC,EAAE,CAAC,GACzB,CACC,CAAC,yWAAyW,CAAC,EAC3W,CAAC,mqCAAmqC,CAAC,CACrqC;;IAED;IACAR,MAAM,CAACb,aAAa,CAAC,WAAW,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;;IAE5E;IACAF,MAAM,CAACb,aAAa,CAAC,WAAW,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAE3EN,uBAAQ,CAACW,SAAS,CAACC,EAAE,CAAC,EAAE,CAAC,GAAGF,uBAAuB;EACpD,CAAC,CAAC;EAEFP,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC;IACA,IAAIU,OAAO,GAAG,SAAVA,OAAOA,CAAA;MAAA,OAAS,IAAAZ,mBAAc,EAAC,cAAc,CAAC;IAAA;IAClDG,MAAM,CAACS,OAAO,CAAC,CAACR,EAAE,SAAM,CAAC,gCAAgC,CAAC;;IAE1D;IACA;IACA;;IAEA;IACAD,MAAM,CAACb,aAAa,CAAC;MAAEiB,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAY,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC3EF,MAAM,CAACb,aAAa,CAAC;MAAEiB,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAY,CAAC,CAAC,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;;IAE5E;IACA;IACA;;IAEA;IACAF,MAAM,CAACb,aAAa,CAAC,aAAa,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAEnD;IACA;IACA;EACD,CAAC,CAAC;EAEFH,EAAE,CAAC,uCAAuC,EAAE,YAAM;IACjD;IACAC,MAAM,CAACb,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7D;IACAF,MAAM,CAACb,aAAa,CAAC,kBAAkB,EAAE;MAAEgB,cAAc,EAAE;IAAK,CAAC,CAAC,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACnF,CAAC,CAAC;EAEFH,EAAE,CAAC,wDAAwD,EAAE,YAAM;IAClEC,MAAM,CAACb,aAAa,CAAC,cAAc,CAAC,CAAC,CAACc,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EACrD,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}