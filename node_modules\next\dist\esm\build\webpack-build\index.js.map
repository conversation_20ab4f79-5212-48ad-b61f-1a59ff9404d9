{"version": 3, "sources": ["../../../src/build/webpack-build/index.ts"], "names": ["Log", "NextBuildContext", "Worker", "origDebug", "path", "exportTraceState", "recordTraceEvents", "debug", "ORDERED_COMPILER_NAMES", "pluginState", "deepMerge", "target", "source", "result", "key", "Object", "keys", "Array", "isArray", "webpackBuildWithWorker", "compilerNamesArg", "compilerNames", "nextBuildSpan", "prunedBuildContext", "combinedResult", "duration", "buildTraceContext", "compilerName", "curR<PERSON>ult", "worker", "join", "__dirname", "exposedMethods", "numWorkers", "maxRetries", "forkOptions", "env", "process", "NEXT_PRIVATE_BUILD_WORKER", "worker<PERSON>ain", "buildContext", "traceState", "defaultParentSpanId", "getId", "shouldSaveTraceEvents", "debugTraceEvents", "end", "telemetryState", "entriesTrace", "entryNameMap", "chunksTrace", "entryNameFilesMap", "length", "event", "webpackBuild", "with<PERSON><PERSON>ker", "webpackBuildImpl", "require"], "mappings": "AACA,YAAYA,SAAS,gBAAe;AACpC,SAASC,gBAAgB,QAAQ,mBAAkB;AAEnD,SAASC,MAAM,QAAQ,mBAAkB;AACzC,OAAOC,eAAe,2BAA0B;AAChD,OAAOC,UAAU,OAAM;AACvB,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,cAAa;AAEjE,MAAMC,QAAQJ,UAAU;AAExB,MAAMK,yBAAyB;IAC7B;IACA;IACA;CACD;AAED,IAAIC,cAAgC,CAAC;AAErC,SAASC,UAAUC,MAAW,EAAEC,MAAW;IACzC,MAAMC,SAAS;QAAE,GAAGF,MAAM;QAAE,GAAGC,MAAM;IAAC;IACtC,KAAK,MAAME,OAAOC,OAAOC,IAAI,CAACH,QAAS;QACrCA,MAAM,CAACC,IAAI,GAAGG,MAAMC,OAAO,CAACP,MAAM,CAACG,IAAI,IAClCH,MAAM,CAACG,IAAI,GAAG;eAAIH,MAAM,CAACG,IAAI;eAAMF,MAAM,CAACE,IAAI,IAAI,EAAE;SAAE,GACvD,OAAOH,MAAM,CAACG,IAAI,IAAI,YAAY,OAAOF,MAAM,CAACE,IAAI,IAAI,WACxDJ,UAAUC,MAAM,CAACG,IAAI,EAAEF,MAAM,CAACE,IAAI,IAClCD,MAAM,CAACC,IAAI;IACjB;IACA,OAAOD;AACT;AAEA,eAAeM,uBACbC,gBAAsD;IAEtD,MAAMC,gBAAgBD,oBAAoBZ;IAC1C,MAAM,EAAEc,aAAa,EAAE,GAAGC,oBAAoB,GAAGtB;IAEjDsB,mBAAmBd,WAAW,GAAGA;IAEjC,MAAMe,iBAAiB;QACrBC,UAAU;QACVC,mBAAmB,CAAC;IACtB;IAEA,KAAK,MAAMC,gBAAgBN,cAAe;YAsCpCO;QArCJ,MAAMC,SAAS,IAAI3B,OAAOE,KAAK0B,IAAI,CAACC,WAAW,YAAY;YACzDC,gBAAgB;gBAAC;aAAa;YAC9BC,YAAY;YACZC,YAAY;YACZC,aAAa;gBACXC,KAAK;oBACH,GAAGC,QAAQD,GAAG;oBACdE,2BAA2B;gBAC7B;YACF;QACF;QAEA,MAAMV,YAAY,MAAMC,OAAOU,UAAU,CAAC;YACxCC,cAAcjB;YACdI;YACAc,YAAY;gBACV,GAAGpC,kBAAkB;gBACrBqC,mBAAmB,EAAEpB,iCAAAA,cAAeqB,KAAK;gBACzCC,uBAAuB;YACzB;QACF;QACA,IAAItB,iBAAiBM,UAAUiB,gBAAgB,EAAE;YAC/CvC,kBAAkBsB,UAAUiB,gBAAgB;QAC9C;QACA,0DAA0D;QAC1D,MAAMhB,OAAOiB,GAAG;QAEhB,sBAAsB;QACtBrC,cAAcC,UAAUD,aAAamB,UAAUnB,WAAW;QAC1Dc,mBAAmBd,WAAW,GAAGA;QAEjC,IAAImB,UAAUmB,cAAc,EAAE;YAC5B9C,iBAAiB8C,cAAc,GAAGnB,UAAUmB,cAAc;QAC5D;QAEAvB,eAAeC,QAAQ,IAAIG,UAAUH,QAAQ;QAE7C,KAAIG,+BAAAA,UAAUF,iBAAiB,qBAA3BE,6BAA6BoB,YAAY,EAAE;gBAUzCpB;YATJ,MAAM,EAAEqB,YAAY,EAAE,GAAGrB,UAAUF,iBAAiB,CAACsB,YAAY;YAEjE,IAAIC,cAAc;gBAChBzB,eAAeE,iBAAiB,CAACsB,YAAY,GAC3CpB,UAAUF,iBAAiB,CAACsB,YAAY;gBAC1CxB,eAAeE,iBAAiB,CAACsB,YAAY,CAAEC,YAAY,GACzDA;YACJ;YAEA,KAAIrB,gCAAAA,UAAUF,iBAAiB,qBAA3BE,8BAA6BsB,WAAW,EAAE;gBAC5C,MAAM,EAAEC,iBAAiB,EAAE,GAAGvB,UAAUF,iBAAiB,CAACwB,WAAW;gBAErE,IAAIC,mBAAmB;oBACrB3B,eAAeE,iBAAiB,CAACwB,WAAW,GAC1CtB,UAAUF,iBAAiB,CAACwB,WAAW;oBAEzC1B,eAAeE,iBAAiB,CAACwB,WAAW,CAAEC,iBAAiB,GAC7DA;gBACJ;YACF;QACF;IACF;IAEA,IAAI9B,cAAc+B,MAAM,KAAK,GAAG;QAC9BpD,IAAIqD,KAAK,CAAC;IACZ;IAEA,OAAO7B;AACT;AAEA,OAAO,SAAS8B,aACdC,UAAmB,EACnBlC,aAAmD;IAEnD,IAAIkC,YAAY;QACdhD,MAAM;QACN,OAAOY,uBAAuBE;IAChC,OAAO;QACLd,MAAM;QACN,MAAMiD,mBAAmBC,QAAQ,UAAUD,gBAAgB;QAC3D,OAAOA,iBAAiB,MAAM;IAChC;AACF"}