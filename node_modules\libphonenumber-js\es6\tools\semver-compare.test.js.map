{"version": 3, "file": "semver-compare.test.js", "names": ["semverCompare", "describe", "it", "versions", "expect", "sort", "to", "deep", "equal"], "sources": ["../../source/tools/semver-compare.test.js"], "sourcesContent": ["import semverCompare from './semver-compare.js'\r\n\r\ndescribe('semver-compare', () => {\r\n\tit('should compare versions', () => {\r\n\t\tconst versions = [\r\n\t\t\t'1.2.3',\r\n\t\t\t'4.11.6',\r\n\t\t\t'4.2.0',\r\n\t\t\t'1.5.19',\r\n\t\t\t'1.5.6',\r\n\t\t\t'1.5.4',\r\n\t\t\t'1.5.5-alpha.beta',\r\n\t\t\t'1.5.5-alpha',\r\n\t\t\t'1.5.5',\r\n\t\t\t'1.5.5-rc.1',\r\n\t\t\t'1.5.5-beta.0',\r\n\t\t\t'4.1.3',\r\n\t\t\t'2.3.1',\r\n\t\t\t'10.5.5',\r\n\t\t\t'11.3.0'\r\n\t\t]\r\n\t\texpect(versions.sort(semverCompare)).to.deep.equal([\r\n\t\t\t'1.2.3',\r\n\t\t\t'1.5.4',\r\n\t\t\t'1.5.5-alpha',\r\n\t\t\t'1.5.5-alpha.beta',\r\n\t\t\t'1.5.5-beta.0',\r\n\t\t\t'1.5.5-rc.1',\r\n\t\t\t'1.5.5',\r\n\t\t\t'1.5.6',\r\n\t\t\t'1.5.19',\r\n\t\t\t'2.3.1',\r\n\t\t\t'4.1.3',\r\n\t\t\t'4.2.0',\r\n\t\t\t'4.11.6',\r\n\t\t\t'10.5.5',\r\n\t\t\t'11.3.0'\r\n\t\t])\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAE/CC,QAAQ,CAAC,gBAAgB,EAAE,YAAM;EAChCC,EAAE,CAAC,yBAAyB,EAAE,YAAM;IACnC,IAAMC,QAAQ,GAAG,CAChB,OAAO,EACP,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,kBAAkB,EAClB,aAAa,EACb,OAAO,EACP,YAAY,EACZ,cAAc,EACd,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,CACR;IACDC,MAAM,CAACD,QAAQ,CAACE,IAAI,CAACL,aAAa,CAAC,CAAC,CAACM,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAClD,OAAO,EACP,OAAO,EACP,aAAa,EACb,kBAAkB,EAClB,cAAc,EACd,YAAY,EACZ,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,CACR,CAAC;EACH,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}