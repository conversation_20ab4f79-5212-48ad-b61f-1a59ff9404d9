{"version": 3, "file": "Max.js", "sourceRoot": "", "sources": ["../../../../src/decorator/number/Max.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC;AAEzB;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,GAAY,EAAE,GAAW;IAC3C,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,GAAG,CAAC;AAC1E,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,QAAgB,EAAE,iBAAqC;IACzE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,GAAG;QACT,WAAW,EAAE,CAAC,QAAQ,CAAC;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACpE,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,iDAAiD,EAC5E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const MAX = 'max';\n\n/**\n * Checks if the first number is less than or equal to the second.\n */\nexport function max(num: unknown, max: number): boolean {\n  return typeof num === 'number' && typeof max === 'number' && num <= max;\n}\n\n/**\n * Checks if the value is less than or equal to the allowed maximum value.\n */\nexport function Max(maxValue: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: MAX,\n      constraints: [maxValue],\n      validator: {\n        validate: (value, args): boolean => max(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must not be greater than $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}