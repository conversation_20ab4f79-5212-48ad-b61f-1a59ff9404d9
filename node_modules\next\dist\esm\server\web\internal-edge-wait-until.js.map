{"version": 3, "sources": ["../../../src/server/web/internal-edge-wait-until.ts"], "names": ["GLOBAL_KEY", "Symbol", "for", "state", "globalThis", "waitUntilCounter", "waitUntilResolve", "undefined", "waitUntilPromise", "resolveOnePromise", "internal_getCurrentFunctionWaitUntil", "internal_runWithWaitUntil", "fn", "result", "then", "finally", "Promise", "resolve"], "mappings": "AAAA,iGAAiG;AACjG,qDAAqD;AAErD,sEAAsE;AACtE,MAAMA,aAAaC,OAAOC,GAAG,CAAC;AAE9B,MAAMC,QAKJ,aAAa;AACbC,UAAU,CAACJ,WAAW,IACtB,aAAa;AACZI,CAAAA,UAAU,CAACJ,WAAW,GAAG;IACxBK,kBAAkB;IAClBC,kBAAkBC;IAClBC,kBAAkB;AACpB,CAAA;AAEF,iFAAiF;AACjF,kFAAkF;AAClF,WAAW;AACX,SAASC;IACPN,MAAME,gBAAgB;IACtB,IAAIF,MAAME,gBAAgB,KAAK,GAAG;QAChCF,MAAMG,gBAAgB;QACtBH,MAAMK,gBAAgB,GAAG;IAC3B;AACF;AAEA,OAAO,SAASE;IACd,OAAOP,MAAMK,gBAAgB;AAC/B;AAEA,OAAO,SAASG,0BAA6BC,EAAW;IACtD,MAAMC,SAASD;IACf,IACEC,UACA,OAAOA,WAAW,YAClB,UAAUA,UACV,aAAaA,UACb,OAAOA,OAAOC,IAAI,KAAK,cACvB,OAAOD,OAAOE,OAAO,KAAK,YAC1B;QACA,IAAI,CAACZ,MAAME,gBAAgB,EAAE;YAC3B,4DAA4D;YAC5DF,MAAMK,gBAAgB,GAAG,IAAIQ,QAAc,CAACC;gBAC1Cd,MAAMG,gBAAgB,GAAGW;YAC3B;QACF;QACAd,MAAME,gBAAgB;QACtB,OAAOQ,OAAOE,OAAO,CAAC;YACpBN;QACF;IACF;IAEA,OAAOI;AACT"}