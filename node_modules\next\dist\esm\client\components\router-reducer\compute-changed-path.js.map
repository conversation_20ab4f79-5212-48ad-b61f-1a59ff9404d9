{"version": 3, "sources": ["../../../../src/client/components/router-reducer/compute-changed-path.ts"], "names": ["INTERCEPTION_ROUTE_MARKERS", "isGroupSegment", "DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "matchSegment", "removeLeadingSlash", "segment", "slice", "segmentToPathname", "normalizeSegments", "segments", "reduce", "acc", "extractPathFromFlightRouterState", "flightRouterState", "Array", "isArray", "some", "m", "startsWith", "undefined", "parallelRoutes", "<PERSON><PERSON><PERSON>", "children", "push", "key", "value", "Object", "entries", "child<PERSON><PERSON>", "computeChangedPathImpl", "treeA", "treeB", "segmentA", "parallelRoutesA", "segmentB", "parallelRoutesB", "normalizedSegmentA", "normalizedSegmentB", "parallel<PERSON><PERSON>er<PERSON>ey", "changedPath", "computeChangedPath", "split"], "mappings": "AAIA,SAASA,0BAA0B,QAAQ,qDAAoD;AAC/F,SACEC,cAAc,EACdC,mBAAmB,EACnBC,gBAAgB,QACX,8BAA6B;AACpC,SAASC,YAAY,QAAQ,oBAAmB;AAEhD,MAAMC,qBAAqB,CAACC;IAC1B,OAAOA,OAAO,CAAC,EAAE,KAAK,MAAMA,QAAQC,KAAK,CAAC,KAAKD;AACjD;AAEA,MAAME,oBAAoB,CAACF;IACzB,IAAI,OAAOA,YAAY,UAAU;QAC/B,uHAAuH;QACvH,gHAAgH;QAChH,IAAIA,YAAY,YAAY,OAAO;QAEnC,OAAOA;IACT;IAEA,OAAOA,OAAO,CAAC,EAAE;AACnB;AAEA,SAASG,kBAAkBC,QAAkB;IAC3C,OACEA,SAASC,MAAM,CAAC,CAACC,KAAKN;QACpBA,UAAUD,mBAAmBC;QAC7B,IAAIA,YAAY,MAAML,eAAeK,UAAU;YAC7C,OAAOM;QACT;QAEA,OAAO,AAAGA,MAAI,MAAGN;IACnB,GAAG,OAAO;AAEd;AAEA,OAAO,SAASO,iCACdC,iBAAoC;IAEpC,MAAMR,UAAUS,MAAMC,OAAO,CAACF,iBAAiB,CAAC,EAAE,IAC9CA,iBAAiB,CAAC,EAAE,CAAC,EAAE,GACvBA,iBAAiB,CAAC,EAAE;IAExB,IACER,YAAYJ,uBACZF,2BAA2BiB,IAAI,CAAC,CAACC,IAAMZ,QAAQa,UAAU,CAACD,KAE1D,OAAOE;IAET,IAAId,QAAQa,UAAU,CAAChB,mBAAmB,OAAO;IAEjD,MAAMO,WAAW;QAACF,kBAAkBF;KAAS;QACtBQ;IAAvB,MAAMO,iBAAiBP,CAAAA,sBAAAA,iBAAiB,CAAC,EAAE,YAApBA,sBAAwB,CAAC;IAEhD,MAAMQ,eAAeD,eAAeE,QAAQ,GACxCV,iCAAiCQ,eAAeE,QAAQ,IACxDH;IAEJ,IAAIE,iBAAiBF,WAAW;QAC9BV,SAASc,IAAI,CAACF;IAChB,OAAO;QACL,KAAK,MAAM,CAACG,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACP,gBAAiB;YACzD,IAAII,QAAQ,YAAY;YAExB,MAAMI,YAAYhB,iCAAiCa;YAEnD,IAAIG,cAAcT,WAAW;gBAC3BV,SAASc,IAAI,CAACK;YAChB;QACF;IACF;IAEA,OAAOpB,kBAAkBC;AAC3B;AAEA,SAASoB,uBACPC,KAAwB,EACxBC,KAAwB;IAExB,MAAM,CAACC,UAAUC,gBAAgB,GAAGH;IACpC,MAAM,CAACI,UAAUC,gBAAgB,GAAGJ;IAEpC,MAAMK,qBAAqB7B,kBAAkByB;IAC7C,MAAMK,qBAAqB9B,kBAAkB2B;IAE7C,IACEnC,2BAA2BiB,IAAI,CAC7B,CAACC,IACCmB,mBAAmBlB,UAAU,CAACD,MAAMoB,mBAAmBnB,UAAU,CAACD,KAEtE;QACA,OAAO;IACT;IAEA,IAAI,CAACd,aAAa6B,UAAUE,WAAW;YAE9BtB;QADP,8FAA8F;QAC9F,OAAOA,CAAAA,oCAAAA,iCAAiCmB,kBAAjCnB,oCAA2C;IACpD;IAEA,IAAK,MAAM0B,qBAAqBL,gBAAiB;QAC/C,IAAIE,eAAe,CAACG,kBAAkB,EAAE;YACtC,MAAMC,cAAcV,uBAClBI,eAAe,CAACK,kBAAkB,EAClCH,eAAe,CAACG,kBAAkB;YAEpC,IAAIC,gBAAgB,MAAM;gBACxB,OAAO,AAAGhC,kBAAkB2B,YAAU,MAAGK;YAC3C;QACF;IACF;IAEA,OAAO;AACT;AAEA,OAAO,SAASC,mBACdV,KAAwB,EACxBC,KAAwB;IAExB,MAAMQ,cAAcV,uBAAuBC,OAAOC;IAElD,IAAIQ,eAAe,QAAQA,gBAAgB,KAAK;QAC9C,OAAOA;IACT;IAEA,mDAAmD;IACnD,OAAO/B,kBAAkB+B,YAAYE,KAAK,CAAC;AAC7C"}