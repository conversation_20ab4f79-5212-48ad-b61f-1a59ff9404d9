{"version": 3, "file": "AsYouTypeFormatter.PatternMatcher.js", "names": ["_AsYouTypeFormatterPatternParser", "_interopRequireDefault", "require", "e", "__esModule", "_createForOfIteratorHelperLoose", "r", "t", "Symbol", "iterator", "call", "next", "bind", "Array", "isArray", "_unsupportedIterableToArray", "length", "o", "done", "value", "TypeError", "a", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "n", "_typeof", "prototype", "_classCallCheck", "_defineProperties", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "i", "_toPrimitive", "toPrimitive", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports", "pattern", "matchTree", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "match", "string", "_ref", "arguments", "undefined", "allowOverflow", "Error", "result", "split", "matched<PERSON><PERSON><PERSON>", "overflow", "characters", "tree", "last", "characterString", "join", "indexOf", "partialMatch", "restCharacters", "subtree", "concat", "JSON", "stringify", "op", "_iterator", "args", "_step", "branch", "_iterator2", "_step2", "char"], "sources": ["../source/AsYouTypeFormatter.PatternMatcher.js"], "sourcesContent": ["import PatternParser from './AsYouTypeFormatter.PatternParser.js'\r\n\r\nexport default class PatternMatcher {\r\n\tconstructor(pattern) {\r\n\t\tthis.matchTree = new PatternParser().parse(pattern)\r\n\t}\r\n\r\n\tmatch(string, { allowOverflow } = {}) {\r\n\t\tif (!string) {\r\n\t\t\tthrow new Error('String is required')\r\n\t\t}\r\n\t\tconst result = match(string.split(''), this.matchTree, true)\r\n\t\tif (result && result.match) {\r\n\t\t\tdelete result.matchedChars\r\n\t\t}\r\n\t\tif (result && result.overflow) {\r\n\t\t\tif (!allowOverflow) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn result\r\n\t}\r\n}\r\n\r\n/**\r\n * Matches `characters` against a pattern compiled into a `tree`.\r\n * @param  {string[]} characters\r\n * @param  {Tree} tree — A pattern compiled into a `tree`. See the `*.d.ts` file for the description of the `tree` structure.\r\n * @param  {boolean} last — Whether it's the last (rightmost) subtree on its level of the match tree.\r\n * @return {object} See the `*.d.ts` file for the description of the result object.\r\n */\r\nfunction match(characters, tree, last) {\r\n\t// If `tree` is a string, then `tree` is a single character.\r\n\t// That's because when a pattern is parsed, multi-character-string parts\r\n\t// of a pattern are compiled into arrays of single characters.\r\n\t// I still wrote this piece of code for a \"general\" hypothetical case\r\n\t// when `tree` could be a string of several characters, even though\r\n\t// such case is not possible with the current implementation.\r\n\tif (typeof tree === 'string') {\r\n\t\tconst characterString = characters.join('')\r\n\t\tif (tree.indexOf(characterString) === 0) {\r\n\t\t\t// `tree` is always a single character.\r\n\t\t\t// If `tree.indexOf(characterString) === 0`\r\n\t\t\t// then `characters.length === tree.length`.\r\n\t\t\t/* istanbul ignore else */\r\n\t\t\tif (characters.length === tree.length) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tmatch: true,\r\n\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// `tree` is always a single character.\r\n\t\t\t// If `tree.indexOf(characterString) === 0`\r\n\t\t\t// then `characters.length === tree.length`.\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\treturn {\r\n\t\t\t\tpartialMatch: true,\r\n\t\t\t\t// matchedChars: characters\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (characterString.indexOf(tree) === 0) {\r\n\t\t\tif (last) {\r\n\t\t\t\t// The `else` path is not possible because `tree` is always a single character.\r\n\t\t\t\t// The `else` case for `characters.length > tree.length` would be\r\n\t\t\t\t// `characters.length <= tree.length` which means `characters.length <= 1`.\r\n\t\t\t\t// `characters` array can't be empty, so that means `characters === [tree]`,\r\n\t\t\t\t// which would also mean `tree.indexOf(characterString) === 0` and that'd mean\r\n\t\t\t\t// that the `if (tree.indexOf(characterString) === 0)` condition before this\r\n\t\t\t\t// `if` condition would be entered, and returned from there, not reaching this code.\r\n\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\tif (characters.length > tree.length) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\toverflow: true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tmatch: true,\r\n\t\t\t\tmatchedChars: characters.slice(0, tree.length)\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn\r\n\t}\r\n\r\n\tif (Array.isArray(tree)) {\r\n\t\tlet restCharacters = characters.slice()\r\n\t\tlet i = 0\r\n\t\twhile (i < tree.length) {\r\n\t\t\tconst subtree = tree[i]\r\n\t\t\tconst result = match(restCharacters, subtree, last && (i === tree.length - 1))\r\n\t\t\tif (!result) {\r\n\t\t\t\treturn\r\n\t\t\t} else if (result.overflow) {\r\n\t\t\t\treturn result\r\n\t\t\t} else if (result.match) {\r\n\t\t\t\t// Continue with the next subtree with the rest of the characters.\r\n\t\t\t\trestCharacters = restCharacters.slice(result.matchedChars.length)\r\n\t\t\t\tif (restCharacters.length === 0) {\r\n\t\t\t\t\tif (i === tree.length - 1) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t\t\t// matchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\tif (result.partialMatch) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t\t// matchedChars: characters\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error(`Unsupported match result:\\n${JSON.stringify(result, null, 2)}`)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ti++\r\n\t\t}\r\n\t\t// If `last` then overflow has already been checked\r\n\t\t// by the last element of the `tree` array.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (last) {\r\n\t\t\treturn {\r\n\t\t\t\toverflow: true\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tmatch: true,\r\n\t\t\tmatchedChars: characters.slice(0, characters.length - restCharacters.length)\r\n\t\t}\r\n\t}\r\n\r\n\tswitch (tree.op) {\r\n\t\tcase '|':\r\n\t\t\tlet partialMatch\r\n\t\t\tfor (const branch of tree.args) {\r\n\t\t\t\tconst result = match(characters, branch, last)\r\n\t\t\t\tif (result) {\r\n\t\t\t\t\tif (result.overflow) {\r\n\t\t\t\t\t\treturn result\r\n\t\t\t\t\t} else if (result.match) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: result.matchedChars\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\t\t\tif (result.partialMatch) {\r\n\t\t\t\t\t\t\tpartialMatch = true\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthrow new Error(`Unsupported match result:\\n${JSON.stringify(result, null, 2)}`)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (partialMatch) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t// matchedChars: ...\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// Not even a partial match.\r\n\t\t\treturn\r\n\r\n\t\tcase '[]':\r\n\t\t\tfor (const char of tree.args) {\r\n\t\t\t\tif (characters[0] === char) {\r\n\t\t\t\t\tif (characters.length === 1) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (last) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\toverflow: true\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\tmatchedChars: [char]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// No character matches.\r\n\t\t\treturn\r\n\r\n\t\t/* istanbul ignore next */\r\n\t\tdefault:\r\n\t\t\tthrow new Error(`Unsupported instruction tree: ${tree}`)\r\n\t}\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,gCAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAiE,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAAA,SAAAE,gCAAAC,CAAA,EAAAH,CAAA,QAAAI,CAAA,yBAAAC,MAAA,IAAAF,CAAA,CAAAE,MAAA,CAAAC,QAAA,KAAAH,CAAA,oBAAAC,CAAA,UAAAA,CAAA,GAAAA,CAAA,CAAAG,IAAA,CAAAJ,CAAA,GAAAK,IAAA,CAAAC,IAAA,CAAAL,CAAA,OAAAM,KAAA,CAAAC,OAAA,CAAAR,CAAA,MAAAC,CAAA,GAAAQ,2BAAA,CAAAT,CAAA,MAAAH,CAAA,IAAAG,CAAA,uBAAAA,CAAA,CAAAU,MAAA,IAAAT,CAAA,KAAAD,CAAA,GAAAC,CAAA,OAAAU,CAAA,kCAAAA,CAAA,IAAAX,CAAA,CAAAU,MAAA,KAAAE,IAAA,WAAAA,IAAA,MAAAC,KAAA,EAAAb,CAAA,CAAAW,CAAA,sBAAAG,SAAA;AAAA,SAAAL,4BAAAT,CAAA,EAAAe,CAAA,QAAAf,CAAA,2BAAAA,CAAA,SAAAgB,iBAAA,CAAAhB,CAAA,EAAAe,CAAA,OAAAd,CAAA,MAAAgB,QAAA,CAAAb,IAAA,CAAAJ,CAAA,EAAAkB,KAAA,6BAAAjB,CAAA,IAAAD,CAAA,CAAAmB,WAAA,KAAAlB,CAAA,GAAAD,CAAA,CAAAmB,WAAA,CAAAC,IAAA,aAAAnB,CAAA,cAAAA,CAAA,GAAAM,KAAA,CAAAc,IAAA,CAAArB,CAAA,oBAAAC,CAAA,+CAAAqB,IAAA,CAAArB,CAAA,IAAAe,iBAAA,CAAAhB,CAAA,EAAAe,CAAA;AAAA,SAAAC,kBAAAhB,CAAA,EAAAe,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAf,CAAA,CAAAU,MAAA,MAAAK,CAAA,GAAAf,CAAA,CAAAU,MAAA,YAAAb,CAAA,MAAA0B,CAAA,GAAAhB,KAAA,CAAAQ,CAAA,GAAAlB,CAAA,GAAAkB,CAAA,EAAAlB,CAAA,IAAA0B,CAAA,CAAA1B,CAAA,IAAAG,CAAA,CAAAH,CAAA,UAAA0B,CAAA;AAAA,SAAAC,QAAAb,CAAA,sCAAAa,OAAA,wBAAAtB,MAAA,uBAAAA,MAAA,CAAAC,QAAA,aAAAQ,CAAA,kBAAAA,CAAA,gBAAAA,CAAA,WAAAA,CAAA,yBAAAT,MAAA,IAAAS,CAAA,CAAAQ,WAAA,KAAAjB,MAAA,IAAAS,CAAA,KAAAT,MAAA,CAAAuB,SAAA,qBAAAd,CAAA,KAAAa,OAAA,CAAAb,CAAA;AAAA,SAAAe,gBAAAX,CAAA,EAAAQ,CAAA,UAAAR,CAAA,YAAAQ,CAAA,aAAAT,SAAA;AAAA,SAAAa,kBAAA9B,CAAA,EAAAG,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAD,CAAA,CAAAU,MAAA,EAAAT,CAAA,UAAAU,CAAA,GAAAX,CAAA,CAAAC,CAAA,GAAAU,CAAA,CAAAiB,UAAA,GAAAjB,CAAA,CAAAiB,UAAA,QAAAjB,CAAA,CAAAkB,YAAA,kBAAAlB,CAAA,KAAAA,CAAA,CAAAmB,QAAA,QAAAC,MAAA,CAAAC,cAAA,CAAAnC,CAAA,EAAAoC,cAAA,CAAAtB,CAAA,CAAAuB,GAAA,GAAAvB,CAAA;AAAA,SAAAwB,aAAAtC,CAAA,EAAAG,CAAA,EAAAC,CAAA,WAAAD,CAAA,IAAA2B,iBAAA,CAAA9B,CAAA,CAAA4B,SAAA,EAAAzB,CAAA,GAAAC,CAAA,IAAA0B,iBAAA,CAAA9B,CAAA,EAAAI,CAAA,GAAA8B,MAAA,CAAAC,cAAA,CAAAnC,CAAA,iBAAAiC,QAAA,SAAAjC,CAAA;AAAA,SAAAoC,eAAAhC,CAAA,QAAAmC,CAAA,GAAAC,YAAA,CAAApC,CAAA,gCAAAuB,OAAA,CAAAY,CAAA,IAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAApC,CAAA,EAAAD,CAAA,oBAAAwB,OAAA,CAAAvB,CAAA,MAAAA,CAAA,SAAAA,CAAA,MAAAJ,CAAA,GAAAI,CAAA,CAAAC,MAAA,CAAAoC,WAAA,kBAAAzC,CAAA,QAAAuC,CAAA,GAAAvC,CAAA,CAAAO,IAAA,CAAAH,CAAA,EAAAD,CAAA,gCAAAwB,OAAA,CAAAY,CAAA,UAAAA,CAAA,YAAAtB,SAAA,yEAAAd,CAAA,GAAAuC,MAAA,GAAAC,MAAA,EAAAvC,CAAA;AAAA,IAE5CwC,cAAc,GAAAC,OAAA;EAClC,SAAAD,eAAYE,OAAO,EAAE;IAAAjB,eAAA,OAAAe,cAAA;IACpB,IAAI,CAACG,SAAS,GAAG,IAAIC,2CAAa,CAAC,CAAC,CAACC,KAAK,CAACH,OAAO,CAAC;EACpD;EAAC,OAAAR,YAAA,CAAAM,cAAA;IAAAP,GAAA;IAAArB,KAAA,EAED,SAAAkC,KAAKA,CAACC,MAAM,EAA0B;MAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAxC,MAAA,QAAAwC,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAJ,CAAC,CAAC;QAApBE,aAAa,GAAAH,IAAA,CAAbG,aAAa;MAC5B,IAAI,CAACJ,MAAM,EAAE;QACZ,MAAM,IAAIK,KAAK,CAAC,oBAAoB,CAAC;MACtC;MACA,IAAMC,MAAM,GAAGP,MAAK,CAACC,MAAM,CAACO,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,CAACX,SAAS,EAAE,IAAI,CAAC;MAC5D,IAAIU,MAAM,IAAIA,MAAM,CAACP,KAAK,EAAE;QAC3B,OAAOO,MAAM,CAACE,YAAY;MAC3B;MACA,IAAIF,MAAM,IAAIA,MAAM,CAACG,QAAQ,EAAE;QAC9B,IAAI,CAACL,aAAa,EAAE;UACnB;QACD;MACD;MACA,OAAOE,MAAM;IACd;EAAC;AAAA;AAGF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,MAAKA,CAACW,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACtC;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAC7B,IAAME,eAAe,GAAGH,UAAU,CAACI,IAAI,CAAC,EAAE,CAAC;IAC3C,IAAIH,IAAI,CAACI,OAAO,CAACF,eAAe,CAAC,KAAK,CAAC,EAAE;MACxC;MACA;MACA;MACA;MACA,IAAIH,UAAU,CAAChD,MAAM,KAAKiD,IAAI,CAACjD,MAAM,EAAE;QACtC,OAAO;UACNqC,KAAK,EAAE,IAAI;UACXS,YAAY,EAAEE;QACf,CAAC;MACF;MACA;MACA;MACA;MACA;MACA,OAAO;QACNM,YAAY,EAAE;QACd;MACD,CAAC;IACF;IACA,IAAIH,eAAe,CAACE,OAAO,CAACJ,IAAI,CAAC,KAAK,CAAC,EAAE;MACxC,IAAIC,IAAI,EAAE;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIF,UAAU,CAAChD,MAAM,GAAGiD,IAAI,CAACjD,MAAM,EAAE;UACpC,OAAO;YACN+C,QAAQ,EAAE;UACX,CAAC;QACF;MACD;MACA,OAAO;QACNV,KAAK,EAAE,IAAI;QACXS,YAAY,EAAEE,UAAU,CAACxC,KAAK,CAAC,CAAC,EAAEyC,IAAI,CAACjD,MAAM;MAC9C,CAAC;IACF;IACA;EACD;EAEA,IAAIH,KAAK,CAACC,OAAO,CAACmD,IAAI,CAAC,EAAE;IACxB,IAAIM,cAAc,GAAGP,UAAU,CAACxC,KAAK,CAAC,CAAC;IACvC,IAAIkB,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGuB,IAAI,CAACjD,MAAM,EAAE;MACvB,IAAMwD,OAAO,GAAGP,IAAI,CAACvB,CAAC,CAAC;MACvB,IAAMkB,MAAM,GAAGP,MAAK,CAACkB,cAAc,EAAEC,OAAO,EAAEN,IAAI,IAAKxB,CAAC,KAAKuB,IAAI,CAACjD,MAAM,GAAG,CAAE,CAAC;MAC9E,IAAI,CAAC4C,MAAM,EAAE;QACZ;MACD,CAAC,MAAM,IAAIA,MAAM,CAACG,QAAQ,EAAE;QAC3B,OAAOH,MAAM;MACd,CAAC,MAAM,IAAIA,MAAM,CAACP,KAAK,EAAE;QACxB;QACAkB,cAAc,GAAGA,cAAc,CAAC/C,KAAK,CAACoC,MAAM,CAACE,YAAY,CAAC9C,MAAM,CAAC;QACjE,IAAIuD,cAAc,CAACvD,MAAM,KAAK,CAAC,EAAE;UAChC,IAAI0B,CAAC,KAAKuB,IAAI,CAACjD,MAAM,GAAG,CAAC,EAAE;YAC1B,OAAO;cACNqC,KAAK,EAAE,IAAI;cACXS,YAAY,EAAEE;YACf,CAAC;UACF,CAAC,MAAM;YACN,OAAO;cACNM,YAAY,EAAE;cACd;YACD,CAAC;UACF;QACD;MACD,CAAC,MAAM;QACN;QACA,IAAIV,MAAM,CAACU,YAAY,EAAE;UACxB,OAAO;YACNA,YAAY,EAAE;YACd;UACD,CAAC;QACF,CAAC,MAAM;UACN,MAAM,IAAIX,KAAK,+BAAAc,MAAA,CAA+BC,IAAI,CAACC,SAAS,CAACf,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAE,CAAC;QACjF;MACD;MACAlB,CAAC,EAAE;IACJ;IACA;IACA;IACA;IACA,IAAIwB,IAAI,EAAE;MACT,OAAO;QACNH,QAAQ,EAAE;MACX,CAAC;IACF;IACA,OAAO;MACNV,KAAK,EAAE,IAAI;MACXS,YAAY,EAAEE,UAAU,CAACxC,KAAK,CAAC,CAAC,EAAEwC,UAAU,CAAChD,MAAM,GAAGuD,cAAc,CAACvD,MAAM;IAC5E,CAAC;EACF;EAEA,QAAQiD,IAAI,CAACW,EAAE;IACd,KAAK,GAAG;MACP,IAAIN,YAAY;MAChB,SAAAO,SAAA,GAAAxE,+BAAA,CAAqB4D,IAAI,CAACa,IAAI,GAAAC,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAA3D,IAAA,GAAE;QAAA,IAArB8D,MAAM,GAAAD,KAAA,CAAA5D,KAAA;QAChB,IAAMyC,OAAM,GAAGP,MAAK,CAACW,UAAU,EAAEgB,MAAM,EAAEd,IAAI,CAAC;QAC9C,IAAIN,OAAM,EAAE;UACX,IAAIA,OAAM,CAACG,QAAQ,EAAE;YACpB,OAAOH,OAAM;UACd,CAAC,MAAM,IAAIA,OAAM,CAACP,KAAK,EAAE;YACxB,OAAO;cACNA,KAAK,EAAE,IAAI;cACXS,YAAY,EAAEF,OAAM,CAACE;YACtB,CAAC;UACF,CAAC,MAAM;YACN;YACA,IAAIF,OAAM,CAACU,YAAY,EAAE;cACxBA,YAAY,GAAG,IAAI;YACpB,CAAC,MAAM;cACN,MAAM,IAAIX,KAAK,+BAAAc,MAAA,CAA+BC,IAAI,CAACC,SAAS,CAACf,OAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAE,CAAC;YACjF;UACD;QACD;MACD;MACA,IAAIU,YAAY,EAAE;QACjB,OAAO;UACNA,YAAY,EAAE;UACd;QACD,CAAC;MACF;MACA;MACA;IAED,KAAK,IAAI;MACR,SAAAW,UAAA,GAAA5E,+BAAA,CAAmB4D,IAAI,CAACa,IAAI,GAAAI,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAA/D,IAAA,GAAE;QAAA,IAAnBiE,KAAI,GAAAD,MAAA,CAAA/D,KAAA;QACd,IAAI6C,UAAU,CAAC,CAAC,CAAC,KAAKmB,KAAI,EAAE;UAC3B,IAAInB,UAAU,CAAChD,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAO;cACNqC,KAAK,EAAE,IAAI;cACXS,YAAY,EAAEE;YACf,CAAC;UACF;UACA,IAAIE,IAAI,EAAE;YACT,OAAO;cACNH,QAAQ,EAAE;YACX,CAAC;UACF;UACA,OAAO;YACNV,KAAK,EAAE,IAAI;YACXS,YAAY,EAAE,CAACqB,KAAI;UACpB,CAAC;QACF;MACD;MACA;MACA;;IAED;IACA;MACC,MAAM,IAAIxB,KAAK,kCAAAc,MAAA,CAAkCR,IAAI,CAAE,CAAC;EAC1D;AACD", "ignoreList": []}