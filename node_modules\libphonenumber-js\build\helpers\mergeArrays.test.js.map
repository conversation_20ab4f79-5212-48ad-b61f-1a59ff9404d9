{"version": 3, "file": "mergeArrays.test.js", "names": ["_mergeArrays", "_interopRequireDefault", "require", "e", "__esModule", "describe", "it", "expect", "mergeArrays", "to", "deep", "equal"], "sources": ["../../source/helpers/mergeArrays.test.js"], "sourcesContent": ["import mergeArrays from './mergeArrays.js'\r\n\r\ndescribe('mergeArrays', () => {\r\n\tit('should merge arrays', () => {\r\n\t\texpect(mergeArrays([1, 2], [2, 3])).to.deep.equal([1, 2, 3])\r\n\t})\r\n})"], "mappings": ";;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAE1CE,QAAQ,CAAC,aAAa,EAAE,YAAM;EAC7BC,EAAE,CAAC,qBAAqB,EAAE,YAAM;IAC/BC,MAAM,CAAC,IAAAC,uBAAW,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAACC,EAAE,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7D,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}