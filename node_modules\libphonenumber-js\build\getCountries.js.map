{"version": 3, "file": "getCountries.js", "names": ["_metadata", "_interopRequireDefault", "require", "e", "__esModule", "getCountries", "metadata", "<PERSON><PERSON><PERSON>"], "sources": ["../source/getCountries.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\n\r\nexport default function getCountries(metadata) {\r\n\treturn new Metadata(metadata).getCountries()\r\n}"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAoC,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,gBAAAA,CAAA;AAErB,SAASE,YAAYA,CAACC,QAAQ,EAAE;EAC9C,OAAO,IAAIC,oBAAQ,CAACD,QAAQ,CAAC,CAACD,YAAY,CAAC,CAAC;AAC7C", "ignoreList": []}