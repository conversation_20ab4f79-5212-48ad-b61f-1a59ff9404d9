{"version": 3, "file": "parsePhoneNumberWithError.test.js", "names": ["_parsePhoneNumber", "metadata", "type", "metadataFull", "parsePhoneNumber", "_len", "arguments", "length", "parameters", "Array", "_key", "push", "apply", "parsePhoneNumberFull", "_len2", "_key2", "describe", "it", "phoneNumber", "expect", "country", "to", "equal", "countryCallingCode", "nationalNumber", "number", "isPossible", "<PERSON><PERSON><PERSON><PERSON>", "getType", "be", "undefined", "carrierCode", "ext", "format", "formatNational", "formatInternational", "getURI"], "sources": ["../source/parsePhoneNumberWithError.test.js"], "sourcesContent": ["import _parsePhoneNumber from './parsePhoneNumberWithError.js'\r\nimport metadata from '../metadata.min.json' with { type: 'json' }\r\nimport metadataFull from '../metadata.max.json' with { type: 'json' }\r\n\r\nfunction parsePhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _parsePhoneNumber.apply(this, parameters)\r\n}\r\n\r\nfunction parsePhoneNumberFull(...parameters) {\r\n\tparameters.push(metadataFull)\r\n\treturn _parsePhoneNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('parsePhoneNumberWithError', () => {\r\n\tit('should parse phone numbers', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('The phone number is: 8 (800) 555 35 35. Some other text.', 'RU')\r\n\t\texpect(phoneNumber.country).to.equal('RU')\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('7')\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('8005553535')\r\n\t\texpect(phoneNumber.number).to.equal('+78005553535')\r\n\t\texpect(phoneNumber.isPossible()).to.equal(true)\r\n\t\texpect(phoneNumber.isValid()).to.equal(true)\r\n\t\t// phoneNumber.isValidForRegion('RU').should.equal(true)\r\n\t\t// Russian phone type regexps aren't included in default metadata.\r\n\t\texpect(parsePhoneNumberFull('Phone: 8 (800) 555 35 35.', 'RU').getType()).to.equal('TOLL_FREE')\r\n\t})\r\n\r\n\tit('shouldn\\'t set country when it\\'s non-derivable', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('****** 555 35 35')\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\texpect(phoneNumber.countryCallingCode).to.equal('7')\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('1115553535')\r\n\t})\r\n\r\n\tit('should parse carrier code', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('0 15 21 5555-5555', 'BR')\r\n\t\texpect(phoneNumber.carrierCode).to.equal('15')\r\n\t})\r\n\r\n\tit('should parse phone extension', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('Phone: 8 (800) 555 35 35 ext. 1234.', 'RU')\r\n\t\texpect(phoneNumber.ext).to.equal('1234')\r\n\t})\r\n\r\n\tit('should validate numbers for countries with no type regular expressions', () => {\r\n\t\texpect(parsePhoneNumber('+380391234567').isValid()).to.equal(true)\r\n\t\texpect(parsePhoneNumber('+380191234567').isValid()).to.equal(false)\r\n\t})\r\n\r\n\tit('should format numbers', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('Phone: 8 (800) 555 35 35.', 'RU')\r\n\t\texpect(phoneNumber.format('NATIONAL')).to.equal('8 (800) 555-35-35')\r\n\t\texpect(phoneNumber.formatNational()).to.equal('8 (800) 555-35-35')\r\n\t\texpect(phoneNumber.format('INTERNATIONAL')).to.equal('****** 555 35 35')\r\n\t\texpect(phoneNumber.formatInternational()).to.equal('****** 555 35 35')\r\n\t})\r\n\r\n\tit('should get tel: URI', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('Phone: 8 (800) 555 35 35 ext. 1234.', 'RU')\r\n\t\texpect(phoneNumber.getURI()).to.equal('tel:+78005553535;ext=1234')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\texpect(() => parsePhoneNumber('+78005553535', -1, {})).to.throw('Invalid second argument')\r\n\t})\r\n\r\n\tit('should throw parse errors', () => {\r\n\t\texpect(() => parsePhoneNumber('8005553535', 'XX')).to.throw('INVALID_COUNTRY')\r\n\t\texpect(() => parsePhoneNumber('+', 'RU')).to.throw('NOT_A_NUMBER')\r\n\t\texpect(() => parsePhoneNumber('a', 'RU')).to.throw('NOT_A_NUMBER')\r\n\t\texpect(() => parsePhoneNumber('1', 'RU')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('+4')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('+44')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('+443')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('+370')).to.throw('TOO_SHORT')\r\n\t\texpect(() => parsePhoneNumber('88888888888888888888', 'RU')).to.throw('TOO_LONG')\r\n\t\texpect(() => parsePhoneNumber('8 (800) 555 35 35')).to.throw('INVALID_COUNTRY')\r\n\t\texpect(() => parsePhoneNumber('+9991112233')).to.throw('INVALID_COUNTRY')\r\n\t\texpect(() => parsePhoneNumber('+9991112233', 'US')).to.throw('INVALID_COUNTRY')\r\n\t\texpect(() => parsePhoneNumber('8005553535                                                                                                                                                                                                                                                 ', 'RU')).to.throw('TOO_LONG')\r\n\t})\r\n\r\n\tit('should parse incorrect international phone numbers', () => {\r\n\t\t// Parsing national prefixes and carrier codes\r\n\t\t// is only required for local phone numbers\r\n\t\t// but some people don't understand that\r\n\t\t// and sometimes write international phone numbers\r\n\t\t// with national prefixes (or maybe even carrier codes).\r\n\t\t// http://ucken.blogspot.ru/2016/03/trunk-prefixes-in-skype4b.html\r\n\t\t// Google's original library forgives such mistakes\r\n\t\t// and so does this library, because it has been requested:\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/127\r\n\r\n\t\tlet phoneNumber\r\n\r\n\t\t// For complete numbers it should strip national prefix.\r\n\t\tphoneNumber = parsePhoneNumber('******* 215 5230')\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('8772155230')\r\n\t\texpect(phoneNumber.country).to.equal('US')\r\n\r\n\t\t// For complete numbers it should strip national prefix.\r\n\t\tphoneNumber = parsePhoneNumber('******* 555 3535')\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('8005553535')\r\n\t\texpect(phoneNumber.country).to.equal('RU')\r\n\r\n\t\t// For incomplete numbers it shouldn't strip national prefix.\r\n\t\tphoneNumber = parsePhoneNumber('******* 555 353')\r\n\t\texpect(phoneNumber.nationalNumber).to.equal('8800555353')\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t})\r\n})\r\n"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,QAAQ,MAAM,sBAAsB,QAAQC,IAAI,EAAE,MAAM;AAC/D,OAAOC,YAAY,MAAM,sBAAsB,QAAQD,IAAI,EAAE,MAAM;AAEnE,SAASE,gBAAgBA,CAAA,EAAgB;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAVF,UAAU,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACtCF,UAAU,CAACG,IAAI,CAACV,QAAQ,CAAC;EACzB,OAAOD,iBAAiB,CAACY,KAAK,CAAC,IAAI,EAAEJ,UAAU,CAAC;AACjD;AAEA,SAASK,oBAAoBA,CAAA,EAAgB;EAAA,SAAAC,KAAA,GAAAR,SAAA,CAAAC,MAAA,EAAZC,UAAU,OAAAC,KAAA,CAAAK,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAVP,UAAU,CAAAO,KAAA,IAAAT,SAAA,CAAAS,KAAA;EAAA;EAC1CP,UAAU,CAACG,IAAI,CAACR,YAAY,CAAC;EAC7B,OAAOH,iBAAiB,CAACY,KAAK,CAAC,IAAI,EAAEJ,UAAU,CAAC;AACjD;AAEAQ,QAAQ,CAAC,2BAA2B,EAAE,YAAM;EAC3CC,EAAE,CAAC,4BAA4B,EAAE,YAAM;IACtC,IAAMC,WAAW,GAAGd,gBAAgB,CAAC,0DAA0D,EAAE,IAAI,CAAC;IACtGe,MAAM,CAACD,WAAW,CAACE,OAAO,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC1CH,MAAM,CAACD,WAAW,CAACK,kBAAkB,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpDH,MAAM,CAACD,WAAW,CAACM,cAAc,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACzDH,MAAM,CAACD,WAAW,CAACO,MAAM,CAAC,CAACJ,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;IACnDH,MAAM,CAACD,WAAW,CAACQ,UAAU,CAAC,CAAC,CAAC,CAACL,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC/CH,MAAM,CAACD,WAAW,CAACS,OAAO,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAC5C;IACA;IACAH,MAAM,CAACN,oBAAoB,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAACe,OAAO,CAAC,CAAC,CAAC,CAACP,EAAE,CAACC,KAAK,CAAC,WAAW,CAAC;EAChG,CAAC,CAAC;EAEFL,EAAE,CAAC,iDAAiD,EAAE,YAAM;IAC3D,IAAMC,WAAW,GAAGd,gBAAgB,CAAC,kBAAkB,CAAC;IACxDe,MAAM,CAACD,WAAW,CAACE,OAAO,CAAC,CAACC,EAAE,CAACQ,EAAE,CAACC,SAAS;IAC3CX,MAAM,CAACD,WAAW,CAACK,kBAAkB,CAAC,CAACF,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACpDH,MAAM,CAACD,WAAW,CAACM,cAAc,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;EAC1D,CAAC,CAAC;EAEFL,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrC,IAAMC,WAAW,GAAGd,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAAC;IAC/De,MAAM,CAACD,WAAW,CAACa,WAAW,CAAC,CAACV,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;EAC/C,CAAC,CAAC;EAEFL,EAAE,CAAC,8BAA8B,EAAE,YAAM;IACxC,IAAMC,WAAW,GAAGd,gBAAgB,CAAC,qCAAqC,EAAE,IAAI,CAAC;IACjFe,MAAM,CAACD,WAAW,CAACc,GAAG,CAAC,CAACX,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;EACzC,CAAC,CAAC;EAEFL,EAAE,CAAC,wEAAwE,EAAE,YAAM;IAClFE,MAAM,CAACf,gBAAgB,CAAC,eAAe,CAAC,CAACuB,OAAO,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;IAClEH,MAAM,CAACf,gBAAgB,CAAC,eAAe,CAAC,CAACuB,OAAO,CAAC,CAAC,CAAC,CAACN,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC;EACpE,CAAC,CAAC;EAEFL,EAAE,CAAC,uBAAuB,EAAE,YAAM;IACjC,IAAMC,WAAW,GAAGd,gBAAgB,CAAC,2BAA2B,EAAE,IAAI,CAAC;IACvEe,MAAM,CAACD,WAAW,CAACe,MAAM,CAAC,UAAU,CAAC,CAAC,CAACZ,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IACpEH,MAAM,CAACD,WAAW,CAACgB,cAAc,CAAC,CAAC,CAAC,CAACb,EAAE,CAACC,KAAK,CAAC,mBAAmB,CAAC;IAClEH,MAAM,CAACD,WAAW,CAACe,MAAM,CAAC,eAAe,CAAC,CAAC,CAACZ,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;IACxEH,MAAM,CAACD,WAAW,CAACiB,mBAAmB,CAAC,CAAC,CAAC,CAACd,EAAE,CAACC,KAAK,CAAC,kBAAkB,CAAC;EACvE,CAAC,CAAC;EAEFL,EAAE,CAAC,qBAAqB,EAAE,YAAM;IAC/B,IAAMC,WAAW,GAAGd,gBAAgB,CAAC,qCAAqC,EAAE,IAAI,CAAC;IACjFe,MAAM,CAACD,WAAW,CAACkB,MAAM,CAAC,CAAC,CAAC,CAACf,EAAE,CAACC,KAAK,CAAC,2BAA2B,CAAC;EACnE,CAAC,CAAC;EAEFL,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrCE,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,yBAAyB,CAAC;EAC3F,CAAC,CAAC;EAEFJ,EAAE,CAAC,2BAA2B,EAAE,YAAM;IACrCE,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,iBAAiB,CAAC;IAC9EF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,cAAc,CAAC;IAClEF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,cAAc,CAAC;IAClEF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,WAAW,CAAC;IAC/DF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,IAAI,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,WAAW,CAAC;IAC1DF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,KAAK,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,WAAW,CAAC;IAC3DF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,MAAM,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,WAAW,CAAC;IAC5DF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,MAAM,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,WAAW,CAAC;IAC5DF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,sBAAsB,EAAE,IAAI,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,UAAU,CAAC;IACjFF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,mBAAmB,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,iBAAiB,CAAC;IAC/EF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,aAAa,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,iBAAiB,CAAC;IACzEF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,iBAAiB,CAAC;IAC/EF,MAAM,CAAC;MAAA,OAAMf,gBAAgB,CAAC,6PAA6P,EAAE,IAAI,CAAC;IAAA,EAAC,CAACiB,EAAE,SAAM,CAAC,UAAU,CAAC;EACzT,CAAC,CAAC;EAEFJ,EAAE,CAAC,oDAAoD,EAAE,YAAM;IAC9D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAIC,WAAW;;IAEf;IACAA,WAAW,GAAGd,gBAAgB,CAAC,kBAAkB,CAAC;IAClDe,MAAM,CAACD,WAAW,CAACM,cAAc,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACzDH,MAAM,CAACD,WAAW,CAACE,OAAO,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE1C;IACAJ,WAAW,GAAGd,gBAAgB,CAAC,kBAAkB,CAAC;IAClDe,MAAM,CAACD,WAAW,CAACM,cAAc,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACzDH,MAAM,CAACD,WAAW,CAACE,OAAO,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,IAAI,CAAC;;IAE1C;IACAJ,WAAW,GAAGd,gBAAgB,CAAC,iBAAiB,CAAC;IACjDe,MAAM,CAACD,WAAW,CAACM,cAAc,CAAC,CAACH,EAAE,CAACC,KAAK,CAAC,YAAY,CAAC;IACzDH,MAAM,CAACD,WAAW,CAACE,OAAO,CAAC,CAACC,EAAE,CAACQ,EAAE,CAACC,SAAS;EAC5C,CAAC,CAAC;AACH,CAAC,CAAC", "ignoreList": []}